# MustardTiger Codebase Analysis

## Project Overview

**MustardTiger** is a C99 rewrite of Street Fighter II World Warrior (MAME romset sf2ua). This is an incomplete but functional game engine that recreates the classic fighting game from the ground up, reverse-engineered from the original M68k arcade ROMs.

### Current State
- **Status**: Work in progress with many bugs
- **Platform**: Currently builds on macOS/Linux with Xcode/CMake
- **Graphics**: OpenGL-based rendering with GLUT/GLCore backends
- **Architecture**: Modular design with clear separation between game logic and platform abstraction

## Architecture Overview

### Core Components

#### 1. **FistBlue** - Game Logic Core
The heart of the game engine, containing all the original M68k game logic:

- **Game State Management**: Hierarchical state machine (`sm.c`) with multiple mode levels
- **Player System**: Complete fighter implementation with 12 characters
- **Combat System**: Collision detection, projectiles, special moves
- **AI System**: Individual AI implementations for each character
- **Animation System**: Sprite-based animation with frame data
- **Sound System**: Audio management and effects
- **Input System**: Controller handling and input processing

#### 2. **RedHammer** - Platform Abstraction Layer
Cross-platform wrapper providing:

- **Graphics Backend**: OpenGL rendering abstraction
- **Threading**: pthread-based task system
- **Memory Management**: ROM loading and endianness handling
- **Platform Services**: File I/O, timing, input mapping

#### 3. **SwiftBeam** - GUI Toolkit (Abandoned)
Basic windowing system (currently unused, candidate for removal)

### Key Data Structures

#### Game State (`struct game`)
- **Hierarchical Modes**: 7 levels of state (mode0-mode6, timer0-timer6)
- **Input State**: Raw buttons, decoded controls, demo playback
- **CPS Emulation**: Capcom Play System register emulation
- **Object Management**: Sprite layers, collision objects, effects
- **Memory Pools**: Pre-allocated object pools for performance

#### Player Structure
- **Fighter Data**: Character-specific stats, moves, animations
- **State Machine**: Position, health, special move states
- **Physics**: Position, velocity, collision boxes
- **AI Data**: Behavior patterns, difficulty scaling

#### Object System
- **Sprites**: Multi-layer sprite rendering with priorities
- **Actions**: Script-driven animation sequences
- **Collision**: Hitboxes, hurtboxes, interaction detection

### Rendering Pipeline

1. **Tile-Based Backgrounds**: 3 scrolling layers (parallax)
2. **Sprite Rendering**: Object-based sprites with Z-ordering
3. **Effects**: Particles, screen effects, transitions
4. **UI Elements**: HUD, menus, text rendering

### Input System

- **Raw Input**: Hardware button states
- **Decoded Input**: Game-specific command interpretation
- **Demo System**: Recorded input playback
- **Special Moves**: Complex input sequence detection

### Memory Management

- **ROM Data**: Original game assets loaded from binary files
- **Object Pools**: Fixed-size pools for sprites, effects, projectiles
- **Endianness**: Runtime conversion between big-endian (M68k) and host

## Technical Challenges

### 1. **Tight Coupling**
- Game functions heavily coupled to global game state
- Side effects throughout the codebase
- Difficult to unit test individual components

### 2. **Legacy Architecture**
- M68k-specific memory layout assumptions
- Global state dependencies
- Manual memory management

### 3. **Platform Dependencies**
- OpenGL 2.x/3.x mixed usage
- Platform-specific input handling
- Threading model assumptions

### 4. **Incomplete Implementation**
- Missing features and game modes
- Bugs in collision detection
- Incomplete AI behaviors

## Strengths for Rust Port

### 1. **Clear Module Boundaries**
- Well-defined separation between game logic and platform code
- Modular character implementations
- Distinct rendering and input systems

### 2. **Documented Data Structures**
- Well-commented structs and enums
- Clear naming conventions
- Comprehensive type definitions

### 3. **State Machine Architecture**
- Hierarchical state management
- Clear game flow control
- Deterministic behavior patterns

### 4. **Performance Considerations**
- Object pooling for memory efficiency
- Tile-based rendering optimization
- Fixed-point arithmetic for consistency

## ROM Dependencies

The engine requires original Street Fighter II ROM data:
- **sf2gfx.bin**: All tile ROMs, interleaved and concatenated
- **allroms.bin**: Complete ROM set for game data

This creates a legal requirement for users to own the original game.

## Build System

- **CMake**: Cross-platform build configuration
- **Dependencies**: OpenGL, GLUT, pthreads
- **Targets**: GLUT command-line app, Cocoa macOS app
- **Testing**: Basic test framework in place

## Code Quality

### Positive Aspects
- **Consistent Style**: Follows Linux kernel coding standards
- **Documentation**: Good inline comments and structure documentation
- **Modularity**: Clear separation of concerns
- **Type Safety**: Strong typing with custom type definitions

### Areas for Improvement
- **Error Handling**: Limited error checking and recovery
- **Memory Safety**: Manual memory management with potential leaks
- **Thread Safety**: Global state access without proper synchronization
- **Testing**: Minimal test coverage due to tight coupling

## Performance Characteristics

- **Real-time**: 60 FPS target with frame-based timing
- **Memory**: Fixed pools, minimal dynamic allocation
- **CPU**: Optimized for single-threaded performance
- **Graphics**: Tile-based rendering with sprite batching

This analysis provides the foundation for planning the Rust/Bevy port, identifying both opportunities and challenges in the migration process.
