[workspace]
members = [
    "sf2_engine",
    "sf2_types", 
    "sf2_assets",
    "sf2_game"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["SF2 Rust Port Team"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/sf2-rust/sf2-bevy-port"
description = "Street Fighter II engine ported to Rust using Bevy"

[workspace.dependencies]
# Bevy engine with required features
bevy = { version = "0.15", features = [
    "default",
    "bevy_asset",
    "bevy_audio",
    "bevy_gilrs",  # Gamepad support
    "bevy_text",
    "bevy_ui",
    "png",
    "vorbis",
    "x11",  # Linux support
    "wayland",  # Linux Wayland support
] }

# Math and utilities
glam = "0.29"
bytemuck = { version = "1.14", features = ["derive"] }
thiserror = "2.0"
anyhow = "1.0"
fastrand = "2.0"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
ron = "0.8"  # Rusty Object Notation for config files
bincode = "1.3"

# File I/O and compression
memmap2 = "0.9"
flate2 = "1.0"

# Logging and debugging
log = "0.4"
env_logger = "0.11"

# Development dependencies
criterion = "0.5"  # Benchmarking

[profile.dev]
opt-level = 1  # Slight optimization for better dev performance

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev.package."*"]
opt-level = 3  # Optimize dependencies even in debug mode

# Fast compile profile for development
[profile.dev-fast]
inherits = "dev"
opt-level = 0
debug = false
