/*
 *  actiondata.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 11/11/10.
 *  Copyright 2010 <PERSON>. All rights reserved.
 *
 */
#pragma mark action_1

const char data_cb60[] = { 6,6,6,6,6,2,6,6,6,6,6,0 };
const char data_cb6c[] = { 0,1,0,0,0,0,0,0,0,0,0,0 };
const short data_cc0e[] = { 31,30,31,29,31,30,31,29};


#pragma mark Act02 Bikes chun li

#pragma mark Act03 Das Boat

#pragma mark Act2e
// act2e_plane.h

#pragma mark Act35


const char data_1fab6[100]={
    0x00, 0x00, 0x01, 0x30, 0x00, 0x00, 0x18, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x0f, 0x02, 0x30, 
    0x03, 0x01, 0x18, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x18, 0x03, 0x30, 0x12, 0x02, 0x18, 0x08, 
    0x01, 0x40, 0x00, 0x00, 0x00, 0x1d, 0x04, 0x30, 0x1a, 0x03, 0x18, 0x14, 0x02, 0x40, 0x0c, 0x01, 
    0x00, 0x20, 0x05, 0x30, 0x1e, 0x04, 0x18, 0x1b, 0x03, 0x40, 0x16, 0x02, 0x00, 0x00, 0x00, 0x30, 
    0x21, 0x05, 0x18, 0x1f, 0x04, 0x40, 0x1c, 0x03, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x21, 
    0x05, 0x40, 0x20, 0x04, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x00, 0x00, 0x40, 0x21, 0x05, 
    0x00, 0x00, 0xff, 0xff,  };
/* next address 0001fb1a */

const char data_1fb1a[40]={
    0x04, 0xf0, 0x01, 0x00, 0xf1, 0x00, 0x0b, 0xf3, 0x00, 0x04, 0xf3, 0x00, 0xfe, 0xf2, 0x01, 0x0a, 
    0xf4, 0x00, 0x00, 0xf4, 0x02, 0xfc, 0xf4, 0x02, 0x06, 0xf6, 0x01, 0xfd, 0xf6, 0x02, 0xf7, 0xf4, 
    0x01, 0x03, 0xf5, 0x02, 0x00, 0x00, 0xff, 0xff,  };
/* next address 0001fb42 */

#pragma mark Palettes

const u16 data_160ea[12][16] = {
    { 0x0111, 0x0fd9, 0x0fb8, 0x0e97, 0x0c86, 0x0965, 0x0643, 0x0fff, 0x0ddb, 0x0ba8, 0x0999, 0x0765, 0x0f00, 0x0b00, 0x0700, 0x0000, },
    { 0x0222, 0x0eec, 0x0ed9, 0x0fb7, 0x0e96, 0x0c75, 0x0a54, 0x0850, 0x0234, 0x0456, 0x0789, 0x0999, 0x0cde, 0x0900, 0x0d43, 0x0000, },
    { 0x0111, 0x0ffc, 0x0ef8, 0x0dc4, 0x0ba0, 0x0980, 0x0760, 0x0650, 0x0fc0, 0x0f80, 0x0d60, 0x0a40, 0x0830, 0x0ccd, 0x0999, 0x0000, },
    { 0x0111, 0x0999, 0x0fff, 0x0fec, 0x0fca, 0x0da8, 0x0b86, 0x0964, 0x0ac8, 0x08a6, 0x0684, 0x0ff7, 0x0db0, 0x0f40, 0x066a, 0x0000, },
    { 0x0111, 0x0fd9, 0x0fb8, 0x0e97, 0x0c86, 0x0965, 0x0643, 0x0f00, 0x0d00, 0x0b00, 0x0900, 0x0999, 0x0fff, 0x0fc5, 0x0e90, 0x0000, },
    { 0x0111, 0x0bff, 0x06de, 0x0d50, 0x0e70, 0x0e94, 0x0fc6, 0x0999, 0x0fff, 0x0fec, 0x0fca, 0x0e98, 0x0c76, 0x0a54, 0x0843, 0x0000, },
    { 0x0111, 0x0640, 0x0854, 0x0a75, 0x0c97, 0x0db9, 0x0edb, 0x0ffd, 0x0999, 0x0a00, 0x0d44, 0x0f55, 0x0008, 0x08af, 0x0eef, 0x0000, },
    { 0x0111, 0x0720, 0x0853, 0x0975, 0x0b86, 0x0da7, 0x0fc9, 0x0feb, 0x0eed, 0x0ccb, 0x0999, 0x0777, 0x0f80, 0x0e60, 0x0c20, 0x0000, },
};
/* next address 0001626a */


