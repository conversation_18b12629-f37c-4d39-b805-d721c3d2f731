/*
 *  aibyte_balrog.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */


/* AGG0 Balrog */
const u8 data_a0b7c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a0b70[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	<PERSON>B_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a0b64[] = { <PERSON><PERSON>_TYPE4, 
	AI<PERSON>_LONGWALK, 0x42, 0x00, 
	<PERSON>B<PERSON><PERSON>ANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a0b26[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a0b40[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a0b52[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a0b8e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a0b34[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};

/* AGG1 Balrog */
const u8 data_a11e8[] = { AIB_TYPE4, 
	AIB_EXIT5_1, 0x00, 
	AIB_JUMP, 0x10, 0x00, 0x40, 0x70, 
	AIB_JUMP, 0x10, 0x04, 0x50, 0x70, 
	AIB_LONGWALK, 0x05, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LONGWALK, 0x09, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LONGWALK, 0x09, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_ATTACK, 0x40, 0x05, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x00, 0x40, 0x70, 
	AIB_JUMP, 0x10, 0x02, 0x40, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_LONGWALK, 0x09, 0x00, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_LONGWALK, 0x09, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LONGWALK, 0x06, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LONGWALK, 0x04, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_KICK, 0x40, 0x05, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x40, 0x05, 0x00, 
	AIB_JUMP, 0x10, 0x04, 0x40, 0x70, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_EXIT4, 0x00, 
};
const u8 data_a0c2a[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0f5e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a11a8[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1088[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0f3c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0cc2[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a10b8[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0ee0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0d9c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0f80[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0c90[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0df0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0fa2[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0cf4[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a1068[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a11c8[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0bc6[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0d6a[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a1128[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1028[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0d38[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0c4c[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0c6e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0d16[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a104a[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a1148[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0eb6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0dc6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0e8c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0fc0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0e5a[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0fe4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0e28[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a10e8[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1108[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1178[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0bf8[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x11, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0ba4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a0f18[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_a1006[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};

/* DEF  Balrog */
const u8 data_ab864[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ab868[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ab86c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_ab87a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_ab880[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ab8f4[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ab8f8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ab8fc[] = { AIB_TYPE2, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_ab90a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_ab910[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ab984[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ab988[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ab98c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_ab99a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_ab9a0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aba0e[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aba12[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aba16[] = { AIB_TYPE2, 
	AIB_SHORTWALK, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_aba76[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x58, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_aba8a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aba8e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x12, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x58, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_abaa2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_abb04[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x58, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_abb18[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_abb1c[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x12, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x58, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_abb30[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_abb98[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_abc10[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x20, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_abc1c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_abc68[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x40, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x40, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_abc84[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_abc88[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_abce8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_abd7e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_abe10[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_abe14[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_abe18[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_abe1e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_abe24[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_abeb2[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_abeb6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_abeba[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_abec0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_abed4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x7e, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x28, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x7e, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x28, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_abf5a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_abf5e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_abf62[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_abf68[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_abf70[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LABEL_B2, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x10, 0x00, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_abff6[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac000[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac076[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_a148e = {
	{3, 3, 3, 3, 3, 4, 4, 4, 1, 1, 1, 1, 3, 4, 4, 4, 1, 1, 1, 1, 2, 2, 2, 2, 0, 0, 0, 2, 2, 2, 2, 0, },
	{data_a1128, data_a1148, data_a1178, data_a11a8, data_a11c8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, data_a11e8, }
};
const AIAggTable data_a144e = {
	{3, 3, 3, 3, 3, 4, 4, 4, 1, 1, 1, 1, 3, 4, 4, 4, 1, 1, 1, 1, 2, 2, 2, 2, 0, 0, 0, 2, 2, 2, 2, 0, },
	{data_a1068, data_a1088, data_a10b8, data_a10e8, data_a1108, data_a1128, data_a1128, data_a1128, data_a1128, data_a1128, data_a1128, data_a1128, data_a1128, data_a1128, data_a1128, data_a1128, }
};
const AIAggTable data_a140e = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 2, 4, 4, 4, 1, 1, 3, 3, 3, 4, 4, 4, },
	{data_a0fc0, data_a0fe4, data_a1006, data_a1028, data_a104a, data_a1068, data_a1068, data_a1068, data_a1068, data_a1068, data_a1068, data_a1068, data_a1068, data_a1068, data_a1068, data_a1068, }
};
const AIAggTable data_a13ce = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 2, 4, 4, 4, 1, 1, 3, 3, 3, 4, 4, 4, },
	{data_a0f18, data_a0f3c, data_a0f5e, data_a0f80, data_a0fa2, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, data_a0fc0, }
};
const AIAggTable data_a138e = {
	{2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, },
	{data_a0e28, data_a0e5a, data_a0e8c, data_a0eb6, data_a0ee0, data_a0f18, data_a0f18, data_a0f18, data_a0f18, data_a0f18, data_a0f18, data_a0f18, data_a0f18, data_a0f18, data_a0f18, data_a0f18, }
};
const AIAggTable data_a134e = {
	{2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, },
	{data_a0d38, data_a0d6a, data_a0d9c, data_a0dc6, data_a0df0, data_a0e28, data_a0e28, data_a0e28, data_a0e28, data_a0e28, data_a0e28, data_a0e28, data_a0e28, data_a0e28, data_a0e28, data_a0e28, }
};
const AIAggTable data_a130e = {
	{0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, },
	{data_a0c6e, data_a0c90, data_a0cc2, data_a0cf4, data_a0d16, data_a0d38, data_a0d38, data_a0d38, data_a0d38, data_a0d38, data_a0d38, data_a0d38, data_a0d38, data_a0d38, data_a0d38, data_a0d38, }
};
const AIAggTable data_a12ce = {
	{0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, },
	{data_a0ba4, data_a0bc6, data_a0bf8, data_a0c2a, data_a0c4c, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, data_a0c6e, }
};
const u8 *data_a127e[8]={
	data_a0b8e,
	data_a0b7c,
	data_a0b70,
	data_a0b64,
	data_a0b52,
	data_a0b40,
	data_a0b34,
	data_a0b26,
};
const AIAggTable *data_a12ae[8]={
	&data_a148e,
	&data_a144e,
	&data_a140e,
	&data_a13ce,
	&data_a138e,
	&data_a134e,
	&data_a130e,
	&data_a12ce,
};
const u8 **data_a126e[]={
	data_a127e, 		/* vs Ryu */
	data_a127e, 		/* vs E.Honda */
	data_a127e, 		/* vs Blanka */
	data_a127e, 		/* vs Guile */
	data_a127e, 		/* vs Ken */
	data_a127e, 		/* vs Chun-Li */
	data_a127e, 		/* vs Zangeif */
	data_a127e, 		/* vs Dhalsim */
};
const AIAggTable **data_a129e[]={
	data_a12ae, 		/* vs Ryu */
	data_a12ae, 		/* vs E.Honda */
	data_a12ae, 		/* vs Blanka */
	data_a12ae, 		/* vs Guile */
	data_a12ae, 		/* vs Ken */
	data_a12ae, 		/* vs Chun-Li */
	data_a12ae, 		/* vs Zangeif */
	data_a12ae, 		/* vs Dhalsim */
};
struct dualptr data_a0b22={data_a126e, data_a129e};
const struct defense_strategy data_ab816={ 
    { 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_ab864, data_ab868, data_ab86c, data_ab87a, data_ab880, },
};
const struct defense_strategy data_ab8a6={ 
    { 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_ab8f4, data_ab8f8, data_ab8fc, data_ab90a, data_ab910, },
};
const struct defense_strategy data_ab936={ 
    { 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_ab984, data_ab988, data_ab98c, data_ab99a, data_ab9a0, },
};
const struct defense_strategy data_ab9c6={ 
    { 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 1, 2, 2, 0, 1, 2, 0, 2, 2, 0, 0, 2,  },
    { 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 1, 2, 2, 0, 1, 2, 0, 2, 2, 0, 0, 2,  },
    { data_aba0e, data_aba12, data_aba16, },
};
const struct defense_strategy data_aba2c={ 
    { 2, 2, 2, 2, 2, 2, 3, 3, 2, 2, 2, 2, 0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3,  },
    { 2, 2, 2, 2, 2, 2, 3, 3, 2, 2, 2, 2, 0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3,  },
    { data_aba76, data_aba8a, data_aba8e, data_abaa2, },
};
const struct defense_strategy data_ababa={ 
    { 2, 2, 2, 2, 2, 2, 3, 3, 2, 2, 2, 2, 0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3,  },
    { 2, 2, 2, 2, 2, 2, 3, 3, 2, 2, 2, 2, 0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3,  },
    { data_abb04, data_abb18, data_abb1c, data_abb30, },
};
const struct defense_strategy data_abb48={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_abb98, },
};
const struct defense_strategy data_abbcc={ 
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { data_abc10, data_abc1c, },
};
const struct defense_strategy data_abc20={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_abc68, data_abc84, data_abc88, },
};
const struct defense_strategy data_abc98={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_abce8, },
};
const struct defense_strategy data_abd32={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_abd7e, },
};
const struct defense_strategy data_abdc2={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_abe10, data_abe14, data_abe18, data_abe1e, data_abe24, },
};
const struct defense_strategy data_abe64={ 
    { 4, 4, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_abeb2, data_abeb6, data_abeba, data_abec0, data_abed4, },
};
const struct defense_strategy data_abf0c={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 4, 4, 4, 3, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 4, 4, 4, 3, 4, 4, 4, 4,  },
    { data_abf5a, data_abf5e, data_abf62, data_abf68, data_abf70, },
};
const struct defense_strategy data_abfa8={ 
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { data_abff6, data_ac000, },
};
const struct defense_strategy data_ac028={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_ac076, },
};
const struct defense_strategy *data_ab7d6[]={&data_ab816, &data_ab8a6, &data_ab936, &data_ab9c6, &data_aba2c, &data_ababa, &data_abb48, &data_abbcc, &data_abc20, &data_abc98, &data_abd32, &data_abdc2, &data_abe64, &data_abf0c, &data_abfa8, &data_ac028,  };

/* END Balrog */


