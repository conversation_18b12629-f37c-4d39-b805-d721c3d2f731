/*
 *  aibyte_blanka.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */



/* AGG0 Blanka */
const u8 data_9ae32[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};

/* AGG1 Blanka */
const u8 data_9b0ce[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	<PERSON><PERSON>_JUMP, 0x11, 0x12, 0x80, 0x50, 
	<PERSON><PERSON>_EXITRAND, 
};
const u8 data_9b32e[] = { AIB_TYPE4, 
	<PERSON>B_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9aeaa[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x4a, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9afb6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b1b8[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x04, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9af14[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SET_0216, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b02c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_JUMP, 0x11, 0x12, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9b090[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x02, 0x00, 
	AIB_JUMP, 0x21, 0x12, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9b452[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b358[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x10, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x10, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9aff6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SET_0216, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b524[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b308[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x60, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b382[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_STANDSTILL, 0x82, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9ae48[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b0fc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b548[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b13a[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x40, 
	AIB_STANDSTILL, 0x82, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b3fa[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b222[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b084[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_EXITRAND, 
};
const u8 data_9b416[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9ae84[] = { AIB_TYPE0, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_SET_0216, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b266[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_STANDSTILL, 0x82, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b564[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b4f2[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b0ba[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x04, 0x00, 
	AIB_JUMP, 0x21, 0x12, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9afde[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b06c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x22, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x22, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_EXITRAND, 
};
const u8 data_9b588[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b3d6[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b00c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_EXITRAND, 
};
const u8 data_9b160[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b2be[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x04, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b2e6[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b188[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b0e4[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x22, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x22, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_EXITRAND, 
};
const u8 data_9b3a4[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b11c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9aed4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9af2a[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b47c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9af66[] = { AIB_TYPE0, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_SET_0216, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b240[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x40, 
	AIB_STANDSTILL, 0x82, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9af8c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x4a, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b018[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x02, 0x00, 
	AIB_JUMP, 0x21, 0x12, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9b042[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x04, 0x00, 
	AIB_JUMP, 0x21, 0x12, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9b202[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b28e[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9af44[] = { AIB_TYPE0, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b056[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_JUMP, 0x11, 0x12, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9b5a0[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x81, 0x40, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b4a6[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x10, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x10, 0x14, 0x90, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b43a[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9aefc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b1e0[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_JUMP, 0x20, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b0a4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_JUMP, 0x11, 0x12, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9ae62[] = { AIB_TYPE0, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_STANDSTILL, 0x82, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9b4d0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_STANDSTILL, 0x82, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};

/* DEF  Blanka */
const u8 data_a4302[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x84, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a4316[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a431a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x84, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4334[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4396[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x84, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a43aa[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a43ae[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a43c8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a442a[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x84, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a443e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a4442[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a445c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a44c0[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a44c4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a44c8[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a44d4[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a44e0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a4536[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a453a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a453e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x10, 0x00, 0x80, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x14, 
	AIB_RESTART, 
};
const u8 data_a4554[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a4560[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a456a[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a4574[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a45cc[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a45d0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a45d4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a45dc[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a45e8[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a45f2[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a4658[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a465c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a4660[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4676[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4686[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4694[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a46ea[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a46f6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a46fa[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a4700[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a474c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x40, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x40, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4772[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a4776[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x20, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a47d2[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a47d6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a47da[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_SHORTWALK, 0xc0, 
	AIB_SHORTWALK, 0xc0, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a47fa[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a4804[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4824[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4844[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a48b0[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a48b4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a48b8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a48ce[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a4926[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a492a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a492e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a493e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x0c, 0x00, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a4956[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x40, 
	AIB_RESTART, 
};
const u8 data_a49ac[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a49b0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a49b4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a49c4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a49f0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x40, 
	AIB_RESTART, 
};
const u8 data_a4a4a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a4a4e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a4a52[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a4a62[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x08, 0x00, 
	AIB_JUMP, 0x10, 0x04, 0x40, 0x30, 
	AIB_BB2, 
	AIB_LONGWALK, 0x08, 0x00, 
	AIB_JUMP, 0x10, 0x04, 0x40, 0x30, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4ada[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a4ade[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a4ae2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a4aea[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4b00[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4b0e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x12, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4b1c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x11, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a4b78[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_9bc28 = {
	{4, 4, 4, 4, 5, 5, 5, 5, 4, 5, 1, 1, 8, 8, 2, 2, 4, 5, 0, 0, 0, 3, 3, 3, 0, 0, 6, 6, 7, 7, 8, 8, },
	{data_9b452, data_9b47c, data_9b4a6, data_9b4d0, data_9b4f2, data_9b524, data_9b548, data_9b564, data_9b588, data_9b5a0, data_9b5a0, data_9b5a0, data_9b5a0, data_9b5a0, data_9b5a0, data_9b5a0, }
};
const AIAggTable data_9bbe8 = {
	{4, 4, 4, 4, 5, 5, 5, 5, 4, 5, 1, 1, 8, 8, 2, 2, 4, 5, 0, 0, 0, 3, 3, 3, 0, 0, 6, 6, 7, 7, 8, 8, },
	{data_9b308, data_9b32e, data_9b358, data_9b382, data_9b3a4, data_9b3d6, data_9b3fa, data_9b416, data_9b43a, data_9b452, data_9b452, data_9b452, data_9b452, data_9b452, data_9b452, data_9b452, }
};
const AIAggTable data_9bba8 = {
	{5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 1, 1, 1, 2, 2, 2, 6, 6, 6, 6, 6, 6, 3, 3, 3, 4, 4, 4, 5, 5, },
	{data_9b202, data_9b222, data_9b240, data_9b266, data_9b28e, data_9b2be, data_9b2e6, data_9b308, data_9b308, data_9b308, data_9b308, data_9b308, data_9b308, data_9b308, data_9b308, data_9b308, }
};
const AIAggTable data_9bb68 = {
	{5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 1, 1, 1, 2, 2, 2, 6, 6, 6, 6, 6, 6, 3, 3, 3, 4, 4, 4, 5, 5, },
	{data_9b0fc, data_9b11c, data_9b13a, data_9b160, data_9b188, data_9b1b8, data_9b1e0, data_9b202, data_9b202, data_9b202, data_9b202, data_9b202, data_9b202, data_9b202, data_9b202, data_9b202, }
};
const AIAggTable data_9bb28 = {
	{0, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, },
	{data_9b084, data_9b090, data_9b0a4, data_9b0ba, data_9b0ce, data_9b0e4, data_9b0fc, data_9b0fc, data_9b0fc, data_9b0fc, data_9b0fc, data_9b0fc, data_9b0fc, data_9b0fc, data_9b0fc, data_9b0fc, }
};
const AIAggTable data_9bae8 = {
	{0, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, },
	{data_9b00c, data_9b018, data_9b02c, data_9b042, data_9b056, data_9b06c, data_9b084, data_9b084, data_9b084, data_9b084, data_9b084, data_9b084, data_9b084, data_9b084, data_9b084, data_9b084, }
};
const AIAggTable data_9baa8 = {
	{0, 0, 0, 0, 5, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 5, 5, 6, 6, 6, },
	{data_9af2a, data_9af44, data_9af66, data_9af8c, data_9afb6, data_9afde, data_9aff6, data_9b00c, data_9b00c, data_9b00c, data_9b00c, data_9b00c, data_9b00c, data_9b00c, data_9b00c, data_9b00c, }
};
const AIAggTable data_9ba68 = {
	{0, 0, 0, 0, 5, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 5, 5, 6, 6, 6, },
	{data_9ae48, data_9ae62, data_9ae84, data_9aeaa, data_9aed4, data_9aefc, data_9af14, data_9af2a, data_9af2a, data_9af2a, data_9af2a, data_9af2a, data_9af2a, data_9af2a, data_9af2a, data_9af2a, }
};
const u8 *data_9ba18[8]={
	data_9ae32,
	data_9ae32,
	data_9ae32,
	data_9ae32,
	data_9ae32,
	data_9ae32,
	data_9ae32,
	data_9ae32,
};
const AIAggTable *data_9ba48[8]={
	&data_9bc28,
	&data_9bbe8,
	&data_9bba8,
	&data_9bb68,
	&data_9bb28,
	&data_9bae8,
	&data_9baa8,
	&data_9ba68,
};
const u8 **data_9ba08[]={
	data_9ba18, 		/* vs Ryu */
	data_9ba18, 		/* vs E.Honda */
	data_9ba18, 		/* vs Blanka */
	data_9ba18, 		/* vs Guile */
	data_9ba18, 		/* vs Ken */
	data_9ba18, 		/* vs Chun-Li */
	data_9ba18, 		/* vs Zangeif */
	data_9ba18, 		/* vs Dhalsim */
};
const AIAggTable **data_9ba38[]={
	data_9ba48, 		/* vs Ryu */
	data_9ba48, 		/* vs E.Honda */
	data_9ba48, 		/* vs Blanka */
	data_9ba48, 		/* vs Guile */
	data_9ba48, 		/* vs Ken */
	data_9ba48, 		/* vs Chun-Li */
	data_9ba48, 		/* vs Zangeif */
	data_9ba48, 		/* vs Dhalsim */
};
struct dualptr data_9adc0={data_9ba08, data_9ba38};
const struct defense_strategy data_a42ba={ 
    { 0, 1, 3, 2, 3, 0, 1, 2, 2, 3, 2, 3, 1, 3, 2, 0, 3, 2, 1, 2, 3, 1, 3, 2, 2, 0, 2, 0, 1, 3, 0, 3,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a4302, data_a4316, data_a431a, data_a4334, },
};
const struct defense_strategy data_a434e={ 
    { 2, 0, 0, 1, 2, 1, 0, 2, 1, 2, 3, 0, 3, 2, 2, 0, 0, 1, 2, 3, 0, 3, 1, 2, 3, 0, 3, 2, 1, 2, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a4396, data_a43aa, data_a43ae, data_a43c8, },
};
const struct defense_strategy data_a43e2={ 
    { 2, 0, 2, 2, 0, 1, 0, 3, 0, 1, 0, 1, 2, 0, 3, 2, 2, 3, 2, 0, 3, 2, 1, 0, 1, 2, 3, 3, 1, 0, 2, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a442a, data_a443e, data_a4442, data_a445c, },
};
const struct defense_strategy data_a4476={ 
    { 4, 0, 2, 0, 3, 4, 0, 4, 1, 3, 4, 1, 4, 2, 3, 0, 0, 1, 2, 3, 1, 4, 4, 1, 3, 2, 4, 4, 0, 2, 1, 4,  },
    { 4, 0, 2, 0, 3, 4, 0, 4, 1, 3, 4, 1, 4, 2, 3, 0, 0, 1, 2, 3, 1, 4, 4, 1, 3, 2, 4, 4, 0, 2, 1, 4,  },
    { data_a44c0, data_a44c4, data_a44c8, data_a44d4, data_a44e0, },
};
const struct defense_strategy data_a44e8={ 
    { 1, 2, 2, 2, 1, 4, 1, 1, 5, 3, 6, 1, 2, 1, 1, 1, 1, 3, 1, 5, 1, 6, 1, 1, 6, 1, 3, 3, 5, 3, 1, 1,  },
    { 1, 2, 2, 2, 1, 4, 1, 1, 5, 3, 6, 1, 2, 1, 1, 1, 1, 3, 1, 5, 1, 6, 1, 1, 6, 1, 3, 3, 5, 3, 1, 1,  },
    { data_a4536, data_a453a, data_a453e, data_a4554, data_a4560, data_a456a, data_a4574, },
};
const struct defense_strategy data_a457e={ 
    { 2, 1, 4, 1, 5, 1, 1, 1, 1, 5, 1, 2, 1, 4, 1, 1, 4, 3, 3, 3, 3, 3, 1, 1, 1, 5, 1, 4, 1, 4, 1, 1,  },
    { 2, 1, 4, 1, 5, 1, 1, 1, 1, 5, 1, 2, 1, 4, 1, 1, 4, 3, 3, 3, 3, 3, 1, 1, 1, 5, 1, 4, 1, 4, 1, 1,  },
    { data_a45cc, data_a45d0, data_a45d4, data_a45dc, data_a45e8, data_a45f2, },
};
const struct defense_strategy data_a460c={ 
    { 2, 4, 0, 5, 0, 2, 5, 3, 3, 2, 3, 3, 1, 4, 5, 5, 0, 3, 1, 2, 3, 3, 2, 0, 0, 2, 4, 5, 0, 4, 0, 2,  },
    { 2, 4, 0, 5, 0, 2, 5, 3, 3, 2, 3, 3, 1, 4, 5, 5, 0, 3, 1, 2, 3, 3, 2, 0, 0, 2, 4, 5, 0, 4, 0, 2,  },
    { data_a4658, data_a465c, data_a4660, data_a4676, data_a4686, data_a4694, },
};
const struct defense_strategy data_a46a2={ 
    { 0, 2, 0, 0, 0, 0, 2, 0, 1, 0, 0, 3, 0, 2, 0, 0, 0, 3, 1, 0, 2, 0, 0, 0, 3, 0, 0, 2, 0, 3, 0, 3,  },
    { 0, 2, 0, 0, 0, 0, 2, 0, 1, 0, 0, 3, 0, 2, 0, 0, 0, 3, 1, 0, 2, 0, 0, 0, 3, 0, 0, 2, 0, 3, 0, 3,  },
    { data_a46ea, data_a46f6, data_a46fa, data_a4700, },
};
const struct defense_strategy data_a4706={ 
    { 1, 2, 2, 0, 1, 2, 2, 0, 2, 1, 2, 2, 2, 1, 2, 2, 0, 0, 1, 2, 0, 0, 1, 2, 2, 2, 0, 1, 2, 2, 0, 1,  },
    { 1, 2, 2, 0, 1, 2, 2, 0, 2, 1, 2, 2, 2, 1, 2, 2, 0, 0, 1, 2, 0, 0, 1, 2, 2, 2, 0, 1, 2, 2, 0, 1,  },
    { data_a474c, data_a4772, data_a4776, },
};
const struct defense_strategy data_a4784={ 
    { 1, 2, 2, 6, 1, 4, 0, 0, 4, 1, 5, 1, 6, 1, 0, 2, 1, 4, 1, 5, 1, 2, 1, 0, 0, 1, 5, 2, 4, 6, 0, 2,  },
    { 1, 2, 2, 6, 1, 4, 0, 0, 4, 1, 5, 1, 6, 1, 0, 2, 1, 4, 1, 5, 1, 2, 1, 0, 0, 1, 5, 2, 4, 6, 0, 2,  },
    { data_a47d2, data_a47d6, data_a47da, data_a47fa, data_a4804, data_a4824, data_a4844, },
};
const struct defense_strategy data_a4868={ 
    { 2, 0, 1, 2, 3, 0, 2, 1, 0, 2, 2, 2, 2, 1, 3, 0, 2, 3, 3, 1, 3, 3, 0, 2, 3, 2, 2, 0, 1, 2, 1, 3,  },
    { 2, 0, 1, 2, 3, 0, 2, 1, 0, 2, 2, 2, 2, 1, 3, 0, 2, 3, 3, 1, 3, 3, 0, 2, 3, 2, 2, 0, 1, 2, 1, 3,  },
    { data_a48b0, data_a48b4, data_a48b8, data_a48ce, },
};
const struct defense_strategy data_a48dc={ 
    { 1, 3, 4, 2, 3, 3, 3, 3, 4, 1, 2, 3, 3, 3, 3, 3, 3, 2, 0, 3, 3, 3, 3, 3, 2, 3, 1, 4, 3, 3, 3, 3,  },
    { 1, 3, 4, 2, 3, 3, 3, 3, 4, 1, 2, 3, 3, 3, 3, 3, 3, 2, 0, 3, 3, 3, 3, 3, 2, 3, 1, 4, 3, 3, 3, 3,  },
    { data_a4926, data_a492a, data_a492e, data_a493e, data_a4956, },
};
const struct defense_strategy data_a4962={ 
    { 3, 3, 1, 3, 3, 3, 3, 3, 4, 1, 2, 3, 3, 3, 3, 3, 1, 2, 4, 3, 3, 3, 3, 3, 3, 0, 3, 1, 3, 3, 3, 3,  },
    { 3, 3, 1, 3, 3, 3, 3, 3, 4, 1, 2, 3, 3, 3, 3, 3, 1, 2, 4, 3, 3, 3, 3, 3, 3, 0, 3, 1, 3, 3, 3, 3,  },
    { data_a49ac, data_a49b0, data_a49b4, data_a49c4, data_a49f0, },
};
const struct defense_strategy data_a4a00={ 
    { 3, 3, 3, 0, 3, 1, 3, 3, 1, 3, 3, 1, 3, 0, 1, 3, 0, 1, 3, 1, 3, 1, 3, 3, 3, 0, 3, 3, 1, 3, 1, 3,  },
    { 3, 3, 3, 0, 3, 1, 3, 3, 1, 3, 3, 1, 3, 0, 1, 3, 0, 1, 3, 1, 3, 1, 3, 3, 3, 0, 3, 3, 1, 3, 1, 3,  },
    { data_a4a4a, data_a4a4e, data_a4a52, data_a4a62, },
};
const struct defense_strategy data_a4a8c={ 
    { 2, 6, 4, 1, 0, 2, 4, 1, 5, 2, 1, 6, 2, 4, 1, 0, 5, 1, 6, 4, 5, 1, 6, 3, 1, 5, 4, 2, 1, 5, 3, 0,  },
    { 2, 6, 4, 1, 0, 2, 4, 1, 5, 2, 1, 6, 2, 4, 1, 0, 5, 1, 6, 4, 5, 1, 6, 3, 1, 5, 4, 2, 1, 5, 3, 0,  },
    { data_a4ada, data_a4ade, data_a4ae2, data_a4aea, data_a4b00, data_a4b0e, data_a4b1c, },
};
const struct defense_strategy data_a4b2a={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a4b78, },
};
const struct defense_strategy *data_a427a[]={&data_a42ba, &data_a434e, &data_a43e2, &data_a4476, &data_a44e8, &data_a457e, &data_a460c, &data_a46a2, &data_a4706, &data_a4784, &data_a4868, &data_a48dc, &data_a4962, &data_a4a00, &data_a4a8c, &data_a4b2a,  };

/* END Blanka */

