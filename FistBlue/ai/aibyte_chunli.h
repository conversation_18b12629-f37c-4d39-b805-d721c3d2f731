/*
 *  aibyte_chunli.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */


/* AGG0 Chun-Li */
const u8 data_9d302[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	<PERSON><PERSON>_<PERSON>YBE_RESTART, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x12, 0x80, 0x50, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc6, 
	AIB_RESTART, 
};
const u8 data_9d362[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x45, 0x00, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON>_<PERSON>Y<PERSON>_RESTART, 
	AIB_SHORTWALK, 0xc6, 
	<PERSON><PERSON>_RESTART, 
};
const u8 data_9d37e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9d370[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9d32c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x41, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x41, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9d318[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x12, 0x80, 0x50, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc6, 
	AIB_RESTART, 
};
const u8 data_9d354[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x45, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc6, 
	AIB_RESTART, 
};
const u8 data_9d340[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x41, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x41, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};

/* AGG1 Chun-Li */
const u8 data_9d85a[] = { AIB_TYPE4, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x92, 0x80, 0x50, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d4be[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x92, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x92, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d5ce[] = { AIB_TYPE4, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x14, 0x80, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d3c2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d7ac[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d59a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d684[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d40c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x92, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x92, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d772[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x82, 0x99, 0x99, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d7e8[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x92, 0x80, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d492[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_SET_0216, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_BA0_DIST_LE, 0x18, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d82a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d644[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d80e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x82, 0x99, 0x99, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d57a[] = { AIB_TYPE4, 
	AIB_KICK, 0x02, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d5b0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x10, 0x94, 0x80, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d78e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d6e2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x38, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d708[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d43e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0xa0, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0xa0, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0xa0, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d5f8[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d4f0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d7c6[] = { AIB_TYPE4, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x92, 0x80, 0x50, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d6c8[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d506[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x10, 0x94, 0x80, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d3e0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_SET_0216, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_BA0_DIST_LE, 0x18, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d38c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0xa0, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0xa0, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0xa0, 0x12, 0x80, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d844[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d474[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d65e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x94, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x38, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d524[] = { AIB_TYPE4, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x14, 0x80, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d74c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x92, 0x80, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9d54e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9d624[] = { AIB_TYPE4, 
	AIB_KICK, 0x02, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_EXITRAND, 
};

/* DEF  Chun-Li */
const u8 data_a6ec8[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a6edc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6ee0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6efa[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6f5c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a6f70[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6f74[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6f8e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6ff0[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a7004[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a7008[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7022[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7086[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a708a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a708e[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a709a[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a70a2[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a70f4[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a70f8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a70fc[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7108[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a7112[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a7166[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a716a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a716e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a717a[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a7184[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a71d8[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a71dc[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a71ea[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a71fa[] = { AIB_TYPE2, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x50, 
	AIB_RESTART, 
};
const u8 data_a7206[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7258[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a72b0[] = { AIB_TYPE2, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x40, 
	AIB_RESTART, 
};
const u8 data_a72bc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a72c0[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_RESTART, 
};
const u8 data_a72ca[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x40, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x40, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7336[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a733a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a733e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x4c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a735c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x4c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a737a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x4c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a73e2[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a73e6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a73ea[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a740e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x50, 
	AIB_BB2, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7482[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7486[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a748a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x0c, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x0c, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a74bc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x05, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x40, 
	AIB_RESTART, 
};
const u8 data_a7516[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a751a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a751e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7550[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x05, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x40, 
	AIB_RESTART, 
};
const u8 data_a75aa[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a75ae[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a75b2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x0a, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x0a, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a75e4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x05, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x40, 
	AIB_RESTART, 
};
const u8 data_a7644[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7648[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a764c[] = { AIB_TYPE2, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a7658[] = { AIB_TYPE2, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x40, 
	AIB_RESTART, 
};
const u8 data_a7664[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7672[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7680[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a76d8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_9daa0 = {
	{1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 4, 4, 4, 4, 2, 2, 2, 2, 3, 3, 3, 3, },
	{data_9d7e8, data_9d80e, data_9d82a, data_9d844, data_9d85a,  }
};
const AIAggTable data_9da60 = {
	{1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 4, 4, 4, 4, 2, 2, 2, 2, 3, 3, 3, 3, },
	{data_9d74c, data_9d772, data_9d78e, data_9d7ac, data_9d7c6, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, data_9d7e8, }
};
const AIAggTable data_9da20 = {
	{2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, },
	{data_9d6c8, data_9d6e2, data_9d708, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, data_9d74c, }
};
const AIAggTable data_9d9e0 = {
	{2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, },
	{data_9d644, data_9d65e, data_9d684, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, data_9d6c8, }
};
const AIAggTable data_9d9a0 = {
	{3, 3, 3, 3, 3, 3, 1, 1, 2, 2, 2, 2, 2, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, },
	{data_9d59a, data_9d5b0, data_9d5ce, data_9d5f8, data_9d624, data_9d644, data_9d644, data_9d644, data_9d644, data_9d644, data_9d644, data_9d644, data_9d644, data_9d644, data_9d644, data_9d644, }
};
const AIAggTable data_9d960 = {
	{3, 3, 3, 3, 3, 3, 1, 1, 2, 2, 2, 2, 2, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, },
	{data_9d4f0, data_9d506, data_9d524, data_9d54e, data_9d57a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, data_9d59a, }
};
const AIAggTable data_9d920 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 3, 3, 3, 3, 3, 3, },
	{data_9d43e, data_9d474, data_9d492, data_9d4be, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, data_9d4f0, }
};
const AIAggTable data_9d8e0 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 3, 3, 3, 3, 3, 3, },
	{data_9d38c, data_9d3c2, data_9d3e0, data_9d40c, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, data_9d43e, }
};
const u8 *data_9d890[8]={
	data_9d37e,
	data_9d370,
	data_9d362,
	data_9d354,
	data_9d340,
	data_9d32c,
	data_9d318,
	data_9d302,
};
const AIAggTable *data_9d8c0[8]={
	&data_9daa0,
	&data_9da60,
	&data_9da20,
	&data_9d9e0,
	&data_9d9a0,
	&data_9d960,
	&data_9d920,
	&data_9d8e0,
};
const u8 **data_9d880[]={
	data_9d890, 		/* vs Ryu */
	data_9d890, 		/* vs E.Honda */
	data_9d890, 		/* vs Blanka */
	data_9d890, 		/* vs Guile */
	data_9d890, 		/* vs Ken */
	data_9d890, 		/* vs Chun-Li */
	data_9d890, 		/* vs Zangeif */
	data_9d890, 		/* vs Dhalsim */
};
const AIAggTable **data_9d8b0[]={
	data_9d8c0, 		/* vs Ryu */
	data_9d8c0, 		/* vs E.Honda */
	data_9d8c0, 		/* vs Blanka */
	data_9d8c0, 		/* vs Guile */
	data_9d8c0, 		/* vs Ken */
	data_9d8c0, 		/* vs Chun-Li */
	data_9d8c0, 		/* vs Zangeif */
	data_9d8c0, 		/* vs Dhalsim */
};
struct dualptr data_9d2fe={data_9d880, data_9d8b0};
const struct defense_strategy data_a6e80={ 
    { 1, 2, 0, 2, 1, 2, 0, 3, 0, 2, 2, 3, 3, 2, 2, 0, 3, 3, 1, 2, 2, 3, 2, 0, 2, 0, 3, 1, 2, 0, 3, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6ec8, data_a6edc, data_a6ee0, data_a6efa, },
};
const struct defense_strategy data_a6f14={ 
    { 1, 2, 0, 2, 1, 2, 0, 3, 0, 2, 2, 3, 3, 2, 2, 0, 3, 3, 1, 2, 2, 3, 2, 0, 2, 0, 3, 1, 2, 0, 3, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6f5c, data_a6f70, data_a6f74, data_a6f8e, },
};
const struct defense_strategy data_a6fa8={ 
    { 1, 2, 0, 2, 1, 2, 0, 3, 0, 2, 2, 3, 3, 2, 2, 0, 3, 3, 1, 2, 2, 3, 2, 0, 2, 0, 3, 1, 2, 0, 3, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6ff0, data_a7004, data_a7008, data_a7022, },
};
const struct defense_strategy data_a703c={ 
    { 1, 3, 2, 4, 2, 4, 0, 3, 3, 2, 3, 1, 4, 2, 3, 4, 2, 4, 1, 3, 4, 3, 4, 4, 4, 0, 4, 0, 3, 1, 0, 2,  },
    { 1, 3, 2, 4, 2, 4, 0, 3, 3, 2, 3, 1, 4, 2, 3, 4, 2, 4, 1, 3, 4, 3, 4, 4, 4, 0, 4, 0, 3, 1, 0, 2,  },
    { data_a7086, data_a708a, data_a708e, data_a709a, data_a70a2, },
};
const struct defense_strategy data_a70aa={ 
    { 1, 4, 4, 0, 4, 2, 1, 1, 3, 1, 4, 4, 2, 3, 1, 1, 4, 3, 2, 1, 3, 0, 1, 1, 2, 4, 3, 3, 0, 4, 1, 1,  },
    { 1, 4, 4, 0, 4, 2, 1, 1, 3, 1, 4, 4, 2, 3, 1, 1, 4, 3, 2, 1, 3, 0, 1, 1, 2, 4, 3, 3, 0, 4, 1, 1,  },
    { data_a70f4, data_a70f8, data_a70fc, data_a7108, data_a7112, },
};
const struct defense_strategy data_a711c={ 
    { 1, 1, 3, 0, 2, 1, 1, 1, 3, 4, 1, 2, 4, 2, 1, 1, 0, 3, 1, 3, 1, 3, 1, 1, 2, 0, 4, 4, 1, 4, 1, 1,  },
    { 1, 1, 3, 0, 2, 1, 1, 1, 3, 4, 1, 2, 4, 2, 1, 1, 0, 3, 1, 3, 1, 3, 4, 1, 2, 0, 4, 4, 1, 4, 1, 1,  },
    { data_a7166, data_a716a, data_a716e, data_a717a, data_a7184, },
};
const struct defense_strategy data_a718e={ 
    { 2, 1, 1, 2, 1, 4, 2, 4, 2, 2, 1, 1, 4, 2, 4, 0, 0, 2, 2, 1, 2, 2, 3, 4, 2, 2, 0, 2, 2, 4, 0, 3,  },
    { 2, 1, 1, 2, 1, 4, 2, 4, 2, 2, 1, 1, 4, 2, 4, 0, 0, 2, 2, 1, 2, 2, 3, 4, 2, 2, 0, 2, 2, 4, 0, 3,  },
    { data_a71d8, data_a71dc, data_a71ea, data_a71fa, data_a7206, },
};
const struct defense_strategy data_a7214={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a7258, },
};
const struct defense_strategy data_a7268={ 
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a72b0, data_a72bc, data_a72c0, data_a72ca, },
};
const struct defense_strategy data_a72ec={ 
    { 1, 4, 2, 2, 4, 3, 4, 2, 2, 1, 3, 2, 2, 2, 3, 1, 4, 2, 1, 3, 2, 3, 1, 4, 3, 4, 2, 2, 3, 1, 0, 4,  },
    { 1, 4, 2, 2, 4, 3, 4, 2, 2, 1, 3, 2, 2, 2, 3, 1, 4, 2, 1, 3, 2, 3, 1, 4, 3, 4, 2, 2, 3, 1, 0, 4,  },
    { data_a7336, data_a733a, data_a733e, data_a735c, data_a737a, },
};
const struct defense_strategy data_a7398={ 
    { 1, 3, 1, 3, 2, 0, 1, 0, 2, 1, 3, 1, 3, 1, 2, 0, 1, 2, 1, 3, 0, 3, 0, 1, 3, 1, 2, 0, 1, 1, 3, 2,  },
    { 1, 3, 1, 3, 2, 0, 1, 0, 2, 1, 3, 1, 3, 1, 2, 0, 1, 2, 1, 3, 0, 3, 0, 1, 3, 1, 2, 0, 1, 1, 3, 2,  },
    { data_a73e2, data_a73e6, data_a73ea, data_a740e, },
};
const struct defense_strategy data_a743a={ 
    { 2, 3, 1, 0, 2, 2, 2, 2, 0, 1, 3, 1, 2, 2, 2, 2, 1, 2, 0, 2, 2, 2, 2, 2, 0, 0, 2, 3, 2, 2, 2, 2,  },
    { 2, 3, 1, 0, 2, 2, 2, 2, 0, 1, 3, 1, 2, 2, 2, 2, 1, 2, 0, 2, 2, 2, 2, 2, 0, 0, 2, 3, 2, 2, 2, 2,  },
    { data_a7482, data_a7486, data_a748a, data_a74bc, },
};
const struct defense_strategy data_a74ce={ 
    { 2, 3, 0, 1, 2, 2, 2, 2, 0, 2, 1, 3, 2, 2, 2, 2, 0, 1, 2, 1, 2, 2, 2, 2, 1, 0, 0, 2, 2, 2, 2, 2,  },
    { 2, 3, 0, 1, 2, 2, 2, 2, 0, 2, 1, 3, 2, 2, 2, 2, 0, 1, 2, 1, 2, 2, 2, 2, 1, 0, 0, 2, 2, 2, 2, 2,  },
    { data_a7516, data_a751a, data_a751e, data_a7550, },
};
const struct defense_strategy data_a7562={ 
    { 2, 2, 2, 2, 0, 1, 2, 0, 2, 2, 2, 2, 1, 2, 1, 2, 2, 2, 2, 2, 2, 3, 0, 1, 2, 2, 2, 2, 3, 0, 1, 0,  },
    { 2, 2, 2, 2, 0, 1, 2, 0, 2, 2, 2, 2, 1, 2, 1, 2, 2, 2, 2, 2, 2, 3, 0, 1, 2, 2, 2, 2, 3, 0, 1, 0,  },
    { data_a75aa, data_a75ae, data_a75b2, data_a75e4, },
};
const struct defense_strategy data_a75f6={ 
    { 2, 5, 1, 3, 2, 5, 2, 4, 1, 2, 5, 2, 2, 2, 4, 6, 6, 3, 2, 1, 5, 2, 3, 5, 3, 4, 2, 2, 6, 1, 5, 6,  },
    { 2, 5, 1, 3, 2, 5, 2, 4, 1, 2, 5, 2, 2, 2, 4, 6, 6, 3, 2, 1, 5, 2, 3, 5, 3, 4, 2, 2, 6, 1, 5, 6,  },
    { data_a7644, data_a7648, data_a764c, data_a7658, data_a7664, data_a7672, data_a7680, },
};
const struct defense_strategy data_a768e={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a76d8, },
};
const struct defense_strategy *data_a6e40[]={&data_a6e80, &data_a6f14, &data_a6fa8, &data_a703c, &data_a70aa, &data_a711c, &data_a718e, &data_a7214, &data_a7268, &data_a72ec, &data_a7398, &data_a743a, &data_a74ce, &data_a7562, &data_a75f6, &data_a768e,  };

/* END Chun-Li */



