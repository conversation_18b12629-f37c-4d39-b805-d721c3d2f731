/*
 *  aibyte_dhalsim.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */


/* AGG0 Dhalsim */
const u8 data_9e80e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	<PERSON>B_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc5, 
	AIB_STANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x00, 
	<PERSON><PERSON>_STANDSTILL, 0x00, 
	<PERSON><PERSON>_STANDSTILL, 0x07, 
	<PERSON>B_STANDSTILL, 0x07, 
	<PERSON>B_GO_AGG1, 
};
const u8 data_9e744[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};
const u8 data_9e76a[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};
const u8 data_9e83a[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};
const u8 data_9e85e[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};
const u8 data_9e7e2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc5, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};
const u8 data_9e78e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_GO_AGG1, 
};
const u8 data_9e7b8[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_GO_AGG1, 
};

/* AGG1 Dhalsim */
const u8 data_9e90c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9f10e[] = { AIB_TYPE4, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e92e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_JUMP, 0x11, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9ebd2[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9e8ec[] = { AIB_TYPE4, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ecc0[] = { AIB_TYPE4, 
	AIB_JUMP, 0x12, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x11, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x10, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9e882[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e99c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x12, 0x84, 0x01, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9edae[] = { AIB_TYPE4, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x21, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x20, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ef52[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x14, 0x90, 0x90, 
	AIB_LONGWALK, 0x00, 0xc0, 
	AIB_JUMP, 0x20, 0x84, 0x01, 0x01, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ec9c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x21, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x20, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9eb00[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x22, 0x04, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9ec44[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9ec24[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ed26[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ef96[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x40, 
	AIB_STANDSTILL, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ed48[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9f052[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ec04[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x12, 0x10, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9eae0[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x12, 0x10, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9eb7a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SETBLOCK, 0x01, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SETBLOCK, 0x01, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ea8c[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9eebe[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e950[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ec62[] = { AIB_TYPE4, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0xaa, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_AC, 
	AIB_STANDSTILL, 0x03, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0xaa, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_AC, 
	AIB_STANDSTILL, 0x03, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0xaa, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_AC, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ee9c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ebac[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9eb40[] = { AIB_TYPE4, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0xaa, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_AC, 
	AIB_STANDSTILL, 0x03, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0xaa, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_AC, 
	AIB_STANDSTILL, 0x03, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0xaa, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_AC, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9edd4[] = { AIB_TYPE4, 
	AIB_JUMP, 0x12, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x11, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x10, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9eab2[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x03, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9f00a[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9edfa[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e9dc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9f0e2[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ef2a[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ee1e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9f0c0[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x14, 0x90, 0x90, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x84, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ef74[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x14, 0x90, 0x90, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x84, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9eb20[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ed66[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e8cc[] = { AIB_TYPE4, 
	AIB_JUMP, 0x12, 0x84, 0x01, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9eee2[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ece4[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9efc2[] = { AIB_TYPE4, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e9bc[] = { AIB_TYPE4, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9ee60[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ee7e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e9fe[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_JUMP, 0x11, 0x14, 0x90, 0x90, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9f076[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ea20[] = { AIB_TYPE4, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0x00, 
	AIB_SHORTWALK, 0xa2, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0x00, 
	AIB_SHORTWALK, 0x0c, 
	AIB_SHORTWALK, 0x00, 
	AIB_BA4_OPPJUMP, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0x00, 
	AIB_SHORTWALK, 0xa2, 
	AIB_JUMP, 0x0a, 0x82, 0x00, 0x00, 
	AIB_LABEL_A4, 0x00, 
	AIB_SHORTWALK, 0x0c, 
	AIB_SHORTWALK, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9f09e[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x14, 0x90, 0x90, 
	AIB_LONGWALK, 0x00, 0xc0, 
	AIB_JUMP, 0x20, 0x84, 0x01, 0x01, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9efe8[] = { AIB_TYPE4, 
	AIB_JUMP, 0x11, 0x84, 0x01, 0x01, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e8a8[] = { AIB_TYPE4, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9ef06[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ea5a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SETBLOCK, 0x01, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SETBLOCK, 0x01, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9f02e[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ed06[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ed84[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_STANDSTILL, 0x05, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_STANDSTILL, 0x05, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_STANDSTILL, 0x05, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9e976[] = { AIB_TYPE4, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_SHORTWALK, 0x42, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};
const u8 data_9ee3e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_EXITRAND, 
};

/* DEF  Dhalsim */
const u8 data_a8cb8[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8cbc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8cc0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8cce[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x70, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8cdc[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x82, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8cee[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8cf6[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x82, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8d02[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8d5c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8d60[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8d64[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8d72[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x70, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8d80[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x82, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8d92[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8d9a[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x82, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8da6[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8e00[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8e04[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8e08[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8e16[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x70, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8e24[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x82, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8e36[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x84, 0x01, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8e3e[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x82, 0x01, 0x01, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8e4a[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8e9c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8ea0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8ea4[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8eac[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8f02[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8f06[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8f0a[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8f12[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8f1e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x12, 0x04, 0x90, 0x90, 
	AIB_RESTART, 
};
const u8 data_a8f72[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8f76[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8f7a[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8f82[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8f8e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x12, 0x04, 0x90, 0x90, 
	AIB_RESTART, 
};
const u8 data_a8fe4[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8fe8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8fec[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8ff8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9008[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9016[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a906a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9076[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a907a[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a90ce[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a90f0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a90f4[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a90fe[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9170[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9174[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9178[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9192[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a91ac[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a91c6[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x12, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x12, 0x04, 0x90, 0x90, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a922e[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9232[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9236[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9244[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9250[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a926e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a92d0[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a92d4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a92d8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a92ec[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a935a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a935e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9362[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x98, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a93d4[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a93d8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a93dc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a93e4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a93ec[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x08, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x90, 0x90, 
	AIB_RESTART, 
};
const u8 data_a9444[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_KICK, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_a944c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9450[] = { AIB_TYPE2, 
	AIB_JUMP, 0x12, 0x04, 0x90, 0x90, 
	AIB_RESTART, 
};
const u8 data_a9458[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a94be[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x04, 0x90, 0x90, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a94d2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a94d6[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a94dc[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a94e2[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a94e8[] = { AIB_TYPE2, 
	AIB_JUMP, 0x11, 0x04, 0x90, 0x90, 
	AIB_RESTART, 
};
const AIAggTable data_9f334 = {
	{0, 0, 0, 0, 5, 5, 5, 5, 7, 7, 0, 0, 6, 6, 6, 6, 7, 8, 8, 0, 0, 1, 1, 2, 2, 0, 0, 3, 3, 0, 4, 4, },
	{data_9efe8, data_9f00a, data_9f02e, data_9f052, data_9f076, data_9f09e, data_9f0c0, data_9f0e2, data_9f10e,  }
};
const AIAggTable data_9f2f4 = {
	{0, 0, 0, 5, 5, 5, 5, 5, 7, 7, 7, 6, 6, 6, 6, 6, 7, 8, 8, 8, 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, },
	{data_9ee9c, data_9eebe, data_9eee2, data_9ef06, data_9ef2a, data_9ef52, data_9ef74, data_9ef96, data_9efc2, data_9efe8, data_9efe8, data_9efe8, data_9efe8, data_9efe8, data_9efe8, data_9efe8, }
};
const AIAggTable data_9f2b4 = {
	{0, 0, 0, 0, 0, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 4, 4, 5, 5, 5, 5, 6, 7, 7, 7, },
	{data_9ed84, data_9edae, data_9edd4, data_9edfa, data_9ee1e, data_9ee3e, data_9ee60, data_9ee7e, data_9ee9c, data_9ee9c, data_9ee9c, data_9ee9c, data_9ee9c, data_9ee9c, data_9ee9c, data_9ee9c, }
};
const AIAggTable data_9f274 = {
	{0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 6, 6, 6, 7, },
	{data_9ec62, data_9ec9c, data_9ecc0, data_9ece4, data_9ed06, data_9ed26, data_9ed48, data_9ed66, data_9ed84, data_9ed84, data_9ed84, data_9ed84, data_9ed84, data_9ed84, data_9ed84, data_9ed84, }
};
const AIAggTable data_9f234 = {
	{0, 0, 0, 0, 0, 1, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 6, 6, 6, 6, 6, 6, 4, 4, 5, 5, 5, 5, },
	{data_9eb40, data_9eb7a, data_9ebac, data_9ebd2, data_9ec04, data_9ec24, data_9ec44, data_9ec62, data_9ec62, data_9ec62, data_9ec62, data_9ec62, data_9ec62, data_9ec62, data_9ec62, data_9ec62, }
};
const AIAggTable data_9f1f4 = {
	{0, 0, 0, 0, 0, 1, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 6, 6, 6, 6, 2, 2, 4, 4, 4, 4, 5, 5, },
	{data_9ea20, data_9ea5a, data_9ea8c, data_9eab2, data_9eae0, data_9eb00, data_9eb20, data_9eb40, data_9eb40, data_9eb40, data_9eb40, data_9eb40, data_9eb40, data_9eb40, data_9eb40, data_9eb40, }
};
const AIAggTable data_9f1b4 = {
	{1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 2, 2, 3, 3, 3, 3, 3, 3, 4, 4, 5, 5, 5, 5, 5, 5, 2, 3, 4, 5, },
	{data_9e950, data_9e976, data_9e99c, data_9e9bc, data_9e9dc, data_9e9fe, data_9ea20, data_9ea20, data_9ea20, data_9ea20, data_9ea20, data_9ea20, data_9ea20, data_9ea20, data_9ea20, data_9ea20, }
};
const AIAggTable data_9f174 = {
	{1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 4, 4, 4, 4, 4, 4, 5, 5, 2, 3, 4, 5, },
	{data_9e882, data_9e8a8, data_9e8cc, data_9e8ec, data_9e90c, data_9e92e, data_9e950, data_9e950, data_9e950, data_9e950, data_9e950, data_9e950, data_9e950, data_9e950, data_9e950, data_9e950, }
};
const u8 *data_9f144[8]={
	data_9e85e,
	data_9e83a,
	data_9e80e,
	data_9e7e2,
	data_9e7b8,
	data_9e78e,
	data_9e76a,
	data_9e744,
};
const AIAggTable *data_9f164[8]={
	&data_9f334,
	&data_9f2f4,
	&data_9f2b4,
	&data_9f274,
	&data_9f234,
	&data_9f1f4,
	&data_9f1b4,
	&data_9f174,
};
const u8 **data_9f134[]={
	data_9f144, 		/* vs Ryu */
	data_9f144, 		/* vs E.Honda */
	data_9f144, 		/* vs Blanka */
	data_9f144, 		/* vs Guile */
	data_9f144, 		/* vs Ken */
	data_9f144, 		/* vs Chun-Li */
	data_9f144, 		/* vs Zangeif */
	data_9f144, 		/* vs Dhalsim */
};
const AIAggTable **data_9f154[]={
	data_9f164, 		/* vs Ryu */
	data_9f164, 		/* vs E.Honda */
	data_9f164, 		/* vs Blanka */
	data_9f164, 		/* vs Guile */
	data_9f164, 		/* vs Ken */
	data_9f164, 		/* vs Chun-Li */
	data_9f164, 		/* vs Zangeif */
	data_9f164, 		/* vs Dhalsim */
};
struct dualptr data_9e740={data_9f134, data_9f154};
const struct defense_strategy data_a8c68={ 
    { 5, 2, 7, 7, 7, 4, 1, 7, 1, 4, 7, 7, 7, 0, 4, 4, 4, 7, 4, 2, 1, 4, 3, 4, 3, 4, 1, 4, 4, 3, 4, 2,  },
    { 5, 2, 7, 7, 7, 4, 1, 7, 1, 4, 7, 7, 7, 0, 4, 4, 4, 7, 4, 2, 1, 4, 3, 4, 3, 4, 1, 4, 4, 3, 4, 2,  },
    { data_a8cb8, data_a8cbc, data_a8cc0, data_a8cce, data_a8cdc, data_a8cee, data_a8cf6, data_a8d02, },
};
const struct defense_strategy data_a8d0c={ 
    { 5, 2, 7, 7, 7, 4, 1, 7, 1, 4, 7, 7, 7, 0, 4, 4, 4, 7, 4, 2, 1, 4, 3, 4, 3, 4, 1, 4, 4, 3, 4, 2,  },
    { 5, 2, 7, 7, 7, 4, 1, 7, 1, 4, 7, 7, 7, 0, 4, 4, 4, 7, 4, 2, 1, 4, 3, 4, 3, 4, 1, 4, 4, 3, 4, 2,  },
    { data_a8d5c, data_a8d60, data_a8d64, data_a8d72, data_a8d80, data_a8d92, data_a8d9a, data_a8da6, },
};
const struct defense_strategy data_a8db0={ 
    { 4, 2, 7, 7, 7, 4, 1, 7, 1, 4, 7, 7, 7, 0, 4, 4, 4, 7, 5, 2, 1, 4, 3, 4, 3, 4, 1, 4, 4, 3, 4, 2,  },
    { 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,  },
    { data_a8e00, data_a8e04, data_a8e08, data_a8e16, data_a8e24, data_a8e36, data_a8e3e, data_a8e4a, },
};
const struct defense_strategy data_a8e54={ 
    { 1, 3, 0, 3, 0, 2, 1, 3, 2, 1, 0, 1, 2, 1, 2, 1, 1, 2, 1, 2, 1, 3, 1, 1, 3, 1, 2, 0, 0, 3, 0, 2,  },
    { 1, 3, 0, 3, 0, 2, 1, 3, 2, 1, 0, 1, 2, 1, 2, 1, 1, 2, 1, 2, 1, 3, 1, 1, 3, 1, 2, 0, 0, 3, 0, 2,  },
    { data_a8e9c, data_a8ea0, data_a8ea4, data_a8eac, },
};
const struct defense_strategy data_a8eb8={ 
    { 1, 4, 1, 4, 1, 3, 1, 4, 1, 1, 2, 1, 1, 1, 3, 1, 1, 2, 1, 3, 1, 4, 1, 3, 2, 1, 1, 1, 2, 1, 0, 1,  },
    { 1, 4, 1, 4, 1, 3, 1, 4, 1, 1, 2, 1, 1, 1, 3, 1, 1, 2, 1, 3, 1, 4, 1, 3, 2, 1, 1, 1, 2, 1, 0, 1,  },
    { data_a8f02, data_a8f06, data_a8f0a, data_a8f12, data_a8f1e, },
};
const struct defense_strategy data_a8f28={ 
    { 1, 4, 1, 4, 1, 3, 1, 4, 1, 1, 2, 1, 1, 1, 3, 1, 1, 2, 1, 3, 1, 4, 1, 3, 2, 1, 1, 1, 2, 1, 0, 1,  },
    { 1, 4, 1, 4, 1, 3, 1, 4, 1, 1, 2, 1, 1, 1, 3, 1, 1, 2, 1, 3, 1, 4, 1, 3, 2, 1, 1, 1, 2, 1, 0, 1,  },
    { data_a8f72, data_a8f76, data_a8f7a, data_a8f82, data_a8f8e, },
};
const struct defense_strategy data_a8f98={ 
    { 2, 3, 4, 4, 3, 5, 0, 5, 1, 2, 4, 3, 4, 3, 5, 5, 0, 4, 2, 3, 4, 5, 3, 5, 4, 3, 4, 2, 5, 5, 3, 3,  },
    { 2, 3, 4, 4, 3, 5, 0, 5, 1, 2, 4, 3, 4, 3, 5, 5, 0, 4, 2, 3, 4, 5, 3, 5, 4, 3, 4, 2, 5, 5, 3, 3,  },
    { data_a8fe4, data_a8fe8, data_a8fec, data_a8ff8, data_a9008, data_a9016, },
};
const struct defense_strategy data_a9024={ 
    { 0, 0, 1, 2, 0, 0, 0, 0, 0, 1, 2, 1, 2, 0, 0, 1, 1, 2, 0, 0, 1, 2, 1, 2, 2, 0, 0, 0, 0, 1, 2, 0,  },
    { 0, 0, 1, 2, 0, 0, 0, 0, 0, 1, 2, 1, 2, 0, 0, 1, 1, 2, 0, 0, 1, 2, 1, 2, 2, 0, 0, 0, 0, 1, 2, 0,  },
    { data_a906a, data_a9076, data_a907a, },
};
const struct defense_strategy data_a9084={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a90ce, data_a90f0, data_a90f4, data_a90fe, },
};
const struct defense_strategy data_a9124={ 
    { 2, 5, 0, 1, 4, 5, 2, 1, 1, 2, 3, 3, 1, 2, 3, 0, 4, 1, 4, 5, 2, 1, 5, 3, 3, 4, 1, 2, 3, 0, 1, 0,  },
    { 2, 5, 0, 1, 4, 5, 2, 1, 1, 2, 3, 3, 1, 2, 3, 0, 4, 1, 4, 5, 2, 1, 5, 3, 3, 4, 1, 2, 3, 0, 1, 0,  },
    { data_a9170, data_a9174, data_a9178, data_a9192, data_a91ac, data_a91c6, },
};
const struct defense_strategy data_a91e2={ 
    { 5, 4, 5, 4, 5, 1, 5, 0, 1, 5, 4, 4, 1, 5, 5, 0, 0, 1, 4, 1, 4, 5, 5, 0, 4, 0, 1, 4, 5, 3, 5, 0,  },
    { 5, 4, 5, 4, 5, 1, 5, 0, 1, 5, 4, 4, 1, 5, 5, 0, 0, 1, 4, 1, 4, 5, 5, 0, 4, 0, 1, 4, 5, 3, 5, 0,  },
    { data_a922e, data_a9232, data_a9236, data_a9244, data_a9250, data_a926e, },
};
const struct defense_strategy data_a9286={ 
    { 2, 3, 0, 2, 2, 3, 2, 2, 2, 2, 3, 2, 1, 2, 3, 2, 1, 2, 2, 2, 2, 3, 2, 0, 3, 2, 2, 3, 3, 2, 3, 2,  },
    { 2, 3, 0, 2, 2, 3, 2, 2, 2, 2, 3, 2, 1, 2, 3, 2, 1, 2, 2, 2, 2, 3, 2, 0, 3, 2, 2, 3, 3, 2, 3, 2,  },
    { data_a92d0, data_a92d4, data_a92d8, data_a92ec, },
};
const struct defense_strategy data_a9310={ 
    { 2, 0, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 0, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { 2, 0, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 0, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a935a, data_a935e, data_a9362, },
};
const struct defense_strategy data_a938a={ 
    { 2, 0, 3, 2, 2, 4, 3, 2, 1, 2, 2, 3, 4, 2, 2, 0, 2, 3, 1, 2, 2, 3, 4, 2, 4, 2, 2, 4, 4, 2, 2, 3,  },
    { 2, 0, 3, 2, 2, 4, 3, 2, 1, 2, 2, 3, 4, 2, 2, 0, 2, 3, 1, 2, 2, 3, 4, 2, 4, 2, 2, 4, 4, 2, 2, 3,  },
    { data_a93d4, data_a93d8, data_a93dc, data_a93e4, data_a93ec, },
};
const struct defense_strategy data_a93f8={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_a9444, data_a944c, data_a9450, data_a9458, },
};
const struct defense_strategy data_a9472={ 
    { 2, 1, 5, 3, 0, 4, 3, 1, 4, 2, 1, 5, 3, 0, 1, 0, 3, 5, 2, 1, 5, 1, 2, 4, 1, 3, 4, 2, 1, 2, 0, 3,  },
    { 2, 1, 5, 3, 0, 4, 3, 1, 4, 2, 1, 5, 3, 0, 1, 0, 3, 5, 2, 1, 5, 1, 2, 4, 1, 3, 4, 2, 1, 2, 0, 3,  },
    { data_a94be, data_a94d2, data_a94d6, data_a94dc, data_a94e2, data_a94e8, },
};
const struct defense_strategy *data_a8c28[]={&data_a8c68, &data_a8d0c, &data_a8db0, &data_a8e54, &data_a8eb8, &data_a8f28, &data_a8f98, &data_a9024, &data_a9084, &data_a9124, &data_a91e2, &data_a9286, &data_a9310, &data_a938a, &data_a93f8, &data_a9472,  };

/* END Dhalsim */


