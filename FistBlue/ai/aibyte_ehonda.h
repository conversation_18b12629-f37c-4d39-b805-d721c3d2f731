/*
 *  aibyte_ehonda.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */



/* AGG0 E<PERSON>Honda */
const u8 data_9a1c2[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x9a, 
	AIB_LONGWALK, 0x41, 0x00, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON>_<PERSON>YBE_RESTART, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9a284[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON>_<PERSON><PERSON><PERSON>_RESTART, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON>_STANDSTILL, 0x07, 
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_RESTART, 
	<PERSON>B_STANDSTILL, 0x07, 
	<PERSON><PERSON>_STANDSTILL, 0x07, 
	<PERSON>B_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9a1da[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x9a, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9a1f2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_RESTART, 
};
const u8 data_9a26a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9a226[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_RESTART, 
};
const u8 data_9a20c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_RESTART, 
};
const u8 data_9a248[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc0, 
	AIB_RESTART, 
};

/* AGG1 E.Honda */
const u8 data_9a674[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a45e[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a49a[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a602[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a2ca[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a5a6[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a4da[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a85e[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0x42, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_SHORTWALK, 0x42, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_SHORTWALK, 0x42, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a6ca[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a51a[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x81, 0x5a, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x28, 
	AIB_SHORTWALK, 0x41, 
	AIB_SHORTWALK, 0x42, 
	AIB_STANDSTILL, 0x81, 0x5a, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x28, 
	AIB_SHORTWALK, 0x41, 
	AIB_SHORTWALK, 0x42, 
	AIB_STANDSTILL, 0x81, 0x5a, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x28, 
	AIB_SHORTWALK, 0x41, 
	AIB_SHORTWALK, 0x42, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a742[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a8a4[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a31a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a7e8[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a714[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0x41, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_SHORTWALK, 0x41, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_SHORTWALK, 0x41, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a2f4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a624[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x81, 0x5a, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x28, 
	AIB_SHORTWALK, 0x41, 
	AIB_SHORTWALK, 0x42, 
	AIB_STANDSTILL, 0x81, 0x5a, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x28, 
	AIB_SHORTWALK, 0x41, 
	AIB_SHORTWALK, 0x42, 
	AIB_STANDSTILL, 0x81, 0x5a, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x28, 
	AIB_SHORTWALK, 0x41, 
	AIB_SHORTWALK, 0x42, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a37a[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x81, 0x40, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a900[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a83e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x12, 0x70, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a818[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a948[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x12, 0x50, 0x30, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x04, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9a770[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0x42, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_SHORTWALK, 0x42, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_SHORTWALK, 0x42, 
	AIB_SHORTWALK, 0x42, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a4f6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a3a4[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a346[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a698[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0x41, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_SHORTWALK, 0x41, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_SHORTWALK, 0x41, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a5e6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a3ce[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a6f2[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SHORTWALK, 0x42, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x10, 0x00, 0xff, 0xff, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a42c[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9a3fa[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a29e[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x81, 0x40, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a8d4[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a56a[] = { AIB_TYPE4, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x03, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_JUMP, 0x10, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9a926[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_JUMP, 0x10, 0x12, 0x70, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9a7ba[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};

/* DEF  E.Honda */
const u8 data_a33be[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a33c2[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a33d4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x01, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a33f4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3460[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3464[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a3476[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x01, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3496[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x01, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a34fe[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3502[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a3514[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x01, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3534[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x01, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a359e[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a35a2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a35a6[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a35b4[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a35c0[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_RESTART, 
};
const u8 data_a361a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a361e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a3622[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x12, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a362c[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x11, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a3636[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x10, 0x04, 0x70, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_RESTART, 
};
const u8 data_a364c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a36ac[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a36b0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a36b4[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0b, 
	AIB_JUMP, 0x12, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a36be[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0b, 
	AIB_JUMP, 0x11, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a36c8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x10, 0x04, 0x70, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x28, 
	AIB_RESTART, 
};
const u8 data_a36de[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a373c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3740[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a3744[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3754[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3764[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a37c0[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_RESTART, 
};
const u8 data_a37ca[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a37ce[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_RESTART, 
};
const u8 data_a3822[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x40, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3848[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a384c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x11, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a38a0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a38be[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a38c2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a38c8[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_RESTART, 
};
const u8 data_a38d6[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x12, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a38f0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x11, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a394e[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3952[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a3956[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3966[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x40, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a39c6[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a39ca[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a39ce[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_BB2, 
	AIB_LONGWALK, 0x0a, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x70, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a39f4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x70, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3a64[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3a68[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a3a6c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x70, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x84, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x70, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3a90[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x70, 
	AIB_STANDSTILL, 0x01, 
	AIB_KICK, 0x84, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x05, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3afe[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3b02[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a3b06[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x08, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x08, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3b94[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3b98[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a3b9c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3bb2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x10, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3bc0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x12, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3bce[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3be4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a3c40[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a3c44[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a3c48[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a3c4e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x50, 
	AIB_RESTART, 
};
const u8 data_a3c5c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x54, 
	AIB_JUMP, 0x12, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_9ab80 = {
	{0, 0, 0, 2, 0, 0, 0, 0, 3, 3, 3, 2, 2, 4, 4, 4, 1, 1, 1, 2, 2, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, },
	{data_9a85e, data_9a8a4, data_9a8d4, data_9a900, data_9a926, data_9a948, data_9a948, data_9a948, data_9a948, data_9a948, data_9a948, data_9a948, data_9a948, data_9a948, data_9a948, data_9a948, }
};
const AIAggTable data_9ab40 = {
	{2, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 0, 2, 3, 3, 3, 3, 3, 4, 4, 2, 4, 4, 1, 1, 1, 1, 2, 2, 2, 2, },
	{data_9a770, data_9a7ba, data_9a7e8, data_9a818, data_9a83e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, data_9a85e, }
};
const AIAggTable data_9ab00 = {
	{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, },
	{data_9a6f2, data_9a714, data_9a742, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, data_9a770, }
};
const AIAggTable data_9aac0 = {
	{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, },
	{data_9a674, data_9a698, data_9a6ca, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, data_9a6f2, }
};
const AIAggTable data_9aa80 = {
	{0, 0, 0, 0, 0, 0, 0, 4, 1, 1, 1, 1, 1, 1, 1, 4, 2, 2, 2, 2, 2, 2, 2, 4, 3, 3, 3, 3, 3, 3, 3, 4, },
	{data_9a56a, data_9a5a6, data_9a5e6, data_9a602, data_9a624, data_9a674, data_9a674, data_9a674, data_9a674, data_9a674, data_9a674, data_9a674, data_9a674, data_9a674, data_9a674, data_9a674, }
};
const AIAggTable data_9aa40 = {
	{0, 0, 0, 0, 0, 0, 0, 4, 1, 1, 1, 1, 1, 1, 1, 4, 2, 2, 2, 2, 2, 2, 2, 4, 3, 3, 3, 3, 3, 3, 3, 4, },
	{data_9a45e, data_9a49a, data_9a4da, data_9a4f6, data_9a51a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, data_9a56a, }
};
const AIAggTable data_9aa00 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 4, 4, 4, 2, 2, 2, 2, 2, 4, 4, 4, 3, 3, 3, 3, 3, 4, 4, 4, },
	{data_9a37a, data_9a3a4, data_9a3ce, data_9a3fa, data_9a42c, data_9a45e, data_9a45e, data_9a45e, data_9a45e, data_9a45e, data_9a45e, data_9a45e, data_9a45e, data_9a45e, data_9a45e, data_9a45e, }
};
const AIAggTable data_9a9c0 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 4, 4, 4, 2, 2, 2, 2, 2, 4, 4, 4, 3, 3, 3, 3, 3, 4, 4, 4, },
	{data_9a29e, data_9a2ca, data_9a2f4, data_9a31a, data_9a346, data_9a37a, data_9a37a, data_9a37a, data_9a37a, data_9a37a, data_9a37a, data_9a37a, data_9a37a, data_9a37a, data_9a37a, data_9a37a, }
};
const u8 *data_9a970[8]={
	data_9a284,
	data_9a26a,
	data_9a248,
	data_9a226,
	data_9a20c,
	data_9a1f2,
	data_9a1da,
	data_9a1c2,
};
const AIAggTable *data_9a9a0[8]={
	&data_9ab80,
	&data_9ab40,
	&data_9ab00,
	&data_9aac0,
	&data_9aa80,
	&data_9aa40,
	&data_9aa00,
	&data_9a9c0,
};
const u8 **data_9a960[]={
	data_9a970, 		/* vs Ryu */
	data_9a970, 		/* vs E.Honda */
	data_9a970, 		/* vs Blanka */
	data_9a970, 		/* vs Guile */
	data_9a970, 		/* vs Ken */
	data_9a970, 		/* vs Chun-Li */
	data_9a970, 		/* vs Zangeif */
	data_9a970, 		/* vs Dhalsim */
};
const AIAggTable **data_9a990[]={
	data_9a9a0, 		/* vs Ryu */
	data_9a9a0, 		/* vs E.Honda */
	data_9a9a0, 		/* vs Blanka */
	data_9a9a0, 		/* vs Guile */
	data_9a9a0, 		/* vs Ken */
	data_9a9a0, 		/* vs Chun-Li */
	data_9a9a0, 		/* vs Zangeif */
	data_9a9a0, 		/* vs Dhalsim */
};
struct dualptr data_9a1be={data_9a960, data_9a990};
const struct defense_strategy data_a3376={ 
    { 0, 2, 2, 2, 2, 0, 2, 2, 0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 0, 2, 2, 2, 3, 0,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_a33be, data_a33c2, data_a33d4, data_a33f4, },
};
const struct defense_strategy data_a3418={ 
    { 0, 2, 2, 2, 2, 0, 2, 2, 0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 0, 2, 2, 3, 3, 0,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_a3460, data_a3464, data_a3476, data_a3496, },
};
const struct defense_strategy data_a34b6={ 
    { 0, 2, 2, 2, 3, 0, 3, 2, 0, 0, 2, 2, 2, 2, 3, 0, 0, 0, 3, 0, 2, 0, 2, 0, 3, 0, 0, 2, 2, 3, 3, 0,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_a34fe, data_a3502, data_a3514, data_a3534, },
};
const struct defense_strategy data_a3554={ 
    { 1, 3, 0, 4, 3, 0, 4, 2, 0, 0, 1, 0, 0, 2, 0, 0, 0, 2, 0, 2, 1, 0, 3, 0, 2, 0, 3, 0, 0, 0, 1, 3,  },
    { 1, 3, 0, 4, 3, 0, 4, 2, 0, 0, 1, 0, 0, 2, 0, 0, 0, 2, 0, 2, 1, 0, 3, 0, 2, 0, 3, 0, 0, 0, 1, 3,  },
    { data_a359e, data_a35a2, data_a35a6, data_a35b4, data_a35c0, },
};
const struct defense_strategy data_a35ce={ 
    { 1, 5, 1, 4, 1, 1, 1, 0, 1, 1, 1, 1, 2, 1, 3, 1, 1, 5, 1, 3, 1, 5, 1, 1, 0, 1, 2, 1, 4, 1, 5, 1,  },
    { 1, 5, 1, 4, 1, 1, 1, 0, 1, 1, 1, 1, 2, 1, 3, 1, 1, 5, 1, 3, 1, 5, 1, 1, 0, 1, 2, 1, 4, 1, 5, 1,  },
    { data_a361a, data_a361e, data_a3622, data_a362c, data_a3636, data_a364c, },
};
const struct defense_strategy data_a3660={ 
    { 1, 2, 1, 0, 1, 3, 1, 3, 2, 1, 2, 1, 3, 1, 2, 1, 4, 4, 4, 4, 4, 4, 1, 2, 5, 5, 5, 5, 5, 5, 2, 1,  },
    { 1, 2, 1, 0, 1, 3, 1, 3, 2, 1, 2, 1, 3, 1, 2, 1, 5, 5, 5, 5, 5, 5, 1, 2, 4, 4, 4, 4, 4, 4, 2, 1,  },
    { data_a36ac, data_a36b0, data_a36b4, data_a36be, data_a36c8, data_a36de, },
};
const struct defense_strategy data_a36f2={ 
    { 4, 2, 2, 3, 4, 2, 2, 3, 4, 2, 3, 4, 3, 2, 3, 3, 3, 4, 2, 3, 2, 3, 4, 3, 0, 0, 2, 0, 0, 4, 0, 2,  },
    { 4, 2, 2, 3, 4, 2, 2, 3, 4, 2, 3, 4, 3, 2, 3, 3, 3, 4, 2, 3, 2, 3, 4, 3, 0, 0, 2, 0, 0, 4, 0, 2,  },
    { data_a373c, data_a3740, data_a3744, data_a3754, data_a3764, },
};
const struct defense_strategy data_a377a={ 
    { 0, 0, 0, 0, 0, 2, 0, 0, 1, 0, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 2, 0, 0, 1, 0, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0,  },
    { data_a37c0, data_a37ca, data_a37ce, },
};
const struct defense_strategy data_a37dc={ 
    { 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a3822, data_a3848, data_a384c, },
};
const struct defense_strategy data_a3854={ 
    { 0, 0, 0, 0, 5, 0, 0, 0, 4, 0, 5, 0, 0, 0, 5, 0, 0, 4, 0, 3, 0, 2, 0, 5, 0, 5, 0, 0, 0, 4, 0, 4,  },
    { 0, 0, 0, 0, 5, 0, 0, 0, 4, 0, 5, 0, 0, 0, 5, 0, 0, 4, 0, 3, 0, 2, 0, 5, 0, 5, 0, 0, 0, 4, 0, 4,  },
    { data_a38a0, data_a38be, data_a38c2, data_a38c8, data_a38d6, data_a38f0, },
};
const struct defense_strategy data_a3906={ 
    { 1, 2, 0, 3, 0, 3, 0, 3, 3, 1, 2, 0, 0, 1, 3, 0, 2, 0, 1, 2, 1, 2, 0, 3, 0, 3, 3, 1, 2, 3, 3, 0,  },
    { 1, 2, 0, 3, 0, 3, 0, 3, 3, 1, 2, 0, 0, 1, 3, 0, 2, 0, 1, 2, 1, 2, 0, 3, 0, 3, 3, 1, 2, 3, 3, 0,  },
    { data_a394e, data_a3952, data_a3956, data_a3966, },
};
const struct defense_strategy data_a397e={ 
    { 2, 3, 3, 3, 2, 2, 2, 2, 3, 0, 1, 2, 2, 2, 2, 2, 2, 3, 0, 1, 2, 2, 2, 2, 3, 0, 3, 3, 2, 2, 2, 2,  },
    { 2, 3, 3, 3, 2, 2, 2, 2, 3, 0, 1, 2, 2, 2, 2, 2, 2, 3, 0, 1, 2, 2, 2, 2, 3, 0, 3, 3, 2, 2, 2, 2,  },
    { data_a39c6, data_a39ca, data_a39ce, data_a39f4, },
};
const struct defense_strategy data_a3a1c={ 
    { 1, 3, 3, 0, 2, 2, 2, 2, 3, 1, 2, 3, 2, 2, 2, 2, 2, 3, 3, 1, 2, 2, 2, 2, 3, 2, 0, 3, 2, 2, 2, 2,  },
    { 1, 3, 3, 0, 2, 2, 2, 2, 3, 1, 2, 3, 2, 2, 2, 2, 2, 3, 3, 1, 2, 2, 2, 2, 3, 2, 0, 3, 2, 2, 2, 2,  },
    { data_a3a64, data_a3a68, data_a3a6c, data_a3a90, },
};
const struct defense_strategy data_a3ab6={ 
    { 2, 2, 0, 2, 2, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 2, 2, 1, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { 2, 2, 0, 2, 2, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 2, 2, 1, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a3afe, data_a3b02, data_a3b06, },
};
const struct defense_strategy data_a3b46={ 
    { 2, 5, 5, 0, 1, 5, 6, 2, 1, 2, 3, 5, 4, 5, 2, 6, 3, 1, 0, 1, 6, 2, 0, 0, 2, 5, 2, 0, 2, 6, 4, 1,  },
    { 2, 5, 5, 0, 1, 5, 6, 2, 1, 2, 3, 5, 4, 5, 2, 6, 3, 1, 0, 1, 6, 2, 0, 0, 2, 5, 2, 0, 2, 6, 4, 1,  },
    { data_a3b94, data_a3b98, data_a3b9c, data_a3bb2, data_a3bc0, data_a3bce, data_a3be4, },
};
const struct defense_strategy data_a3bf4={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_a3c40, data_a3c44, data_a3c48, data_a3c4e, data_a3c5c, },
};
const struct defense_strategy *data_a3336[]={&data_a3376, &data_a3418, &data_a34b6, &data_a3554, &data_a35ce, &data_a3660, &data_a36f2, &data_a377a, &data_a37dc, &data_a3854, &data_a3906, &data_a397e, &data_a3a1c, &data_a3ab6, &data_a3b46, &data_a3bf4,  };

/* END E.Honda */
