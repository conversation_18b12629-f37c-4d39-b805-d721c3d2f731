/*
 *  aibyte_guile.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */





/* AGG0 Guile */
const u8 data_9be9e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	<PERSON>B_STANDSTILL, 0x07, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc1, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON>_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc1, 
	<PERSON><PERSON>_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON>_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9bed2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	<PERSON>B_STANDSTILL, 0x07, 
	<PERSON><PERSON>_STANDSTILL, 0x07, 
	<PERSON><PERSON><PERSON>MA<PERSON><PERSON>_<PERSON>ESTA<PERSON>, 
	AIB_LONGWALK, 0x44, 0x00, 
	<PERSON>B_STANDSTILL, 0x07, 
	<PERSON>B_STANDSTILL, 0x07, 
	<PERSON>B_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9be7a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x45, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9beb6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9be86[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc1, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0xc1, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9be6c[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	AIB_LONGWALK, 0x45, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9bec4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9bf00[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};

/* AGG1 Guile */
const u8 data_9bfd4[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9bf5c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x08, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9c002[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x08, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9c07a[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c2a2[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x82, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9bfaa[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9c496[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_JUMP, 0x22, 0x14, 0x80, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9c0fe[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c2f2[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c182[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x0b, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c0c2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x90, 0x94, 0x40, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c2ca[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x82, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c35c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x0b, 0x00, 
	AIB_JUMP, 0xa0, 0x94, 0x80, 0x80, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0xa1, 0x94, 0x80, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9bf7c[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9c146[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x90, 0x94, 0x40, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c4ba[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9bf2e[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9c46c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x0b, 0x00, 
	AIB_JUMP, 0xa0, 0x94, 0x80, 0x80, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0xa1, 0x94, 0x80, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_9c050[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9c274[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x0b, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c1bc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x0b, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c1ea[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x82, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c0e0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0xa0, 0x94, 0x40, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c23a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x0b, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c38a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_JUMP, 0x22, 0x14, 0x80, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c406[] = { AIB_TYPE4, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c212[] = { AIB_TYPE4, 
	AIB_STANDSTILL, 0x82, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c164[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0xa0, 0x94, 0x40, 0x30, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c3b2[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c022[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_EXITRAND, 
};
const u8 data_9c32c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x08, 
	AIB_KICK, 0x04, 0x00, 
	AIB_JUMP, 0xa1, 0x94, 0x80, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9c440[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x08, 
	AIB_KICK, 0x04, 0x00, 
	AIB_JUMP, 0xa1, 0x94, 0x80, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};

/* DEF  Guile */
const u8 data_a5276[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a528a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a528e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a5296[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a52aa[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a52c4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a532a[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a533e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a5342[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a534a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a535e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a5378[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a53de[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a53f2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a53f6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a53fe[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a5412[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a542c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a5492[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a5496[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a549a[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a54a6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a54ae[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a54b4[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a5504[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a5518[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a551c[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a5528[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a5532[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a5586[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a559a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a559e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a55aa[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a55b4[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a5604[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a5608[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a561c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x58, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a5680[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a568c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a5690[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a56ec[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a56f0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a56f4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a575c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a5760[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a5764[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a576c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x10, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a5796[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x10, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a57b4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x10, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a581a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a581e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a5822[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x20, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a5888[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a588c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a5890[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a58de[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a58e2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a58e6[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a5934[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a5938[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a593c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a5992[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_RESTART, 
};
const u8 data_a5998[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a599c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a59aa[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a59c8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a5a3e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_9c72a = {
	{0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, },
	{data_9c406, data_9c440, data_9c46c, data_9c496, data_9c4ba,  }
};
const AIAggTable data_9c6ea = {
	{0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, },
	{data_9c2f2, data_9c32c, data_9c35c, data_9c38a, data_9c3b2, data_9c406, data_9c406, data_9c406, data_9c406, data_9c406, data_9c406, data_9c406, data_9c406, data_9c406, data_9c406, data_9c406, }
};
const AIAggTable data_9c6aa = {
	{2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 0, 0, 0, 0, 1, 1, 1, 1, },
	{data_9c23a, data_9c274, data_9c2a2, data_9c2ca, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, data_9c2f2, }
};
const AIAggTable data_9c66a = {
	{2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 0, 0, 0, 0, 1, 1, 1, 1, },
	{data_9c182, data_9c1bc, data_9c1ea, data_9c212, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, data_9c23a, }
};
const AIAggTable data_9c62a = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2, 1, 1, 2, 2, },
	{data_9c0fe, data_9c146, data_9c164, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, data_9c182, }
};
const AIAggTable data_9c5ea = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2, 1, 1, 2, 2, },
	{data_9c07a, data_9c0c2, data_9c0e0, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, data_9c0fe, }
};
const AIAggTable data_9c5aa = {
	{0, 0, 0, 0, 0, 0, 2, 3, 3, 3, 3, 1, 1, 1, 2, 2, 1, 1, 2, 2, 2, 2, 2, 2, 1, 1, 0, 1, 2, 3, 0, 1, },
	{data_9bfd4, data_9c002, data_9c022, data_9c050, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, data_9c07a, }
};
const AIAggTable data_9c56a = {
	{0, 0, 0, 0, 0, 0, 2, 3, 3, 3, 3, 1, 1, 1, 2, 2, 1, 1, 2, 2, 2, 2, 2, 2, 1, 1, 0, 1, 2, 3, 0, 1, },
	{data_9bf2e, data_9bf5c, data_9bf7c, data_9bfaa, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, data_9bfd4, }
};
const u8 *data_9c51a[8]={
	data_9bf00,
	data_9bed2,
	data_9bec4,
	data_9beb6,
	data_9be9e,
	data_9be86,
	data_9be7a,
	data_9be6c,
};
const AIAggTable *data_9c54a[8]={
	&data_9c72a,
	&data_9c6ea,
	&data_9c6aa,
	&data_9c66a,
	&data_9c62a,
	&data_9c5ea,
	&data_9c5aa,
	&data_9c56a,
};
const u8 **data_9c50a[]={
	data_9c51a, 		/* vs Ryu */
	data_9c51a, 		/* vs E.Honda */
	data_9c51a, 		/* vs Blanka */
	data_9c51a, 		/* vs Guile */
	data_9c51a, 		/* vs Ken */
	data_9c51a, 		/* vs Chun-Li */
	data_9c51a, 		/* vs Zangeif */
	data_9c51a, 		/* vs Dhalsim */
};
const AIAggTable **data_9c53a[]={
	data_9c54a, 		/* vs Ryu */
	data_9c54a, 		/* vs E.Honda */
	data_9c54a, 		/* vs Blanka */
	data_9c54a, 		/* vs Guile */
	data_9c54a, 		/* vs Ken */
	data_9c54a, 		/* vs Chun-Li */
	data_9c54a, 		/* vs Zangeif */
	data_9c54a, 		/* vs Dhalsim */
};
struct dualptr data_9be68={data_9c50a, data_9c53a};
const struct defense_strategy data_a522a={ 
    { 3, 1, 5, 2, 5, 3, 2, 1, 5, 1, 1, 4, 2, 4, 5, 4, 2, 5, 3, 1, 5, 2, 5, 5, 4, 4, 1, 5, 3, 5, 4, 2,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a5276, data_a528a, data_a528e, data_a5296, data_a52aa, data_a52c4, },
};
const struct defense_strategy data_a52de={ 
    { 1, 5, 4, 1, 2, 5, 2, 4, 5, 2, 1, 4, 4, 5, 4, 5, 5, 1, 1, 4, 5, 5, 5, 4, 1, 4, 5, 2, 5, 2, 5, 2,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a532a, data_a533e, data_a5342, data_a534a, data_a535e, data_a5378, },
};
const struct defense_strategy data_a5392={ 
    { 4, 1, 2, 1, 1, 2, 4, 2, 4, 5, 5, 4, 5, 4, 5, 5, 5, 1, 5, 2, 5, 4, 1, 5, 2, 4, 5, 5, 5, 1, 2, 4,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a53de, data_a53f2, data_a53f6, data_a53fe, data_a5412, data_a542c, },
};
const struct defense_strategy data_a5446={ 
    { 0, 0, 2, 3, 5, 1, 3, 3, 1, 4, 0, 4, 4, 2, 5, 0, 0, 5, 0, 1, 0, 4, 0, 0, 0, 3, 4, 0, 2, 0, 3, 1,  },
    { 0, 0, 2, 3, 5, 1, 3, 3, 1, 4, 0, 4, 4, 2, 5, 0, 0, 5, 0, 1, 0, 4, 0, 0, 0, 3, 4, 0, 2, 0, 3, 1,  },
    { data_a5492, data_a5496, data_a549a, data_a54a6, data_a54ae, data_a54b4, },
};
const struct defense_strategy data_a54ba={ 
    { 0, 1, 3, 1, 1, 1, 3, 2, 0, 2, 1, 0, 1, 2, 1, 1, 0, 3, 1, 2, 2, 1, 4, 4, 0, 1, 4, 3, 1, 4, 2, 1,  },
    { 0, 1, 3, 1, 1, 1, 3, 2, 0, 2, 1, 0, 1, 2, 1, 1, 0, 3, 1, 2, 2, 1, 4, 4, 0, 1, 4, 3, 1, 4, 2, 1,  },
    { data_a5504, data_a5518, data_a551c, data_a5528, data_a5532, },
};
const struct defense_strategy data_a553c={ 
    { 0, 2, 1, 0, 1, 1, 1, 2, 0, 4, 3, 1, 2, 2, 1, 4, 0, 1, 1, 2, 1, 3, 1, 3, 0, 2, 1, 1, 4, 1, 2, 1,  },
    { 0, 2, 1, 0, 1, 1, 1, 2, 0, 4, 3, 1, 2, 2, 1, 4, 0, 1, 1, 2, 1, 3, 1, 3, 0, 2, 1, 1, 4, 1, 2, 1,  },
    { data_a5586, data_a559a, data_a559e, data_a55aa, data_a55b4, },
};
const struct defense_strategy data_a55be={ 
    { 2, 0, 1, 2, 0, 1, 2, 1, 0, 2, 0, 2, 2, 1, 1, 2, 0, 1, 2, 1, 2, 2, 0, 1, 2, 0, 1, 2, 1, 2, 2, 1,  },
    { 2, 0, 1, 2, 0, 1, 2, 1, 0, 2, 0, 2, 2, 1, 1, 2, 0, 1, 2, 1, 2, 2, 0, 1, 2, 0, 1, 2, 1, 2, 2, 1,  },
    { data_a5604, data_a5608, data_a561c, },
};
const struct defense_strategy data_a563a={ 
    { 0, 2, 0, 2, 1, 2, 0, 2, 0, 1, 0, 0, 0, 0, 1, 0, 0, 2, 0, 1, 2, 0, 0, 2, 2, 0, 2, 0, 2, 1, 2, 0,  },
    { 0, 2, 0, 2, 1, 2, 0, 2, 0, 1, 0, 0, 0, 0, 1, 0, 0, 2, 0, 1, 2, 0, 0, 2, 2, 0, 2, 0, 2, 1, 2, 0,  },
    { data_a5680, data_a568c, data_a5690, },
};
const struct defense_strategy data_a56a4={ 
    { 0, 2, 1, 2, 0, 2, 0, 2, 2, 0, 2, 0, 0, 2, 1, 0, 0, 0, 2, 2, 1, 2, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0,  },
    { 0, 2, 1, 2, 0, 2, 0, 2, 2, 0, 2, 0, 0, 2, 1, 0, 0, 0, 2, 2, 1, 2, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0,  },
    { data_a56ec, data_a56f0, data_a56f4, },
};
const struct defense_strategy data_a5710={ 
    { 2, 3, 2, 3, 1, 2, 1, 4, 1, 2, 4, 1, 4, 1, 5, 0, 0, 1, 5, 1, 2, 2, 0, 2, 2, 0, 1, 4, 1, 5, 3, 1,  },
    { 2, 3, 2, 3, 1, 2, 1, 4, 1, 2, 4, 1, 4, 1, 5, 0, 0, 1, 5, 1, 2, 2, 0, 2, 2, 0, 1, 4, 1, 5, 3, 1,  },
    { data_a575c, data_a5760, data_a5764, data_a576c, data_a5796, data_a57b4, },
};
const struct defense_strategy data_a57d2={ 
    { 1, 2, 0, 2, 1, 2, 2, 1, 2, 1, 0, 1, 0, 1, 2, 2, 2, 0, 2, 2, 1, 2, 2, 2, 2, 0, 1, 0, 2, 1, 2, 1,  },
    { 1, 2, 0, 2, 1, 2, 2, 1, 2, 1, 0, 1, 0, 1, 2, 2, 2, 0, 2, 2, 1, 2, 2, 2, 2, 0, 1, 0, 2, 1, 2, 1,  },
    { data_a581a, data_a581e, data_a5822, },
};
const struct defense_strategy data_a5842={ 
    { 2, 2, 2, 1, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 1, 2, 1, 2, 2, 2, 1, 2,  },
    { 2, 2, 2, 1, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 1, 2, 1, 2, 2, 2, 1, 2,  },
    { data_a5888, data_a588c, data_a5890, },
};
const struct defense_strategy data_a5898={ 
    { 0, 2, 2, 0, 2, 2, 1, 2, 2, 2, 2, 2, 2, 0, 2, 2, 2, 1, 2, 2, 2, 2, 1, 2, 2, 0, 2, 2, 1, 2, 2, 2,  },
    { 0, 2, 2, 0, 2, 2, 1, 2, 2, 2, 2, 2, 2, 0, 2, 2, 2, 1, 2, 2, 2, 2, 1, 2, 2, 0, 2, 2, 1, 2, 2, 2,  },
    { data_a58de, data_a58e2, data_a58e6, },
};
const struct defense_strategy data_a58ee={ 
    { 0, 2, 2, 0, 2, 2, 2, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 2, 2, 2, 0, 2, 2, 0, 1, 2, 2, 2, 2, 2,  },
    { 0, 2, 2, 0, 2, 2, 2, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 2, 2, 2, 0, 2, 2, 0, 1, 2, 2, 2, 2, 2,  },
    { data_a5934, data_a5938, data_a593c, },
};
const struct defense_strategy data_a5944={ 
    { 0, 1, 0, 2, 3, 3, 0, 2, 0, 2, 0, 0, 2, 0, 0, 1, 1, 4, 0, 3, 0, 1, 0, 2, 0, 0, 0, 2, 0, 0, 3, 0,  },
    { 0, 1, 0, 2, 3, 3, 0, 2, 0, 2, 0, 0, 2, 0, 0, 1, 1, 4, 0, 3, 0, 1, 0, 2, 0, 0, 0, 2, 0, 0, 3, 0,  },
    { data_a5992, data_a5998, data_a599c, data_a59aa, data_a59c8, },
};
const struct defense_strategy data_a59f2={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a5a3e, },
};
const struct defense_strategy *data_a51ea[]={&data_a522a, &data_a52de, &data_a5392, &data_a5446, &data_a54ba, &data_a553c, &data_a55be, &data_a563a, &data_a56a4, &data_a5710, &data_a57d2, &data_a5842, &data_a5898, &data_a58ee, &data_a5944, &data_a59f2,  };

/* END Guile */
