/*
 *  aibyte_ken.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */


/* AGG0 Ken */
const u8 data_9c9b0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x22, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x22, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	<PERSON>B_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9ca18[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x40, 0x00, 
	<PERSON><PERSON>_EXIT3, 0x00, 
	<PERSON><PERSON>_RESTART, 
};
const u8 data_9ca26[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x04, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_EXIT3, 0xff, 
	AIB_RESTART, 
};
const u8 data_9c96e[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_RESTART, 
};
const u8 data_9c990[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_RESTART, 
};
const u8 data_9ca02[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_STANDSTILL, 0x80, 
	AIB_STANDSTILL, 0x80, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9c9ec[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_STANDSTILL, 0x80, 
	AIB_STANDSTILL, 0x80, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9c9ce[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x46, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x22, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x22, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};

/* AGG1 Ken */
const u8 data_9ce20[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9cb70[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ca42[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x05, 0x00, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ce70[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SHORTWALK, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9cd2a[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cac0[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cb1e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x05, 0x00, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cbd2[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cc4c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cb9c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cc38[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cdb6[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9cbfa[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cc6a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cda2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cc8a[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ce06[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SHORTWALK, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9cb48[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cd64[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ccf8[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x98, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cdea[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9ce3a[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9ccc2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ca94[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ce54[] = { AIB_TYPE4, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9cc18[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cc9e[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9caf6[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cd84[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x98, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cd4e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ccd8[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ca6c[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x04, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9cdd0[] = { AIB_TYPE4, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x04, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9cd16[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ce8a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SHORTWALK, 0x40, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SHORTWALK, 0x40, 
	AIB_KICK, 0x84, 0x00, 
	AIB_EXITRAND, 
};

/* DEF  Ken */
const u8 data_a6108[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a611c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6120[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a612e[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x20, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6138[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a619c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a61b0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a61b4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a61c2[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x20, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a61cc[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6230[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a6244[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6248[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6256[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x20, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6260[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a62c6[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a62ca[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a62ce[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a62d6[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a62de[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a62e4[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a633a[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a6344[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6348[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a6352[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a635c[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6362[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a63ba[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a63c4[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a63c8[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a63d2[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a63dc[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a63e2[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a6438[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a643c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6440[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6446[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a645a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a64b2[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_RESTART, 
};
const u8 data_a64bc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a64c0[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a64c6[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6512[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a651a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a651e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6570[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x10, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6584[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6588[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a658e[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x58, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a65f4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6604[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a6608[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a660e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6684[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a66f8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x98, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6766[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a67ce[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a67d2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a67d6[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a67e8[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a67f2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6804[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x40, 0x06, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6856[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a6876[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a687a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a6880[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const AIAggTable data_9d0be = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, },
	{data_9ce20, data_9ce3a, data_9ce54, data_9ce70, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, data_9ce8a, }
};
const AIAggTable data_9d07e = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, },
	{data_9cdb6, data_9cdd0, data_9cdea, data_9ce06, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, data_9ce20, }
};
const AIAggTable data_9d03e = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 4, 4, },
	{data_9cd2a, data_9cd4e, data_9cd64, data_9cd84, data_9cda2, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, data_9cdb6, }
};
const AIAggTable data_9cffe = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 4, 4, },
	{data_9cc9e, data_9ccc2, data_9ccd8, data_9ccf8, data_9cd16, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, data_9cd2a, }
};
const AIAggTable data_9cfbe = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2, },
	{data_9cc4c, data_9cc6a, data_9cc8a, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, data_9cc9e, }
};
const AIAggTable data_9cf7e = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2, },
	{data_9cbfa, data_9cc18, data_9cc38, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, data_9cc4c, }
};
const AIAggTable data_9cf3e = {
	{4, 4, 4, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 1, 1, 1, 2, 2, 2, 2, },
	{data_9cb1e, data_9cb48, data_9cb70, data_9cb9c, data_9cbd2, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, data_9cbfa, }
};
const AIAggTable data_9cefe = {
	{4, 4, 4, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 1, 1, 1, 2, 2, 2, 2, },
	{data_9ca42, data_9ca6c, data_9ca94, data_9cac0, data_9caf6, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, data_9cb1e, }
};
const u8 *data_9ceae[8]={
	data_9ca26,
	data_9ca18,
	data_9ca02,
	data_9c9ec,
	data_9c9ce,
	data_9c9b0,
	data_9c990,
	data_9c96e,
};
const AIAggTable *data_9cede[8]={
	&data_9d0be,
	&data_9d07e,
	&data_9d03e,
	&data_9cffe,
	&data_9cfbe,
	&data_9cf7e,
	&data_9cf3e,
	&data_9cefe,
};
const u8 **data_9ce9e[]={
	data_9ceae, 		/* vs Ryu */
	data_9ceae, 		/* vs E.Honda */
	data_9ceae, 		/* vs Blanka */
	data_9ceae, 		/* vs Guile */
	data_9ceae, 		/* vs Ken */
	data_9ceae, 		/* vs Chun-Li */
	data_9ceae, 		/* vs Zangeif */
	data_9ceae, 		/* vs Dhalsim */
};
const AIAggTable **data_9cece[]={
	data_9cede, 		/* vs Ryu */
	data_9cede, 		/* vs E.Honda */
	data_9cede, 		/* vs Blanka */
	data_9cede, 		/* vs Guile */
	data_9cede, 		/* vs Ken */
	data_9cede, 		/* vs Chun-Li */
	data_9cede, 		/* vs Zangeif */
	data_9cede, 		/* vs Dhalsim */
};
struct dualptr data_9c96a={data_9ce9e, data_9cece};
const struct defense_strategy data_a60be={ 
    { 3, 1, 4, 2, 0, 4, 3, 0, 2, 3, 0, 0, 4, 2, 0, 2, 0, 2, 1, 3, 2, 3, 2, 3, 2, 0, 0, 4, 0, 2, 0, 2,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6108, data_a611c, data_a6120, data_a612e, data_a6138, },
};
const struct defense_strategy data_a6152={ 
    { 3, 1, 4, 2, 0, 3, 4, 0, 2, 3, 4, 4, 3, 2, 3, 2, 0, 2, 2, 1, 2, 4, 2, 0, 2, 4, 4, 0, 0, 4, 3, 2,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a619c, data_a61b0, data_a61b4, data_a61c2, data_a61cc, },
};
const struct defense_strategy data_a61e6={ 
    { 4, 1, 3, 3, 2, 4, 3, 3, 0, 2, 2, 0, 4, 2, 3, 0, 2, 3, 2, 1, 2, 4, 4, 2, 0, 4, 2, 4, 4, 0, 2, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6230, data_a6244, data_a6248, data_a6256, data_a6260, },
};
const struct defense_strategy data_a627a={ 
    { 5, 1, 0, 4, 0, 0, 4, 5, 3, 5, 3, 5, 1, 2, 0, 2, 0, 3, 1, 3, 5, 4, 4, 0, 5, 2, 0, 0, 0, 1, 0, 2,  },
    { 5, 1, 0, 4, 0, 0, 4, 5, 3, 5, 3, 5, 1, 2, 0, 2, 0, 3, 1, 3, 5, 4, 4, 0, 5, 2, 0, 0, 0, 1, 0, 2,  },
    { data_a62c6, data_a62ca, data_a62ce, data_a62d6, data_a62de, data_a62e4, },
};
const struct defense_strategy data_a62ee={ 
    { 5, 5, 1, 3, 1, 4, 0, 0, 5, 5, 3, 1, 2, 1, 4, 1, 5, 5, 0, 2, 1, 3, 1, 0, 5, 5, 1, 1, 3, 1, 4, 4,  },
    { 5, 5, 1, 3, 1, 4, 0, 0, 5, 5, 3, 1, 2, 1, 4, 1, 5, 5, 0, 2, 1, 3, 1, 0, 5, 5, 1, 1, 3, 1, 4, 4,  },
    { data_a633a, data_a6344, data_a6348, data_a6352, data_a635c, data_a6362, },
};
const struct defense_strategy data_a636e={ 
    { 5, 5, 1, 0, 3, 0, 1, 1, 5, 5, 3, 0, 2, 1, 3, 1, 5, 5, 4, 1, 4, 1, 0, 1, 5, 5, 0, 0, 1, 2, 1, 0,  },
    { 5, 5, 1, 0, 3, 0, 1, 1, 5, 5, 3, 0, 2, 1, 3, 1, 5, 5, 4, 1, 4, 1, 0, 1, 5, 5, 0, 0, 1, 2, 1, 0,  },
    { data_a63ba, data_a63c4, data_a63c8, data_a63d2, data_a63dc, data_a63e2, },
};
const struct defense_strategy data_a63ee={ 
    { 0, 4, 4, 2, 0, 2, 4, 4, 1, 4, 4, 4, 1, 4, 4, 2, 0, 2, 2, 3, 0, 4, 4, 4, 0, 0, 2, 0, 2, 0, 2, 4,  },
    { 0, 4, 4, 2, 0, 2, 4, 4, 1, 4, 4, 4, 1, 4, 4, 2, 0, 2, 2, 3, 0, 4, 4, 4, 0, 0, 2, 0, 2, 0, 2, 4,  },
    { data_a6438, data_a643c, data_a6440, data_a6446, data_a645a, },
};
const struct defense_strategy data_a646a={ 
    { 0, 0, 0, 2, 0, 2, 0, 3, 1, 3, 1, 0, 1, 0, 2, 0, 3, 0, 2, 3, 0, 3, 0, 2, 0, 3, 0, 0, 2, 0, 1, 0,  },
    { 0, 0, 0, 2, 0, 2, 0, 3, 1, 3, 1, 0, 1, 0, 2, 0, 3, 0, 2, 3, 0, 3, 0, 2, 0, 3, 0, 0, 2, 0, 1, 0,  },
    { data_a64b2, data_a64bc, data_a64c0, data_a64c6, },
};
const struct defense_strategy data_a64cc={ 
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6512, data_a651a, data_a651e, },
};
const struct defense_strategy data_a6524={ 
    { 1, 0, 0, 1, 0, 0, 1, 0, 3, 1, 3, 0, 1, 0, 0, 1, 1, 0, 1, 0, 3, 1, 0, 0, 3, 1, 0, 1, 0, 0, 1, 0,  },
    { 1, 0, 0, 1, 0, 0, 1, 0, 3, 1, 3, 0, 1, 0, 0, 1, 1, 0, 1, 0, 3, 1, 0, 0, 3, 1, 0, 1, 0, 0, 1, 0,  },
    { data_a6570, data_a6584, data_a6588, data_a658e, },
};
const struct defense_strategy data_a65a8={ 
    { 1, 3, 0, 3, 3, 3, 1, 0, 3, 1, 3, 0, 3, 1, 0, 1, 0, 3, 1, 3, 1, 3, 2, 0, 3, 0, 0, 1, 0, 2, 0, 0,  },
    { 1, 3, 0, 3, 3, 3, 1, 0, 3, 1, 3, 0, 3, 1, 0, 1, 0, 3, 1, 3, 1, 3, 2, 0, 3, 0, 0, 1, 0, 2, 0, 0,  },
    { data_a65f4, data_a6604, data_a6608, data_a660e, },
};
const struct defense_strategy data_a663e={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6684, },
};
const struct defense_strategy data_a66b2={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a66f8, },
};
const struct defense_strategy data_a6720={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a6766, },
};
const struct defense_strategy data_a6782={ 
    { 1, 2, 4, 5, 1, 3, 4, 1, 5, 3, 3, 1, 3, 2, 3, 3, 1, 5, 2, 2, 4, 3, 1, 2, 5, 3, 1, 5, 5, 1, 2, 4,  },
    { 1, 2, 4, 5, 1, 3, 4, 1, 5, 3, 3, 1, 3, 2, 3, 3, 1, 5, 2, 2, 4, 3, 1, 2, 5, 3, 1, 5, 5, 1, 2, 4,  },
    { data_a67ce, data_a67d2, data_a67d6, data_a67e8, data_a67f2, data_a6804, },
};
const struct defense_strategy data_a680a={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_a6856, data_a6876, data_a687a, data_a6880, },
};
const struct defense_strategy *data_a607e[]={&data_a60be, &data_a6152, &data_a61e6, &data_a627a, &data_a62ee, &data_a636e, &data_a63ee, &data_a646a, &data_a64cc, &data_a6524, &data_a65a8, &data_a663e, &data_a66b2, &data_a6720, &data_a6782, &data_a680a,  };

/* END Ken */

