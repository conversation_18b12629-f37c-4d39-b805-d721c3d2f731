/*
 *  aibyte_mbison.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */



/* AGG0 M.Bison */
const u8 data_9f378[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	<PERSON><PERSON>_GO_AGG1, 
};
const u8 data_9f3be[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9f380[] = { AIB_TYPE4, 
	AIB_GO_AGG1, 
};
const u8 data_9f3b0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAY<PERSON>_RESTART, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON><PERSON>LONGWALK, 0x44, 0x00, 
	<PERSON><PERSON><PERSON><PERSON>YBE_RESTART, 
	<PERSON>B_STANDSTILL, 0x07, 
	AI<PERSON>_RESTART, 
};
const u8 data_9f3a2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9f386[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9f394[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9f3cc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};

/* AGG1 M.Bison */
const u8 data_9f582[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f56c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f4c4[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f620[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f5f8[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f8e4[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9fb2c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f532[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f434[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f94c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9fa96[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f9b2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f7fa[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x90, 
	AIB_JUMP, 0x22, 0x10, 0x90, 0x90, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f9d8[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f44a[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f4fa[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9fac6[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f932[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9fa18[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f6a2[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9fb0c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9faec[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f46a[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f89e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f81e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f838[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f5cc[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f412[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f3e8[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f724[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x90, 
	AIB_JUMP, 0x22, 0x10, 0x90, 0x90, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f7c8[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f9f8[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f6f2[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f7a0[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f652[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x90, 
	AIB_JUMP, 0x22, 0x10, 0x90, 0x90, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9fa60[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f86e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f548[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f48e[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9fb5a[] = { AIB_TYPE0, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_JUMP, 0x22, 0x00, 0x40, 0x70, 
	AIB_LONGWALK, 0x0a, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x40, 0x70, 
	AIB_COLLDIS, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_COLLDIS, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_EXIT4, 0x00, 
};
const u8 data_9fa46[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f402[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f4de[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f982[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f748[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f774[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f5a0[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f4b0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f8c4[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f676[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f6ca[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_JUMP, 0x20, 0x10, 0x90, 0x90, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f904[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_BB0_NOTWITHIN, 0x00, 0xa0, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x10, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_9f510[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x02, 0x00, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_KICK, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};

/* DEF  M.Bison */
const u8 data_a9b32[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9b36[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9b3a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9b40[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9b46[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9b5a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9bc4[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9bc8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9bcc[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9bd2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9bd8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9bec[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9c56[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9c5a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9c5e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9c64[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9c6a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9c7e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x30, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9ce2[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9ce6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9cea[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_KICK, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9cf2[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9d46[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9d4a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9d4e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a9d58[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a9d62[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a9db4[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9db8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9dbc[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a9dc6[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a9dd0[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a9e2a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9e2e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9e32[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9e40[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9e46[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9e4c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_a9e54[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9eae[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9eba[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9f06[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x60, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9f28[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9f2c[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9f8c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a9f90[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a9f94[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9fa2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a9fa8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9fc0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9fd8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a9ff0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x80, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aa060[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aa06e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aa0f8[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aa0fc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aa100[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_aa106[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_aa10c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x00, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aa190[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aa194[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aa198[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_aa19e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_aa1a4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x68, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x00, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x00, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aa228[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aa22c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aa230[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_aa236[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_aa23c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x90, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x00, 0x90, 0x90, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xd0, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aa2c0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x00, 0x90, 0x90, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aa2d2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aa348[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_9fdb6 = {
	{0, 0, 0, 0, 3, 3, 4, 4, 5, 5, 5, 5, 5, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6, },
	{data_9fa46, data_9fa60, data_9fa96, data_9fac6, data_9faec, data_9fb0c, data_9fb2c, data_9fb5a, data_9fb5a, data_9fb5a, data_9fb5a, data_9fb5a, data_9fb5a, data_9fb5a, data_9fb5a, data_9fb5a, }
};
const AIAggTable data_9fd76 = {
	{0, 0, 0, 0, 3, 3, 4, 4, 5, 5, 5, 5, 5, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6, },
	{data_9f932, data_9f94c, data_9f982, data_9f9b2, data_9f9d8, data_9f9f8, data_9fa18, data_9fa46, data_9fa46, data_9fa46, data_9fa46, data_9fa46, data_9fa46, data_9fa46, data_9fa46, data_9fa46, }
};
const AIAggTable data_9fd36 = {
	{0, 0, 0, 0, 3, 3, 4, 4, 5, 5, 5, 5, 5, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6, },
	{data_9f81e, data_9f838, data_9f86e, data_9f89e, data_9f8c4, data_9f8e4, data_9f904, data_9f932, data_9f932, data_9f932, data_9f932, data_9f932, data_9f932, data_9f932, data_9f932, data_9f932, }
};
const AIAggTable data_9fcf6 = {
	{3, 3, 3, 3, 3, 2, 2, 2, 2, 3, 0, 0, 0, 0, 0, 3, 0, 0, 1, 1, 1, 1, 1, 3, 1, 1, 4, 4, 4, 4, 3, 3, },
	{data_9f748, data_9f774, data_9f7a0, data_9f7c8, data_9f7fa, data_9f81e, data_9f81e, data_9f81e, data_9f81e, data_9f81e, data_9f81e, data_9f81e, data_9f81e, data_9f81e, data_9f81e, data_9f81e, }
};
const AIAggTable data_9fcb6 = {
	{3, 3, 3, 3, 3, 2, 2, 2, 2, 3, 0, 0, 0, 0, 0, 3, 0, 0, 1, 1, 1, 1, 1, 3, 1, 1, 4, 4, 4, 4, 3, 3, },
	{data_9f676, data_9f6a2, data_9f6ca, data_9f6f2, data_9f724, data_9f748, data_9f748, data_9f748, data_9f748, data_9f748, data_9f748, data_9f748, data_9f748, data_9f748, data_9f748, data_9f748, }
};
const AIAggTable data_9fc76 = {
	{3, 3, 3, 3, 3, 2, 2, 2, 2, 3, 0, 0, 0, 0, 0, 3, 0, 0, 1, 1, 1, 1, 1, 3, 1, 1, 4, 4, 4, 4, 3, 3, },
	{data_9f5a0, data_9f5cc, data_9f5f8, data_9f620, data_9f652, data_9f676, data_9f676, data_9f676, data_9f676, data_9f676, data_9f676, data_9f676, data_9f676, data_9f676, data_9f676, data_9f676, }
};
const AIAggTable data_9fc36 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 7, 7, 7, 7, 7, 7, 6, 6, 6, 4, 4, 4, 5, 5, 5, 2, 2, 2, 3, 3, },
	{data_9f4c4, data_9f4de, data_9f4fa, data_9f510, data_9f532, data_9f548, data_9f56c, data_9f582, data_9f5a0, data_9f5a0, data_9f5a0, data_9f5a0, data_9f5a0, data_9f5a0, data_9f5a0, data_9f5a0, }
};
const AIAggTable data_9fbf6 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 7, 7, 7, 7, 7, 7, 6, 6, 6, 4, 4, 4, 5, 5, 5, 2, 2, 2, 3, 3, },
	{data_9f3e8, data_9f402, data_9f412, data_9f434, data_9f44a, data_9f46a, data_9f48e, data_9f4b0, data_9f4c4, data_9f4c4, data_9f4c4, data_9f4c4, data_9f4c4, data_9f4c4, data_9f4c4, data_9f4c4, }
};
const u8 *data_9fba6[8]={
	data_9f3cc,
	data_9f3be,
	data_9f3b0,
	data_9f3a2,
	data_9f394,
	data_9f386,
	data_9f380,
	data_9f378,
};
const AIAggTable *data_9fbd6[8]={
	&data_9fdb6,
	&data_9fd76,
	&data_9fd36,
	&data_9fcf6,
	&data_9fcb6,
	&data_9fc76,
	&data_9fc36,
	&data_9fbf6,
};
const u8 **data_9fb96[]={
	data_9fba6, 		/* vs Ryu */
	data_9fba6, 		/* vs E.Honda */
	data_9fba6, 		/* vs Blanka */
	data_9fba6, 		/* vs Guile */
	data_9fba6, 		/* vs Ken */
	data_9fba6, 		/* vs Chun-Li */
	data_9fba6, 		/* vs Zangeif */
	data_9fba6, 		/* vs Dhalsim */
};
const AIAggTable **data_9fbc6[]={
	data_9fbd6, 		/* vs Ryu */
	data_9fbd6, 		/* vs E.Honda */
	data_9fbd6, 		/* vs Blanka */
	data_9fbd6, 		/* vs Guile */
	data_9fbd6, 		/* vs Ken */
	data_9fbd6, 		/* vs Chun-Li */
	data_9fbd6, 		/* vs Zangeif */
	data_9fbd6, 		/* vs Dhalsim */
};
struct dualptr data_9f374={data_9fb96, data_9fbc6};
const struct defense_strategy data_a9ae4={ 
    { 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a9b32, data_a9b36, data_a9b3a, data_a9b40, data_a9b46, data_a9b5a, },
};
const struct defense_strategy data_a9b76={ 
    { 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a9bc4, data_a9bc8, data_a9bcc, data_a9bd2, data_a9bd8, data_a9bec, },
};
const struct defense_strategy data_a9c08={ 
    { 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a9c56, data_a9c5a, data_a9c5e, data_a9c64, data_a9c6a, data_a9c7e, },
};
const struct defense_strategy data_a9c9a={ 
    { 3, 2, 1, 2, 2, 2, 3, 2, 2, 2, 2, 3, 3, 3, 1, 0, 2, 2, 2, 3, 1, 2, 3, 3, 1, 3, 0, 3, 2, 0, 0, 2,  },
    { 3, 2, 1, 2, 2, 2, 3, 2, 2, 2, 2, 3, 3, 3, 1, 0, 2, 2, 2, 3, 1, 2, 3, 3, 1, 3, 0, 3, 2, 0, 0, 2,  },
    { data_a9ce2, data_a9ce6, data_a9cea, data_a9cf2, },
};
const struct defense_strategy data_a9cfc={ 
    { 2, 3, 3, 2, 2, 1, 3, 1, 1, 0, 3, 1, 2, 3, 1, 2, 4, 2, 1, 3, 0, 2, 4, 1, 1, 1, 2, 1, 3, 2, 2, 3,  },
    { 2, 3, 3, 2, 2, 1, 3, 1, 1, 0, 3, 1, 2, 3, 1, 2, 4, 2, 1, 3, 0, 2, 4, 1, 1, 1, 2, 1, 3, 2, 2, 3,  },
    { data_a9d46, data_a9d4a, data_a9d4e, data_a9d58, data_a9d62, },
};
const struct defense_strategy data_a9d6a={ 
    { 4, 4, 2, 3, 4, 4, 4, 4, 4, 2, 0, 3, 1, 2, 1, 1, 2, 4, 4, 4, 0, 4, 2, 4, 4, 4, 4, 4, 3, 4, 4, 4,  },
    { 4, 4, 2, 3, 4, 4, 4, 4, 4, 2, 0, 3, 1, 2, 1, 1, 2, 4, 4, 4, 0, 4, 2, 4, 4, 4, 4, 4, 3, 4, 4, 4,  },
    { data_a9db4, data_a9db8, data_a9dbc, data_a9dc6, data_a9dd0, },
};
const struct defense_strategy data_a9dda={ 
    { 2, 2, 0, 0, 0, 2, 0, 0, 6, 0, 2, 2, 2, 0, 2, 6, 0, 2, 0, 2, 2, 6, 0, 0, 2, 6, 2, 6, 2, 6, 6, 6,  },
    { 2, 2, 0, 0, 0, 2, 0, 0, 6, 0, 2, 2, 2, 0, 2, 6, 0, 2, 0, 2, 2, 6, 0, 0, 2, 6, 2, 6, 2, 6, 6, 6,  },
    { data_a9e2a, data_a9e2e, data_a9e32, data_a9e40, data_a9e46, data_a9e4c, data_a9e54, },
};
const struct defense_strategy data_a9e6a={ 
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a9eae, data_a9eba, },
};
const struct defense_strategy data_a9ebe={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a9f06, data_a9f28, data_a9f2c, },
};
const struct defense_strategy data_a9f3c={ 
    { 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 5, 6, 6, 7, 5, 6, 7, 6, 6, 1, 7, 6, 1, 1, 2, 1,  },
    { 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 5, 6, 6, 7, 5, 6, 7, 6, 6, 1, 7, 6, 1, 1, 2, 1,  },
    { data_a9f8c, data_a9f90, data_a9f94, data_a9fa2, data_a9fa8, data_a9fc0, data_a9fd8, data_a9ff0, },
};
const struct defense_strategy data_aa014={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_aa060, data_aa06e, },
};
const struct defense_strategy data_aa0aa={ 
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4,  },
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4,  },
    { data_aa0f8, data_aa0fc, data_aa100, data_aa106, data_aa10c, },
};
const struct defense_strategy data_aa142={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_aa190, data_aa194, data_aa198, data_aa19e, data_aa1a4, },
};
const struct defense_strategy data_aa1da={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_aa228, data_aa22c, data_aa230, data_aa236, data_aa23c, },
};
const struct defense_strategy data_aa272={ 
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { data_aa2c0, data_aa2d2, },
};
const struct defense_strategy data_aa2fa={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_aa348, },
};
const struct defense_strategy *data_a9aa4[]={&data_a9ae4, &data_a9b76, &data_a9c08, &data_a9c9a, &data_a9cfc, &data_a9d6a, &data_a9dda, &data_a9e6a, &data_a9ebe, &data_a9f3c, &data_aa014, &data_aa0aa, &data_aa142, &data_aa1da, &data_aa272, &data_aa2fa,  };

/* END M.Bison */



