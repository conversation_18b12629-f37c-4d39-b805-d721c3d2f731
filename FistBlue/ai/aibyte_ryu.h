/*
 *  aidatatest.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */

/* AGG0 Ryu */
const u8 data_996ba[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x0f, 0x00, 
	<PERSON>B_STANDSTILL, 0x04, 
	<PERSON>B_STANDSTILL, 0x04, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x04, 
	AIB_STANDSTILL, 0x04, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_996aa[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x0f, 0x00, 
	AIB_STANDSTILL, 0x04, 
	<PERSON>B_STANDSTILL, 0x04, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x04, 
	AIB_STANDSTILL, 0x04, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_996d4[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	<PERSON><PERSON>_EXIT3, 0x00, 
	AIB_SHORTWALK, 0x45, 
	AIB_RESTART, 
};
const u8 data_99686[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_STANDSTILL, 0x05, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_99698[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_STANDSTILL, 0x05, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_996ca[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_EXIT3, 0xff, 
	AIB_SHORTWALK, 0x45, 
	AIB_RESTART, 
};
const u8 data_99672[] = { AIB_TYPE2, 
	AIB_EXIT5_2, 0x00, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_STANDSTILL, 0x05, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_996de[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_EXIT3, 0x00, 
	AIB_SHORTWALK, 0x41, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_MAYBE_RESTART, 
	AIB_SHORTWALK, 0x40, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};

/* AGG1 Ryu */
const u8 data_99d4a[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_SHORTWALK, 0x04, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_997fe[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99c3c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_STANDSTILL, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_998e6[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99a22[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99b46[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_LONGWALK, 0x00, 0x01, 
	AIB_KICK, 0x40, 0x06, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99cd4[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_99c88[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_99b68[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_999b2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_99956[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_99ab8[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_99870[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9988e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99992[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_997c0[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99a02[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_99c12[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9992a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_998ae[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99846[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9976a[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99d2e[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99bec[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99a54[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_997de[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99af4[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99c6c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99bc6[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_99a74[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_LONGWALK, 0x00, 0x01, 
	AIB_KICK, 0x40, 0x06, 
	AIB_B94_NODIZZY, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99ae6[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x14, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_EXITRAND, 
};
const u8 data_99cfe[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_STANDSTILL, 0x01, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_99906[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_998ca[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99b8a[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x02, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_99796[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99976[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_99b26[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_9981a[] = { AIB_TYPE2, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x02, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_99bb8[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x14, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_EXITRAND, 
};
const u8 data_99a96[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};
const u8 data_99cae[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_EXITRAND, 
};
const u8 data_999d6[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x14, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_SHORTWALK, 0xc2, 
	AIB_SHORTWALK, 0xc2, 
	AIB_EXITRAND, 
};

/* DEF  Ryu */
const u8 data_a2458[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a245c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a2470[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a247e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2498[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a24fc[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a2500[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a2514[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2522[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a253c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a25a0[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a25a4[] = { AIB_TYPE2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a25b8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a25c6[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a25e0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2642[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a2646[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a264a[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a2656[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x40, 0x06, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a26ac[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a26b0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a26c4[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a26ce[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a26d8[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a272c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a2730[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a2744[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x40, 0x03, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a2750[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a275c[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a27b6[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a27ba[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a27be[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a27c4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x60, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a27d4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x60, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a27e4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a27f2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2800[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2852[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_RESTART, 
};
const u8 data_a285c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a28a8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a28c2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a28c6[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a28ce[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a2924[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x10, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a293e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a2942[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a2946[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a2950[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a2956[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x01, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2974[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x01, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2992[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x01, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x01, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a29f8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2a08[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a2a0c[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a2a12[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2a20[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x02, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2a36[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x01, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2a90[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2b16[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x98, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2b9c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x10, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_STUN, 0x02, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2bac[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2bba[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xa0, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2c28[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a2c2c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_RESTART, 
};
const u8 data_a2c34[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2c46[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2c58[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2c6a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2c7c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x04, 0x70, 0x50, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2c8e[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x30, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_a2ce0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a2ce4[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a2cee[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a2d0e[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const AIAggTable data_99f7e = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 4, 4, 0, 0, 0, 0, },
	{data_99c88, data_99cae, data_99cd4, data_99cfe, data_99d2e, data_99d4a, data_99d4a, data_99d4a, data_99d4a, data_99d4a, data_99d4a, data_99d4a, data_99d4a, data_99d4a, data_99d4a, data_99d4a, }
};
const AIAggTable data_99f3e = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 4, 4, 4, },
	{data_99bc6, data_99bec, data_99c12, data_99c3c, data_99c6c, data_99c88, data_99c88, data_99c88, data_99c88, data_99c88, data_99c88, data_99c88, data_99c88, data_99c88, data_99c88, data_99c88, }
};
const AIAggTable data_99efe = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 4, 4, 4, },
	{data_99af4, data_99b26, data_99b46, data_99b68, data_99b8a, data_99bb8, data_99bc6, data_99bc6, data_99bc6, data_99bc6, data_99bc6, data_99bc6, data_99bc6, data_99bc6, data_99bc6, data_99bc6, }
};
const AIAggTable data_99ebe = {
	{2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 0, 0, 0, 0, },
	{data_99a22, data_99a54, data_99a74, data_99a96, data_99ab8, data_99ae6, data_99af4, data_99af4, data_99af4, data_99af4, data_99af4, data_99af4, data_99af4, data_99af4, data_99af4, data_99af4, }
};
const AIAggTable data_99e7e = {
	{2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, },
	{data_99976, data_99992, data_999b2, data_999d6, data_99a02, data_99a22, data_99a22, data_99a22, data_99a22, data_99a22, data_99a22, data_99a22, data_99a22, data_99a22, data_99a22, data_99a22, }
};
const AIAggTable data_99e3e = {
	{3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 2, 4, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 4, 4, 4, 4, 4, 4, },
	{data_998ca, data_998e6, data_99906, data_9992a, data_99956, data_99976, data_99976, data_99976, data_99976, data_99976, data_99976, data_99976, data_99976, data_99976, data_99976, data_99976, }
};
const AIAggTable data_99dfe = {
	{3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 2, 4, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 4, 4, 4, 4, 4, 4, },
	{data_9981a, data_99846, data_99870, data_9988e, data_998ae, data_998ca, data_998ca, data_998ca, data_998ca, data_998ca, data_998ca, data_998ca, data_998ca, data_998ca, data_998ca, data_998ca, }
};
const AIAggTable data_99dbe = {
	{3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 2, 4, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 4, 4, 4, 4, 4, 4, },
	{data_9976a, data_99796, data_997c0, data_997de, data_997fe, data_9981a, data_9981a, data_9981a, data_9981a, data_9981a, data_9981a, data_9981a, data_9981a, data_9981a, data_9981a, data_9981a, }
};
#pragma mark RyuLookup
const u8 *data_99d6e[8]={
	data_996de,
	data_996d4,
	data_996ca,
	data_996ba,
	data_996aa,
	data_99698,
	data_99686,
	data_99672,
};
const AIAggTable *data_99d9e[8]={
	&data_99f7e,
	&data_99f3e,
	&data_99efe,
	&data_99ebe,
	&data_99e7e,
	&data_99e3e,
	&data_99dfe,
	&data_99dbe,
};
const u8 **data_99d5e[]={
	data_99d6e, 		/* vs Ryu */
	data_99d6e, 		/* vs E.Honda */
	data_99d6e, 		/* vs Blanka */
	data_99d6e, 		/* vs Guile */
	data_99d6e, 		/* vs Ken */
	data_99d6e, 		/* vs Chun-Li */
	data_99d6e, 		/* vs Zangeif */
	data_99d6e, 		/* vs Dhalsim */
};
const AIAggTable **data_99d8e[]={
	data_99d9e, 		/* vs Ryu */
	data_99d9e, 		/* vs E.Honda */
	data_99d9e, 		/* vs Blanka */
	data_99d9e, 		/* vs Guile */
	data_99d9e, 		/* vs Ken */
	data_99d9e, 		/* vs Chun-Li */
	data_99d9e, 		/* vs Zangeif */
	data_99d9e, 		/* vs Dhalsim */
};
struct dualptr data_9966e={data_99d5e, data_99d8e};
const struct defense_strategy data_a240e={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 4, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 4, 4, 4,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_a2458, data_a245c, data_a2470, data_a247e, data_a2498, },
};
const struct defense_strategy data_a24b2={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 4, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 4, 4, 4,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_a24fc, data_a2500, data_a2514, data_a2522, data_a253c, },
};
const struct defense_strategy data_a2556={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 4, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 4, 4, 4,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_a25a0, data_a25a4, data_a25b8, data_a25c6, data_a25e0, },
};
const struct defense_strategy data_a25fa={ 
    { 3, 0, 1, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 1, 0, 0, 0, 2, 0, 1, 2, 0, 0, 1, 3, 0, 3, 2, 0, 0, 2,  },
    { 3, 0, 1, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 1, 0, 0, 0, 2, 0, 1, 2, 0, 0, 1, 3, 0, 3, 2, 0, 0, 2,  },
    { data_a2642, data_a2646, data_a264a, data_a2656, },
};
const struct defense_strategy data_a2662={ 
    { 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 3, 0, 0, 3, 0, 4, 4, 2, 0, 0, 0, 2, 4, 1, 1, 1, 2, 1, 0, 0, 1, 4,  },
    { 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 3, 0, 0, 3, 0, 4, 4, 2, 0, 0, 0, 2, 4, 1, 1, 1, 2, 1, 0, 0, 1, 4,  },
    { data_a26ac, data_a26b0, data_a26c4, data_a26ce, data_a26d8, },
};
const struct defense_strategy data_a26e2={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 4, 1, 3, 4, 1, 3, 1, 1, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 4, 1, 3, 4, 1, 3, 1, 1, 1,  },
    { data_a272c, data_a2730, data_a2744, data_a2750, data_a275c, },
};
const struct defense_strategy data_a2766={ 
    { 2, 6, 4, 2, 4, 4, 4, 4, 4, 4, 2, 4, 6, 4, 7, 4, 4, 5, 0, 5, 3, 4, 5, 0, 7, 0, 3, 6, 2, 0, 6, 4,  },
    { 2, 6, 4, 2, 4, 4, 4, 4, 4, 4, 2, 4, 6, 4, 7, 4, 4, 5, 0, 5, 3, 4, 5, 0, 7, 0, 3, 6, 2, 0, 6, 4,  },
    { data_a27b6, data_a27ba, data_a27be, data_a27c4, data_a27d4, data_a27e4, data_a27f2, data_a2800, },
};
const struct defense_strategy data_a280e={ 
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a2852, data_a285c, },
};
const struct defense_strategy data_a2860={ 
    { 0, 1, 1, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_a28a8, data_a28c2, data_a28c6, data_a28ce, },
};
const struct defense_strategy data_a28d4={ 
    { 0, 7, 0, 0, 0, 6, 0, 0, 5, 0, 0, 0, 0, 0, 6, 0, 0, 0, 6, 0, 5, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0,  },
    { 0, 7, 0, 0, 0, 6, 0, 0, 5, 0, 0, 0, 0, 0, 6, 0, 0, 0, 6, 0, 5, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0,  },
    { data_a2924, data_a293e, data_a2942, data_a2946, data_a2950, data_a2956, data_a2974, data_a2992, },
};
const struct defense_strategy data_a29ac={ 
    { 0, 5, 0, 0, 4, 0, 0, 3, 0, 1, 0, 3, 5, 1, 5, 0, 5, 0, 4, 1, 0, 3, 0, 1, 1, 4, 5, 0, 1, 4, 3, 5,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a29f8, data_a2a08, data_a2a0c, data_a2a12, data_a2a20, data_a2a36, },
};
const struct defense_strategy data_a2a4a={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a2a90, },
};
const struct defense_strategy data_a2ad0={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a2b16, },
};
const struct defense_strategy data_a2b56={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a2b9c, data_a2bac, data_a2bba, },
};
const struct defense_strategy data_a2bd8={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 6, 6, 2, 2, 2, 3, 3, 3, 2, 3, 4, 4, 5, 5,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_a2c28, data_a2c2c, data_a2c34, data_a2c46, data_a2c58, data_a2c6a, data_a2c7c, data_a2c8e, },
};
const struct defense_strategy data_a2c98={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3,  },
    { data_a2ce0, data_a2ce4, data_a2cee, data_a2d0e, },
};
const struct defense_strategy *data_a23ce[]={&data_a240e, &data_a24b2, &data_a2556, &data_a25fa, &data_a2662, &data_a26e2, &data_a2766, &data_a280e, &data_a2860, &data_a28d4, &data_a29ac, &data_a2a4a, &data_a2ad0, &data_a2b56, &data_a2bd8, &data_a2c98,  };

/* END Ryu */


