/*
 *  aibyte_sagat.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */



/* AGG0 Sagat */
const u8 data_a000a[] = { AIB_TYPE4, 
	AIB_GO_AGG1, 
};
const u8 data_a0034[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a0044[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9fffa[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	AIB_GO_AGG1, 
};
const u8 data_a003c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a0018[] = { AIB_TYPE4, 
	AIB_GO_AGG1, 
};
const u8 data_a0052[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a0026[] = { AIB_TYPE4, 
	AIB_GO_AGG1, 
};

/* AGG1 Sagat */
const u8 data_a0144[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x45, 0x00, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0594[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a021e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a034e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a02d0[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0294[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a04cc[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a062a[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0234[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0572[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a05d6[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a00ea[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a052a[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a050e[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0088[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_SHORTWALK, 0xc0, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0610[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a01d2[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x45, 0x00, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a02aa[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0462[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a03f4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0486[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a01a4[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_SHORTWALK, 0xc0, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a041e[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0178[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a04e8[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0646[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a03d6[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a05bc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a00d0[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0548[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0116[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_SHORTWALK, 0xc0, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_KICK, 0x00, 0x00, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a066e[] = { AIB_TYPE0, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_ATTACK, 0x52, 0x02, 0x00, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_JUMP, 0x21, 0x10, 0x40, 0x30, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_JUMP, 0x22, 0x12, 0x40, 0x30, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_JUMP, 0x20, 0x10, 0x40, 0x30, 
	AIB_ATTACK, 0x54, 0x02, 0x00, 
	AIB_EXIT4, 0x00, 
};
const u8 data_a0280[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a031c[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0262[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0364[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a038a[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0208[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a02ee[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a01ee[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a015e[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a05f0[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a03a8[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a00b6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x45, 0x00, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a033a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0100[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a018e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x00, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0408[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_JUMP, 0x20, 0x12, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a04b0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a0444[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x80, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x28, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};

/* DEF  Sagat */
const u8 data_aa9ec[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aa9f0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aa9f4[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_aaa02[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aaa10[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aaa84[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aaa88[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aaa8c[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_aaa9a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aaaa8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aab1c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aab20[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aab24[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_aab32[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aab40[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aabae[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aabb2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aabb6[] = { AIB_TYPE2, 
	AIB_SHORTWALK, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_aac16[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aac1a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aac1e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x20, 0x02, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_aac28[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x02, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_aac32[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x02, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_aac8c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aac90[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aac94[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x20, 0x02, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_aac9e[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x02, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_aaca8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_JUMP, 0x20, 0x02, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x40, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_aad08[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_aad0e[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aad88[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x20, 
	AIB_KICK, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_aad92[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aadde[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aadf8[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aae60[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aae64[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aae68[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x10, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_BB2, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aae7c[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x30, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_aae88[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_aae8e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x81, 0x10, 
	AIB_ATTACK, 0x54, 0x04, 0x00, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aaf0e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aaf94[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_aaf98[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_aaf9c[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_aafa2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x60, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ab020[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ab024[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ab028[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ab02e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x98, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x98, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ab0ac[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ab0b0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ab0b4[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ab0ba[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ab138[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_ab140[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ab1b6[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_ATTACK, 0x50, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_a08e2 = {
	{1, 1, 7, 7, 6, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 3, 3, 3, 3, 3, 4, 4, 5, 5, 5, 5, 5, 5, },
	{data_a0572, data_a0594, data_a05bc, data_a05d6, data_a05f0, data_a0610, data_a062a, data_a0646, data_a066e, data_a066e, data_a066e, data_a066e, data_a066e, data_a066e, data_a066e, data_a066e, }
};
const AIAggTable data_a08a2 = {
	{1, 1, 7, 7, 6, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 3, 3, 3, 3, 3, 4, 4, 5, 5, 5, 5, 5, 5, },
	{data_a0462, data_a0486, data_a04b0, data_a04cc, data_a04e8, data_a050e, data_a052a, data_a0548, data_a0572, data_a0572, data_a0572, data_a0572, data_a0572, data_a0572, data_a0572, data_a0572, }
};
const AIAggTable data_a0862 = {
	{0, 0, 0, 0, 0, 0, 1, 1, 5, 5, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, },
	{data_a03a8, data_a03d6, data_a03f4, data_a0408, data_a041e, data_a0444, data_a0462, data_a0462, data_a0462, data_a0462, data_a0462, data_a0462, data_a0462, data_a0462, data_a0462, data_a0462, }
};
const AIAggTable data_a0822 = {
	{0, 0, 0, 0, 0, 0, 1, 1, 5, 5, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, },
	{data_a02ee, data_a031c, data_a033a, data_a034e, data_a0364, data_a038a, data_a03a8, data_a03a8, data_a03a8, data_a03a8, data_a03a8, data_a03a8, data_a03a8, data_a03a8, data_a03a8, data_a03a8, }
};
const AIAggTable data_a07e2 = {
	{0, 0, 0, 0, 0, 0, 1, 1, 5, 5, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, },
	{data_a0234, data_a0262, data_a0280, data_a0294, data_a02aa, data_a02d0, data_a02ee, data_a02ee, data_a02ee, data_a02ee, data_a02ee, data_a02ee, data_a02ee, data_a02ee, data_a02ee, data_a02ee, }
};
const AIAggTable data_a07a2 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 2, 2, 2, 2, 2, 2, 0, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, },
	{data_a01a4, data_a01d2, data_a01ee, data_a0208, data_a021e, data_a0234, data_a0234, data_a0234, data_a0234, data_a0234, data_a0234, data_a0234, data_a0234, data_a0234, data_a0234, data_a0234, }
};
const AIAggTable data_a0762 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 2, 2, 2, 2, 2, 2, 2, 0, 3, 3, 3, 3, 3, 0, 4, 4, 4, 4, },
	{data_a0116, data_a0144, data_a015e, data_a0178, data_a018e, data_a01a4, data_a01a4, data_a01a4, data_a01a4, data_a01a4, data_a01a4, data_a01a4, data_a01a4, data_a01a4, data_a01a4, data_a01a4, }
};
const AIAggTable data_a0722 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 0, 2, 2, 2, 2, 0, 2, 0, 0, 3, 3, 0, 3, 3, 4, 0, 4, 4, 4, },
	{data_a0088, data_a00b6, data_a00d0, data_a00ea, data_a0100, data_a0116, data_a0116, data_a0116, data_a0116, data_a0116, data_a0116, data_a0116, data_a0116, data_a0116, data_a0116, data_a0116, }
};
const u8 *data_a06d2[8]={
	data_a0052,
	data_a0044,
	data_a003c,
	data_a0034,
	data_a0026,
	data_a0018,
	data_a000a,
	data_9fffa,
};
const AIAggTable *data_a0702[8]={
	&data_a08e2,
	&data_a08a2,
	&data_a0862,
	&data_a0822,
	&data_a07e2,
	&data_a07a2,
	&data_a0762,
	&data_a0722,
};
const u8 **data_a06c2[]={
	data_a06d2, 		/* vs Ryu */
	data_a06d2, 		/* vs E.Honda */
	data_a06d2, 		/* vs Blanka */
	data_a06d2, 		/* vs Guile */
	data_a06d2, 		/* vs Ken */
	data_a06d2, 		/* vs Chun-Li */
	data_a06d2, 		/* vs Zangeif */
	data_a06d2, 		/* vs Dhalsim */
};
const AIAggTable **data_a06f2[]={
	data_a0702, 		/* vs Ryu */
	data_a0702, 		/* vs E.Honda */
	data_a0702, 		/* vs Blanka */
	data_a0702, 		/* vs Guile */
	data_a0702, 		/* vs Ken */
	data_a0702, 		/* vs Chun-Li */
	data_a0702, 		/* vs Zangeif */
	data_a0702, 		/* vs Dhalsim */
};
struct dualptr data_9fff6={data_a06c2, data_a06f2};
const struct defense_strategy data_aa99e={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_aa9ec, data_aa9f0, data_aa9f4, data_aaa02, data_aaa10, },
};
const struct defense_strategy data_aaa36={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_aaa84, data_aaa88, data_aaa8c, data_aaa9a, data_aaaa8, },
};
const struct defense_strategy data_aaace={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_aab1c, data_aab20, data_aab24, data_aab32, data_aab40, },
};
const struct defense_strategy data_aab66={ 
    { 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 1, 0, 0, 0, 2, 0, 1, 2, 0, 0, 1, 2, 0, 2, 2, 0, 0, 2,  },
    { 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 1, 0, 0, 0, 2, 0, 1, 2, 0, 0, 1, 2, 0, 2, 2, 0, 0, 2,  },
    { data_aabae, data_aabb2, data_aabb6, },
};
const struct defense_strategy data_aabcc={ 
    { 2, 2, 2, 2, 2, 2, 4, 4, 2, 0, 3, 2, 2, 3, 4, 4, 3, 2, 1, 1, 0, 2, 4, 4, 3, 3, 2, 3, 3, 3, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 4, 4, 2, 0, 3, 2, 2, 3, 4, 4, 3, 2, 1, 1, 0, 2, 4, 4, 3, 3, 2, 3, 3, 3, 4, 4,  },
    { data_aac16, data_aac1a, data_aac1e, data_aac28, data_aac32, },
};
const struct defense_strategy data_aac42={ 
    { 2, 2, 2, 2, 2, 2, 4, 4, 2, 0, 3, 2, 2, 3, 4, 4, 3, 2, 1, 1, 0, 2, 4, 4, 3, 3, 2, 3, 3, 3, 4, 4,  },
    { 2, 2, 2, 2, 2, 2, 4, 4, 2, 0, 3, 2, 2, 3, 4, 4, 3, 2, 1, 1, 0, 2, 4, 4, 3, 3, 2, 3, 3, 3, 4, 4,  },
    { data_aac8c, data_aac90, data_aac94, data_aac9e, data_aaca8, },
};
const struct defense_strategy data_aacb8={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_aad08, data_aad0e, },
};
const struct defense_strategy data_aad44={ 
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { data_aad88, data_aad92, },
};
const struct defense_strategy data_aad96={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { data_aadde, data_aadf8, },
};
const struct defense_strategy data_aae10={ 
    { 2, 5, 2, 5, 2, 5, 2, 5, 5, 2, 5, 2, 5, 2, 5, 2, 2, 5, 2, 5, 2, 5, 2, 5, 5, 2, 5, 2, 2, 2, 5, 5,  },
    { 2, 5, 2, 5, 2, 5, 2, 5, 5, 2, 5, 2, 5, 2, 5, 2, 2, 5, 2, 5, 2, 5, 2, 5, 5, 2, 5, 2, 2, 2, 5, 5,  },
    { data_aae60, data_aae64, data_aae68, data_aae7c, data_aae88, data_aae8e, },
};
const struct defense_strategy data_aaec2={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_aaf0e, },
};
const struct defense_strategy data_aaf46={ 
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_aaf94, data_aaf98, data_aaf9c, data_aafa2, },
};
const struct defense_strategy data_aafd2={ 
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_ab020, data_ab024, data_ab028, data_ab02e, },
};
const struct defense_strategy data_ab05e={ 
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_ab0ac, data_ab0b0, data_ab0b4, data_ab0ba, },
};
const struct defense_strategy data_ab0ea={ 
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { data_ab138, data_ab140, },
};
const struct defense_strategy data_ab168={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_ab1b6, },
};
const struct defense_strategy *data_aa95e[]={&data_aa99e, &data_aaa36, &data_aaace, &data_aab66, &data_aabcc, &data_aac42, &data_aacb8, &data_aad44, &data_aad96, &data_aae10, &data_aaec2, &data_aaf46, &data_aafd2, &data_ab05e, &data_ab0ea, &data_ab168,  };

/* END Sagat */


