/*
 *  aibyte_vega.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */



/* AGG0 Vega */
const u8 data_a16fc[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a1742[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	<PERSON><PERSON><PERSON>STANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x00, 
	<PERSON><PERSON>_STANDSTILL, 0x07, 
	<PERSON>B_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a1764[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a172c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a1758[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a16e8[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_SHORTWALK, 0xc5, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_a1714[] = { AIB_TYPE4, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_STANDSTILL, 0x07, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_a16d2[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_SHORTWALK, 0xc5, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};

/* AGG1 Vega */
const u8 data_a1970[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x22, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x60, 
};
const u8 data_a18a4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x20, 
};
const u8 data_a183c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x22, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x00, 
};
const u8 data_a1b08[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x02, 
};
const u8 data_a1d30[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x90, 0x40, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1d62[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1ae0[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x02, 
};
const u8 data_a1dbe[] = { AIB_TYPE2, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a18d6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x00, 
};
const u8 data_a1cc4[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1e02[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x90, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1a00[] = { AIB_TYPE2, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x02, 
};
const u8 data_a1c4a[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1c38[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1870[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x60, 
};
const u8 data_a1d86[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1eec[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1e46[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a193c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x04, 
};
const u8 data_a1aa8[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x00, 
};
const u8 data_a1e90[] = { AIB_TYPE2, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1770[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x48, 
};
const u8 data_a1b30[] = { AIB_TYPE2, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x80, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x02, 
};
const u8 data_a17a2[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x1c, 
};
const u8 data_a1808[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x04, 
};
const u8 data_a1e34[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1b6e[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xa8, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x22, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x04, 
};
const u8 data_a1cd6[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a17d4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x04, 
};
const u8 data_a1e58[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1908[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x04, 
};
const u8 data_a1c6c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1d0a[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a19a4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x20, 
};
const u8 data_a1c10[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x02, 
};
const u8 data_a19d8[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x02, 
};
const u8 data_a1d4c[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1ed8[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x90, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1bd8[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x50, 
};
const u8 data_a1de2[] = { AIB_TYPE2, 
	AIB_WALLBOUNCE, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1d74[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x52, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1cae[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x82, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1a3e[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0xa8, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x22, 0x10, 0x70, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x70, 
};
const u8 data_a1ba8[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x81, 0x90, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x94, 
};
const u8 data_a1eb4[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1f0a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x54, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1ce8[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x80, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1c92[] = { AIB_TYPE2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x90, 0x40, 0x50, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x10, 0x70, 0x50, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};
const u8 data_a1a78[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x81, 0x80, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_ATTACK, 0x00, 0x00, 0x00, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_94, 
	AIB_EXIT4, 0x9c, 
};
const u8 data_a1e16[] = { AIB_TYPE2, 
	AIB_STANDSTILL, 0x81, 0x90, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x20, 0x10, 0x70, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_EXITRAND, 
};

/* DEF  Vega */
const u8 data_ac6fc[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac700[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac704[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac70a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac710[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ac77c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac780[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac784[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac78a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac790[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ac7fc[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac800[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac804[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac80a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac810[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ac876[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac87a[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac87e[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac888[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac8dc[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac8e6[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac8ea[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_ac8f4[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x00, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_ac8fe[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x00, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_ac952[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac95c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac960[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_ac96a[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x22, 0x00, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_ac974[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x05, 
	AIB_JUMP, 0x21, 0x00, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_ac9ce[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_ac9d2[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_ac9d6[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac9dc[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac9e2[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_ac9e8[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_ac9f0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x22, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ac9fe[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_JUMP, 0x21, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_aca50[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_BA0_DIST_LE, 0x70, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_aca5e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_acaaa[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_acacc[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_acad0[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x00, 0x80, 0x80, 
	AIB_RESTART, 
};
const u8 data_acb32[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_acb36[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_acb3a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_acb40[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_acb46[] = { AIB_TYPE2, 
	AIB_KICK, 0x04, 0x00, 
	AIB_RESTART, 
};
const u8 data_acb4c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_acb5a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x22, 0x04, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_acbd2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_acc5c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_acc60[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_acc64[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_acc6a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_acc70[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0xb0, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_accfc[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_acd00[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_acd04[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_acd0a[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_acd10[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_acd9c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_acda0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_acda4[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_acdaa[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x02, 0x00, 
	AIB_RESTART, 
};
const u8 data_acdb0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ace3c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_WALLBOUNCE, 
	AIB_JUMP, 0x21, 0x00, 0x90, 0x90, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_ace4c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_acec2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x02, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x90, 
	AIB_JUMP, 0x20, 0x00, 0x80, 0x80, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const AIAggTable data_a214e = {
	{0, 0, 6, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5, 5, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, },
	{data_a1e46, data_a1e58, data_a1e90, data_a1eb4, data_a1ed8, data_a1eec, data_a1f0a,  }
};
const AIAggTable data_a210e = {
	{0, 0, 6, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5, 5, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, },
	{data_a1d74, data_a1d86, data_a1dbe, data_a1de2, data_a1e02, data_a1e16, data_a1e34, data_a1e46, data_a1e46, data_a1e46, data_a1e46, data_a1e46, data_a1e46, data_a1e46, data_a1e46, data_a1e46, }
};
const AIAggTable data_a20ce = {
	{0, 0, 0, 0, 0, 0, 5, 5, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 1, 2, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 3, },
	{data_a1cd6, data_a1ce8, data_a1d0a, data_a1d30, data_a1d4c, data_a1d62, data_a1d74, data_a1d74, data_a1d74, data_a1d74, data_a1d74, data_a1d74, data_a1d74, data_a1d74, data_a1d74, data_a1d74, }
};
const AIAggTable data_a208e = {
	{0, 0, 0, 0, 0, 0, 5, 5, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 1, 2, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 3, },
	{data_a1c38, data_a1c4a, data_a1c6c, data_a1c92, data_a1cae, data_a1cc4, data_a1cd6, data_a1cd6, data_a1cd6, data_a1cd6, data_a1cd6, data_a1cd6, data_a1cd6, data_a1cd6, data_a1cd6, data_a1cd6, }
};
const AIAggTable data_a204e = {
	{0, 0, 0, 0, 5, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 3, 3, 3, },
	{data_a1b08, data_a1b30, data_a1b6e, data_a1ba8, data_a1bd8, data_a1c10, data_a1c38, data_a1c38, data_a1c38, data_a1c38, data_a1c38, data_a1c38, data_a1c38, data_a1c38, data_a1c38, data_a1c38, }
};
const AIAggTable data_a200e = {
	{0, 0, 0, 0, 5, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4, 3, 3, 3, },
	{data_a19d8, data_a1a00, data_a1a3e, data_a1a78, data_a1aa8, data_a1ae0, data_a1b08, data_a1b08, data_a1b08, data_a1b08, data_a1b08, data_a1b08, data_a1b08, data_a1b08, data_a1b08, data_a1b08, }
};
const AIAggTable data_a1fce = {
	{0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 1, 1, 1, 1, 1, 5, 4, 4, 4, 4, 2, 2, 2, 2, 3, 3, 3, 3, 5, 5, 5, 5, },
	{data_a18a4, data_a18d6, data_a1908, data_a193c, data_a1970, data_a19a4, data_a19d8, data_a19d8, data_a19d8, data_a19d8, data_a19d8, data_a19d8, data_a19d8, data_a19d8, data_a19d8, data_a19d8, }
};
const AIAggTable data_a1f8e = {
	{0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 1, 1, 1, 1, 1, 5, 4, 4, 4, 4, 2, 2, 2, 2, 3, 3, 3, 3, 5, 5, 5, 5, },
	{data_a1770, data_a17a2, data_a17d4, data_a1808, data_a183c, data_a1870, data_a18a4, data_a18a4, data_a18a4, data_a18a4, data_a18a4, data_a18a4, data_a18a4, data_a18a4, data_a18a4, data_a18a4, }
};
const u8 *data_a1f3e[8]={
	data_a1764,
	data_a1758,
	data_a1742,
	data_a172c,
	data_a1714,
	data_a16fc,
	data_a16e8,
	data_a16d2,
};
const AIAggTable *data_a1f6e[8]={
	&data_a214e,
	&data_a210e,
	&data_a20ce,
	&data_a208e,
	&data_a204e,
	&data_a200e,
	&data_a1fce,
	&data_a1f8e,
};
const u8 **data_a1f2e[]={
	data_a1f3e, 		/* vs Ryu */
	data_a1f3e, 		/* vs E.Honda */
	data_a1f3e, 		/* vs Blanka */
	data_a1f3e, 		/* vs Guile */
	data_a1f3e, 		/* vs Ken */
	data_a1f3e, 		/* vs Chun-Li */
	data_a1f3e, 		/* vs Zangeif */
	data_a1f3e, 		/* vs Dhalsim */
};
const AIAggTable **data_a1f5e[]={
	data_a1f6e, 		/* vs Ryu */
	data_a1f6e, 		/* vs E.Honda */
	data_a1f6e, 		/* vs Blanka */
	data_a1f6e, 		/* vs Guile */
	data_a1f6e, 		/* vs Ken */
	data_a1f6e, 		/* vs Chun-Li */
	data_a1f6e, 		/* vs Zangeif */
	data_a1f6e, 		/* vs Dhalsim */
};
struct dualptr data_a16ce={data_a1f2e, data_a1f5e};
const struct defense_strategy data_ac6ae={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_ac6fc, data_ac700, data_ac704, data_ac70a, data_ac710, },
};
const struct defense_strategy data_ac72e={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_ac77c, data_ac780, data_ac784, data_ac78a, data_ac790, },
};
const struct defense_strategy data_ac7ae={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_ac7fc, data_ac800, data_ac804, data_ac80a, data_ac810, },
};
const struct defense_strategy data_ac82e={ 
    { 1, 0, 1, 1, 0, 1, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { 1, 0, 1, 1, 0, 1, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,  },
    { data_ac876, data_ac87a, data_ac87e, data_ac888, },
};
const struct defense_strategy data_ac892={ 
    { 1, 1, 1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 2, 4, 4, 4, 0,  },
    { 1, 1, 1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 2, 4, 4, 4, 0,  },
    { data_ac8dc, data_ac8e6, data_ac8ea, data_ac8f4, data_ac8fe, },
};
const struct defense_strategy data_ac908={ 
    { 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 2, 4, 4, 4, 4,  },
    { 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 2, 4, 4, 4, 4,  },
    { data_ac952, data_ac95c, data_ac960, data_ac96a, data_ac974, },
};
const struct defense_strategy data_ac97e={ 
    { 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,  },
    { 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,  },
    { data_ac9ce, data_ac9d2, data_ac9d6, data_ac9dc, data_ac9e2, data_ac9e8, data_ac9f0, data_ac9fe, },
};
const struct defense_strategy data_aca0c={ 
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,  },
    { data_aca50, data_aca5e, },
};
const struct defense_strategy data_aca62={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_acaaa, data_acacc, data_acad0, },
};
const struct defense_strategy data_acae2={ 
    { 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,  },
    { 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,  },
    { data_acb32, data_acb36, data_acb3a, data_acb40, data_acb46, data_acb4c, data_acb5a, },
};
const struct defense_strategy data_acb86={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_acbd2, },
};
const struct defense_strategy data_acc0e={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_acc5c, data_acc60, data_acc64, data_acc6a, data_acc70, },
};
const struct defense_strategy data_accae={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_accfc, data_acd00, data_acd04, data_acd0a, data_acd10, },
};
const struct defense_strategy data_acd4e={ 
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,  },
    { data_acd9c, data_acda0, data_acda4, data_acdaa, data_acdb0, },
};
const struct defense_strategy data_acdee={ 
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,  },
    { data_ace3c, data_ace4c, },
};
const struct defense_strategy data_ace74={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_acec2, },
};
const struct defense_strategy *data_ac66e[]={&data_ac6ae, &data_ac72e, &data_ac7ae, &data_ac82e, &data_ac892, &data_ac908, &data_ac97e, &data_aca0c, &data_aca62, &data_acae2, &data_acb86, &data_acc0e, &data_accae, &data_acd4e, &data_acdee, &data_ace74,  };

/* END Vega */
