/*
 *  aibyte_zangeif.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 7/02/11.
 *  Copyright 2011 <PERSON>. All rights reserved.
 *
 */




/* AGG0 Zangeif */
const u8 data_9dcfe[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x41, 0x00, 
	<PERSON><PERSON>_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	<PERSON><PERSON>_RESTART, 
};
const u8 data_9dce4[] = { AIB_TYPE4, 
	AIB_EXIT5_2, 0x00, 
	<PERSON><PERSON>_MAYBE_RESTART, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	<PERSON>B_STANDSTILL, 0x00, 
	<PERSON><PERSON><PERSON><PERSON>ANDSTILL, 0x07, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9dd72[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};
const u8 data_9dd16[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9dd46[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};
const u8 data_9dd86[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x43, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x42, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_GO_AGG1, 
};
const u8 data_9dd2e[] = { AIB_TYPE4, 
	AIB_MAYBE_RESTART, 
	AIB_LONGWALK, 0x40, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_LONGWALK, 0x41, 0x00, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_RESTART, 
};
const u8 data_9dd5c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x44, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_STANDSTILL, 0x07, 
	AIB_MAYBE_RESTART, 
	AIB_RESTART, 
};

/* AGG1 Zangeif */
const u8 data_9e11e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ddea[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e21c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_KICK, 0x82, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e2c6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9dfbe[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x10, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e118[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e27e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e24e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9df2a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e096[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x10, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9ddb8[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e020[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e234[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9de5c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_SET_0216, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9dffe[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e152[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e296[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e100[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x90, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9dfa0[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e16a[] = { AIB_TYPE4, 
	AIB_SHORTWALK, 0xc0, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9de8a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9deda[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e204[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9dfdc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x82, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9df06[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x82, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e040[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x90, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9dd9a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_SET_0216, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e138[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x10, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e266[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9dea8[] = { AIB_TYPE4, 
	AIB_SET_0216, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_ATTACK, 0x02, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_ATTACK, 0x04, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e0ae[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x82, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9de16[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x82, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e07e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e05e[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e1ec[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e1a4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e170[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e2ae[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_KICK, 0x82, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e18a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x10, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e1bc[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9de3a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x05, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e0ca[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x38, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9e1d4[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x52, 0x04, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9df4c[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x20, 
	AIB_SET_0216, 
	AIB_KICK, 0x00, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x30, 
	AIB_KICK, 0x02, 0x00, 
	AIB_STANDSTILL, 0x03, 
	AIB_LONGWALK, 0x00, 0x40, 
	AIB_KICK, 0x04, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};
const u8 data_9e0e6[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x48, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_STANDSTILL, 0x07, 
	AIB_SETBLOCK, 0x01, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x82, 
	AIB_EXITRAND, 
};
const u8 data_9df7a[] = { AIB_TYPE4, 
	AIB_LONGWALK, 0x00, 0x80, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x03, 
	AIB_JUMP, 0x20, 0x14, 0xa0, 0xa0, 
	AIB_STANDSTILL, 0x07, 
	AIB_B94_NODIZZY, 
	AIB_LONGWALK, 0x00, 0x10, 
	AIB_STANDSTILL, 0x82, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_LABEL_94, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_STANDSTILL, 0x00, 
	AIB_EXITRAND, 
};

/* DEF  Zangeif */
const u8 data_a7dc6[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a7dda[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a7dde[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7df8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x02, 0x00, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7e12[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a7e60[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a7e74[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a7e78[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7e92[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x02, 0x00, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7eac[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7efa[] = { AIB_TYPE2, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_RESTART, 
};
const u8 data_a7f0e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a7f12[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7f2c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x30, 
	AIB_STUN, 0x02, 
	AIB_BB2, 
	AIB_JUMP, 0x02, 0x00, 0x00, 0x00, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a7f46[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7f94[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7f98[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a7f9c[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7fa8[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a7fb2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8002[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8006[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a800a[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8016[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_RESTART, 
};
const u8 data_a8020[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x04, 0xa0, 0xa0, 
	AIB_RESTART, 
};
const u8 data_a8074[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8078[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a807c[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x0c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8088[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_RESTART, 
};
const u8 data_a8092[] = { AIB_TYPE2, 
	AIB_SETIMMUNE, 0x07, 
	AIB_JUMP, 0x22, 0x04, 0xa0, 0xa0, 
	AIB_RESTART, 
};
const u8 data_a80ec[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a80f0[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a80f4[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x70, 
	AIB_KICK, 0x04, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8104[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x04, 0x50, 
	AIB_STUN, 0x01, 
	AIB_BB2, 
	AIB_STANDSTILL, 0xc0, 0x70, 
	AIB_ATTACK, 0x40, 0x06, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8114[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x82, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8120[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a812c[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x82, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8136[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8186[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_ATTACK, 0x84, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8192[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8196[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a81e2[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x58, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_BB2, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x50, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a8202[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8206[] = { AIB_TYPE2, 
	AIB_JUMP, 0x21, 0x04, 0xa0, 0xa0, 
	AIB_RESTART, 
};
const u8 data_a8258[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a825c[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8260[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x70, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a827e[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x60, 
	AIB_SETBLOCK, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_STANDSTILL, 0x81, 0x60, 
	AIB_JUMP, 0x22, 0x04, 0xa0, 0xa0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a82f6[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a82fa[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a82fe[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x82, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x18, 
	AIB_STANDSTILL, 0xc0, 0x00, 
	AIB_KICK, 0x82, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a831a[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x40, 
	AIB_STUN, 0x01, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_KICK, 0x84, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_STANDSTILL, 0xc0, 0x10, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a837e[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a8382[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8386[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x00, 0x3c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_BB2, 
	AIB_LONGWALK, 0x00, 0x3c, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a83a0[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x48, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x0a, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x0a, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a841a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a841e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8422[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_RESTART, 
};
const u8 data_a842c[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x78, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x07, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a84a6[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a84aa[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a84ae[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x03, 0x00, 
	AIB_KICK, 0x84, 0x00, 
	AIB_RESTART, 
};
const u8 data_a84b8[] = { AIB_TYPE2, 
	AIB_BB0_NOTWITHIN, 0x00, 0x80, 
	AIB_STUN, 0x02, 
	AIB_LONGWALK, 0x09, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_BB2, 
	AIB_LONGWALK, 0x09, 0x00, 
	AIB_JUMP, 0x20, 0x04, 0xa0, 0xa0, 
	AIB_BA0_DIST_LE, 0x50, 
	AIB_STANDSTILL, 0x01, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_LABEL_A0, 
	AIB_LABEL_B2, 
	AIB_RESTART, 
};
const u8 data_a853a[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a853e[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a8542[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const u8 data_a8548[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a85ba[] = { AIB_TYPE2, 
	AIB_STUN, 0x01, 
	AIB_RESTART, 
};
const u8 data_a85be[] = { AIB_TYPE2, 
	AIB_STUN, 0x02, 
	AIB_RESTART, 
};
const u8 data_a85c2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x40, 0x08, 0x00, 
	AIB_RESTART, 
};
const u8 data_a85c8[] = { AIB_TYPE2, 
	AIB_SETBLOCK, 0x00, 
	AIB_KICK, 0x04, 0x00, 
	AIB_SETBLOCK, 0x01, 
	AIB_RESTART, 
};
const u8 data_a85d2[] = { AIB_TYPE2, 
	AIB_ATTACK, 0x50, 0x00, 0x00, 
	AIB_RESTART, 
};
const AIAggTable data_9e500 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 1, 1, 1, 2, 2, 5, 5, 5, 2, 3, 3, 3, 4, 4, 4, 0, },
	{data_9e24e, data_9e266, data_9e27e, data_9e296, data_9e2ae, data_9e2c6,  }
};
const AIAggTable data_9e4c0 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 1, 1, 1, 2, 2, 5, 5, 5, 2, 3, 3, 3, 4, 4, 4, 0, },
	{data_9e1bc, data_9e1d4, data_9e1ec, data_9e204, data_9e21c, data_9e234, data_9e24e, data_9e24e, data_9e24e, data_9e24e, data_9e24e, data_9e24e, data_9e24e, data_9e24e, data_9e24e, data_9e24e, }
};
const AIAggTable data_9e480 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 3, 3, 3, 3, },
	{data_9e16a, data_9e170, data_9e18a, data_9e1a4, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, data_9e1bc, }
};
const AIAggTable data_9e440 = {
	{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 3, 3, 3, 3, },
	{data_9e118, data_9e11e, data_9e138, data_9e152, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, data_9e16a, }
};
const AIAggTable data_9e400 = {
	{0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 6, 6, 6, 6, 3, 3, 3, 3, 4, 1, 2, 6, 4, 4, 4, 5, 5, 5, 5, 5, },
	{data_9e05e, data_9e07e, data_9e096, data_9e0ae, data_9e0ca, data_9e0e6, data_9e100, data_9e118, data_9e118, data_9e118, data_9e118, data_9e118, data_9e118, data_9e118, data_9e118, data_9e118, }
};
const AIAggTable data_9e3c0 = {
	{0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 6, 6, 6, 6, 3, 3, 3, 3, 4, 1, 2, 6, 4, 4, 4, 5, 5, 5, 5, 5, },
	{data_9df7a, data_9dfa0, data_9dfbe, data_9dfdc, data_9dffe, data_9e020, data_9e040, data_9e05e, data_9e05e, data_9e05e, data_9e05e, data_9e05e, data_9e05e, data_9e05e, data_9e05e, data_9e05e, }
};
const AIAggTable data_9e380 = {
	{2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 1, 1, 1, 1, 4, 4, 3, 3, 3, 3, 1, 1, },
	{data_9de8a, data_9dea8, data_9deda, data_9df06, data_9df2a, data_9df4c, data_9df7a, data_9df7a, data_9df7a, data_9df7a, data_9df7a, data_9df7a, data_9df7a, data_9df7a, data_9df7a, data_9df7a, }
};
const AIAggTable data_9e340 = {
	{2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 1, 1, 1, 1, 4, 4, 3, 3, 3, 3, 1, 1, },
	{data_9dd9a, data_9ddb8, data_9ddea, data_9de16, data_9de3a, data_9de5c, data_9de8a, data_9de8a, data_9de8a, data_9de8a, data_9de8a, data_9de8a, data_9de8a, data_9de8a, data_9de8a, data_9de8a, }
};
const u8 *data_9e2f0[8]={
	data_9dd86,
	data_9dd72,
	data_9dd5c,
	data_9dd46,
	data_9dd2e,
	data_9dd16,
	data_9dcfe,
	data_9dce4,
};
const AIAggTable *data_9e320[8]={
	&data_9e500,
	&data_9e4c0,
	&data_9e480,
	&data_9e440,
	&data_9e400,
	&data_9e3c0,
	&data_9e380,
	&data_9e340,
};
const u8 **data_9e2e0[]={
	data_9e2f0, 		/* vs Ryu */
	data_9e2f0, 		/* vs E.Honda */
	data_9e2f0, 		/* vs Blanka */
	data_9e2f0, 		/* vs Guile */
	data_9e2f0, 		/* vs Ken */
	data_9e2f0, 		/* vs Chun-Li */
	data_9e2f0, 		/* vs Zangeif */
	data_9e2f0, 		/* vs Dhalsim */
};
const AIAggTable **data_9e310[]={
	data_9e320, 		/* vs Ryu */
	data_9e320, 		/* vs E.Honda */
	data_9e320, 		/* vs Blanka */
	data_9e320, 		/* vs Guile */
	data_9e320, 		/* vs Ken */
	data_9e320, 		/* vs Chun-Li */
	data_9e320, 		/* vs Zangeif */
	data_9e320, 		/* vs Dhalsim */
};
struct dualptr data_9dce0={data_9e2e0, data_9e310};
const struct defense_strategy data_a7d7c={ 
    { 2, 2, 2, 2, 2, 2, 4, 3, 2, 1, 2, 2, 2, 2, 2, 2, 1, 4, 2, 1, 3, 2, 3, 0, 2, 2, 4, 2, 2, 1, 0, 4,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a7dc6, data_a7dda, data_a7dde, data_a7df8, data_a7e12, },
};
const struct defense_strategy data_a7e16={ 
    { 2, 2, 2, 2, 2, 2, 4, 3, 2, 1, 2, 2, 2, 2, 2, 2, 1, 4, 2, 1, 3, 2, 3, 0, 2, 2, 4, 2, 2, 1, 0, 4,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a7e60, data_a7e74, data_a7e78, data_a7e92, data_a7eac, },
};
const struct defense_strategy data_a7eb0={ 
    { 2, 2, 2, 2, 2, 2, 4, 3, 2, 1, 2, 2, 2, 2, 2, 2, 1, 4, 2, 1, 3, 2, 3, 0, 2, 2, 4, 2, 2, 1, 0, 4,  },
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  },
    { data_a7efa, data_a7f0e, data_a7f12, data_a7f2c, data_a7f46, },
};
const struct defense_strategy data_a7f4a={ 
    { 2, 4, 1, 3, 0, 4, 1, 0, 4, 1, 3, 2, 3, 1, 4, 1, 1, 2, 4, 0, 1, 2, 3, 2, 3, 3, 2, 1, 4, 0, 2, 0,  },
    { 2, 4, 1, 3, 0, 4, 1, 0, 4, 1, 3, 2, 3, 1, 4, 1, 1, 2, 4, 0, 1, 2, 3, 2, 3, 3, 2, 1, 4, 0, 2, 0,  },
    { data_a7f94, data_a7f98, data_a7f9c, data_a7fa8, data_a7fb2, },
};
const struct defense_strategy data_a7fb8={ 
    { 4, 0, 3, 1, 3, 1, 1, 1, 2, 2, 2, 2, 2, 1, 1, 1, 0, 1, 2, 1, 2, 1, 1, 1, 1, 3, 3, 4, 1, 1, 1, 1,  },
    { 4, 0, 3, 1, 3, 1, 1, 1, 2, 2, 2, 2, 2, 1, 1, 1, 0, 1, 2, 1, 2, 1, 1, 1, 1, 3, 3, 4, 1, 1, 1, 1,  },
    { data_a8002, data_a8006, data_a800a, data_a8016, data_a8020, },
};
const struct defense_strategy data_a802a={ 
    { 2, 0, 3, 2, 3, 1, 1, 4, 2, 2, 2, 2, 2, 1, 1, 2, 0, 2, 2, 1, 2, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1,  },
    { 2, 0, 3, 2, 3, 1, 1, 1, 2, 2, 2, 2, 2, 1, 1, 2, 0, 2, 2, 1, 2, 1, 1, 1, 1, 4, 4, 2, 1, 1, 1, 1,  },
    { data_a8074, data_a8078, data_a807c, data_a8088, data_a8092, },
};
const struct defense_strategy data_a809c={ 
    { 3, 4, 4, 5, 5, 6, 6, 7, 3, 4, 4, 5, 5, 6, 6, 7, 0, 1, 2, 2, 2, 2, 7, 7, 0, 0, 0, 2, 0, 2, 0, 0,  },
    { 3, 4, 4, 5, 5, 6, 6, 7, 3, 4, 4, 5, 5, 6, 6, 7, 0, 1, 2, 2, 2, 2, 7, 7, 0, 0, 0, 2, 0, 2, 0, 0,  },
    { data_a80ec, data_a80f0, data_a80f4, data_a8104, data_a8114, data_a8120, data_a812c, data_a8136, },
};
const struct defense_strategy data_a8140={ 
    { 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 1, 0, 0, 0, 0, 1, 2, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 2, 0, 0,  },
    { 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 1, 0, 0, 0, 0, 1, 2, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 2, 0, 0,  },
    { data_a8186, data_a8192, data_a8196, },
};
const struct defense_strategy data_a819c={ 
    { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1,  },
    { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,  },
    { data_a81e2, data_a8202, data_a8206, },
};
const struct defense_strategy data_a820e={ 
    { 2, 0, 2, 3, 1, 2, 0, 2, 0, 2, 3, 1, 2, 1, 2, 0, 1, 3, 1, 2, 3, 3, 1, 2, 3, 1, 2, 2, 2, 2, 2, 1,  },
    { 2, 0, 2, 3, 1, 2, 0, 2, 0, 2, 3, 1, 2, 1, 2, 0, 1, 3, 1, 2, 3, 3, 1, 2, 3, 1, 2, 2, 2, 2, 2, 1,  },
    { data_a8258, data_a825c, data_a8260, data_a827e, },
};
const struct defense_strategy data_a82ae={ 
    { 2, 2, 3, 2, 3, 2, 2, 3, 3, 2, 2, 3, 0, 2, 2, 0, 3, 1, 2, 1, 3, 0, 3, 3, 2, 3, 3, 0, 2, 3, 0, 1,  },
    { 2, 2, 3, 2, 3, 2, 2, 3, 3, 2, 2, 3, 0, 2, 2, 0, 3, 1, 2, 1, 3, 0, 3, 3, 2, 3, 3, 0, 2, 3, 0, 1,  },
    { data_a82f6, data_a82fa, data_a82fe, data_a831a, },
};
const struct defense_strategy data_a8336={ 
    { 3, 3, 3, 3, 2, 2, 0, 2, 3, 3, 3, 3, 1, 3, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 2, 3, 2, 1,  },
    { 3, 3, 3, 3, 2, 2, 0, 2, 3, 3, 3, 3, 1, 3, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 2, 3, 2, 1,  },
    { data_a837e, data_a8382, data_a8386, data_a83a0, },
};
const struct defense_strategy data_a83d2={ 
    { 3, 3, 3, 3, 3, 1, 3, 0, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 0, 3, 3,  },
    { 3, 3, 3, 3, 3, 1, 3, 0, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 0, 3, 3,  },
    { data_a841a, data_a841e, data_a8422, data_a842c, },
};
const struct defense_strategy data_a845e={ 
    { 3, 3, 3, 3, 3, 1, 3, 0, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 0, 3, 3,  },
    { 3, 3, 3, 3, 3, 1, 3, 0, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 0, 3, 3,  },
    { data_a84a6, data_a84aa, data_a84ae, data_a84b8, },
};
const struct defense_strategy data_a84ea={ 
    { 3, 3, 0, 3, 3, 3, 3, 3, 1, 3, 3, 1, 3, 3, 3, 0, 3, 1, 3, 3, 1, 3, 3, 1, 3, 3, 0, 3, 3, 3, 1, 3,  },
    { 3, 3, 0, 3, 3, 3, 3, 3, 1, 3, 3, 1, 3, 3, 3, 0, 3, 1, 3, 3, 1, 3, 3, 1, 3, 3, 0, 3, 3, 3, 1, 3,  },
    { data_a853a, data_a853e, data_a8542, data_a8548, },
};
const struct defense_strategy data_a8570={ 
    { 2, 3, 0, 1, 4, 4, 2, 3, 1, 2, 3, 0, 1, 2, 3, 2, 4, 1, 2, 4, 2, 1, 4, 1, 3, 4, 1, 2, 3, 0, 1, 0,  },
    { 2, 3, 0, 1, 4, 4, 2, 3, 1, 2, 3, 0, 1, 2, 3, 2, 4, 1, 2, 4, 2, 1, 4, 1, 3, 4, 1, 2, 3, 0, 1, 0,  },
    { data_a85ba, data_a85be, data_a85c2, data_a85c8, data_a85d2, },
};
const struct defense_strategy *data_a7d3c[]={&data_a7d7c, &data_a7e16, &data_a7eb0, &data_a7f4a, &data_a7fb8, &data_a802a, &data_a809c, &data_a8140, &data_a819c, &data_a820e, &data_a82ae, &data_a8336, &data_a83d2, &data_a845e, &data_a84ea, &data_a8570,  };

/* END Zangeif */

