
/*

struct playerparam {
    void *HitBoxList;
    short ThrowX;
    short ThrowY;
} data_38e8[12] = {
{data_91364, 0x1d, 0x35},  // Ryu 
{data_91750, 0x2e, 0x35},
{data_91ba0, 0x29, 0x35},
{data_91f24, 0x22, 0x35},
{data_91364, 0x1d, 0x35},
{data_92260, 0x19, 0x35},
{data_926d0, 0x2d, 0x35},
{data_929a0, 0x21, 0x35},
{data_92c90, 0x00, 0x35},  // zero width? 
{data_92e08, 0x00, 0x35},
{data_92fb0, 0x23, 0x35},
{data_93140, 0x18, 0x35},
};


*/

/* ********************************************************** */

struct smalladjust data_29126[]={
    {2, -2}, {2, -2}, {2, -2}, {1, -1}, {1, -1}, {1, -1}, {1, -1}, 
    /* possibly only goes this far */
    {11, 0}, {0, 0}, {0, 0}, {0, 0}, {0, 0}, 
};


char data_29144[12]={
      4,   4,   4,   4,   3,   3,   3,   2,   1,   1,   1,  -1,  };

char data_29150[18]={
      6,   6,   6,   5,   5,   5,   4,   4,
      3,   3,   2,   1,   1,   1,   1,   1, 
     -1,  -1,  
};

char data_29162[22]={
      8,   8,   8,   7,   7,   7,   6,   5,
      5,   4,   3,   2,   1,   1,   1,   1, 
      1,   1,   1,   1,  -1,  -1,  
};

char *data_2913e[3]={ data_29144, data_29150, data_29162,  };



char data_292d4[32]={
      5,   1,   3,   7,  11,   5,  15,   3,  17,  13,   9,  11,   2,  15,  17,   7, 
      3,  11,   4,  19,  13,   1,   5,   9,   7,   2,  15,   9,   4,  19,  13,   4, 
};

const short data_2aa30[12][4][4] = {        /* jump vectors used in set_jumping() */
									/* todo: need rereading with signed values 
									  and remove cast from comp_set_jump{ */
	
    /* VelX   AclX   VelY   AclY  */
{
    { 0xfc80, 0xfffb, 0x0700, 0x0048,  },   /* Ryu */
    { 0x0400, 0x0005, 0x0740, 0x0048,  },
    { 0x0000, 0x0000, 0x0720, 0x0048,  },
    { 0x0000, 0x0000, 0x0720, 0x0048,  },
},{ 
    { 0xfcc0, 0xfffb, 0x0680, 0x004c,  },   /* E.Honda */
    { 0x0360, 0x0005, 0x06a0, 0x004c,  },
    { 0x0000, 0x0000, 0x06a0, 0x004c,  },
    { 0x0000, 0x0000, 0x06a0, 0x004c,  },
},{ 
    { 0xfc80, 0xfffb, 0x0b00, 0x0090,  },	//Blanka
    { 0x0398, 0x0005, 0x0b40, 0x0090,  },
    { 0x0000, 0x0000, 0x0b20, 0x0090,  },
    { 0x0000, 0x0000, 0x0b20, 0x0090,  },
},{ 
    { 0xfc80, 0xfffb, 0x06c0, 0x0048,  },	//Guile
    { 0x0400, 0x0005, 0x0700, 0x0048,  },
    { 0x0000, 0x0000, 0x06e0, 0x0048,  },
    { 0x0000, 0x0000, 0x06e0, 0x0048,  },
},{ 
    { 0xfc80, 0xfffb, 0x0700, 0x0048,  },	//Ken
    { 0x0400, 0x0005, 0x0740, 0x0048,  },
    { 0x0000, 0x0000, 0x0720, 0x0048,  },
    { 0x0000, 0x0000, 0x0720, 0x0048,  },
},{ 
    { 0xfbe0, 0xfffb, 0x0a00, 0x0070,  },	//ChunLi
    { 0x0440, 0x0005, 0x0a50, 0x0070,  },
    { 0x0000, 0x0000, 0x0a30, 0x0070,  },
    { 0x0000, 0x0000, 0x0a30, 0x0070,  },
},{ 
    { 0xfd60, 0xfffb, 0x0700, 0x0058,  },	//Zangeif
    { 0x02c0, 0x0005, 0x0780, 0x0058,  },
    { 0x0000, 0x0000, 0x0740, 0x0058,  },
    { 0x0000, 0x0000, 0x0740, 0x0058,  },
},{ 
    { 0xfdc0, 0xfffb, 0x0600, 0x0030,  },	//Dhalsim
    { 0x0260, 0x0005, 0x0620, 0x0030,  },
    { 0x0000, 0x0000, 0x0600, 0x0030,  },
    { 0x0000, 0x0000, 0x0600, 0x0030,  },
},{ 
    { 0xfb80, 0xfffb, 0x0900, 0x0060,  },	//MBison
    { 0x0500, 0x0005, 0x0960, 0x0060,  },
    { 0x0000, 0x0000, 0x0930, 0x0060,  },
    { 0x0000, 0x0000, 0x0930, 0x0060,  },
},{ 
    { 0xfd60, 0xfffb, 0x0700, 0x0058,  },
    { 0x02c0, 0x0005, 0x0780, 0x0058,  },
    { 0x0000, 0x0000, 0x0740, 0x0058,  },
    { 0x0000, 0x0000, 0x0740, 0x0058,  },
},{ 
    { 0xfd00, 0xfffb, 0x0700, 0x0050,  },
    { 0x0380, 0x0005, 0x0740, 0x0050,  },
    { 0x0000, 0x0000, 0x0720, 0x0050,  },
    { 0x0000, 0x0000, 0x0720, 0x0050,  },
},{ 
    { 0xfb00, 0xfff0, 0x0c00, 0x00b0,  },
    { 0x0580, 0x0010, 0x0c40, 0x00b0,  },
    { 0x0000, 0x0000, 0x0c20, 0x00b0,  },
    { 0x0000, 0x0000, 0x0c20, 0x00b0,  },
}, };


/* ********************************************************** */





/* damage vs difficulty tables */


const char data_93420[32]={
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x14, 0x14, 0x14, 0x14, 0x14, 0x10, 0x10, 0x10, 0x10, 0x10, 
    0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x08, 0x08, 0x08, 0x08, 0x08, 0x04, 0x04, 0x04, 0x04, 0x04, 0xc0, 
     };
 /* next address 00093440 */

const char data_99324[16][32] = {
    {   4,   5,   5,   5,   5,   6,   6,   6,   6,   6,   6,   7,   7,   7,   7,   7,   7,   8,   8,   8,   8,   8,   8,   9,   9,   9,   9,   9,  10,  10,  10,  12,  },
    {   5,   6,   6,   6,   6,   7,   7,   7,   7,   7,   7,   8,   8,   8,   8,   8,   8,   9,   9,   9,   9,   9,   9,  10,  10,  10,  11,  11,  11,  12,  12,  14,  },
    {   5,   6,   6,   7,   7,   7,   8,   8,   8,   8,   9,   9,   9,   9,  10,  10,  10,  11,  11,  11,  12,  12,  12,  13,  13,  13,  14,  14,  15,  15,  16,  18,  },
    {   7,   8,   8,   9,   9,   9,  10,  10,  10,  11,  11,  11,  11,  12,  12,  12,  12,  13,  13,  13,  14,  14,  14,  15,  15,  16,  16,  17,  17,  18,  18,  20,  },
    {   8,   9,  10,  10,  11,  11,  12,  12,  12,  13,  13,  13,  13,  14,  14,  14,  14,  15,  15,  15,  16,  16,  16,  17,  17,  18,  18,  19,  19,  20,  20,  23,  },
    {  10,  11,  12,  12,  13,  13,  14,  14,  14,  15,  15,  15,  15,  16,  16,  16,  16,  17,  17,  17,  18,  18,  18,  19,  19,  20,  20,  21,  21,  22,  22,  24,  },
    {  10,  13,  14,  14,  15,  15,  16,  16,  16,  17,  17,  17,  17,  18,  18,  18,  18,  19,  19,  19,  20,  20,  20,  21,  21,  22,  22,  23,  23,  24,  24,  26,  },
    {  12,  15,  16,  16,  17,  17,  18,  18,  18,  19,  19,  19,  19,  20,  20,  20,  20,  21,  21,  21,  22,  22,  22,  23,  23,  24,  24,  25,  25,  26,  26,  28,  },
    {  12,  13,  14,  15,  16,  16,  17,  17,  18,  18,  19,  19,  20,  20,  22,  22,  22,  23,  23,  24,  24,  25,  25,  26,  26,  27,  28,  29,  30,  31,  32,  34,  },
    {  15,  16,  17,  18,  19,  19,  20,  20,  21,  21,  22,  22,  23,  23,  24,  24,  24,  25,  25,  26,  26,  27,  27,  28,  28,  29,  30,  31,  32,  33,  34,  36,  },
    {  15,  16,  17,  18,  19,  19,  20,  20,  21,  21,  22,  22,  23,  23,  24,  24,  24,  25,  25,  26,  26,  27,  27,  28,  28,  29,  30,  31,  32,  33,  34,  36,  },
    {  17,  18,  19,  20,  21,  21,  22,  22,  23,  23,  24,  24,  25,  25,  26,  26,  26,  27,  27,  28,  28,  29,  29,  30,  30,  31,  32,  33,  34,  35,  36,  38,  },
    {  18,  19,  20,  21,  22,  23,  23,  24,  24,  25,  25,  26,  26,  27,  27,  28,  28,  29,  29,  30,  30,  31,  31,  32,  32,  33,  34,  35,  36,  37,  38,  40,  },
    {  22,  23,  24,  25,  26,  27,  27,  28,  28,  29,  29,  30,  30,  31,  31,  32,  32,  33,  33,  34,  34,  35,  35,  36,  36,  37,  38,  39,  40,  41,  42,  44,  },
    {  26,  27,  28,  29,  30,  31,  31,  32,  32,  33,  33,  34,  34,  35,  35,  36,  36,  37,  37,  38,  38,  39,  39,  40,  40,  41,  42,  43,  44,  45,  46,  48,  },
    {  30,  31,  32,  33,  34,  35,  35,  36,  36,  37,  37,  38,  38,  39,  39,  40,  40,  41,  41,  42,  42,  43,  43,  44,  44,  45,  46,  47,  48,  49,  50,  52,  },
 };
 /* next address 00099524 */

u16 data_99544[16]={		/* Scores */
    0x2002, 0x2002, 0x2002, 0x2004, 0x2004, 0x201c, 0x201e, 0x201e, 
    0x2006, 0x2006, 0x2006, 0x2006, 0x2006, 0x2008, 0x2008, 0x2020, 
     };
 /* next address 00099564 */

char data_99566[4][8] = {
    { 0x0001, 0x0014, 0x000a, 0x003c, 0x001e, 0x0000, 0x0000, 0x0000,  },
    { 0x0001, 0x001a, 0x000d, 0x0050, 0x0028, 0x0000, 0x0000, 0x0000,  },
    { 0x0001, 0x001e, 0x000f, 0x0064, 0x0032, 0x0000, 0x0000, 0x0000,  },
    { 0x0002, 0x0028, 0x001e, 0x0064, 0x004b, 0x0000, 0x0000, 0x0000,  },
 };
 /* next address 000995a6 */

u16 data_995a6[4]={0x200a, 0x200a, 0x2022, 0x2022,  };
 /* next address 000995ae */


/* HitBoxes */

signed char data_91370[164]={0, 0, 0, 0, 252, 79, 12, 8, 247, 79, 12, 8, 7, 79, 12, 8, 255, 97, 12, 8, 228, 81, 12, 8, 223, 42, 12, 8, 27, 37, 12, 8, 60, 47, 12, 8, 43, 91, 12, 8, 7, 96, 12, 8, 255, 96, 14, 9, 4, 79, 14, 9, 254, 86, 14, 9, 242, 51, 12, 9, 246, 58, 12, 9, 5, 50, 12, 9, 234, 84, 12, 9, 248, 61, 12, 9, 18, 86, 15, 9, 50, 86, 15, 9, 31, 67, 10, 10, 15, 78, 10, 10, 32, 77, 10, 10, 42, 76, 10, 10, 47, 77, 10, 10, 4, 80, 10, 10, 1, 75, 14, 9, 3, 72, 14, 9, 3, 72, 14, 9, 14, 57, 14, 9, 8, 81, 14, 9, 30, 53, 12, 9, 235, 77, 14, 9, 251, 79, 14, 9, 206, 69, 13, 9, 3, 98, 19, 11, 11, 95, 13, 11, 6, 93, 13, 10, 8, 82, 12, 11, 226, 67, 20, 26, };
signed char data_91414[116]={0, 0, 0, 0, 6, 52, 20, 21, 6, 68, 20, 21, 250, 69, 20, 21, 250, 59, 20, 21, 10, 66, 20, 21, 28, 56, 26, 20, 26, 67, 22, 19, 250, 33, 22, 12, 250, 40, 22, 12, 243, 54, 20, 22, 255, 40, 20, 15, 20, 59, 21, 19, 36, 59, 21, 19, 4, 56, 21, 21, 13, 50, 20, 23, 26, 50, 20, 23, 33, 47, 20, 23, 16, 52, 20, 23, 10, 47, 21, 21, 14, 46, 19, 18, 22, 43, 19, 15, 9, 51, 21, 20, 18, 34, 20, 16, 252, 51, 20, 23, 3, 51, 20, 23, 216, 45, 20, 19, 3, 71, 19, 18, 6, 71, 17, 18, };
signed char data_91488[104]={0, 0, 0, 0, 6, 14, 20, 16, 4, 32, 19, 18, 28, 58, 19, 18, 12, 93, 22, 24, 246, 64, 24, 17, 7, 47, 24, 17, 6, 41, 20, 13, 250, 11, 22, 12, 243, 16, 20, 17, 255, 14, 20, 15, 10, 26, 22, 25, 246, 24, 21, 25, 234, 11, 34, 12, 254, 57, 32, 14, 10, 17, 20, 19, 13, 17, 20, 19, 3, 20, 20, 19, 9, 16, 21, 17, 13, 16, 21, 17, 19, 16, 21, 17, 3, 18, 23, 18, 229, 14, 39, 15, 3, 49, 19, 8, 4, 49, 15, 10, 3, 56, 19, 9, };
signed char data_914f0[8]={0, 0, 0, 0, 4, 38, 12, 37, };
signed char data_914f8[576]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 223, 85, 19, 8, 2, 1, 40, 0, 0, 0, 1, 0, 221, 75, 25, 9, 1, 1, 40, 0, 0, 0, 1, 0, 206, 77, 34, 7, 5, 1, 41, 0, 1, 1, 2, 0, 216, 71, 26, 13, 6, 1, 41, 0, 1, 1, 2, 0, 213, 71, 31, 13, 11, 1, 42, 0, 2, 2, 3, 0, 212, 88, 36, 20, 11, 1, 42, 0, 2, 2, 3, 0, 215, 42, 29, 11, 1, 1, 40, 0, 0, 0, 1, 0, 212, 42, 36, 11, 5, 1, 41, 0, 1, 1, 2, 0, 219, 68, 28, 20, 9, 1, 42, 0, 2, 2, 3, 0, 222, 85, 21, 30, 5, 1, 42, 0, 2, 2, 3, 0, 227, 74, 21, 16, 1, 1, 40, 2, 0, 2, 3, 0, 226, 74, 31, 16, 6, 1, 41, 2, 1, 2, 3, 0, 226, 74, 42, 21, 11, 1, 42, 2, 2, 2, 3, 0, 204, 21, 33, 21, 2, 1, 43, 0, 0, 0, 1, 0, 240, 83, 33, 14, 1, 1, 43, 0, 0, 0, 1, 0, 234, 83, 40, 14, 5, 1, 44, 0, 1, 1, 2, 0, 203, 57, 20, 21, 6, 1, 44, 0, 1, 1, 2, 0, 222, 82, 20, 16, 9, 1, 45, 0, 2, 2, 3, 0, 206, 91, 32, 19, 9, 1, 45, 0, 2, 2, 3, 0, 196, 68, 31, 17, 11, 1, 45, 0, 2, 2, 3, 0, 189, 89, 33, 13, 4, 2, 45, 0, 2, 2, 2, 0, 200, 9, 33, 12, 1, 1, 43, 0, 0, 0, 1, 0, 198, 9, 40, 12, 5, 1, 44, 0, 1, 1, 2, 0, 198, 9, 43, 12, 9, 1, 45, 1, 2, 2, 3, 0, 238, 70, 25, 20, 11, 1, 45, 2, 2, 2, 3, 0, 222, 65, 46, 16, 11, 1, 45, 2, 2, 2, 3, 0, 246, 75, 34, 19, 2, 1, 43, 2, 0, 2, 3, 0, 240, 75, 39, 19, 6, 1, 44, 2, 1, 2, 3, 0, 232, 61, 49, 12, 5, 1, 44, 2, 1, 1, 2, 0, 226, 55, 56, 18, 9, 1, 45, 2, 2, 2, 3, 0, 226, 45, 28, 22, 14, 1, 42, 3, 2, 2, 2, 20, 231, 88, 24, 35, 5, 2, 42, 3, 2, 2, 2, 20, 221, 67, 34, 16, 6, 1, 45, 3, 2, 2, 3, 32, 35, 67, 34, 16, 6, 1, 45, 3, 2, 2, 3, 32, 243, 56, 26, 15, 1, 1, 43, 2, 0, 0, 1, 0, 202, 77, 38, 7, 9, 1, 42, 0, 2, 2, 3, 0, 221, 67, 34, 16, 6, 1, 45, 3, 2, 2, 3, 32, 35, 67, 34, 16, 6, 1, 45, 3, 2, 2, 3, 32, 221, 67, 34, 16, 6, 1, 45, 3, 2, 3, 5, 32, 35, 67, 34, 16, 6, 1, 45, 3, 2, 3, 5, 32, 226, 45, 28, 22, 14, 1, 42, 3, 2, 2, 3, 20, 231, 88, 24, 35, 5, 2, 42, 3, 2, 2, 2, 20, 226, 45, 28, 22, 14, 1, 42, 3, 2, 3, 3, 20, 231, 88, 24, 35, 5, 2, 42, 3, 2, 2, 2, 20, 227, 74, 21, 16, 2, 1, 40, 2, 0, 2, 3, 0, 226, 74, 31, 16, 5, 1, 41, 2, 2, 2, 3, 0, 226, 74, 42, 21, 9, 1, 42, 2, 2, 2, 3, 0, };
signed char data_91738[24]={0, 0, 0, 0, 0, 40, 16, 39, 0, 57, 16, 33, 0, 28, 16, 29, 0, 24, 16, 25, 0, 69, 16, 21, };

struct hitboxes data_91364={
	(HitBox *)data_91370, 
	(HitBox *)data_91414, 
	(HitBox *)data_91488, 
	(HitBox *)data_914f0, 
	(HitBoxAct *)data_914f8, 
	(HitBox *)data_91738, 
};

signed char data_9175c[144]={0, 0, 0, 0, 246, 72, 15, 13, 235, 50, 15, 11, 245, 94, 15, 13, 13, 51, 15, 14, 255, 51, 15, 14, 226, 66, 15, 13, 12, 49, 15, 13, 41, 66, 15, 13, 39, 90, 15, 13, 43, 90, 15, 13, 9, 118, 15, 13, 250, 106, 15, 13, 26, 81, 14, 12, 42, 81, 14, 12, 243, 68, 14, 12, 240, 50, 14, 12, 26, 64, 14, 13, 50, 61, 14, 13, 242, 76, 14, 13, 8, 82, 14, 13, 22, 87, 14, 13, 221, 81, 14, 13, 45, 54, 15, 13, 34, 57, 15, 13, 201, 52, 15, 13, 241, 94, 15, 13, 20, 89, 15, 13, 255, 58, 15, 13, 234, 92, 15, 13, 234, 68, 15, 13, 249, 96, 15, 13, 18, 109, 15, 13, 205, 60, 15, 13, 227, 70, 32, 18, 219, 62, 46, 18, };
signed char data_917ec[100]={0, 0, 0, 0, 1, 48, 31, 20, 255, 39, 31, 15, 11, 72, 20, 17, 219, 50, 28, 16, 253, 81, 19, 20, 249, 67, 23, 15, 7, 63, 21, 19, 23, 67, 22, 21, 35, 65, 17, 17, 13, 90, 21, 23, 11, 86, 29, 15, 27, 52, 26, 19, 23, 41, 26, 16, 230, 55, 21, 20, 29, 38, 25, 15, 42, 41, 23, 16, 229, 43, 36, 15, 5, 79, 24, 19, 18, 67, 24, 23, 2, 39, 26, 21, 0, 78, 28, 17, 7, 58, 31, 15, 1, 81, 27, 14, 242, 62, 28, 17, };
signed char data_91850[96]={0, 0, 0, 0, 6, 14, 38, 14, 3, 12, 40, 12, 21, 43, 29, 14, 221, 18, 34, 17, 32, 83, 11, 26, 249, 90, 29, 10, 241, 81, 16, 20, 243, 61, 14, 23, 17, 47, 17, 17, 13, 52, 25, 15, 13, 63, 31, 11, 229, 19, 27, 15, 26, 13, 28, 13, 47, 14, 31, 13, 240, 14, 41, 15, 15, 49, 25, 13, 0, 46, 29, 14, 241, 37, 26, 6, 9, 56, 26, 9, 39, 45, 21, 14, 1, 62, 34, 7, 26, 62, 14, 17, 199, 19, 57, 14, };
signed char data_918b0[8]={0, 0, 0, 0, 4, 38, 16, 37, };
signed char data_918b8[720]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 200, 65, 35, 11, 5, 1, 40, 0, 0, 2, 4, 0, 195, 67, 40, 11, 7, 1, 41, 0, 1, 2, 4, 0, 222, 87, 27, 19, 11, 1, 42, 0, 2, 3, 4, 0, 193, 60, 47, 16, 11, 1, 42, 0, 2, 3, 4, 0, 214, 50, 34, 15, 11, 1, 45, 0, 2, 3, 4, 0, 191, 81, 37, 12, 6, 2, 45, 0, 2, 2, 2, 0, 212, 12, 40, 12, 5, 1, 43, 0, 0, 3, 4, 0, 197, 12, 48, 12, 7, 1, 44, 0, 1, 3, 4, 0, 222, 51, 29, 15, 8, 1, 45, 0, 1, 2, 4, 0, 214, 57, 33, 23, 8, 2, 45, 0, 1, 2, 4, 0, 175, 18, 30, 13, 4, 1, 43, 1, 0, 3, 4, 0, 172, 18, 41, 13, 6, 1, 44, 1, 1, 3, 4, 0, 173, 18, 52, 13, 9, 1, 45, 1, 2, 3, 4, 0, 203, 11, 36, 13, 9, 1, 45, 1, 2, 2, 4, 0, 94, 11, 36, 13, 9, 2, 45, 1, 2, 2, 4, 0, 207, 46, 29, 20, 4, 1, 43, 0, 0, 2, 4, 0, 223, 40, 28, 15, 6, 1, 44, 0, 1, 2, 4, 0, 196, 46, 37, 20, 6, 2, 44, 0, 1, 2, 4, 0, 181, 44, 37, 12, 4, 1, 40, 0, 0, 2, 4, 0, 181, 44, 39, 23, 6, 1, 41, 0, 1, 2, 4, 0, 184, 17, 47, 16, 9, 1, 42, 1, 2, 2, 4, 0, 206, 63, 42, 16, 6, 1, 0, 2, 0, 5, 5, 0, 217, 101, 31, 23, 8, 1, 40, 2, 1, 5, 5, 0, 206, 63, 42, 25, 8, 1, 41, 2, 1, 5, 5, 0, 206, 71, 37, 25, 12, 1, 42, 2, 2, 5, 5, 0, 2, 30, 27, 16, 5, 1, 43, 2, 0, 4, 4, 0, 11, 50, 53, 22, 7, 1, 44, 2, 1, 4, 4, 0, 202, 60, 36, 15, 6, 1, 43, 2, 0, 2, 4, 0, 202, 60, 36, 15, 8, 1, 44, 2, 1, 2, 4, 0, 202, 60, 36, 15, 12, 1, 45, 2, 2, 2, 4, 0, 187, 64, 56, 17, 11, 1, 42, 3, 2, 4, 4, 22, 201, 63, 30, 24, 11, 2, 42, 3, 2, 4, 4, 22, 184, 26, 48, 24, 11, 3, 42, 3, 2, 4, 4, 22, 208, 46, 48, 18, 11, 4, 42, 3, 2, 4, 4, 22, 176, 59, 44, 18, 11, 5, 42, 3, 2, 4, 4, 22, 211, 18, 39, 18, 11, 6, 42, 3, 2, 4, 4, 22, 187, 64, 56, 17, 11, 1, 42, 3, 2, 4, 4, 22, 201, 63, 30, 24, 11, 2, 42, 3, 2, 4, 4, 22, 184, 26, 48, 24, 11, 3, 42, 3, 2, 4, 4, 22, 208, 46, 48, 18, 11, 4, 42, 3, 2, 4, 4, 22, 176, 59, 44, 18, 11, 5, 42, 3, 2, 4, 4, 22, 211, 18, 39, 18, 11, 6, 42, 3, 2, 4, 4, 22, 187, 64, 56, 17, 13, 1, 42, 3, 2, 4, 4, 22, 201, 63, 30, 24, 13, 2, 42, 3, 2, 4, 4, 22, 184, 26, 48, 24, 13, 3, 42, 3, 2, 4, 4, 22, 208, 46, 48, 18, 13, 4, 42, 3, 2, 4, 4, 22, 176, 59, 44, 18, 13, 5, 42, 3, 2, 4, 4, 22, 211, 18, 39, 18, 13, 6, 42, 3, 2, 4, 4, 22, 206, 63, 42, 16, 5, 1, 40, 2, 0, 4, 4, 0, 217, 101, 31, 23, 7, 1, 41, 2, 1, 4, 4, 0, 206, 63, 42, 25, 7, 1, 41, 2, 1, 4, 4, 0, 206, 71, 37, 25, 11, 1, 42, 2, 2, 4, 4, 0, 202, 60, 36, 15, 11, 1, 45, 2, 2, 5, 5, 0, 215, 71, 32, 18, 12, 1, 40, 3, 2, 4, 5, 20, 232, 62, 36, 23, 12, 2, 40, 3, 2, 4, 5, 20, 215, 71, 32, 18, 12, 1, 41, 3, 2, 5, 5, 20, 232, 62, 36, 23, 12, 2, 41, 3, 2, 5, 5, 20, 215, 71, 32, 18, 12, 1, 42, 3, 2, 5, 5, 20, 232, 62, 36, 23, 12, 2, 42, 3, 2, 5, 5, 20, };
signed char data_91b88[24]={0, 0, 0, 0, 0, 34, 30, 35, 0, 25, 30, 26, 0, 66, 31, 30, 0, 1, 31, 24, 0, 65, 15, 14, };

signed char *data_91750[6]={data_9175c, data_917ec, data_91850, data_918b0, data_918b8, data_91b88, };

signed char data_91bac[124]={0, 0, 0, 0, 237, 75, 15, 13, 211, 56, 14, 12, 240, 69, 14, 12, 225, 40, 14, 12, 236, 98, 14, 12, 232, 85, 14, 12, 0, 82, 14, 12, 226, 45, 12, 15, 2, 65, 14, 12, 217, 76, 14, 12, 230, 105, 14, 12, 247, 81, 14, 12, 22, 70, 14, 12, 1, 51, 14, 12, 253, 47, 14, 12, 224, 46, 14, 12, 194, 32, 14, 12, 250, 94, 14, 12, 156, 48, 14, 12, 253, 49, 14, 12, 5, 64, 14, 12, 24, 82, 14, 12, 50, 80, 14, 12, 70, 67, 14, 12, 13, 76, 14, 12, 38, 72, 14, 12, 51, 58, 14, 12, 194, 25, 34, 18, 179, 37, 50, 24, 0, 0, 0, 0, };
signed char data_91c28[84]={0, 0, 0, 0, 253, 52, 27, 20, 254, 37, 27, 15, 252, 84, 24, 18, 252, 80, 23, 13, 0, 63, 24, 16, 251, 56, 17, 20, 250, 70, 24, 23, 237, 58, 19, 20, 245, 90, 17, 18, 243, 61, 21, 15, 254, 86, 25, 14, 16, 75, 17, 17, 26, 52, 14, 17, 249, 52, 16, 20, 226, 32, 20, 14, 191, 42, 22, 25, 249, 64, 26, 26, 22, 43, 19, 19, 45, 54, 21, 18, 15, 50, 18, 16, };
signed char data_91c7c[64]={0, 0, 0, 0, 253, 17, 27, 18, 254, 12, 27, 13, 252, 55, 24, 13, 252, 58, 23, 10, 0, 23, 29, 24, 22, 50, 9, 18, 231, 22, 16, 20, 247, 60, 17, 15, 214, 66, 8, 19, 217, 83, 16, 14, 240, 92, 20, 13, 31, 79, 13, 15, 38, 70, 16, 13, 24, 42, 12, 17, 222, 10, 39, 9, };
signed char data_91cbc[12]={0, 0, 0, 0, 0, 45, 28, 44, 249, 64, 26, 26, };
signed char data_91cc8[588]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 216, 73, 37, 15, 9, 1, 42, 0, 2, 2, 4, 0, 205, 67, 51, 24, 9, 2, 42, 0, 2, 2, 4, 0, 190, 67, 51, 13, 7, 1, 41, 0, 1, 1, 1, 0, 190, 67, 38, 13, 5, 1, 40, 0, 0, 0, 1, 0, 196, 78, 43, 10, 8, 1, 42, 0, 2, 2, 4, 0, 199, 95, 30, 19, 8, 1, 42, 0, 2, 2, 4, 0, 196, 88, 43, 16, 6, 1, 41, 0, 1, 1, 1, 0, 200, 73, 40, 12, 4, 1, 40, 0, 0, 0, 1, 0, 180, 12, 51, 12, 9, 1, 42, 1, 2, 2, 4, 0, 181, 60, 28, 16, 4, 1, 40, 0, 0, 0, 1, 0, 180, 82, 39, 8, 6, 1, 41, 0, 1, 1, 2, 0, 216, 71, 27, 15, 5, 1, 43, 0, 0, 0, 1, 0, 217, 54, 31, 15, 7, 1, 44, 0, 1, 1, 2, 0, 217, 67, 40, 20, 7, 2, 44, 0, 1, 1, 2, 0, 187, 79, 42, 10, 8, 1, 45, 0, 2, 2, 4, 0, 213, 90, 37, 19, 8, 1, 45, 0, 2, 2, 4, 0, 185, 69, 44, 20, 8, 1, 45, 0, 2, 2, 4, 0, 213, 90, 37, 19, 8, 1, 45, 0, 2, 2, 4, 0, 207, 44, 32, 26, 8, 1, 42, 2, 2, 3, 4, 0, 206, 62, 36, 15, 5, 1, 40, 2, 0, 1, 4, 0, 206, 62, 42, 24, 7, 1, 41, 2, 1, 2, 4, 0, 204, 14, 39, 12, 4, 1, 43, 0, 0, 0, 1, 0, 192, 14, 49, 12, 6, 1, 44, 0, 1, 1, 2, 0, 180, 17, 49, 12, 4, 1, 40, 0, 0, 0, 1, 0, 179, 39, 48, 12, 6, 1, 41, 0, 1, 1, 2, 0, 163, 50, 49, 12, 8, 1, 42, 0, 2, 2, 4, 0, 190, 79, 45, 16, 9, 1, 42, 2, 2, 3, 4, 0, 197, 51, 35, 20, 5, 1, 43, 2, 0, 1, 4, 0, 197, 45, 43, 16, 7, 1, 44, 2, 1, 2, 4, 0, 207, 46, 27, 16, 4, 1, 43, 2, 0, 1, 1, 0, 200, 49, 34, 20, 6, 1, 44, 2, 1, 2, 2, 0, 189, 49, 46, 20, 9, 1, 45, 2, 2, 3, 4, 0, 198, 45, 31, 16, 8, 1, 45, 2, 2, 3, 4, 0, 197, 75, 22, 21, 6, 1, 41, 0, 1, 1, 1, 0, 175, 41, 39, 15, 6, 1, 41, 0, 1, 1, 1, 0, 182, 15, 53, 15, 6, 2, 41, 0, 1, 1, 1, 0, 251, 33, 48, 33, 12, 1, 134, 3, 2, 2, 5, 26, 251, 39, 57, 40, 13, 1, 134, 3, 2, 2, 5, 26, 251, 48, 70, 49, 14, 1, 134, 3, 2, 2, 5, 26, 250, 66, 31, 31, 11, 1, 42, 3, 2, 2, 5, 20, 250, 66, 31, 31, 11, 1, 42, 3, 2, 2, 5, 20, 250, 66, 31, 31, 11, 1, 42, 3, 2, 2, 5, 20, 206, 62, 36, 15, 4, 1, 40, 2, 0, 1, 1, 0, 206, 62, 42, 24, 6, 1, 41, 2, 1, 2, 2, 0, 251, 33, 48, 33, 14, 2, 40, 3, 2, 2, 5, 26, 251, 39, 57, 40, 14, 2, 41, 3, 2, 2, 5, 26, 251, 48, 70, 49, 15, 2, 42, 3, 2, 2, 5, 26, 180, 12, 51, 12, 8, 1, 45, 1, 2, 2, 4, 0, };
signed char data_91f14[16]={0, 0, 0, 0, 0, 37, 24, 38, 0, 24, 24, 25, 0, 72, 24, 25, };

signed char *data_91ba0[6]={data_91bac, data_91c28, data_91c7c, data_91cbc, data_91cc8, data_91f14, };

signed char data_91f30[132]={0, 0, 0, 0, 241, 80, 13, 10, 255, 85, 11, 10, 25, 65, 13, 10, 236, 84, 13, 10, 215, 83, 13, 10, 213, 71, 13, 10, 226, 55, 13, 10, 0, 50, 13, 10, 19, 55, 13, 10, 28, 84, 13, 10, 34, 81, 13, 10, 244, 49, 13, 10, 11, 80, 13, 10, 51, 76, 13, 10, 57, 66, 13, 10, 249, 62, 13, 10, 18, 47, 13, 10, 9, 57, 13, 10, 41, 52, 13, 10, 224, 78, 13, 10, 11, 80, 13, 10, 221, 75, 13, 10, 3, 105, 13, 10, 32, 81, 15, 13, 214, 87, 13, 10, 249, 57, 13, 10, 228, 90, 13, 10, 13, 18, 13, 10, 23, 21, 13, 10, 231, 23, 13, 10, 233, 49, 13, 10, 214, 69, 27, 35, };
signed char data_91fb4[96]={0, 0, 0, 0, 252, 57, 18, 17, 244, 66, 21, 12, 27, 43, 18, 16, 237, 74, 15, 15, 240, 71, 16, 17, 240, 71, 19, 10, 250, 69, 19, 10, 4, 67, 16, 12, 7, 77, 16, 12, 15, 70, 17, 13, 251, 34, 20, 11, 29, 58, 20, 15, 18, 52, 18, 17, 42, 36, 19, 15, 19, 39, 18, 14, 224, 53, 16, 14, 3, 84, 21, 15, 237, 87, 16, 16, 234, 77, 18, 17, 231, 61, 19, 19, 255, 36, 18, 14, 253, 30, 17, 15, 0, 32, 18, 16, };
signed char data_92014[80]={0, 0, 0, 0, 252, 20, 18, 21, 244, 50, 21, 7, 3, 57, 17, 18, 10, 74, 11, 17, 251, 83, 17, 8, 250, 84, 19, 8, 241, 75, 10, 14, 249, 61, 11, 14, 251, 59, 12, 17, 251, 11, 20, 12, 229, 18, 35, 21, 3, 65, 21, 9, 252, 87, 9, 16, 240, 21, 31, 23, 206, 14, 51, 15, 224, 41, 18, 14, 253, 56, 17, 15, 235, 54, 40, 14, 207, 63, 52, 16, };
signed char data_92064[12]={0, 0, 0, 0, 0, 36, 13, 37, 255, 38, 18, 39, };
signed char data_92070[480]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 201, 72, 25, 9, 2, 1, 40, 0, 0, 0, 1, 0, 198, 74, 38, 5, 1, 1, 40, 0, 0, 0, 1, 0, 216, 72, 31, 9, 6, 1, 41, 0, 1, 1, 2, 0, 196, 74, 30, 6, 5, 1, 41, 0, 1, 1, 2, 0, 199, 73, 30, 12, 11, 1, 42, 0, 2, 2, 3, 0, 155, 78, 31, 7, 9, 1, 42, 0, 2, 2, 3, 0, 208, 40, 38, 12, 2, 1, 43, 0, 0, 0, 1, 0, 213, 87, 29, 28, 6, 1, 44, 0, 1, 1, 2, 0, 196, 46, 26, 17, 6, 1, 44, 0, 1, 1, 2, 0, 196, 76, 40, 10, 5, 1, 44, 0, 1, 1, 2, 0, 214, 76, 34, 16, 11, 1, 45, 0, 2, 2, 3, 0, 189, 74, 56, 20, 11, 1, 45, 0, 2, 2, 3, 0, 193, 89, 42, 13, 9, 1, 45, 0, 2, 2, 3, 0, 205, 42, 25, 10, 1, 1, 40, 0, 0, 0, 1, 0, 204, 37, 25, 10, 5, 1, 41, 0, 1, 1, 2, 0, 208, 71, 27, 12, 9, 1, 42, 0, 2, 2, 3, 0, 214, 90, 20, 21, 9, 1, 42, 0, 2, 2, 3, 0, 207, 13, 44, 13, 1, 1, 43, 0, 0, 0, 1, 0, 185, 13, 58, 13, 5, 1, 44, 0, 1, 1, 2, 0, 204, 13, 53, 13, 9, 1, 45, 1, 2, 2, 3, 0, 183, 13, 67, 13, 9, 1, 45, 1, 2, 2, 3, 0, 206, 62, 22, 18, 2, 1, 40, 2, 0, 2, 3, 0, 198, 53, 22, 18, 6, 1, 41, 2, 1, 2, 3, 0, 196, 48, 31, 23, 11, 1, 42, 2, 2, 2, 3, 0, 213, 65, 21, 31, 2, 1, 43, 2, 0, 2, 3, 0, 207, 65, 31, 31, 6, 1, 44, 2, 1, 2, 3, 0, 189, 67, 52, 13, 11, 1, 45, 2, 2, 2, 3, 0, 222, 87, 25, 14, 1, 1, 40, 2, 0, 0, 1, 0, 218, 84, 36, 14, 5, 1, 41, 2, 1, 1, 2, 0, 218, 81, 42, 20, 9, 1, 42, 2, 2, 2, 3, 0, 228, 64, 25, 17, 1, 1, 43, 2, 0, 0, 1, 0, 211, 51, 40, 15, 5, 1, 44, 2, 1, 1, 2, 0, 189, 67, 52, 13, 9, 1, 45, 2, 2, 2, 3, 0, 210, 41, 42, 34, 13, 1, 45, 3, 2, 3, 5, 28, 185, 53, 43, 48, 13, 1, 45, 3, 2, 3, 5, 28, 210, 41, 42, 34, 13, 1, 45, 3, 2, 3, 5, 28, 185, 53, 43, 48, 13, 1, 45, 3, 2, 3, 5, 28, 210, 41, 42, 34, 13, 1, 45, 3, 2, 3, 5, 28, 185, 53, 43, 48, 13, 1, 45, 3, 2, 3, 5, 28, };
signed char data_92250[0]={};

// Chun-Li
signed char data_9226c[128]={0, 0, 0, 0, 5, 77, 15, 10, 240, 76, 15, 10, 248, 49, 15, 10, 248, 92, 15, 10, 232, 76, 15, 10, 247, 46, 15, 10, 32, 47, 15, 10, 41, 70, 15, 10, 22, 82, 15, 10, 47, 75, 15, 10, 11, 68, 15, 10, 21, 56, 15, 10, 14, 60, 15, 10, 39, 52, 15, 10, 15, 77, 15, 10, 217, 70, 15, 10, 13, 58, 15, 10, 249, 105, 15, 10, 255, 62, 15, 10, 243, 65, 15, 10, 230, 75, 15, 10, 4, 42, 15, 10, 26, 44, 13, 10, 24, 48, 13, 10, 230, 92, 13, 10, 5, 41, 15, 10, 4, 18, 15, 9, 252, 41, 13, 10, 253, 21, 13, 7, 10, 54, 13, 10, 27, 92, 12, 10, };
signed char data_922ec[108]={0, 0, 0, 0, 14, 53, 17, 20, 0, 39, 17, 12, 4, 77, 15, 13, 0, 76, 15, 15, 254, 62, 18, 12, 17, 61, 15, 11, 20, 71, 15, 13, 25, 50, 19, 20, 27, 47, 17, 17, 50, 47, 18, 17, 21, 53, 18, 19, 225, 47, 19, 16, 236, 88, 15, 13, 236, 84, 15, 12, 249, 82, 15, 11, 2, 61, 15, 14, 1, 51, 16, 12, 1, 40, 14, 12, 253, 94, 15, 15, 4, 63, 31, 15, 4, 39, 15, 14, 252, 62, 15, 13, 253, 38, 15, 11, 255, 70, 15, 13, 6, 92, 16, 15, 7, 51, 15, 14, };
signed char data_92358[108]={0, 0, 0, 0, 11, 15, 22, 16, 252, 14, 21, 12, 4, 58, 18, 9, 21, 70, 14, 16, 8, 76, 15, 13, 5, 83, 15, 12, 251, 67, 14, 13, 16, 14, 22, 17, 33, 14, 22, 15, 225, 17, 37, 18, 221, 74, 15, 10, 237, 101, 15, 10, 17, 75, 15, 14, 255, 80, 15, 11, 233, 53, 17, 14, 242, 25, 14, 15, 216, 10, 41, 11, 197, 43, 27, 15, 22, 87, 13, 13, 2, 83, 16, 10, 1, 60, 13, 10, 7, 74, 15, 11, 255, 81, 14, 10, 0, 54, 13, 11, 241, 85, 15, 13, 236, 88, 15, 12, };
signed char data_923c4[8]={0, 0, 0, 0, 7, 35, 14, 35,};
signed char data_923dc[756]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 68, 24, 11, 3, 1, 40, 0, 0, 2, 5, 0, 221, 73, 31, 6, 1, 1, 40, 0, 0, 0, 1, 0, 223, 68, 31, 11, 5, 1, 41, 0, 1, 2, 5, 0, 189, 70, 40, 6, 3, 1, 41, 0, 1, 1, 2, 0, 217, 48, 37, 16, 7, 1, 42, 0, 2, 3, 5, 0, 188, 71, 42, 6, 5, 1, 42, 0, 2, 2, 3, 0, 207, 43, 25, 14, 3, 1, 43, 0, 0, 2, 5, 0, 193, 73, 33, 5, 1, 1, 43, 0, 0, 0, 1, 0, 193, 46, 33, 14, 5, 1, 44, 0, 1, 3, 5, 0, 195, 75, 40, 9, 3, 1, 44, 0, 1, 1, 2, 0, 201, 68, 34, 14, 7, 1, 45, 0, 2, 3, 5, 0, 186, 89, 39, 13, 5, 1, 45, 0, 2, 2, 3, 0, 193, 51, 33, 13, 5, 1, 44, 4, 1, 3, 5, 28, 193, 66, 33, 17, 5, 1, 44, 4, 1, 3, 5, 28, 193, 85, 33, 33, 5, 1, 44, 4, 1, 3, 5, 28, 8, 11, 28, 25, 4, 1, 45, 4, 2, 3, 5, 30, 212, 44, 31, 8, 1, 1, 40, 0, 0, 0, 1, 0, 201, 44, 36, 9, 3, 1, 41, 0, 1, 1, 2, 0, 193, 44, 43, 9, 5, 1, 42, 0, 2, 2, 3, 0, 216, 8, 37, 9, 1, 1, 43, 0, 0, 0, 1, 0, 196, 8, 49, 9, 3, 1, 44, 0, 1, 1, 2, 0, 191, 44, 44, 16, 5, 1, 45, 1, 2, 3, 5, 0, 225, 65, 26, 20, 3, 1, 40, 2, 0, 3, 3, 0, 215, 61, 26, 20, 5, 1, 41, 2, 1, 3, 3, 0, 212, 61, 30, 20, 7, 1, 42, 2, 2, 3, 5, 0, 223, 61, 30, 20, 3, 1, 43, 2, 0, 3, 3, 0, 218, 61, 35, 20, 5, 1, 44, 2, 1, 3, 3, 0, 0, 23, 19, 17, 5, 1, 44, 2, 1, 3, 3, 0, 0, 23, 19, 17, 5, 1, 45, 2, 2, 3, 5, 0, 0, 23, 19, 17, 4, 1, 44, 2, 1, 3, 3, 0, 0, 23, 19, 17, 4, 1, 45, 2, 2, 3, 5, 0, 193, 51, 33, 13, 7, 1, 45, 4, 2, 3, 5, 28, 193, 66, 33, 17, 7, 1, 45, 4, 2, 3, 5, 28, 193, 85, 33, 33, 7, 1, 45, 4, 2, 3, 5, 28, 211, 49, 31, 13, 2, 1, 43, 2, 0, 3, 3, 0, 206, 44, 38, 15, 4, 1, 44, 2, 1, 3, 3, 0, 213, 52, 39, 21, 6, 1, 45, 2, 2, 3, 5, 0, 231, 66, 22, 14, 2, 1, 40, 2, 0, 3, 3, 0, 228, 63, 26, 17, 4, 1, 41, 2, 1, 3, 3, 0, 225, 59, 30, 20, 6, 1, 42, 2, 2, 3, 5, 0, 7, 75, 75, 20, 12, 1, 43, 3, 2, 1, 3, 32, 2, 75, 75, 20, 12, 2, 43, 3, 2, 1, 3, 32, 7, 75, 75, 20, 12, 1, 44, 3, 2, 2, 5, 32, 2, 75, 75, 20, 12, 1, 44, 3, 2, 2, 5, 32, 7, 75, 75, 20, 12, 1, 45, 3, 2, 2, 5, 32, 2, 75, 75, 20, 12, 1, 45, 3, 2, 2, 5, 32, 7, 76, 0, 0, 12, 2, 43, 3, 2, 1, 3, 32, 7, 76, 0, 0, 12, 1, 44, 3, 2, 2, 5, 32, 7, 76, 0, 0, 12, 1, 45, 3, 2, 2, 5, 32, 183, 69, 15, 7, 12, 1, 42, 3, 2, 2, 3, 22, 197, 34, 35, 19, 12, 1, 42, 3, 2, 2, 3, 22, 174, 50, 25, 12, 12, 1, 42, 3, 2, 2, 3, 22, 200, 14, 41, 14, 12, 1, 42, 3, 2, 2, 3, 22, 183, 69, 15, 7, 12, 1, 42, 3, 2, 2, 5, 22, 197, 34, 35, 19, 12, 1, 42, 3, 2, 2, 5, 22, 174, 50, 25, 12, 12, 1, 42, 3, 2, 2, 5, 22, 200, 14, 41, 14, 12, 1, 42, 3, 2, 2, 5, 22, 183, 69, 15, 7, 12, 1, 42, 3, 2, 2, 5, 22, 197, 34, 35, 19, 12, 1, 42, 3, 2, 2, 5, 22, 174, 50, 25, 12, 12, 1, 42, 3, 2, 2, 5, 22, 200, 14, 41, 14, 12, 1, 42, 3, 2, 2, 5, 22, 254, 68, 0, 0, 6, 1, 45, 2, 2, 3, 5, 0,};
signed char data_923cc[16]={0, 0, 0, 0, 0, 34, 18, 35, 0, 24, 18, 25, 0, 70, 18, 19,};

signed char data_926dc[84]={0, 0, 0, 0, 241, 92, 16, 12, 12, 97, 16, 12, 238, 53, 16, 12, 225, 98, 16, 12, 27, 99, 16, 12, 45, 102, 16, 12, 12, 99, 16, 12, 0, 62, 16, 12, 20, 70, 16, 12, 18, 92, 16, 12, 233, 81, 16, 12, 210, 90, 16, 12, 31, 76, 16, 12, 26, 88, 16, 12, 42, 80, 16, 12, 11, 28, 16, 12, 199, 35, 16, 12, 209, 84, 16, 12, 229, 102, 16, 12, 234, 100, 19, 12, };
signed char data_92730[64]={0, 0, 0, 0, 4, 67, 21, 25, 245, 44, 25, 16, 233, 86, 20, 16, 21, 72, 25, 21, 13, 66, 24, 26, 27, 54, 22, 22, 6, 48, 23, 18, 230, 63, 20, 25, 14, 58, 24, 16, 16, 61, 22, 17, 18, 53, 20, 15, 223, 51, 19, 16, 225, 67, 23, 15, 234, 83, 21, 15, 234, 83, 21, 15, };
signed char data_92770[52]={0, 0, 0, 0, 2, 19, 25, 20, 245, 15, 25, 16, 233, 64, 20, 8, 239, 38, 23, 13, 235, 20, 33, 21, 241, 58, 15, 19, 235, 57, 22, 17, 18, 79, 22, 16, 253, 69, 26, 12, 3, 61, 19, 15, 227, 62, 20, 9, 234, 11, 19, 13, };
signed char data_927a4[28]={0, 0, 0, 0, 255, 41, 25, 41, 0, 0, 0, 0, 0, 40, 26, 41, 0, 27, 26, 28, 0, 72, 22, 28, 0, 44, 22, 25, };
signed char data_927c0[0]={};
signed char data_927ac[500]={0, 0, 0, 0, 0, 40, 26, 41, 0, 27, 26, 28, 0, 72, 22, 28, 0, 44, 22, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 211, 95, 33, 16, 4, 1, 40, 0, 0, 1, 3, 0, 198, 73, 40, 12, 4, 1, 40, 0, 0, 1, 3, 0, 197, 74, 40, 12, 3, 1, 41, 0, 0, 1, 3, 0, 194, 74, 45, 12, 5, 1, 41, 0, 1, 2, 3, 0, 194, 74, 51, 12, 7, 1, 42, 0, 2, 2, 3, 0, 221, 71, 24, 17, 4, 1, 43, 0, 0, 1, 3, 0, 205, 48, 43, 28, 6, 1, 44, 0, 1, 2, 3, 0, 188, 79, 47, 10, 5, 1, 44, 0, 1, 2, 3, 0, 196, 16, 42, 14, 3, 1, 43, 0, 0, 1, 3, 0, 202, 83, 49, 14, 7, 1, 45, 0, 2, 2, 3, 0, 199, 62, 40, 16, 7, 1, 45, 0, 2, 2, 3, 0, 195, 93, 48, 24, 7, 2, 45, 0, 2, 2, 3, 0, 212, 52, 37, 12, 3, 1, 40, 0, 0, 1, 3, 0, 199, 52, 50, 12, 5, 1, 42, 0, 2, 2, 3, 0, 214, 11, 41, 12, 3, 1, 43, 0, 0, 1, 3, 0, 206, 11, 50, 12, 5, 1, 44, 0, 1, 2, 3, 0, 199, 11, 59, 12, 7, 1, 45, 1, 2, 2, 3, 0, 195, 78, 33, 16, 4, 1, 40, 2, 0, 2, 3, 0, 194, 78, 43, 16, 6, 1, 41, 2, 1, 2, 3, 0, 229, 98, 27, 19, 11, 1, 41, 0, 1, 2, 3, 0, 186, 78, 49, 16, 8, 1, 42, 2, 2, 2, 3, 0, 229, 98, 27, 19, 11, 1, 42, 0, 2, 2, 3, 0, 213, 59, 39, 19, 4, 1, 43, 2, 0, 2, 3, 0, 204, 59, 50, 19, 6, 1, 44, 2, 1, 2, 3, 0, 197, 59, 59, 19, 8, 1, 45, 2, 2, 2, 3, 0, 190, 59, 42, 19, 7, 1, 42, 2, 2, 1, 3, 0, 250, 62, 57, 26, 7, 1, 42, 2, 2, 1, 3, 0, 214, 51, 29, 20, 3, 1, 43, 2, 0, 1, 3, 0, 211, 56, 37, 26, 5, 1, 44, 2, 1, 1, 3, 0, 199, 56, 44, 21, 7, 1, 45, 2, 2, 1, 3, 0, 211, 79, 34, 14, 11, 1, 42, 3, 2, 3, 5, 20, 23, 79, 29, 14, 11, 2, 42, 3, 2, 3, 5, 20, 207, 79, 42, 14, 11, 3, 42, 3, 2, 3, 5, 20, 31, 79, 38, 14, 11, 4, 42, 3, 2, 3, 5, 20, 201, 79, 51, 14, 11, 5, 42, 3, 2, 3, 5, 20, 37, 79, 43, 14, 11, 6, 42, 3, 2, 3, 5, 20, 200, 74, 37, 14, 3, 1, 40, 2, 0, 2, 3, 0, 191, 74, 46, 14, 5, 1, 41, 2, 0, 1, 3, 0, 205, 52, 44, 12, 7, 1, 41, 0, 1, 2, 3, 0, };

signed char data_929ac[136]={0, 0, 0, 0, 244, 79, 12, 10, 250, 89, 12, 11, 236, 54, 12, 10, 4, 110, 12, 10, 245, 105, 12, 10, 228, 94, 12, 10, 2, 55, 12, 10, 21, 77, 11, 10, 222, 77, 11, 10, 200, 55, 12, 10, 192, 43, 14, 11, 207, 62, 11, 12, 207, 62, 11, 12, 44, 28, 11, 11, 191, 64, 12, 11, 48, 83, 12, 12, 2, 84, 13, 11, 13, 84, 13, 11, 28, 82, 18, 12, 39, 81, 22, 12, 249, 77, 13, 10, 32, 87, 13, 10, 228, 85, 13, 10, 204, 68, 13, 10, 192, 56, 13, 10, 237, 79, 12, 11, 219, 76, 12, 11, 147, 39, 55, 11, 142, 30, 62, 16, 155, 14, 49, 15, 168, 65, 24, 12, 139, 53, 33, 12, 140, 99, 27, 12, };
signed char data_92a34[64]={0, 0, 0, 0, 250, 55, 23, 19, 250, 36, 23, 13, 250, 92, 23, 16, 244, 76, 25, 16, 235, 53, 25, 19, 223, 22, 21, 23, 30, 16, 30, 19, 226, 64, 31, 22, 12, 64, 29, 18, 25, 60, 25, 22, 239, 62, 23, 21, 235, 36, 25, 36, 207, 81, 24, 12, 196, 66, 33, 12, 198, 86, 33, 12, };
signed char data_92a74[48]={0, 0, 0, 0, 250, 17, 23, 19, 250, 13, 23, 15, 250, 68, 23, 10, 244, 98, 25, 10, 235, 23, 25, 25, 253, 22, 15, 23, 223, 16, 34, 19, 26, 64, 25, 22, 245, 46, 27, 11, 180, 75, 52, 16, 250, 83, 23, 25, };
signed char data_92aa4[8]={0, 0, 0, 0, 0, 38, 15, 39, };
signed char data_92aac[456]={0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 226, 86, 22, 12, 1, 1, 40, 0, 0, 0, 1, 0, 212, 80, 35, 6, 1, 1, 40, 0, 0, 0, 1, 0, 183, 60, 41, 11, 0, 1, 40, 0, 0, 0, 1, 0, 209, 50, 17, 15, 3, 1, 41, 0, 1, 1, 2, 0, 201, 56, 16, 15, 3, 1, 41, 0, 1, 1, 2, 0, 0, 40, 26, 14, 2, 165, 41, 0, 1, 1, 2, 0, 214, 59, 24, 17, 5, 1, 42, 0, 2, 1, 2, 0, 202, 56, 35, 17, 5, 2, 42, 0, 2, 1, 2, 0, 0, 40, 41, 14, 4, 160, 42, 0, 2, 1, 2, 0, 182, 83, 40, 14, 1, 1, 43, 0, 0, 0, 1, 0, 184, 81, 42, 13, 0, 1, 43, 0, 0, 0, 1, 0, 129, 86, 35, 13, 3, 1, 44, 0, 1, 1, 2, 0, 0, 93, 35, 13, 2, 142, 44, 0, 1, 1, 2, 0, 206, 57, 37, 20, 5, 1, 45, 0, 2, 1, 2, 0, 0, 93, 36, 17, 4, 156, 45, 0, 2, 1, 2, 0, 192, 48, 33, 12, 1, 1, 41, 0, 0, 1, 2, 0, 188, 48, 41, 12, 3, 1, 41, 0, 1, 1, 2, 0, 0, 15, 30, 12, 0, 147, 40, 0, 0, 0, 1, 0, 0, 15, 48, 12, 2, 139, 41, 0, 1, 0, 1, 0, 0, 24, 56, 25, 5, 130, 42, 0, 2, 2, 2, 0, 225, 15, 45, 13, 0, 1, 43, 0, 0, 1, 1, 0, 225, 15, 45, 13, 0, 1, 44, 0, 1, 1, 1, 0, 225, 15, 45, 13, 5, 1, 45, 1, 2, 2, 3, 0, 203, 13, 45, 13, 1, 1, 43, 0, 0, 2, 3, 0, 203, 13, 45, 13, 3, 1, 44, 0, 1, 2, 3, 0, 137, 45, 27, 13, 1, 1, 40, 0, 0, 1, 1, 0, 137, 45, 27, 13, 3, 1, 41, 0, 1, 1, 1, 0, 137, 45, 27, 13, 6, 1, 42, 2, 2, 2, 3, 0, 208, 64, 29, 19, 3, 1, 42, 0, 2, 4, 5, 0, 208, 64, 29, 19, 3, 1, 42, 0, 2, 4, 5, 0, 0, 54, 33, 19, 1, 143, 40, 0, 0, 1, 1, 0, 0, 54, 33, 19, 3, 143, 41, 0, 1, 1, 1, 0, 131, 98, 36, 23, 6, 1, 42, 2, 2, 2, 3, 0, 226, 32, 32, 20, 3, 1, 45, 0, 2, 4, 5, 0, 226, 32, 32, 20, 3, 1, 45, 0, 2, 4, 5, 0, 144, 40, 26, 14, 2, 1, 41, 0, 1, 1, 2, 0, 146, 40, 26, 14, 4, 1, 42, 0, 2, 1, 2, 0, };
signed char data_92c74[28]={0, 0, 0, 0, 0, 38, 22, 39, 0, 30, 22, 31, 0, 24, 22, 25, 0, 80, 22, 26, 0, 83, 22, 22, 0, 46, 22, 13, };

signed char data_92c9c[36]={0, 0, 0, 0, 6, 79, 18, 10, 2, 48, 18, 10, 247, 91, 15, 11, 41, 81, 14, 13, 16, 61, 15, 12, 31, 76, 17, 11, 220, 69, 17, 10, 49, 63, 17, 11, };
signed char data_92cc0[40]={0, 0, 0, 0, 7, 49, 24, 23, 8, 30, 24, 12, 251, 74, 21, 10, 22, 57, 20, 21, 45, 50, 19, 19, 34, 50, 19, 19, 228, 44, 19, 18, 40, 35, 24, 18, 246, 56, 20, 10, };
signed char data_92ce8[36]={0, 0, 0, 0, 1, 13, 30, 13, 2, 10, 35, 11, 251, 56, 24, 7, 40, 16, 26, 17, 26, 13, 23, 15, 233, 14, 27, 15, 5, 11, 27, 12, 229, 60, 44, 13, };
signed char data_92d0c[12]={0, 0, 0, 0, 0, 38, 19, 39, 246, 56, 20, 10, };
signed char data_92d18[216]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 206, 72, 33, 13, 9, 1, 42, 0, 2, 1, 2, 0, 203, 47, 41, 14, 9, 1, 42, 0, 2, 1, 2, 0, 244, 7, 41, 14, 9, 1, 42, 1, 2, 1, 2, 0, 229, 65, 44, 15, 5, 1, 42, 0, 2, 1, 2, 0, 233, 25, 29, 22, 5, 2, 42, 0, 2, 1, 2, 0, 178, 56, 57, 16, 9, 1, 42, 0, 2, 1, 2, 0, 210, 43, 40, 12, 9, 1, 42, 0, 2, 1, 2, 0, 198, 62, 43, 15, 11, 1, 42, 2, 2, 1, 2, 0, 221, 58, 49, 15, 9, 1, 42, 2, 2, 1, 2, 0, 248, 16, 18, 21, 6, 1, 42, 4, 2, 1, 2, 36, 247, 20, 18, 26, 5, 1, 42, 0, 0, 0, 0, 0, 3, 57, 87, 24, 15, 1, 42, 3, 2, 3, 5, 34, 3, 57, 87, 24, 15, 1, 42, 3, 2, 3, 5, 34, 3, 57, 87, 24, 15, 1, 42, 3, 2, 3, 5, 34, 228, 40, 28, 16, 12, 1, 42, 2, 2, 3, 5, 0, 206, 54, 38, 16, 12, 2, 42, 2, 2, 3, 5, 0, 36, 57, 50, 16, 12, 1, 42, 0, 2, 3, 5, 0, };
signed char data_92df0[24]={0, 0, 0, 0, 0, 35, 23, 36, 0, 22, 23, 23, 0, 70, 23, 20, 0, 56, 31, 13, 43, 35, 23, 36, };

signed char data_92e14[44]={0, 0, 0, 0, 243, 105, 16, 11, 233, 109, 15, 11, 243, 53, 16, 10, 196, 95, 15, 10, 211, 95, 15, 10, 218, 123, 12, 11, 220, 85, 13, 12, 24, 104, 15, 11, 246, 77, 17, 12, 3, 109, 15, 11, };
signed char data_92e40[40]={0, 0, 0, 0, 248, 66, 22, 29, 238, 90, 22, 13, 244, 36, 25, 10, 214, 58, 22, 27, 215, 61, 18, 27, 211, 95, 18, 21, 244, 72, 20, 17, 15, 67, 22, 30, 14, 63, 20, 28, };
signed char data_92e68[36]={0, 0, 0, 0, 251, 18, 26, 21, 238, 69, 22, 9, 244, 12, 37, 14, 236, 19, 31, 19, 215, 63, 18, 13, 251, 45, 25, 8, 9, 22, 24, 22, 7, 17, 25, 17, };
signed char data_92e8c[12]={0, 0, 0, 0, 255, 52, 16, 55, 214, 92, 16, 41, };
signed char data_92e98[264]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 187, 80, 35, 12, 9, 1, 42, 0, 2, 1, 2, 0, 170, 75, 53, 12, 9, 1, 42, 0, 2, 1, 2, 0, 188, 44, 40, 12, 9, 1, 42, 0, 2, 1, 2, 0, 222, 58, 23, 15, 9, 1, 45, 0, 2, 1, 2, 0, 179, 87, 46, 17, 9, 2, 45, 0, 2, 1, 2, 0, 222, 58, 23, 15, 9, 1, 45, 0, 2, 1, 2, 0, 170, 78, 55, 12, 9, 2, 45, 0, 2, 1, 2, 0, 212, 23, 21, 21, 9, 1, 45, 0, 2, 1, 2, 0, 173, 44, 51, 13, 9, 2, 45, 0, 2, 1, 2, 0, 185, 61, 42, 12, 11, 1, 45, 2, 2, 1, 2, 0, 185, 61, 42, 12, 9, 1, 45, 2, 2, 1, 2, 0, 217, 64, 28, 18, 9, 1, 45, 2, 2, 1, 2, 0, 214, 44, 28, 14, 15, 1, 42, 3, 2, 2, 3, 20, 199, 68, 42, 18, 15, 1, 42, 3, 2, 2, 3, 20, 195, 118, 33, 39, 15, 1, 42, 3, 2, 2, 3, 20, 214, 44, 28, 14, 15, 1, 42, 3, 2, 2, 3, 20, 199, 68, 42, 18, 15, 1, 42, 3, 2, 2, 3, 20, 195, 118, 33, 39, 15, 1, 42, 3, 2, 2, 3, 20, 214, 44, 28, 14, 15, 1, 42, 3, 2, 3, 5, 20, 199, 68, 42, 18, 15, 1, 42, 3, 2, 3, 5, 20, 195, 118, 33, 39, 15, 1, 42, 3, 2, 3, 5, 20, };
signed char data_92fa0[16]={0, 0, 0, 0, 0, 42, 23, 43, 0, 82, 23, 23, 0, 22, 23, 23, };

signed char data_92fbc[56]={0, 0, 0, 0, 244, 94, 13, 12, 238, 69, 14, 11, 250, 62, 14, 11, 243, 75, 12, 12, 13, 104, 12, 12, 13, 71, 12, 12, 217, 90, 12, 12, 249, 94, 12, 12, 26, 94, 12, 12, 34, 97, 12, 12, 17, 65, 12, 12, 193, 87, 12, 12, 212, 92, 12, 13, };
signed char data_92ff4[40]={0, 0, 0, 0, 254, 63, 23, 25, 251, 54, 24, 12, 1, 45, 20, 17, 18, 57, 18, 24, 239, 62, 21, 26, 19, 63, 21, 29, 27, 65, 22, 28, 203, 60, 20, 23, 222, 64, 24, 25, };
signed char data_9301c[24]={0, 0, 0, 0, 254, 18, 23, 19, 251, 39, 24, 7, 1, 14, 20, 15, 220, 19, 43, 19, 231, 20, 43, 21, };
signed char data_93034[12]={0, 0, 0, 0, 255, 42, 16, 43, 255, 44, 21, 43, };
signed char data_93040[240]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 207, 80, 39, 15, 2, 1, 40, 0, 0, 1, 2, 0, 188, 80, 48, 15, 5, 1, 41, 0, 1, 1, 2, 0, 182, 80, 41, 15, 8, 1, 42, 0, 2, 1, 2, 0, 191, 75, 26, 35, 8, 1, 42, 0, 2, 1, 2, 0, 216, 59, 30, 16, 5, 1, 41, 0, 1, 1, 2, 0, 193, 82, 39, 16, 2, 1, 40, 0, 0, 1, 2, 0, 218, 46, 34, 16, 7, 1, 42, 2, 2, 1, 2, 0, 199, 46, 47, 16, 5, 1, 41, 0, 1, 1, 2, 0, 191, 84, 26, 26, 8, 1, 42, 0, 2, 1, 2, 0, 182, 80, 41, 15, 9, 1, 42, 0, 2, 1, 2, 0, 178, 70, 44, 31, 9, 1, 42, 0, 2, 1, 2, 0, 155, 65, 62, 14, 6, 1, 42, 0, 2, 1, 2, 0, 181, 53, 42, 13, 6, 1, 42, 0, 2, 1, 2, 0, 215, 69, 37, 29, 13, 1, 42, 3, 2, 4, 4, 20, 197, 58, 52, 13, 13, 1, 42, 3, 2, 4, 4, 20, 197, 58, 52, 13, 16, 1, 42, 3, 2, 4, 4, 20, 218, 46, 34, 16, 6, 1, 42, 2, 2, 1, 2, 0, 218, 46, 34, 16, 7, 1, 42, 2, 2, 1, 2, 0, 218, 46, 34, 16, 6, 1, 42, 2, 2, 1, 2, 0, };
signed char data_93130[16]={0, 0, 0, 0, 0, 43, 21, 44, 0, 52, 21, 21, 0, 28, 21, 29, };

signed char data_93158[44]={0, 0, 0, 0, 250, 97, 14, 11, 249, 51, 15, 10, 251, 104, 12, 10, 15, 82, 14, 10, 5, 61, 12, 9, 29, 87, 13, 10, 21, 50, 13, 10, 211, 71, 13, 10, 213, 56, 12, 10, 176, 67, 14, 10, };
signed char data_93184[36]={0, 0, 0, 0, 5, 62, 18, 26, 249, 33, 20, 12, 0, 85, 15, 12, 20, 55, 15, 21, 35, 48, 17, 20, 218, 47, 18, 16, 221, 37, 17, 16, 183, 46, 16, 15, };
signed char data_931a8[48]={0, 0, 0, 0, 254, 16, 23, 17, 249, 9, 25, 12, 254, 66, 18, 8, 8, 14, 21, 15, 17, 14, 21, 15, 225, 15, 31, 15, 224, 11, 23, 11, 190, 18, 31, 15, 228, 9, 32, 11, 215, 9, 42, 11, 246, 56, 39, 19, };
signed char data_931d8[148]={0, 0, 0, 0, 1, 40, 18, 40, 0, 79, 18, 24, 0, 49, 18, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 193, 71, 36, 12, 6, 1, 42, 0, 2, 1, 1, 0, 192, 54, 44, 12, 6, 1, 42, 0, 2, 1, 1, 0, 156, 54, 44, 12, 6, 1, 40, 0, 0, 1, 1, 0, 204, 10, 44, 12, 8, 1, 45, 1, 2, 4, 5, 0, 204, 10, 44, 12, 8, 1, 45, 1, 2, 4, 5, 0, 218, 68, 44, 12, 12, 1, 45, 2, 2, 4, 5, 0, 218, 41, 34, 18, 11, 1, 45, 2, 2, 4, 5, 0, 158, 67, 34, 13, 11, 1, 42, 3, 2, 5, 5, 20, 253, 30, 19, 26, 14, 1, 42, 3, 2, 4, 5, 38, 255, 40, 73, 18, 14, 1, 42, 3, 2, 4, 5, 38, };
signed char data_9326c[132]={0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 156, 82, 32, 8, 139, 1, 54, 0, 2, 1, 1, 0, 147, 57, 32, 8, 139, 1, 54, 0, 2, 1, 1, 0, 0, 57, 32, 8, 139, 143, 54, 0, 0, 1, 1, 0, 204, 10, 44, 12, 8, 1, 45, 1, 2, 4, 5, 0, 204, 10, 44, 12, 8, 1, 45, 1, 2, 4, 5, 0, 218, 68, 44, 12, 12, 1, 45, 2, 2, 4, 5, 0, 218, 41, 34, 18, 11, 1, 45, 2, 2, 4, 5, 0, 0, 72, 32, 8, 142, 130, 54, 3, 2, 5, 5, 20, 253, 20, 19, 36, 144, 1, 54, 3, 2, 4, 5, 38, 13, 40, 86, 18, 144, 1, 54, 3, 2, 4, 5, 38, };
signed char data_932f0[16]={0, 0, 0, 0, 0, 40, 20, 41, 0, 21, 20, 23, 0, 76, 20, 23, };


signed char *data_91f24[6]={data_91f30, data_91fb4, data_92014, data_92064, data_92070, data_92250, };
signed char *data_92260[6]={data_9226c, data_922ec, data_92358, data_923c4, data_923dc, data_923cc, };
signed char *data_926d0[6]={data_926dc, data_92730, data_92770, data_927a4, data_927c0, data_927ac, };
signed char *data_929a0[6]={data_929ac, data_92a34, data_92a74, data_92aa4, data_92aac, data_92c74, };
signed char *data_92c90[6]={data_92c9c, data_92cc0, data_92ce8, data_92d0c, data_92d18, data_92df0, };
signed char *data_92e08[6]={data_92e14, data_92e40, data_92e68, data_92e8c, data_92e98, data_92fa0, };
signed char *data_92fb0[6]={data_92fbc, data_92ff4, data_9301c, data_93034, data_93040, data_93130, };
signed char *data_93140[6]={data_93158, data_93184, data_931a8, data_931d8, data_9326c, data_932f0, };

struct {	
	struct hitboxes *HitBoxList;
	short ThrowX;
	short ThrowY;
} data_38e8[12]= {
	{&data_91364, 29, 53, },
	{(struct hitboxes *)data_91750, 46, 53, },
	{(struct hitboxes *)data_91ba0, 41, 53, },
	{(struct hitboxes *)data_91f24, 34, 53, },
	{&data_91364, 29, 53, },
	{(struct hitboxes *)data_92260, 25, 53, },
	{(struct hitboxes *)data_926d0, 45, 53, },
	{(struct hitboxes *)data_929a0, 33, 53, },
	{(struct hitboxes *)data_92c90, 0, 53, },
	{(struct hitboxes *)data_92e08, 0, 53, },
	{(struct hitboxes *)data_92fb0, 35, 53, },
	{(struct hitboxes *)data_93140, 24, 53, },
};

signed char data_93440[1][1][15][4] = {		// MORETODO not complete, only have Ryu
	/* Opponent Ryu */
	{	/* player Ryu */
		{   // xoff yoff  flip/prio, subsel     0x20 = flip, 0x01 = Priority
			{ -25,   0,  32,   1,  },
			{ -45,  29,  32,   5,  },
			{ -44,  71,   0,  22,  },
			{  21,  51,   0,  21,  },
			{  56,  33,   0,  20,  },
			{ -15,  13,  32,   5,  },
			{  36,  46,   0,  24,  },
			{  76,  25,   0,  15,  },
			{ 114,  28,   0,  19,  },
			{  -2,  27,   0,  21,  },
			{  -6, -15,   0,  20,  },
			{   1, -22,  32,   8,  },
			{   6,  -5,  32,   6,  },
			{ 127,  44,  32,   9,  },
			{ 127,  32,  32,   9,  },
		}
	}
};

signed char data_991a4[12][32] = {
    {   0,   0,   0,   0,   1,   1,   1,   2,   2,   2,   3,   3,   3,   4,   4,   4,   5,   5,   5,   6,   6,   6,   7,   7,   8,   8,   9,   9,  10,  11,  12,  13,  },
    {   0,   0,   0,   0,   1,   1,   1,   2,   2,   2,   3,   3,   3,   4,   4,   4,   5,   5,   5,   6,   6,   6,   7,   7,   8,   8,   9,   9,  10,  11,  12,  13,  },
    {   0,   0,   0,   0,   1,   1,   1,   2,   2,   2,   3,   3,   3,   4,   4,   4,   5,   5,   5,   6,   6,   6,   7,   7,   8,   8,   9,   9,  10,  11,  12,  13,  },
    {   0,   0,   0,   0,   1,   1,   1,   2,   2,   2,   3,   3,   3,   4,   4,   4,   5,   5,   5,   6,   6,   6,   7,   7,   8,   8,   9,   9,  10,  11,  12,  13,  },
    {   0,   0,   0,   0,   1,   1,   1,   2,   2,   2,   3,   3,   3,   4,   4,   4,   5,   5,   5,   6,   6,   6,   7,   7,   8,   8,   9,   9,  10,  11,  12,  13,  },
    {   0,   0,   0,   0,   1,   1,   1,   2,   2,   2,   3,   3,   3,   4,   4,   4,   5,   5,   5,   6,   6,   6,   7,   7,   8,   8,   9,   9,  10,  11,  12,  13,  },
    {   4,   4,   5,   6,   7,   8,   9,   9,  10,  10,  11,  11,  11,  12,  12,  12,  13,  13,  14,  14,  15,  15,  16,  16,  17,  18,  19,  20,  21,  22,  23,  24,  },
    {   0,   1,   2,   3,   4,   5,   6,   6,   7,   7,   8,   8,   8,   9,   9,   9,  10,  10,  11,  11,  12,  12,  13,  13,  14,  15,  16,  17,  18,  19,  20,  22,  },
    {   2,   3,   4,   5,   6,   7,   8,   8,   9,   9,  10,  10,  10,  11,  11,  11,  12,  12,  13,  13,  14,  14,  15,  15,  16,  17,  18,  19,  20,  21,  22,  24,  },
    {   0,   1,   2,   3,   4,   5,   6,   6,   7,   7,   8,   8,   8,   9,   9,   9,  10,  10,  11,  11,  12,  12,  13,  13,  14,  15,  16,  17,  18,  19,  20,  22,  },
    {   0,   1,   2,   3,   4,   5,   6,   6,   7,   7,   8,   8,   8,   9,   9,   9,  10,  10,  11,  11,  12,  12,  13,  13,  14,  15,  16,  17,  18,  19,  20,  22,  },
    {   0,   0,   0,   0,   1,   1,   1,   2,   2,   2,   3,   3,   3,   4,   4,   4,   5,   5,   5,   6,   6,   6,   7,   7,   8,   8,   9,   9,  10,  11,  12,  13,  },
};

