/*
 *  compdata.h
 *  GLUTBasics
 *
 *  Created by <PERSON> on 4/11/10.
 *  Copyright 2010 <PERSON>. All rights reserved.
 *
 */
/* for comp_diceroll */
const u32 data_2c388[32]={			// chance out of 32 of being hit
	0x00008000, 0x04000000, 0x00008000, 0x01000000, // 1,1,1,1
	0x04000200, 0x10004000, 0x00408000, 0x0000c000, // 2,2,2,2
	0x04040400, 0x02010100, 0x08002080, 0x40100400, // 3,3,3,3
	0x10020408, 0x81002040, 0x10040804, 0x80402040, // 4,4,4,4
	0x41010408, 0x04402084, 0x21040410, 0x44104202, // 5,5,5,6
	0x210210a1, 0x11412450, 0x88245242, 0x8a490a24, // 7,7,9,9
	0x44a49512, 0x22a492c9, 0x4d96aa28, 0xaaaaaaaa, // 11,12,14,16
	0x7e5caa55, 0xa<PERSON><PERSON><PERSON><PERSON>, 0xf56ede6b, 0x77777776, // 18,21,22,24
};		


short data_995ae[12][2] = {
	//       , JumpMoveHeight
	{ 0x0040, 0x0078,  },		//Ryu
	{ 0x0030, 0x0078,  },		//E.Honda
	{ 0x0050, 0x0078,  },		//Blanka
	{ 0x0030, 0x0078,  },		//Guile
	{ 0x0040, 0x0078,  },		//Ken
	{ 0x0050, 0x0078,  },
	{ 0x0030, 0x0078,  },
	{ 0x0050, 0x0068,  },		//Dhalsim
	{ 0x0070, 0x0030,  },		//M.Bison
	{ 0x0070, 0x0030,  },
	{ 0x0030, 0x0060,  },
	{ 0x0070, 0x0030,  },
};




