/* rules.c */

#include "sf2.h"

#include "structs.h"
#include "player.h"

#include "particle.h"
#include "structs.h"

#include "lib.h"
#include "sf2io.h"

#include "rules.h"

#ifdef APPLICATION_TESTS
#include	<stdio.h>
#endif


extern Game g;

extern DR dr;			/* XXX yuck */

static void victory_p1(void);
static void victory_p2(void);
static void game_over_one_ply_remains(void);
static void game_over_for_only_player(void);
static void sub_89d4(short);
static void kill_ply1(void);
static void kill_ply2(void);
static int get_struggle_1(Player *ply);



static const u8 PLYWIDTHS[12]={0x28, 0x38, 0x35, 0x28, 0x28, 0x24, 0x2e, 0x28, 0x28, 0x2c, 0x28, 0x28};
static const u8 PLYWIDTHS_SMALLER[12]=
{0x18, 0x2b, 0x21, 0x1c, 0x18, 0x20, 0x20, 0x1c, 0x20, 0x20, 0x1e, 0x23};


#ifdef APPLICATION_TESTS
int main(void) {
	printf("rules.c 0.01\n");
}
#endif

void set_fixed_difficulty(void) {				//2cb2
	g.Player1.Difficulty = 0x1f;
	g.Player2.Difficulty = 0x1f;
}

static void _KnockPlayerOut(Player *victim) {     /* 34ec player knocked out */
    victim->Energy = victim->EnergyDash = -1;
    LBStartTimeWarp();
    LBThrowClear(victim, victim->Opponent);
    victim->mode0 = PLMODE_KNOCKED_OUT;
    victim->mode1   = 0;
    victim->mode2   = 0;
    victim->mode3   = 0;
    victim->Attacking   = 0;
    victim->ProjHit = FALSE;
    victim->DizzyStun = FALSE;
    victim->ThrownFromDizzy = FALSE;
}


void check_level_sequence(Player *ply) {		// 0x2e94 player %a3
	int i=0;
	
	while (g.LevelScript[i+1] != 0x10) {
		if (g.LevelScript[i+1] == ply->FighterID) {     // don't fight ourselves
			g.LevelScript[i]   = -1;
			g.LevelScript[i+1] = -1;
		}
		else {
		    i += 1;
		}
	}
}
void copy_level_table(short d0) {		// 2ecc 
	g.LevelCursor = 0;
	g.CurrentStage = 0;
	const u16 *data;
	int i;
	
	static const char data_94d60[8][16] = {
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
	};
	/* next address 00094de0 */
	static const u16 data_94de0[8][4][16] = {
		{ 
			{ 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0005, 0x0002, 0x0001, 0x0003, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0006, 0x0005, 0x0004, 0x0003, 0x0002, 0x0001, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0003, 0x0002, 0x0001, 0x0004, 0x0005, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{ 
			{ 0x0000, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0005, 0x0002, 0x0000, 0x0003, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0006, 0x0005, 0x0004, 0x0003, 0x0002, 0x0000, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0003, 0x0002, 0x0000, 0x0004, 0x0005, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{ 
			{ 0x0001, 0x0000, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0005, 0x0000, 0x0001, 0x0003, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0006, 0x0005, 0x0004, 0x0003, 0x0000, 0x0001, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0003, 0x0000, 0x0001, 0x0004, 0x0005, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{ 
			{ 0x0001, 0x0002, 0x0000, 0x0004, 0x0005, 0x0006, 0x0007, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0005, 0x0002, 0x0001, 0x0000, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0006, 0x0005, 0x0004, 0x0000, 0x0002, 0x0001, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0000, 0x0002, 0x0001, 0x0004, 0x0005, 0x0007, 0x0006, 0x0010, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{ 
			{ 0x0001, 0x0002, 0x0003, 0x0000, 0x0005, 0x0006, 0x0007, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0000, 0x0005, 0x0002, 0x0001, 0x0003, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0006, 0x0005, 0x0000, 0x0003, 0x0002, 0x0001, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0003, 0x0002, 0x0001, 0x0000, 0x0005, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{ 
			{ 0x0001, 0x0002, 0x0003, 0x0004, 0x0000, 0x0006, 0x0007, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0000, 0x0002, 0x0001, 0x0003, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0006, 0x0000, 0x0004, 0x0003, 0x0002, 0x0001, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0003, 0x0002, 0x0001, 0x0004, 0x0000, 0x0007, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{ 
			{ 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0000, 0x0007, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0005, 0x0002, 0x0001, 0x0003, 0x0007, 0x0000, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0000, 0x0005, 0x0004, 0x0003, 0x0002, 0x0001, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0003, 0x0002, 0x0001, 0x0004, 0x0005, 0x0007, 0x0000, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{ 
			{ 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0000, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0005, 0x0002, 0x0001, 0x0003, 0x0000, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0000, 0x0006, 0x0005, 0x0004, 0x0003, 0x0002, 0x0001, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0003, 0x0002, 0x0001, 0x0004, 0x0005, 0x0000, 0x0006, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		}, };
	/* next address 000951e0 */

	static const char data_951e0[8][16] = {
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
		{ 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6, 0x0, 0x2, 0x4, 0x6,  },
	};
	/* next address 00095260 */
	static const u16 data_95260[8][4][16] = {
		{		// RYU
			{ 0x0002, 0x0003, 0x0001, 0x0004, 0x0006, 0x0005, 0x0007, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0005, 0x0006, 0x0004, 0x0001, 0x0003, 0x0002, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0001, 0x0007, 0x0003, 0x0006, 0x0002, 0x0005, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0005, 0x0002, 0x0006, 0x0003, 0x0007, 0x0001, 0x0004, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{		// HONDA
			{ 0x0002, 0x0003, 0x0000, 0x0004, 0x0006, 0x0005, 0x0007, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0005, 0x0006, 0x0004, 0x0000, 0x0003, 0x0002, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0000, 0x0007, 0x0003, 0x0006, 0x0002, 0x0005, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0005, 0x0002, 0x0006, 0x0003, 0x0007, 0x0000, 0x0004, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{		// BLANKA
			{ 0x0000, 0x0003, 0x0001, 0x0004, 0x0006, 0x0005, 0x0007, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0005, 0x0006, 0x0004, 0x0001, 0x0003, 0x0000, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0001, 0x0007, 0x0003, 0x0006, 0x0000, 0x0005, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0005, 0x0000, 0x0006, 0x0003, 0x0007, 0x0001, 0x0004, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{		// GUILE
			{ 0x0002, 0x0000, 0x0001, 0x0004, 0x0006, 0x0005, 0x0007, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0005, 0x0006, 0x0004, 0x0001, 0x0000, 0x0002, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0001, 0x0007, 0x0000, 0x0006, 0x0002, 0x0005, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0005, 0x0002, 0x0006, 0x0000, 0x0007, 0x0001, 0x0004, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{		// KEN
			{ 0x0002, 0x0003, 0x0001, 0x0000, 0x0006, 0x0005, 0x0007, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0005, 0x0006, 0x0000, 0x0001, 0x0003, 0x0002, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0000, 0x0001, 0x0007, 0x0003, 0x0006, 0x0002, 0x0005, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0005, 0x0002, 0x0006, 0x0003, 0x0007, 0x0001, 0x0000, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{		// CHUNLI
			{ 0x0002, 0x0003, 0x0001, 0x0004, 0x0006, 0x0000, 0x0007, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0000, 0x0006, 0x0004, 0x0001, 0x0003, 0x0002, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0001, 0x0007, 0x0003, 0x0006, 0x0002, 0x0000, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0000, 0x0002, 0x0006, 0x0003, 0x0007, 0x0001, 0x0004, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{		// ZANGEIF
			{ 0x0002, 0x0003, 0x0001, 0x0004, 0x0000, 0x0005, 0x0007, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0007, 0x0005, 0x0000, 0x0004, 0x0001, 0x0003, 0x0002, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0001, 0x0007, 0x0003, 0x0000, 0x0002, 0x0005, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0005, 0x0002, 0x0000, 0x0003, 0x0007, 0x0001, 0x0004, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		},{		// DHALSIM
			{ 0x0002, 0x0003, 0x0001, 0x0004, 0x0006, 0x0005, 0x0000, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0000, 0x0005, 0x0006, 0x0004, 0x0001, 0x0003, 0x0002, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0004, 0x0001, 0x0000, 0x0003, 0x0006, 0x0002, 0x0005, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
			{ 0x0005, 0x0002, 0x0006, 0x0003, 0x0000, 0x0001, 0x0004, 0x000a, 0x000b, 0x0009, 0x0008, 0x0010, 0x0010, 0x0010, 0x0010, 0x0010,  },
		}, };
	/* next address 00095660 */
	
	
	if (g.NotUsed) {
		d0 = data_94d60[d0][RAND16];
		data = data_94de0[g.PlayersSelectedDash][d0/2];
	} else {
		d0 = data_951e0[d0][RAND16];
		data = data_95260[g.PlayersSelectedDash][d0/2];
	}

	for (i=0; i<16; i++) {
		g.LevelScript[i] = data[i];
    }
#ifdef DEBUG
#if REDHAMMER_DEBUG_GEN >= 4
        redhammer_print_level_table();
#endif
#endif

	if (g.Debug && (g.JPDifficulty & JP_JAPANJUMP)) {
		/* 2f34 todo one day */
	}
}

int ply_opp_has_struggled_free(Player *ply) {			// 3fd8
	ply->Opponent->Damage1 -= get_struggle_1(ply->Opponent);
	if (ply->Opponent->Damage1 > 0) {
		return FALSE;
	}
	return TRUE;
}
/*!
 @abstract Apply grip damage
 @param ply the player to apply damage to (%??)
 @param d2 always zero? should remove (%d2)
 @param subsel (%d3)
 @param xoff X offset of hits (%d4)
 @param yoff Y offset of hits (%d5)
 @param sound sound ID to play (%d6)
 @return TRUE if victim was knocked out
 @discussion sf2ua:0x3466
 */
short ply_opp_apply_grip_damage(Player *ply, 
								short d2, short subsel_d3, 
								short xoff_d4, short yoff_d5, 
								short sound_d6) {		// 3466 
	/* returns true if victim knocked out. */
	Object *obj;
	short d4;
	
	// todo: d2 is always zero when called, remove from args
	ply->Timer2 = 12;
	if ((obj = AllocActor())) {
		INITOBJC(obj, SF2ACT_HITSTUN, subsel_d3, PLAYERX, PLAYERY);
		obj->XPI += xoff_d4;
		obj->YPI += yoff_d5;
		obj->Owner = ply;
		obj->Flip = ply->Opponent->Flip;
	}
	hitsound(sound_d6);
	if (ply->x0150 >= 0) {		// damage set by random_damage_adjust_1
		d2 = ply->x0150;
		ply->x0150 = -1;
	}
	LBGetDamage(ply, ply->Opponent, d2);
	d4 = _EnergyDamageAdjust(ply, dr.damage);		// XXX never read
	QueueEffect(dr.d5, ply->Side);
	if (g.FastEndingFight == 0) {
		ply->Energy     -= dr.damage;
		ply->EnergyDash -= dr.damage;
	}
	if (ply->Energy < 0) {
		_KnockPlayerOut(ply);
		return TRUE;
	} else {
		return FALSE;
	}
}

/*!
 @abstract measure joystick struggle
 @param ply the player (%a4)
 @return a value from 0-4 indicating struggle
 @discussion sf2ua:0x4014
 */
static int get_human_struggle(Player *ply) {		//4014 ply %a4
	short d1 = 0;
	short d3 = 0;
	if (ply->JoyDecodeDash.full & JOY_MOVEMASK) {
		d1 = 1;
	}
	if (d1 && ((~ply->JoyDecodeDash.full) & ply->JoyDecode.full & JOY_MOVEMASK)) {
		d3 = 3;
	}
	if (((~ply->JoyDecodeDash.full) & ply->JoyDecode.full & BUTTON_MASK)) {
		d3 += 1;
	}
	return d3;
}
/*!
 @abstract measure struggle for human/computer
 @param ply the player (%a4)
 @return a value indicating struggle
 @discussion sf2ua:0x4004
 */
static int get_struggle_1(Player *ply) {
	static const u16 data_98e42[32]={
		0x0000, 0x0000, 0x0000, 0x0000, 0x0002, 0x0000, 0x0000, 0x0800, 
		0x0200, 0x0000, 0x0020, 0x0020, 0x0800, 0x2000, 0x0020, 0x0020, 
		0x1000, 0x8004, 0x0040, 0x0801, 0x8004, 0x0010, 0x0800, 0x9002, 
		0x1100, 0x2040, 0x0208, 0x0804, 0x2101, 0x0201, 0x8102, 0x0201, 
	};
	
	if (ply->Human) {
		return get_human_struggle(ply);
	} else {
		if (data_98e42[ply->Difficulty] & (1 << RAND32)) {
			return (char []){4,4,2,4,4,2,7,3,8,4,3,2}[ply->FighterID];
		}
		return 0;
	}
}
/*!
 @abstract measure struggle for human/computer
 @param ply the player (%a4)
 @return a value indicating struggle
 @discussion sf2ua:0x400e
 */
static int get_struggle_2(Player *ply, Player *opp) {
	static const u16 data_98ec2[32]={
		0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x8000, 0x0010, 0x0000, 
		0x0000, 0x0200, 0x2000, 0x0040, 0x0002, 0x0800, 0x8000, 0x0001, 
		0x0003, 0x8000, 0x1004, 0x0100, 0x0080, 0x4010, 0x0820, 0x0408, 
		0x2100, 0x8100, 0x0204, 0x0402, 0x8080, 0x8202, 0x0811, 0x1020, 
	};
	
	if (ply->Human) {
		return get_human_struggle(opp);
	} else {
		if (data_98ec2[opp->Difficulty] & (1 << RAND32)) {
			return (char []){ 4,6,8,4,4,2,6,10,8,4,3,2 }[opp->FighterID];
		}
		return 0;
	}
}


int ply_opp_has_struggled_2(Player *ply) {				// 3fee
	ply->Damage1 -= get_struggle_2(ply, ply);
	if (ply->Damage1 >= 0) {
		return 0;
	} else {
		ply->Damage1 = ply->Damage2;
		return 1;
	}
}

void set_defeated_true(Player *ply) {		// 2eb6
	g.Defeated[ply->FighterID] = TRUE;
}

void set_defeated_false(Player *ply) {		//2eba
	g.Defeated[ply->FighterID] = FALSE;
}

void proc_round_result(void) {		/* 8c80 */
    if(g.OnBonusStage) {
        g.mode2    +=4;
        g.BonusDone++;
    } else {
        if (g.RoundResult < 0) {
            if (g.RoundCnt == 10) {
				/* 8ccc */
				g.x0a50 = TRUE;
				if (g.Player1.Human | g.Player1.x02ae) {
					g.PlyLostToComp = 0;
					g.BattleLoser = g.Player1.FighterID;
				} else {
					g.BattleLoser = g.Player2.FighterID;
					g.PlyLostToComp = 1;
				}
				kill_ply1();
				kill_ply2();
				/* redid 8d6a */
				g.BattleWinner = g.WinningFighter;
				g.BattleLoser = g.LosingFighter;
				g.x09f5 = g.HumanWinner;
				g.x09f6 = g.HumanLoser;
				g.BattleWinSide = g.RoundWinnerSide;
				g.PlyLostToPly = g.RoundLoserSide;
				g.mode2 += 4;
			}
			else if (g.RoundCnt != 9 ) {
				g.Player1.RoundsWon = 1;
				g.Player2.RoundsWon = 1;
			}
			g.mode2 += 2;
        } else {
            if(g.Player1.RoundsWon == 2) { victory_p1(); }
            else if(g.Player2.RoundsWon == 2) { victory_p2(); }
            else { g.mode2 += 2; }
        }
    }
}
int check_special_ability(Player *ply) {       /* 0x32e4 */
    if (    ply->Human 
        && !ply->Airborne 
        && !g.PreRoundAnim 
        && !g.RoundResult 
        && !g.PlayersThrowing 
        &&  ply->mode1 != PLSTAT_IN_POWERMOVE
        &&  ply->mode1 != PLSTAT_REEL
        &&  ply->mode1 != PLSTAT_TUMBLE
        &&  ply->Energy == ply->EnergyDash ) {
        return 0;
    } else {
        return 1;
    }
}

/*!
 @note sf2ua:0x3338
 @param airthrow set to TRUE if airthrow
 @param ply the player performing the throw
 */
int _check_throw(int airthrow, Player *ply) {
    int throwX , throwY;
 	
    Player *opp = ply->Opponent;
    
    if (ply->ThrowDisable)									{ return FALSE; }
    if (ply->Human == FALSE && g.DebugNoCollide != FALSE)	{ return FALSE; }
    if (g.CurrentStage == STAGE_BONUS_DRUMS ||
        ply->BlockStun						||
        ply->Energy < 0						||
        ply->EnergyDash != ply->Energy		||
        opp->BlockStun						||
        opp->Energy < 0						||
        opp->EnergyDash != opp->Energy		||
        opp->exists == FALSE				||
        opp->mode1 == PLSTAT_TUMBLE
        )
        return FALSE;
    
    if (opp->DizzyStun == 0 && 
        opp->mode1 == PLSTAT_REEL)		{ return FALSE; }
    
    
    if (!airthrow && opp->Airborne)		{ return FALSE; }
    
    if (opp->NoThrow_t)					{ return FALSE; }
    if (opp->ActionScript->HB_Head == 0 &&
		opp->ActionScript->HB_Body == 0 &&
		opp->ActionScript->HB_Foot == 0 &&
		opp->ActionScript->HB_Weak == 0)        { return FALSE; }
    if (opp->Invincible)						{ return FALSE; }
    if (opp->TCollDis)							{ return FALSE; }
    
    if (ply->Flip)
        throwX = ply->XPI - ply->Throw[0] - opp->XPI + ply->Throw[2] + opp->ThrowCatchX;
    else
        throwX = ply->XPI + ply->Throw[0] - opp->XPI + ply->Throw[2] + opp->ThrowCatchX;
    
    if (throwX > (2 * (ply->Throw[2] + opp->ThrowCatchX)) || throwX < 0) { return 0; }
    
	throwY = ply->YPI + ply->Throw[1] - (opp->YPI + opp->ThrowCatchY) + ply->Throw[3];
    if (throwY > (2 * ply->Throw[3]) || throwY < 0 )   { return 0; }
    
    opp->DSOffsetX = 0;
    ply->ThrowStat = 1;
    opp->ThrowStat = -1;
    ply->Attacking = FALSE;
    opp->Attacking = FALSE;
    ply->x01b0++;
    opp->ThrownFromDizzy = opp->DizzyStun;
	opp->DizzyStun       = 0;
    g.PlayersThrowing |= (1 << ply->Side);
    
    bumpdifficulty_10(); /* difficulty */
    
    return TRUE;
}

/**
 @note sf2ua: 0x3332
 */
int airthrowvalid(Player *ply) {
    return _check_throw(TRUE, ply);
}
/**
 @note sf2ua: 0x3338
 */
int throwvalid(Player *ply) {
    return _check_throw(FALSE, ply);
}

void set_initial_positions(void) {          /* 0x37da */
    g.Player1.XPI = get_scr2x();
    g.Player1.YPI = get_scr2y();
    g.Player2.XPI = get_scr2x();
    g.Player2.YPI = get_scr2y();
    g.Player1.XPI += (192 - 88);
    g.Player1.YPI +=  40;
    g.Player2.XPI += (192 + 88);
    g.Player2.YPI +=  40;
    g.Player1.Flip   			 = FACING_RIGHT;
    g.Player1.EnemyDirection     = FACING_RIGHT;
    g.Player2.Flip   			 = FACING_LEFT;
    g.Player2.EnemyDirection     = FACING_LEFT;
    
    g.Player1.OldX.part.integer = g.Player1.XPI;
    g.Player1.OldY.part.integer = g.Player1.YPI;
	g.Player1.OldX.part.integer = g.Player2.XPI;
	g.Player1.OldY.part.integer = g.Player2.YPI;
    /* player2 copy never gets made in original, strange, not fixed here yet
	bug still exists in sf2ce */

    if(g.CurrentStage == STAGE_BONUS_CAR || g.CurrentStage == STAGE_BONUS_DRUMS) {
        g.Player1.Size = PLYWIDTHS_SMALLER[g.Player1.FighterID];
        g.Player2.Size = PLYWIDTHS_SMALLER[g.Player2.FighterID];
    } else {
        g.Player1.Size = PLYWIDTHS[g.Player1.FighterID];
        g.Player2.Size = PLYWIDTHS[g.Player2.FighterID];
    }
}
   
void give_one_point(short side) {		//53d6
	Player *ply = side ? PLAYER2 : PLAYER1;
	if (g.PlayersOnline & (1 << ply->Side)) {
		add_bcd_32(0x1, &ply->Score);
		if (ply->Score > 0x9999999) {
			ply->Score = 0x9999999;
		}
	}
}

static void boss_level_check (void) {			//2c1a
	if (g.LevelCursor >= 7) {
		g.UpToBosses = TRUE;
		if (g.LevelCursor == 8) {
			g.OnLevel8 = TRUE;		/* u8 */
		}
	}
}

static void bonus_level_setup(short stage) {	// 2be6
	g.OnBonusStage = TRUE;
	g.CurrentStage = stage;
	boss_level_check();
}

void sub_2b7c(void) {
	static const short SF2_DEMO_STAGES[4] = {			// 2b6e
		STAGE_JAPAN_RYU, 
		STAGE_USA_GUILE,
		STAGE_INDIA_DHALSIM, 
		STAGE_JAPAN_EHONDA
	};

	if (g.InDemo) {
		g.CurrentStage = SF2_DEMO_STAGES[g.DemoStageIndex];       /* 2b60 */
	} else if (g.x0a0f == 0) {
		if (g.OnBonusStage == FALSE) {
			g.LastFightStage = g.CurrentStage;
		} 
		g.OnBonusStage = FALSE;
		if (g.BonusDone == 0 && g.x09f9 == (g.NotUsed ? 2 : 3)) {
			bonus_level_setup(STAGE_BONUS_CAR);
		} else if  (g.BonusDone == 1 && g.x09f9 == (g.NotUsed ? 4 : 6)) {
			bonus_level_setup(STAGE_BONUS_BARRELS);
		} else if (g.BonusDone == 2  && g.NoLoser) {
			bonus_level_setup(STAGE_BONUS_DRUMS);
		} else {
           	while ((g.CurrentStage = g.LevelScript[g.LevelCursor]) < 0) {
                /* 2b76 */
                ++g.LevelCursor;
            }
                
            if (g.LevelScript[g.LevelCursor+1] ==  0x10) { g.OnFinalStage = TRUE; }
            boss_level_check();
		}
	} else {
		bonus_level_setup(g.x0a10);		
	}
}

#pragma mark DIFFICULTY

void sub_4720(void) {
	static const u16 data_4754[32]={
		0x000a, 0x0020, 0x0002, 0x0000, 0x000a, 0x0008, 0x0010, 0x0008, 
		0x0014, 0x0008, 0x0010, 0x000a, 0x0010, 0x000a, 0x0014, 0x0010,
		0x0002, 0x0010, 0x000a, 0x0008, 0x000a, 0x0020, 0x000a, 0x0002, 
		0x0000, 0x000a, 0x0008, 0x0014, 0x0008, 0x000a, 0x0010, 0x000a,  
	};

	short d0;
	if(g.ActiveHumans == BOTH_HUMAN || g.OnBonusStage) { return; }
	d0 = data_4754[(RAND32 & 0x3e) >> 1];
	g.Diff_0a04 -= d0;
	g.Diff_0a04 &= 0xff;
	g.x8a40 = -d0;
	FBUpdateDifficulty();
}

void sub_4794(void) {			// 4794
	if (g.ActiveHumans != BOTH_HUMAN) {
		g.Diff_0a04 += 8;
		g.Diff_0a04 &= 0xff;
	}
}

void FBUpdateDifficulty(void) {				// 4414
	static const u16 data_98f42[8][16][2] = {
		{ 
			{ 0x0000, 0x0018, }, { 0x0000, 0x0030, }, { 0x0000, 0x0050, }, { 0x0000, 0x0088, },
			{ 0x0000, 0x00a8, }, { 0x0000, 0x00a8, }, { 0x0000, 0x00a8, }, { 0x0000, 0x00a8, },
			{ 0x0000, 0x00a8, }, { 0x0000, 0x00a8, }, { 0x0000, 0x00a8, }, { 0x0000, 0x00a8, },
			{ 0x0000, 0x00a8, }, { 0x0000, 0x00a8, }, { 0x0000, 0x00a8, }, { 0x0000, 0x00a8, },
		},{ 
			{ 0x0018, 0x0030, }, { 0x0018, 0x0048, }, { 0x0018, 0x0068, }, { 0x0018, 0x0098, },
			{ 0x0018, 0x00c0, }, { 0x0018, 0x00c0, }, { 0x0018, 0x00c0, }, { 0x0018, 0x00c0, },
			{ 0x0018, 0x00c0, }, { 0x0018, 0x00c0, }, { 0x0018, 0x00c0, }, { 0x0018, 0x00c0, },
			{ 0x0018, 0x00c0, }, { 0x0018, 0x00c0, }, { 0x0018, 0x00c0, }, { 0x0018, 0x00c0, },
		},{ 
			{ 0x0028, 0x0040, }, { 0x0028, 0x0058, }, { 0x0028, 0x0078, }, { 0x0028, 0x00a8, },
			{ 0x0028, 0x00d0, }, { 0x0028, 0x00d0, }, { 0x0028, 0x00d0, }, { 0x0028, 0x00d0, },
			{ 0x0028, 0x00d0, }, { 0x0028, 0x00d0, }, { 0x0028, 0x00d0, }, { 0x0028, 0x00d0, },
			{ 0x0028, 0x00d0, }, { 0x0028, 0x00d0, }, { 0x0028, 0x00d0, }, { 0x0028, 0x00d0, },
		},{ 
			{ 0x0038, 0x0050, }, { 0x0038, 0x0068, }, { 0x0038, 0x0088, }, { 0x0038, 0x00b8, },
			{ 0x0038, 0x00e0, }, { 0x0038, 0x00e0, }, { 0x0038, 0x00e0, }, { 0x0038, 0x00e0, },
			{ 0x0038, 0x00e0, }, { 0x0038, 0x00e0, }, { 0x0038, 0x00e0, }, { 0x0038, 0x00e0, },
			{ 0x0038, 0x00e0, }, { 0x0038, 0x00e0, }, { 0x0038, 0x00e0, }, { 0x0038, 0x00e0, },
		},{
			{ 0x0048, 0x0060, }, { 0x0048, 0x0078, }, { 0x0048, 0x0098, }, { 0x0048, 0x00c8, },
			{ 0x0048, 0x00f0, }, { 0x0048, 0x00f0, }, { 0x0048, 0x00f0, }, { 0x0048, 0x00f0, },
			{ 0x0048, 0x00f0, }, { 0x0048, 0x00f0, }, { 0x0048, 0x00f0, }, { 0x0048, 0x00f0, },
			{ 0x0048, 0x00f0, }, { 0x0048, 0x00f0, }, { 0x0048, 0x00f0, }, { 0x0048, 0x00f0, },
		},{
			{ 0x0058, 0x0070, }, { 0x0058, 0x0088, }, { 0x0058, 0x00a8, }, { 0x0058, 0x00d8, },
			{ 0x0058, 0x00ff, }, { 0x0058, 0x00ff, }, { 0x0058, 0x00ff, }, { 0x0058, 0x00ff, },
			{ 0x0058, 0x00ff, }, { 0x0058, 0x00ff, }, { 0x0058, 0x00ff, }, { 0x0058, 0x00ff, },
			{ 0x0058, 0x00ff, }, { 0x0058, 0x00ff, }, { 0x0058, 0x00ff, }, { 0x0058, 0x00ff, },
		},{ 
			{ 0x0068, 0x0080, }, { 0x0068, 0x0098, }, { 0x0068, 0x00b8, }, { 0x0068, 0x00e8, },
			{ 0x0068, 0x00ff, }, { 0x0068, 0x00ff, }, { 0x0068, 0x00ff, }, { 0x0068, 0x00ff, },
			{ 0x0068, 0x00ff, }, { 0x0068, 0x00ff, }, { 0x0068, 0x00ff, }, { 0x0068, 0x00ff, },
			{ 0x0068, 0x00ff, }, { 0x0068, 0x00ff, }, { 0x0068, 0x00ff, }, { 0x0068, 0x00ff, },
		},{ 
			{ 0x0078, 0x0090, }, { 0x0078, 0x00a8, }, { 0x0078, 0x00c8, }, { 0x0078, 0x00ff, },
			{ 0x0078, 0x00ff, }, { 0x0078, 0x00ff, }, { 0x0078, 0x00ff, }, { 0x0078, 0x00ff, },
			{ 0x0078, 0x00ff, }, { 0x0078, 0x00ff, }, { 0x0078, 0x00ff, }, { 0x0078, 0x00ff, },
			{ 0x0078, 0x00ff, }, { 0x0078, 0x00ff, }, { 0x0078, 0x00ff, }, { 0x0078, 0x00ff, },
		},
	};
	static const char data_4976[256]={
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 
		0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 
		0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 
		0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 
		0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
		0x0a, 0x0a, 0x0a, 0x0a, 0x0a, 0x0a, 0x0a, 0x0a, 0x0b, 0x0b, 0x0b, 0x0b, 0x0b, 0x0b, 0x0b, 0x0b, 
		0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 
		0x0e, 0x0e, 0x0e, 0x0e, 0x0e, 0x0e, 0x0e, 0x0e, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 
		0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 
		0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x13, 0x13, 0x13, 0x13, 0x13, 0x13, 0x13, 0x13, 
		0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x15, 0x15, 0x15, 0x15, 0x15, 0x15, 0x15, 0x15, 
		0x16, 0x16, 0x16, 0x16, 0x16, 0x16, 0x16, 0x16, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 
		0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x19, 0x19, 0x19, 0x19, 0x19, 0x19, 0x19, 0x19, 
		0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 
		0x1c, 0x1c, 0x1c, 0x1c, 0x1c, 0x1c, 0x1c, 0x1c, 0x1d, 0x1d, 0x1d, 0x1d, 0x1d, 0x1d, 0x1d, 0x1d, 
		0x1e, 0x1e, 0x1e, 0x1e, 0x1e, 0x1e, 0x1e, 0x1e, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 
	};
	
	if(g.InDemo) { return; };
	if(g.x0a18) {		// not found set
		/* addr_sf2ua: 43d6 */
		g.Diff_0a06 = 0x69;
		g.Diff_0a08 = 0xff;
		g.CurrentDifficulty = 0x1f;
	} else {
		g.Diff_0a06 = data_98f42[g.Difficulty][g.x0a16][0];
		g.Diff_0a08 = data_98f42[g.Difficulty][g.x0a16][1];
        
		if (g.Diff_0a06 > g.Diff_0a04) {g.Diff_0a04 = g.Diff_0a06;}
		if (g.Diff_0a08 > g.Diff_0a04) {g.Diff_0a04 = g.Diff_0a08;}     //x0a08 is invariably greater than x0a06
        
		g.CurrentDifficulty = data_4976[g.Diff_0a04];		/* u8 array */
	}
}

void bumpdifficulty_4576(void) {            // called when a player continues
	g.x0a0e++;
	if ((g.x0a0e & 1) == 0) {
		bumpdifficulty_01();
	}
}

/*! 
 decrease the difficulty when a player continues
 sf2ua: 0x453c
 */
void bumpdifficulty_01(void) {
	static const char data_4566[] = {
		24, 32, 40, 48, 56, 60, 64, 68, 
		72, 76, 80, 84, 88, 92, 96, 99
	};
    
	if (g.x0a0c < 16) {
		g.Diff_0a04 -= data_4566[g.x0a0c];
	} else {
		g.Diff_0a04 -= 84;
	}

	if (g.Diff_0a04 < 0) {
		g.Diff_0a04 = 0;
	}
	g.Diff_0a04 &= 0xff;
	FBUpdateDifficulty();
	++g.x0a0c;
}

/*!
 Set the initial difficulty for a new game
 Called each newgame()
 sf2ua: 0x43d0
 */
void BumpDiff_NewGame(void) {
    if(g.InDemo) {
		/* 43d6 inlined */
		g.Diff_0a06 = 0x69;
		g.Diff_0a08 = 0x00ff;
		g.CurrentDifficulty = 0x1f;
    } else {
        g.Diff_GameCnt++;
        if(g.Diff_GameCnt &= 0xf) { /* deliberate */
            // every 16th game doesn't reset the difficulty
            FBUpdateDifficulty();
        } else if( 0x00802000 & (1 << RAND32) ) {
            //1 in 16 chance of new game with last difficulty
            FBUpdateDifficulty();
        } else {
            g.Diff_0a06 = g.Diff_0a04 = 0;
            g.CurrentDifficulty = 0;
            FBUpdateDifficulty();
        }
    }
}

/*!
 Called once per frame, increases difficulty throughout round.
 sf2ua: 0x4468
 */
void bumpdifficulty_08(void) {
	short d0;
	if (g.ActiveHumans == BOTH_HUMAN || g.OnBonusStage) { return; }

	g.x0a0a++;
	if (g.x0a0a >= 0xf0) {
		g.x0a0a = 0;
		g.Diff_0a04 += 3;
		g.Diff_0a04 &= 0xff;
		FBUpdateDifficulty();
	}
	d0 = (!g.ContrP1DB.full) & g.ContrP1.full;

    if (g.Player1.Human == FALSE) {
		d0 = (!g.ContrP2DB.full) & g.ContrP2.full;
	}
    
	if (d0 & BUTTON_MASK) {
        if (g.Diff_0a06 < -1) {
            g.Diff_0a06 = -1;
        } else {
            g.Diff_0a06 = g.Diff_0a06 + 1;
        }
    }
}

/*!
 Called at end of each round
 sf2ua: 0x44c6
 */
void bumpdifficulty_02(void) {
	static const char data_44f6[70]={
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
		0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 
		0x02, 0x02, 0x02, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 
		0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 
	};

	if (g.ActiveHumans != BOTH_HUMAN && !g.OnBonusStage)
    {
        if(g.Diff_0ad6 < 0x3d) {
            g.x8a36      = data_44f6[g.Diff_0ad6];
            g.Diff_0a04 += data_44f6[g.Diff_0ad6];		/* u16 */
        } else {
            g.x8a36      = 4;
            g.Diff_0a04 += 4;
        }
        g.Diff_0a04 &= 0xff;
        FBUpdateDifficulty();
    }
}
void bumpdifficulty_03(void) {	// 46e2
	static const char data_4716[] = {0,2,4,8,12,16,20,24,28,32};
	
	if (g.ActiveHumans == 3 || g.OnBonusStage) { return; }
	if (g.Diff_WeakBoxCnt > 8) {
		g.Diff_0a04 += 32;
		g.x8a38     += 32;
	} else {
		g.Diff_0a04 += data_4716[g.Diff_WeakBoxCnt];
		g.x8a38     += data_4716[g.Diff_WeakBoxCnt];
	}

	g.Diff_0a04 &= 0xff;
	FBUpdateDifficulty();	
}
void bumpdifficulty_04(void) { /* 47aa */
	short d1 = 0;
	short d0;
	
	if (g.ActiveHumans != 3 && g.OnBonusStage == FALSE) {
		d0 = g.x0ae4 * 100;
		if (d0 > 0 && g.HumanMoveCnt > 0) {
			d1 = d0 / g.HumanMoveCnt;
		}
		if (d1 <= 28) {
			d0 = 0;
		} else if (d1 <= 60) {
			d0 = 0;
		} else if (d1 <= 80) {
			d0 = 1;
		} else if (d1 <= 90 ) {
			d0 = 2;
		} else {
			d0 = 3;
		}
		g.x8a3a = d0;
		g.Diff_0a04 += d0;
		g.Diff_0a04 &= 0xff;
		FBUpdateDifficulty();
	}
}
void bumpdifficulty_05(void) { /* 4584 */ 
	const char *data;
	u8	d6;
	
	static const char data_4836[] = {
		3, 3, 3, 3, 3, 3, 3, 3, 3, 3, -1, -1, -1, -1, -1, -1,
		3, 3, 3, 3, 3, 3, 3, 3, 3, 3, -1, -1, -1, -1, -1, -1,
		1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1,
		1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1,
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, -1, -1, -1, -1,
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, -1, -1, -1, -1,
		1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1,
		1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1,
		3, 3, 3, 3, 3, 3, 3, 3, 3, 3, -1, -1, -1, -1, -1, -1,
		3, 3, 3, 3, 3, 3, 3, 3, 3, 3, -1, -1, -1, -1, -1, -1,
	};
	
	static const char data_48d6[] = {
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, -1, -1, -1, -1,
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, -1, -1, -1, -1,
		4, 4, 4, 4, 4, 4, 4, 4, 4, 4, -1, -1, -1, -1, -1, -1,
		4, 4, 4, 4, 4, 4, 4, 4, 4, 4, -1, -1, -1, -1, -1, -1,
		6, 6, 6, 6, 6, 6, 6, 6, 6, 6, -1, -1, -1, -1, -1, -1,
		6, 6, 6, 6, 6, 6, 6, 6, 6, 6, -1, -1, -1, -1, -1, -1,
		9, 9, 9, 9, 9, 9, 9, 9, 9, 9, -1, -1, -1, -1, -1, -1,
		9, 9, 9, 9, 9, 9, 9, 9, 9, 9, -1, -1, -1, -1, -1, -1,
	   15,15,15,15,15,15,15,15,15,15, -1, -1, -1, -1, -1, -1,
	   15,15,15,15,15,15,15,15,15,15, -1, -1, -1, -1, -1, -1,
	};
		
	if (g.ActiveHumans != BOTH_HUMAN && g.OnBonusStage == FALSE) {
		if (g.HumanLoser) {
			d6 = FALSE;
			data = data_48d6;
		} else {
			d6 = TRUE;
			data = data_4836;
		}
		/* machine divides by zero if lookup below is negative */
		if (d6) {
			g.Diff_0a04 += data[g.TimeRemainBCD];
			g.Diff_0a04 &= 0xff;
			g.x8a3c = data[g.TimeRemainBCD];
		} else {
			g.Diff_0a04 -= data[g.TimeRemainBCD];
			g.Diff_0a04 &= 0xff;
			g.x8a3c = -data[g.TimeRemainBCD];
		}
		FBUpdateDifficulty();
	}
}
void bumpdifficulty_06(void) { /* 45ea */ 
	static const char data_46aa[] = {0, 2, 8, 16, 20, 24, 32, 40, 48, -1};
	short d1, d2, d3;
	short d6;
	
	if (g.ActiveHumans != BOTH_HUMAN && g.OnBonusStage == FALSE) {
		if (g.RoundResult < 0) {
			// 4684
			if(g.Diff06Cnt >= 9) {
				d1 = 0x30;
			} else {
				d1 = data_46aa[g.Diff06Cnt];
			}
			g.Diff_0a04 += d1;
			g.Diff_0a04 &= 0xff;
		} else {
			if (g.RoundResult & ROUNDRESULT_P1_WINS) {
				d1 = g.Player1.Energy;
				d6 = g.Player1.Side;
			} else {
				d1 = g.Player2.Energy;
				d6 = g.Player2.Side;
			}
			if (d1 >= 0x71) {
				d2 = 4;
				d3 = 12;
			} else if (d1 >= 0x51) {
				d2 = 3;
				d3 = 9;
			} else if (d1 >= 0x31) {
				d2 = 1;
				d3 = 3;
			} else if (d1 >= 0x11) {
				d2 = 1;
				d3 = 3;
			} else {
				d2 = 3;
				d3 = 6;
			}
			if (g.BattleWinSide == d6) {
				g.x8a3e = d2;
				g.Diff_0a04 += d2;
				g.Diff_0a04 &= 0xff;
			} else {
				g.Diff_0a04 -= d3;
				g.Diff_0a04 &= 0xff;
				g.x8a3e = -d3;
			}
		}
		FBUpdateDifficulty();
	}
}

void bumpdifficulty_10(void) {		/* 0x46b4 */
	g.Diff_0a04 = (g.Diff_0a04 + 2) & 0xff;
	FBUpdateDifficulty();
}

void bump_difficulty_4816(void) {  /* 0x4816 */
	if(g.ActiveHumans != BOTH_HUMAN && g.OnBonusStage == 0) { 
		g.Diff_0a04++;
		g.Diff_0a04 &= 0xff;
		FBUpdateDifficulty();
	}
}

void BumpDiff_PowerMove(void) {	// 46c2 same as 4816?
	if (g.ActiveHumans != BOTH_HUMAN && g.OnBonusStage == 0) {
		++g.Diff_0a04;
		g.Diff_0a04 &= 0xff;
		FBUpdateDifficulty();
	}
}

#pragma mark State machine

/*!
 sf2ua: 0x8d34
 */
static void game_over_one_ply_remains(void) {
	g.BattleWinner  = g.WinningFighter;
	g.BattleLoser   = g.LosingFighter;
	g.x09f5         = g.HumanWinner;
	g.x09f6         = g.HumanLoser;
	g.BattleWinSide = g.RoundWinnerSide;
	g.PlyLostToPly  = g.RoundLoserSide;
	g.mode2 += 4;
}

/*!
 sf2ua: 0x8d64
 */
static void game_over_for_only_player(void) {
	g.BattleLoser   = g.LosingFighter;
	g.BattleWinner  = g.WinningFighter;
	g.x09f5         = g.HumanWinner;
	g.x09f6         = g.HumanLoser;
	g.BattleWinSide = g.RoundWinnerSide;
	g.PlyLostToPly  = g.RoundLoserSide;
	g.mode1 = 8;
	g.mode2 = 0;	/* Game Over */
}

/*!
 sf2ua: 0x8d94
 */
static void update_level_sequence_defeated(short losing_side) {
	int cursor;
	
	Player *pl_lose;
	Player *pl_wins;
	
	if (losing_side) {
		pl_lose = PLAYER2;		// loser
		pl_wins = PLAYER1;
	} else {
		pl_lose = PLAYER1;		// loser
		pl_wins = PLAYER2;
	}
	if (g.ActiveHumans == BOTH_HUMAN) {
		//8e0e
		check_level_sequence(pl_wins);
		set_defeated_false(pl_wins);
		if (pl_lose->FighterID != g.PlayersSelectedDash) {
			if (g.Defeated[pl_lose->FighterID]) {
				++g.x0a16;
				g.x0a16 &= 0xf;
				++g.x09f9;
			}
		}
		check_level_sequence(pl_lose);
		set_defeated_false(pl_lose);
	} else {
		if (pl_wins->x02ae || pl_wins->Human) {
			++g.x09f9;
			++g.x0a16;
			g.x0a16 &= 0xf;
			check_level_sequence(pl_wins);
			set_defeated_false(pl_wins);
			check_level_sequence(pl_lose);
			set_defeated_false(pl_lose);
		} else if (pl_lose->FighterID == g.PlayersSelectedDash) {
			check_level_sequence(pl_lose);
			set_defeated_false(pl_lose);
		}
		for (cursor = g.LevelCursor * 2; g.LevelScript[cursor+1] != 16 ; cursor += 2) {
			if(g.LevelScript[g.LevelCursor * 2] == pl_lose->FighterID) {
				return;
			}
		}
		check_level_sequence(pl_lose);
		set_defeated_false(pl_lose);
	}
}

static void victory_p1(void) {
	g.VictoryCnt++;
	update_level_sequence_defeated(1);
	kill_ply2();
	if(g.ActiveHumans == ONLY_P2) {
		game_over_for_only_player();
	} else {
		g.Player2.Alive = FALSE;
		game_over_one_ply_remains();
	}
}
static void victory_p2(void) {
	g.VictoryCnt++;
	update_level_sequence_defeated(0);
	kill_ply1();
	if(g.ActiveHumans == ONLY_P1) {
		game_over_for_only_player();
	} else {
		g.Player1.Alive = FALSE;
		game_over_one_ply_remains();
	}
}

static void kill_ply1(void) {
	if(g.Player1.Human | g.Player1.x02ae) {
		g.Player1.FighterSelect = g.Player1.FighterID;
	}
	g.Player1.Human = FALSE;
	g.Player1.x02ae = FALSE;
	g.Player1.SelectComplete = FALSE;
	g.Player1.FighterID = 0;
}
static void kill_ply2(void) {
	if(g.Player2.Human | g.Player2.x02ae) {
		g.Player2.FighterSelect = g.Player2.FighterID;
	}
	g.Player2.Human = FALSE;
	g.Player2.x02ae = FALSE;
	g.Player2.SelectComplete = FALSE;
	g.Player2.FighterID = 0;
}

        
