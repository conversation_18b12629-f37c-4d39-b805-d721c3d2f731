/* sf2 sprite.c */
#include "sf2.h"

#include "gstate.h"
#include "structs.h"
#include "player.h"

#include "lib.h"
#include "rules.h"
#include "particle.h"
#include "gfxlib.h"

#include "gemu.h"

#include "playerstate.h"
#include "reactmode.h"
#include "sprite.h"

#include <stdio.h>

extern Game g;
extern ScrollState gstate_Scroll1;
extern ScrollState gstate_Scroll2;
extern ScrollState gstate_Scroll3;

static void sprite_coords(Object *obj, short *coordpair);


/* globals for sprite lib */
u16 *DSObjCur_g;			/* number of next object tile to draw to */
short g_tilecount;	/* tile budget */

/* BEGIN DATA */


/* Trigonometry data cos(a), sin(a), -sin(a), cos(a) 
   steps of 11.25 degrees, 8 steps = 90 degrees    */

/* Two curiosities here: it's not clear why there's a second column for cos(), and
 * I imagine integer wrap-around is responsible for sin(270) to be wrong. */

static const short data_trig[32][4] = {		/* 7f030 */
    {   256,     0,     0,   256,  },
    {   250,    49,   -49,   250,  },
    {   236,    97,   -97,   236,  },
    {   212,   142,  -142,   212,  },
    {   180,   180,  -180,   180,  },		/* 45 */
    {   142,   212,  -212,   142,  },
    {    97,   236,  -236,    97,  },
    {    49,   250,  -250,    49,  },
    {     0,   256,  -256,     0,  },		/* 90 degrees */
    {   -49,   250,  -250,   -49,  },
    {   -97,   236,  -236,   -97,  },
    {  -142,   212,  -212,  -142,  },
    {  -180,   180,  -180,  -180,  },
    {  -212,   142,  -142,  -212,  },
    {  -236,    97,   -97,  -236,  },
    {  -250,    49,   -49,  -250,  },
    {  -256,     0,     0,  -256,  },		/* 180 degrees */
    {  -250,   -49,    49,  -250,  },
    {  -236,   -97,    97,  -236,  },
    {  -212,  -142,   142,  -212,  },
    {  -180,  -180,   180,  -180,  },
    {  -142,  -212,   212,  -142,  },
    {   -97,  -236,   236,   -97,  },
    {   -49,  -250,   250,   -49,  },
    {     0,   256,   256,     0,  },		/* 270 degrees, sin(a) suspect  */
    {    49,  -250,   250,    49,  },
    {    97,  -236,   236,    97,  },
    {   142,  -212,   212,   142,  },
    {   180,  -180,   180,   180,  },
    {   212,  -142,   142,   212,  },
    {   236,   -97,    97,   236,  },
    {   250,   -49,    49,   250,  },
};

// these values are converted from m68k offset tables
// to u16 offsets in the 0x7f7f2 array

static const short data_7f6f2[]={
	3288, 4560, 4484, 2604, 2766, 3080, 4036, 4388,
	1740, 1752, 3598, 3070, 2352, 2886, 4340, 2370, 
	1764, 3612, 3800, 4020, 3440, 2154, 4220, 2778,
	3626, 3090, 1776, 4148, 3996, 3810, 3448, 4044,
	2170, 1648, 2790, 3932, 3890, 2754, 3790, 3060, 
	3780, 3910, 3962, 3908, 3960, 3906, 2618, 2632, 
	3528, 1656, 1696, 3220, 1692, 3408, 4356, 2138, 
	
	3268, 4028, 3202, 2590, 4372, 4052, 2122, 3030, 
	1728, 3432, 3958, 2742, 2886, 3584, 4166, 3456, 
	4012, 1688, 1652, 2730, 3956, 2742, 2576, 3940, 
	3416, 1788, 4154, 3214, 4160, 2556, 2540, 2532, 
	2298, 2106, 2904, 2094, 3570, 1884, 1716, 4136, 
	1684, 3208, 1680, 4124, 4142, 4130, 3190, 1640, 
	1626, 1644, 2922, 4240, 2334, 1584, 1538, 1452, 
	1366, 1210, 1064, 3424, 976, 912, 844, 776,
	766, 676, 424, 392, 352, 52, 20, 0, 
};
static const short data_81c1e[]={
	0x0640, 0x0628, 0x0618, 0x0298, 0x026C, 0x01EC, 0x014C, 0x00BC, 0x008C, 0x0014, 
};
static const short data_7f7f2[4630]={
    -40, -16, -40, 0, -24, -16, -24, 0, -8, -16, -8, 0, 8, -16, 8, 0, 24, -16, 24, 0,
	-128, -16, -112, -16, -96, -16, -80, -16, -64, -16, -48, -16, -32, 
	-16, -16, -16, 0, -16, 16, -16, 32, -16, 48, -16, 64, -16, 80, -16, 96, 
	-16, 112, -16, -200, -96, -200, -80, -200, -70, -200, -48, -200, 26, -200, 48, -184, 
	-96, -184, -80, -184, -64, -184, -48, -184, 32, -184, 48, -168, -96, -168, -80, -168, 
	-64, -168, -48, -168, 32, -168, 48, -152, -96, -152, -80, -152, -64, -152, -48, -152, 
	32, -152, 48, -136, -96, -136, -80, -136, -64, -136, -48, -136, 32, -136, 48, -120, 
	-96, -120, -80, -120, -64, -120, -48, -120, 32, -120, 48, -104, -96, -104, -80, -104, 
	-64, -104, -48, -104, 32, -104, 48, -88, -96, -88, -80, -88, -64, -88, -48, -88, 
	32, -88, 48, -72, -96, -72, -80, -72, -64, -72, -48, -72, 32, -72, 48, -56, 
	-96, -56, -80, -56, -64, -56, -48, -56, 32, -56, 48, -40, -96, -40, -80, -40, 
	-64, -40, -48, -40, 32, -40, 48, -24, -96, -24, -80, -24, -64, -24, -48, -24, 
	32, -24, 48, -8, -96, -8, -80, -8, -64, -8, -48, -8, 32, -8, 48, 8, 
	-96, 8, -80, 8, -64, 8, -48, 8, 32, 8, 48, 24, -96, 24, -80, 24, 
	-64, 24, -48, 24, 32, 24, 48, 40, -96, 40, -80, 40, -64, 40, -48, 40, 
	32, 40, 48, 56, -96, 56, -80, 56, -64, 56, -48, 56, 32, 56, 48, 72, 
	-96, 72, -80, 72, -70, 72, -48, 72, 32, 72, 48, 88, -96, 88, -80, 88, 
	-64, 88, -48, 88, 32, 88, 48, 104, -96, 104, -80, 104, -64, 104, -48, 104, 
	32, 104, 48, 120, -96, 120, -80, 120, -64, 120, -48, 120, 32, 120, 48, 136, 
	-96, 136, -80, 136, -64, 136, -48, 136, 32, 136, 48, 152, -96, 152, -80, 152, 
	-64, 152, -48, 152, 32, 152, 48, 168, -96, 168, -80, 168, -64, 168, -48, 168, 
	32, 168, 48, 184, -96, 184, -80, 184, -64, 184, -48, 184, 32, 184, 42, -56, 
	-24, -24, -24, 8, -24, 40, -24, -64, -32, -64, -16, -48, -32, -48, -16, -32, 
	-32, -32, -16, -16, -32, -16, -16, 0, -32, 0, -16, 16, -32, 16, -16, 32, 
	-32, 32, -16, 48, -32, 48, -16, -64, -16, -48, -16, -32, -16, -16, -16, 0, 
	-16, 16, -16, 32, -16, 48, -16, -64, -8, -48, -8, -32, -8, -16, -8, 0, 
	-8, 16, -8, 32, -8, 48, -8, -144, -56, -144, -40, -144, -24, -144, -8, -144, 
	8, -144, 24, -144, 40, -128, -56, -128, -40, -128, -24, -128, -8, -128, 8, -128, 
	24, -128, 40, -112, -56, -112, -40, -112, -24, -112, -8, -112, 8, -112, 24, -112, 
	40, -96, -56, -96, -40, -96, -24, -96, -8, -96, 8, -96, 24, -96, 40, -80, 
	-56, -80, -40, -80, -24, -80, -8, -80, 8, -80, 24, -80, 40, -64, -56, -64, 
	-40, -64, -24, -64, -8, -64, 8, -64, 24, -64, 40, -48, -56, -48, -40, -48, 
	-24, -48, -8, -48, 8, -48, 24, -48, 40, -32, -56, -32, -40, -32, -24, -32, 
	-8, -32, 8, -32, 24, -32, 40, -16, -56, -16, -40, -16, -24, -16, -8, -16, 
	8, -16, 24, -16, 40, 0, -56, 0, -40, 0, -24, 0, -8, 0, 8, 0, 
	24, 0, 40, 16, -56, 16, -40, 16, -24, 16, -8, 16, 8, 16, 24, 16, 
	40, 32, -56, 32, -40, 32, -24, 32, -8, 32, 8, 32, 24, 32, 40, 48, 
	-56, 48, -40, 48, -24, 48, -8, 48, 8, 48, 24, 48, 40, 64, -56, 64, 
	-40, 64, -24, 64, -8, 64, 8, 64, 24, 64, 40, 80, -56, 80, -40, 80, 
	-24, 80, -8, 80, 8, 80, 24, 80, 40, 96, -56, 96, -40, 96, -24, 96, 
	-8, 96, 8, 96, 24, 96, 40, 112, -56, 112, -40, 112, -24, 112, -8, 112, 
	8, 112, 24, 112, 40, 128, -56, 128, -40, 128, -24, 128, -8, 128, 8, 128, 
	24, 128, 40, -72, -40, -72, -24, -72, -8, -72, 8, -72, 24, -56, -40, -56, 
	-24, -56, -8, -56, 8, -56, 24, -40, -40, -40, -24, -40, -8, -40, 8, -40, 
	24, -24, -40, -24, -24, -24, -8, -24, 8, -24, 24, -8, -40, -8, -24, -8, 
	-8, -8, 8, -8, 24, 8, -40, 8, -24, 8, -8, 8, 8, 8, 24, 24, 
	-40, 24, -24, 24, -8, 24, 8, 24, 24, 40, -40, 40, -24, 40, -8, 40, 
	8, 40, 24, 56, -40, 56, -24, 56, -8, 56, 8, 56, 24, -8, 0, -8, 
	8, -8, 16, -8, 24, -8, 32, -56, -48, -56, -32, -40, -48, -40, -32, -40, 
	-16, -24, -48, -24, -32, -24, -16, -8, -48, -8, -32, 8, -48, 8, -32, 8, 
	-16, 24, -48, 24, -32, 24, -16, 40, -48, -44, -72, -44, -56, -28, -72, -28, 
	-56, -12, -72, -12, -56, 4, -72, 4, -56, 20, -88, 20, -72, 20, -56, 36, 
	-88, 36, -72, 36, -56, 52, -88, 52, -72, 52, -56, -48, -48, -48, -32, -32, 
	-48, -32, -32, -32, -16, -16, -48, -16, -32, -16, -16, 0, -48, 0, -32, 16, 
	-48, 16, -32, 16, -16, 32, -48, 32, -32, 32, -16, -45, -56, -29, -56, -13, 
	-72, -13, -56, 3, -88, 3, -72, 3, -56, 19, -88, 19, -72, 19, -56, 35, 
	-104, 35, -88, 35, -72, 35, -56, 51, -104, 51, -88, 51, -72, 51, -56, -48, 
	-48, -48, -32, -32, -48, -32, -32, -32, -16, -16, -48, -16, -32, -16, -16, 0, 
	-48, 0, -32, 16, -48, 16, -32, 16, -16, 32, -48, 32, -32, 32, -16, -41, 
	-56, -25, -56, -9, -88, -9, -72, -9, -56, 7, -104, 7, -88, 7, -72, 7, 
	-56, 23, -104, 23, -88, 23, -72, 23, -56, 39, -88, 39, -72, 39, -56, -32, 
	-176, -32, -160, -32, -144, -32, -128, -32, -112, -32, -96, -32, -80, -32, -64, -32, 
	-48, -32, -32, -32, -16, -16, -176, -16, -160, -16, -144, -16, -128, -16, -112, -16, 
	-96, -16, -80, -16, -64, -16, -48, -16, -32, -16, -16, 0, -176, 0, -160, 0, 
	-144, 0, -128, 0, -112, 0, -96, 0, -80, 0, -64, 0, -48, 0, -32, 0, 
	-16, 16, -176, 16, -160, 16, -144, 16, -128, 16, -112, 16, -96, 16, -80, 16, 
	-64, 16, -48, 16, -32, 16, -16, -56, -160, -56, -144, -56, -128, -56, -112, -40, 
	-160, -40, -144, -40, -128, -40, -112, -24, -160, -24, -144, -24, -128, -24, -112, -8, 
	-160, -8, -144, -8, -128, -8, -112, 8, -160, 8, -144, 8, -128, 8, -112, 24, 
	-160, 24, -144, 24, -128, 24, -112, -56, -112, -56, -96, -56, -80, -56, -64, -56, 
	-48, -56, -32, -56, -16, -40, -112, -40, -96, -40, -80, -40, -64, -40, -48, -40, 
	-32, -40, -16, -24, -112, -24, -96, -24, -80, -24, -64, -24, -48, -24, -32, -24, 
	-16, -8, -112, -8, -96, -8, -80, -8, -64, -8, -48, -8, -32, -8, -16, 8, 
	-112, 8, -96, 8, -80, 8, -64, 8, -48, 8, -32, 8, -16, 24, -112, 24, 
	-96, 24, -80, 24, -64, 24, -48, 24, -32, 24, -16, 40, -112, 40, -96, 40, 
	-80, 40, -64, 40, -48, 40, -32, 40, -16, -64, -128, -64, -112, -64, -96, -64, 
	-80, -64, -64, -64, -48, -48, -128, -48, -112, -48, -96, -48, -80, -48, -64, -48, 
	-48, -32, -128, -32, -112, -32, -96, -32, -80, -32, -64, -32, -48, -16, -128, -16, 
	-112, -16, -96, -16, -80, -16, -64, -16, -48, 0, -128, 0, -112, 0, -96, 0, 
	-80, 0, -64, 0, -48, 16, -128, 16, -112, 16, -96, 16, -80, 16, -64, 16, 
	-48, -32, -112, -32, -96, -32, -80, -32, -64, -32, -48, -32, -32, -32, -16, -16, 
	-112, -16, -96, -16, -80, -16, -64, -16, -48, -16, -32, -16, -16, 0, -112, 0, 
	-96, 0, -80, 0, -64, 0, -48, 0, -32, 0, -16, 16, -112, 16, -96, 16, 
	-80, 16, -64, 16, -48, 16, -32, 16, -16, 32, -112, 32, -96, 32, -80, 32, 
	-64, 32, -48, 32, -32, 32, -16, 48, -112, 48, -96, 48, -80, 48, -64, 48, 
	-48, 48, -32, 48, -16, -48, -112, -48, -96, -48, -80, -48, -64, -48, -48, -48, 
	-32, -32, -112, -32, -96, -32, -80, -32, -64, -32, -48, -32, -32, -16, -112, -16, 
	-96, -16, -80, -16, -64, -16, -48, -16, -32, 0, -112, 0, -96, 0, -80, 0, 
	-64, 0, -48, 0, -32, 16, -112, 16, -96, 16, -80, 16, -64, 16, -48, 16, 
	-32, 32, -112, 32, -96, 32, -80, 32, -64, 32, -48, 32, -32, -16, -112, -16, 
	-96, 0, -112, 0, -96, -32, -16, -16, -16, 0, -16, -56, -96, -56, -80, -56, 
	-64, -56, -48, -56, -32, -40, -96, -40, -80, -40, -64, -40, -48, -40, -32, -24, 
	-96, -24, -80, -24, -64, -24, -48, -24, -32, -8, -96, -8, -80, -8, -64, -8, 
	-48, -8, -32, 8, -96, 8, -80, 8, -64, 8, -48, 8, -32, 24, -96, 24, 
	-80, 24, -64, 24, -48, 24, -32, 40, -96, 40, -80, 40, -64, 40, -48, 40, 
	-32, -24, -112, -24, -96, -8, -112, -8, -96, -24, -32, -24, -16, -8, -32, -8, 
	-16, -40, -48, -40, -32, -40, -16, -24, -48, -24, -32, -24, -16, -8, -48, -8, 
	-32, -8, -16, 8, -48, 8, -32, 8, -16, 24, -16, -19, -64, -3, -64, 13, 
	-80, 13, -64, 13, -48, 13, -32, 29, -80, 29, -64, 29, -48, 29, -32, -40, 
	-48, -40, -32, -40, -16, -24, -48, -24, -32, -24, -16, -8, -48, -8, -32, -8, 
	-16, 8, -48, 8, -32, 8, -16, 24, -16, -17, -64, -1, -64, 15, -64, 15, 
	-48, 15, -32, 31, -64, 31, -48, 31, -32, -32, -32, -32, -16, -16, -32, -16, 
	-16, 0, -32, 0, -16, 16, -24, -80, -32, -80, -16, -64, -32, -64, -16, -48, 
	-32, -48, -16, -32, -32, -32, -16, -16, -32, -16, -16, 0, -32, 0, -16, 16, 
	-32, 16, -16, 32, -32, 32, -16, 48, -32, 48, -16, 64, -32, 64, -16, -72, 
	-32, -72, -16, -56, -32, -56, -16, -40, -32, -40, -16, -24, -32, -24, -16, -8, 
	-32, -8, -16, 8, -32, 8, -16, 24, -32, 24, -16, 40, -32, 40, -16, 56, 
	-32, 56, -16, -112, -96, -112, -80, -112, -64, -112, -48, -112, -32, -112, -16, -96, 
	-96, -96, -80, -96, -64, -96, -48, -96, -32, -96, -16, -80, -96, -80, -80, -80, 
	-64, -80, -48, -80, -32, -80, -16, -64, -96, -64, -80, -64, -64, -64, -48, -64, 
	-32, -64, -16, -48, -96, -48, -80, -48, -64, -48, -48, -48, -32, -48, -16, -32, 
	-96, -32, -80, -32, -64, -32, -48, -32, -32, -32, -16, -16, -96, -16, -80, -16, 
	-64, -16, -48, -16, -32, -16, -16, 0, -96, 0, -80, 0, -64, 0, -48, 0, 
	-32, 0, -16, 16, -96, 16, -80, 16, -64, 16, -48, 16, -32, 16, -16, 32, 
	-96, 32, -80, 32, -64, 32, -48, 32, -32, 32, -16, 48, -96, 48, -80, 48, 
	-64, 48, -48, 48, -32, 48, -16, 64, -96, 64, -80, 64, -64, 64, -48, 64, 
	-32, 64, -16, 80, -96, 80, -80, 80, -64, 80, -48, 80, -32, 80, -16, 96, 
	-96, 96, -80, 96, -64, 96, -48, 96, -32, 96, -16, -144, -80, -144, -64, -144, 
	-48, -144, -32, -144, -16, -128, -96, -128, -80, -128, -64, -128, -48, -128, -32, -128, 
	-16, -112, -128, -112, -112, -112, -96, -112, -80, -112, -64, -112, -48, -112, -32, -112, 
	-16, -96, -144, -96, -128, -96, -112, -96, -96, -96, -80, -96, -64, -96, -48, -96, 
	-32, -96, -16, -80, -144, -80, -128, -80, -112, -80, -96, -80, -80, -80, -64, -80, 
	-48, -80, -32, -80, -16, -64, -144, -64, -128, -64, -112, -64, -96, -64, -64, -64, 
	-48, -64, -32, -64, -16, -48, -80, -48, -64, -48, -48, -48, -32, -48, -16, -32, 
	-112, -32, -96, -32, -80, -32, -64, -32, -48, -32, -32, -32, -16, -16, -112, -16, 
	-96, -16, -80, -16, -64, -16, -48, -16, -32, 0, -112, 0, -96, 0, -80, 0, 
	-64, 0, -48, 16, -64, 16, -48, 32, -80, 32, -64, 32, -48, 32, -32, 32, 
	-16, 48, -64, 48, -48, 48, -32, 48, -16, 64, -80, 64, -64, 64, -48, 64, 
	-32, 64, -16, 80, -96, 80, -80, 80, -64, 80, -48, 80, -32, 80, -16, 96, 
	-96, 96, -80, 96, -64, 96, -48, 96, -32, 96, -16, 112, -96, 112, -80, 112, 
	-64, 112, -48, 112, -32, 112, -16, 128, -48, 128, -32, 128, -16, -48, -48, -32, 
	-48, 0, -96, 16, -96, 48, -32, 48, -16, -96, -128, -96, -112, -96, -96, -96, 
	-80, -96, -64, -96, -48, -96, -32, -96, -16, -80, -128, -80, -112, -80, -96, -80, 
	-80, -80, -64, -80, -48, -80, -32, -80, -16, -64, -128, -64, -112, -64, -96, -64, 
	-80, -64, -64, -64, -48, -64, -32, -64, -16, -48, -128, -48, -112, -48, -96, -48, 
	-80, -48, -64, -48, -48, -48, -32, -48, -16, -32, -128, -32, -112, -32, -96, -32, 
	-80, -32, -64, -32, -48, -32, -32, -32, -16, -16, -128, -16, -112, -16, -96, -16, 
	-80, -16, -64, -16, -48, -16, -32, -16, -16, 0, -128, 0, -112, 0, -96, 0, 
	-80, 0, -64, 0, -48, 0, -32, 0, -16, 16, -128, 16, -112, 16, -96, 16, 
	-80, 16, -64, 16, -48, 16, -32, 16, -16, 32, -128, 32, -112, 32, -96, 32, 
	-80, 32, -64, 32, -48, 32, -32, 32, -16, 48, -128, 48, -112, 48, -96, 48, 
	-80, 48, -64, 48, -48, 48, -32, 48, -16, 64, -128, 64, -112, 64, -96, 64, 
	-80, 64, -64, 64, -48, 64, -32, 64, -16, 80, -128, 80, -112, 80, -96, 80, 
	-80, 80, -64, 80, -48, 80, -32, 80, -16, -104, -144, -104, -128, -104, -112, -104, 
	-96, -104, -80, -104, -64, -104, -48, -104, -32, -104, -16, -88, -144, -88, -128, -88, 
	-112, -88, -96, -88, -80, -88, -64, -88, -48, -88, -32, -88, -16, -72, -144, -72, 
	-128, -72, -112, -72, -96, -72, -80, -72, -64, -72, -48, -72, -32, -72, -16, -56, 
	-144, -56, -128, -56, -112, -56, -96, -56, -80, -56, -64, -56, -48, -56, -32, -56, 
	-16, -40, -144, -40, -128, -40, -112, -40, -96, -40, -80, -40, -64, -40, -48, -40, 
	-32, -40, -16, -24, -144, -24, -128, -24, -112, -24, -96, -24, -80, -24, -64, -24, 
	-48, -24, -32, -24, -16, -8, -144, -8, -128, -8, -112, -8, -96, -8, -80, -8, 
	-64, -8, -48, -8, -32, -8, -16, 8, -144, 8, -128, 8, -112, 8, -96, 8, 
	-80, 8, -64, 8, -48, 8, -32, 8, -16, 24, -144, 24, -128, 24, -112, 24, 
	-96, 24, -80, 24, -64, 24, -48, 24, -32, 24, -16, 40, -144, 40, -128, 40, 
	-112, 40, -96, 40, -80, 40, -64, 40, -48, 40, -32, 40, -16, 56, -144, 56, 
	-128, 56, -112, 56, -96, 56, -80, 56, -64, 56, -48, 56, -32, 56, -16, 72, 
	-144, 72, -128, 72, -112, 72, -96, 72, -80, 72, -64, 72, -48, 72, -32, 72, 
	-16, 88, -144, 88, -128, 88, -112, 88, -96, 88, -80, 88, -64, 88, -48, 88, 
	-32, 88, -16, -112, -56, -112, 40, 96, -56, 96, 40, -16, -32, -16, -16, 0, 
	-32, 0, -16, -16, -32, -16, -16, 0, -32, 0, -16, -16, -45, -16, -29, -16, 
	-13, 0, -45, 0, -29, 0, -13, -16, -32, -16, -16, 0, -32, 0, -16, -88, 
	-112, -88, -96, -88, -80, -88, -64, -88, -48, -88, -32, -88, -16, -72, -112, -72, 
	-96, -72, -80, -72, -64, -72, -48, -72, -32, -72, -16, -56, -112, -56, -96, -56, 
	-80, -56, -64, -56, -48, -56, -32, -56, -16, -40, -112, -40, -96, -40, -80, -40, 
	-64, -40, -48, -40, -32, -40, -16, -24, -112, -24, -96, -24, -80, -24, -64, -24, 
	-48, -24, -32, -24, -16, -8, -112, -8, -96, -8, -80, -8, -64, -8, -48, -8, 
	-32, -8, -16, 8, -112, 8, -96, 8, -80, 8, -64, 8, -48, 8, -32, 8, 
	-16, 24, -112, 24, -96, 24, -80, 24, -64, 24, -48, 24, -32, 24, -16, 40, 
	-112, 40, -96, 40, -80, 40, -64, 40, -48, 40, -32, 40, -16, 56, -112, 56, 
	-96, 56, -80, 56, -64, 56, -48, 56, -32, 56, -16, 72, -112, 72, -96, 72, 
	-80, 72, -64, 72, -48, 72, -32, 72, -16, -104, -96, -104, -80, -104, -64, -104, 
	-48, -104, -32, -104, -16, -88, -96, -88, -80, -88, -64, -88, -48, -88, -32, -88, 
	-16, -72, -96, -72, -80, -72, -64, -72, -48, -72, -32, -72, -16, -56, -96, -56, 
	-80, -56, -64, -56, -48, -56, -32, -56, -16, -40, -96, -40, -80, -40, -64, -40, 
	-48, -40, -32, -40, -16, -24, -96, -24, -80, -24, -64, -24, -48, -24, -32, -24, 
	-16, -8, -96, -8, -80, -8, -64, -8, -48, -8, -32, -8, -16, 8, -96, 8, 
	-80, 8, -64, 8, -48, 8, -32, 8, -16, 24, -96, 24, -80, 24, -64, 24, 
	-48, 24, -32, 24, -16, 40, -96, 40, -80, 40, -64, 40, -48, 40, -32, 40, 
	-16, 56, -96, 56, -80, 56, -64, 56, -48, 56, -32, 56, -16, 72, -96, 72, 
	-80, 72, -64, 72, -48, 72, -32, 72, -16, 88, -96, 88, -80, 88, -64, 88, 
	-48, 88, -32, 88, -16, -64, -144, -64, -128, -64, -112, -64, -96, -64, -80, -64, 
	-64, -64, -48, -64, -32, -64, -16, -48, -144, -48, -128, -48, -112, -48, -96, -48, 
	-80, -48, -64, -48, -48, -48, -32, -48, -16, -32, -144, -32, -128, -32, -112, -32, 
	-96, -32, -80, -32, -64, -32, -48, -32, -32, -32, -16, -16, -144, -16, -128, -16, 
	-112, -16, -96, -16, -80, -16, -64, -16, -48, -16, -32, -16, -16, 0, -144, 0, 
	-128, 0, -112, 0, -96, 0, -80, 0, -64, 0, -48, 0, -32, 0, -16, 16, 
	-144, 16, -128, 16, -112, 16, -96, 16, -80, 16, -64, 16, -48, 16, -32, 16, 
	-16, 32, -144, 32, -128, 32, -112, 32, -96, 32, -80, 32, -64, 32, -48, 32, 
	-32, 32, -16, 48, -144, 48, -128, 48, -112, 48, -96, 48, -80, 48, -64, 48, 
	-48, 48, -32, 48, -16, -128, -80, -128, -64, -128, -48, -128, -32, -128, -16, -112, 
	-80, -112, -64, -112, -48, -112, -32, -112, -16, -96, -80, -96, -64, -96, -48, -96, 
	-32, -96, -16, -80, -80, -80, -64, -80, -48, -80, -32, -80, -16, -64, -80, -64, 
	-64, -64, -48, -64, -32, -64, -16, -48, -80, -48, -64, -48, -48, -48, -32, -48, 
	-16, -32, -80, -32, -64, -32, -48, -32, -32, -32, -16, -16, -80, -16, -64, -16, 
	-48, -16, -32, -16, -16, 0, -80, 0, -64, 0, -48, 0, -32, 0, -16, 16, 
	-80, 16, -64, 16, -48, 16, -32, 16, -16, 32, -80, 32, -64, 32, -48, 32, 
	-32, 32, -16, 48, -80, 48, -64, 48, -48, 48, -32, 48, -16, 64, -80, 64, 
	-64, 64, -48, 64, -32, 64, -16, 80, -80, 80, -64, 80, -48, 80, -32, 80, 
	-16, 96, -80, 96, -64, 96, -48, 96, -32, 96, -16, 112, -80, 112, -64, 112, 
	-48, 112, -32, 112, -16, -104, -48, -104, -32, -104, -16, -88, -48, -88, -32, -88, 
	-16, -72, -48, -72, -32, -72, -16, -56, -48, -56, -32, -56, -16, -40, -48, -40, 
	-32, -40, -16, -24, -48, -24, -32, -24, -16, -8, -48, -8, -32, -8, -16, 8, 
	-48, 8, -32, 8, -16, 24, -48, 24, -32, 24, -16, 40, -48, 40, -32, 40, 
	-16, 56, -48, 56, -32, 56, -16, 72, -48, 72, -32, 72, -16, 88, -48, 88, 
	-32, 88, -16, -56, -160, -56, -144, -56, -128, -56, -112, -56, -96, -56, -80, -56, 
	-64, -56, -48, -56, -32, -56, -16, -40, -160, -40, -144, -40, -128, -40, -112, -40, 
	-96, -40, -80, -40, -64, -40, -48, -40, -32, -40, -16, -24, -160, -24, -144, -24, 
	-128, -24, -112, -24, -96, -24, -80, -24, -64, -24, -48, -24, -32, -24, -16, -8, 
	-160, -8, -144, -8, -128, -8, -112, -8, -96, -8, -80, -8, -64, -8, -48, -8, 
	-32, -8, -16, 8, -160, 8, -144, 8, -128, 8, -112, 8, -96, 8, -80, 8, 
	-64, 8, -48, 8, -32, 8, -16, 24, -160, 24, -144, 24, -128, 24, -112, 24, 
	-96, 24, -80, 24, -64, 24, -48, 24, -32, 24, -16, 40, -160, 40, -144, 40, 
	-128, 40, -112, 40, -96, 40, -80, 40, -64, 40, -48, 40, -32, 40, -16, -120, 
	-64, -120, -48, -120, -32, -120, -16, -104, -64, -104, -48, -104, -32, -104, -16, -88, 
	-64, -88, -48, -88, -32, -88, -16, -72, -64, -72, -48, -72, -32, -72, -16, -56, 
	-64, -56, -48, -56, -32, -56, -16, -40, -64, -40, -48, -40, -32, -40, -16, -24, 
	-64, -24, -48, -24, -32, -24, -16, -8, -64, -8, -48, -8, -32, -8, -16, 8, 
	-64, 8, -48, 8, -32, 8, -16, 24, -64, 24, -48, 24, -32, 24, -16, 40, 
	-64, 40, -48, 40, -32, 40, -16, 56, -64, 56, -48, 56, -32, 56, -16, 72, 
	-64, 72, -48, 72, -32, 72, -16, 88, -64, 88, -48, 88, -32, 88, -16, 104, 
	-64, 104, -48, 104, -32, 104, -16, -144, -112, -144, -96, -144, -80, -144, -64, -144, 
	-48, -144, -32, -144, -16, -128, -112, -128, -96, -128, -80, -128, -64, -128, -48, -128, 
	-32, -128, -16, -112, -112, -112, -96, -112, -80, -112, -64, -112, -48, -112, -32, -112, 
	-16, -96, -112, -96, -96, -96, -80, -96, -64, -96, -48, -96, -32, -96, -16, -80, 
	-112, -80, -96, -80, -80, -80, -64, -80, -48, -80, -32, -80, -16, -64, -112, -64, 
	-96, -64, -80, -64, -64, -64, -48, -64, -32, -64, -16, -48, -112, -48, -96, -48, 
	-80, -48, -64, -48, -48, -48, -32, -48, -16, -32, -112, -32, -96, -32, -80, -32, 
	-64, -32, -48, -32, -32, -32, -16, -16, -112, -16, -96, -16, -80, -16, -64, -16, 
	-48, -16, -32, -16, -16, 0, -112, 0, -96, 0, -80, 0, -64, 0, -48, 0, 
	-32, 0, -16, 16, -112, 16, -96, 16, -80, 16, -64, 16, -48, 16, -32, 16, 
	-16, 32, -112, 32, -96, 32, -80, 32, -64, 32, -48, 32, -32, 32, -16, 48, 
	-112, 48, -96, 48, -80, 48, -64, 48, -48, 48, -32, 48, -16, 64, -112, 64, 
	-96, 64, -80, 64, -64, 64, -48, 64, -32, 64, -16, 80, -112, 80, -96, 80, 
	-80, 80, -64, 80, -48, 80, -32, 80, -16, 96, -112, 96, -96, 96, -80, 96, 
	-64, 96, -48, 96, -32, 96, -16, 112, -112, 112, -96, 112, -80, 112, -64, 112, 
	-48, 112, -32, 112, -16, 128, -112, 128, -96, 128, -80, 128, -64, 128, -48, 128, 
	-32, 128, -16, -88, -80, -88, -64, -88, -48, -88, -32, -88, -16, -72, -80, -72, 
	-64, -72, -48, -72, -32, -72, -16, -56, -80, -56, -64, -56, -48, -56, -32, -56, 
	-16, -40, -80, -40, -64, -40, -48, -40, -32, -40, -16, -24, -80, -24, -64, -24, 
	-48, -24, -32, -24, -16, -8, -80, -8, -64, -8, -48, -8, -32, -8, -16, 8, 
	-80, 8, -64, 8, -48, 8, -32, 8, -16, 24, -80, 24, -64, 24, -48, 24, 
	-32, 24, -16, 40, -80, 40, -64, 40, -48, 40, -32, 40, -16, 56, -80, 56, 
	-64, 56, -48, 56, -32, 56, -16, 72, -80, 72, -64, 72, -48, 72, -32, 72, 
	-16, -168, -16, -152, -16, -136, -16, -120, -16, -104, -16, -88, -16, -72, -16, -56, 
	-16, -40, -16, -24, -16, -8, -16, 8, -16, 24, -16, 40, -16, 56, -16, 72, 
	-16, 88, -16, 104, -16, 120, -16, 136, -16, 152, -16, -256, -16, -240, -16, -224, 
	-16, -208, -16, -192, -16, -176, -16, -160, -16, -144, -16, -128, -16, -112, -16, -96, 
	-16, -80, -16, -64, -16, -48, -16, -32, -16, -16, -16, 0, -16, 16, -16, 32, 
	-16, 48, -16, 64, -16, 80, -16, 96, -16, 112, -16, 128, -16, 144, -16, 160, 
	-16, 176, -16, 192, -16, 208, -16, 224, -16, 240, -16, -128, -64, -128, -48, -128, 
	-32, -128, -16, -112, -64, -112, -48, -112, -32, -112, -16, -96, -64, -96, -48, -96, 
	-32, -96, -16, -80, -64, -80, -48, -80, -32, -80, -16, -64, -64, -64, -48, -64, 
	-32, -64, -16, -48, -64, -48, -48, -48, -32, -48, -16, -32, -64, -32, -48, -32, 
	-32, -32, -16, -16, -64, -16, -48, -16, -32, -16, -16, 0, -64, 0, -48, 0, 
	-32, 0, -16, 16, -64, 16, -48, 16, -32, 16, -16, 32, -64, 32, -48, 32, 
	-32, 32, -16, 48, -64, 48, -48, 48, -32, 48, -16, 64, -64, 64, -48, 64, 
	-32, 64, -16, 80, -64, 80, -48, 80, -32, 80, -16, 96, -64, 96, -48, 96, 
	-32, 96, -16, 112, -64, 112, -48, 112, -32, 112, -16, -128, -48, -128, -32, -128, 
	-16, -112, -48, -112, -32, -112, -16, -96, -48, -96, -32, -96, -16, -80, -48, -80, 
	-32, -80, -16, -64, -48, -64, -32, -64, -16, -48, -48, -48, -32, -48, -16, -32, 
	-48, -32, -32, -32, -16, -16, -48, -16, -32, -16, -16, 0, -48, 0, -32, 0, 
	-16, 16, -48, 16, -32, 16, -16, 32, -48, 32, -32, 32, -16, 48, -48, 48, 
	-32, 48, -16, 64, -48, 64, -32, 64, -16, 80, -48, 80, -32, 80, -16, 96, 
	-48, 96, -32, 96, -16, 112, -48, 112, -32, 112, -16, -48, -160, -48, -144, -48, 
	-128, -48, -112, -48, -96, -48, -80, -48, -64, -48, -48, -48, -32, -48, -16, -32, 
	-160, -32, -144, -32, -128, -32, -112, -32, -96, -32, -80, -32, -64, -32, -48, -32, 
	-32, -32, -16, -16, -160, -16, -144, -16, -128, -16, -112, -16, -96, -16, -80, -16, 
	-64, -16, -48, -16, -32, -16, -16, 0, -160, 0, -144, 0, -128, 0, -112, 0, 
	-96, 0, -80, 0, -64, 0, -48, 0, -32, 0, -16, 16, -160, 16, -144, 16, 
	-128, 16, -112, 16, -96, 16, -80, 16, -64, 16, -48, 16, -32, 16, -16, 32, 
	-160, 32, -144, 32, -128, 32, -112, 32, -96, 32, -80, 32, -64, 32, -48, 32, 
	-32, 32, -16, -72, -128, -72, -112, -72, -96, -72, -80, -72, -64, -72, -48, -72, 
	-32, -72, -16, -56, -128, -56, -112, -56, -96, -56, -80, -56, -64, -56, -48, -56, 
	-32, -56, -16, -40, -128, -40, -112, -40, -96, -40, -80, -40, -64, -40, -48, -40, 
	-32, -40, -16, -24, -128, -24, -112, -24, -96, -24, -80, -24, -64, -24, -48, -24, 
	-32, -24, -16, -8, -128, -8, -112, -8, -96, -8, -80, -8, -64, -8, -48, -8, 
	-32, -8, -16, 8, -128, 8, -112, 8, -96, 8, -80, 8, -64, 8, -48, 8, 
	-32, 8, -16, 24, -128, 24, -112, 24, -96, 24, -80, 24, -64, 24, -48, 24, 
	-32, 24, -16, 40, -128, 40, -112, 40, -96, 40, -80, 40, -64, 40, -48, 40, 
	-32, 40, -16, 56, -128, 56, -112, 56, -96, 56, -80, 56, -64, 56, -48, 56, 
	-32, 56, -16, -104, -16, -88, -16, -72, -32, -72, -16, -56, -32, -56, -16, -40, 
	-80, -40, -64, -40, -48, -40, -32, -40, -16, -24, -80, -24, -64, -24, -48, -24, 
	-32, -24, -16, -8, -80, -8, -64, -8, -48, -8, -32, -8, -16, 8, -80, 8, 
	-64, 8, -48, 8, -32, 8, -16, 24, -80, 24, -64, 24, -48, 24, -32, 24, 
	-16, 40, -64, 40, -48, 40, -32, 40, -16, 56, -32, 56, -16, 72, -16, -64, 
	-48, -64, -32, -64, -16, -48, -48, -48, -32, -48, -16, -32, -64, -32, -48, -32, 
	-32, -32, -16, -16, -112, -16, -96, -16, -80, -16, -64, -16, -48, -16, -32, -16, 
	-16, 0, -112, 0, -96, 0, -80, 0, -64, 0, -48, 0, -32, 0, -16, 16, 
	-80, 16, -64, 16, -48, 16, -32, 16, -16, 32, -48, 32, -32, 32, -16, 48, 
	-48, 48, -32, 48, -16,  
};
static const short data_81c32[] = {
	0x0050, 0xFFE5, 0x0051, 0xFFE5, 0x0052, 0xFFE5, 0x0053, 0xFFE5, 
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
	0x0000, 0x0000, 0x0000, 0x0000, 0xFF80, 0xFFF0, 0xFF90, 0xFFF0, 
	0xFFA0, 0xFFF0, 0xFFB0, 0xFFF0, 0xFFC0, 0xFFF0, 0xFFD0, 0xFFF0, 
	0xFFE0, 0xFFF0, 0xFFF0, 0xFFF0, 0x0000, 0xFFF0, 0x0010, 0xFFF0, 
	0x0020, 0xFFF0, 0x0030, 0xFFF0, 0x0040, 0xFFF0, 0x0050, 0xFFF0, 
	0x0060, 0xFFF0, 0x006F, 0xFFF0, 0x0080, 0xFFF0, 0x0090, 0xFFF0, 
	0x00A0, 0xFFF0, 0x00B0, 0xFFF0, 0xFFB0, 0xFFA8, 0xFFB0, 0xFFB8, 
	0xFFC0, 0xFFA8, 0xFFC0, 0xFFB8, 0x0030, 0xFFA8, 0x0030, 0xFFB8, 
	0x0040, 0xFFA8, 0x0040, 0xFFB8, 0xFFB8, 0xFFE8, 0xFFB8, 0xFFF8, 
	0xFFC8, 0xFFE8, 0xFFC8, 0xFFF8, 0xFFB0, 0xFF98, 0xFFB0, 0xFFA8, 
	0xFFC0, 0xFF98, 0xFFC0, 0xFFA8, 0x0030, 0xFFA8, 0x0030, 0xFFB8, 
	0x0040, 0xFFA8, 0x0040, 0xFFB8, 0xFFB0, 0xFFC0, 0xFFB0, 0xFFD0, 
	0xFFC0, 0xFFC0, 0xFFC0, 0xFFD0, 0x0030, 0xFFD0, 0x0030, 0xFFE0, 
	0x0040, 0xFFD0, 0x0040, 0xFFE0, 0xFFB0, 0xFFE8, 0xFFB0, 0xFFF8, 
	0xFFC0, 0xFFE8, 0xFFC0, 0xFFF8, 0xFFB0, 0x0028, 0xFFB0, 0x0038, 
	0xFFC0, 0x0028, 0xFFC0, 0x0038, 0x0030, 0x0028, 0x0030, 0x0038, 
	0x0040, 0x0028, 0x0040, 0x0038, 0xFFB0, 0x0050, 0xFFB0, 0x0060, 
	0xFFC0, 0x0050, 0xFFC0, 0x0060, 0x0030, 0x0050, 0x0030, 0x0060, 
	0x0040, 0x0050, 0x0040, 0x0060, 0xFFB0, 0xFF98, 0xFFB0, 0xFFA8, 
	0xFFC0, 0xFF98, 0xFFC0, 0xFFA8, 0x0030, 0xFFA8, 0x0030, 0xFFB8, 
	0x0040, 0xFFA8, 0x0040, 0xFFB8, 0xFFB0, 0xFFC0, 0xFFB0, 0xFFD0, 
	0xFFC0, 0xFFC0, 0xFFC0, 0xFFD0, 0x0030, 0xFFD0, 0x0030, 0xFFE0, 
	0x0040, 0xFFD0, 0x0040, 0xFFE0, 0xFFB0, 0xFFE8, 0xFFB0, 0xFFF8, 
	0xFFC0, 0xFFE8, 0xFFC0, 0xFFF8, 0x0030, 0xFFF8, 0x0030, 0x0008, 
	0x0040, 0xFFF8, 0x0040, 0x0008, 0xFFB0, 0x0010, 0xFFB0, 0x0020, 
	0xFFC0, 0x0010, 0xFFC0, 0x0020, 0x0030, 0x0020, 0x0030, 0x0030, 
	0x0040, 0x0020, 0x0040, 0x0030, 0xFFB0, 0x0038, 0xFFB0, 0x0048, 
	0xFFC0, 0x0038, 0xFFC0, 0x0048, 0x0030, 0x0048, 0x0030, 0x0058, 
	0x0040, 0x0048, 0x0040, 0x0058, 0xFFB0, 0xFFB0, 0xFFB0, 0xFFC0, 
	0xFFC0, 0xFFB0, 0xFFC0, 0xFFC0, 0x0030, 0xFFB0, 0x0030, 0xFFC0, 
	0x0040, 0xFFB0, 0x0040, 0xFFC0, 0xFFB0, 0xFFF0, 0xFFB0, 0x0000, 
	0xFFC0, 0xFFF0, 0xFFC0, 0x0000, 0x0030, 0x0000, 0x0030, 0x0010, 
	0x0040, 0x0000, 0x0040, 0x0010, 0xFFB0, 0x0018, 0xFFB0, 0x0028, 
	0xFFC0, 0x0018, 0xFFC0, 0x0028, 0x0030, 0x0028, 0x0030, 0x0038, 
	0x0040, 0x0028, 0x0040, 0x0038, 0xFFB0, 0x0040, 0xFFB0, 0x0050, 
	0xFFC0, 0x0040, 0xFFC0, 0x0050, 0x0030, 0x0050, 0x0030, 0x0060, 
	0x0040, 0x0050, 0x0040, 0x0060, 0xFFA8, 0xFFF0, 0xFFB8, 0xFFF0, 
	0xFFC8, 0xFFF0, 0xFFD8, 0xFFF0, 0xFFE8, 0xFFF0, 0xFFF8, 0xFFF0, 
	0x0008, 0xFFF0, 0x0018, 0xFFF0, 0x0028, 0xFFF0, 0x0038, 0xFFF0, 
	0x0048, 0xFFF0, 0xFF20, 0xFF80, 0xFF20, 0xFFA0, 0xFF20, 0xFFB0, 
	0xFF20, 0xFFD0, 0xFF20, 0xFFF0, 0xFF20, 0x0000, 0xFF20, 0x0060, 
	0xFF20, 0x0070, 0xFF30, 0xFF80, 0xFF30, 0xFFA0, 0xFF30, 0xFFB0, 
	0xFF30, 0xFFD0, 0xFF30, 0xFFF0, 0xFF30, 0x0000, 0xFF30, 0x0060, 
	0xFF30, 0x0070, 0xFF40, 0xFF80, 0xFF40, 0xFFA0, 0xFF40, 0xFFB0, 
	0xFF40, 0xFFD0, 0xFF40, 0xFFF0, 0xFF40, 0x0000, 0xFF40, 0x0060, 
	0xFF40, 0x0070, 0xFF50, 0xFF80, 0xFF50, 0xFFA0, 0xFF50, 0xFFB0, 
	0xFF50, 0xFFD0, 0xFF50, 0xFFF0, 0xFF50, 0x0000, 0xFF50, 0x0060, 
	0xFF50, 0x0070, 0xFF60, 0xFF80, 0xFF60, 0xFFA0, 0xFF60, 0xFFB0, 
	0xFF60, 0xFFD0, 0xFF60, 0xFFF0, 0xFF60, 0x0000, 0xFF60, 0x0060, 
	0xFF60, 0x0070, 0xFF70, 0xFF80, 0xFF70, 0xFFA0, 0xFF70, 0xFFB0, 
	0xFF70, 0xFFD0, 0xFF70, 0xFFF0, 0xFF70, 0x0000, 0xFF70, 0x0060, 
	0xFF70, 0x0070, 0xFF80, 0xFF80, 0xFF80, 0xFFA0, 0xFF80, 0xFFB0, 
	0xFF80, 0xFFD0, 0xFF80, 0xFFF0, 0xFF80, 0x0000, 0xFF80, 0x0060, 
	0xFF80, 0x0070, 0xFF90, 0xFF80, 0xFF90, 0xFFA0, 0xFF90, 0xFFB0, 
	0xFF90, 0xFFD0, 0xFF90, 0xFFF0, 0xFF90, 0x0000, 0xFF90, 0x0060, 
	0xFF90, 0x0070, 0xFFA0, 0xFF80, 0xFFA0, 0xFFA0, 0xFFA0, 0xFFB0, 
	0xFFA0, 0xFFD0, 0xFFA0, 0xFFF0, 0xFFA0, 0x0000, 0xFFA0, 0x0060, 
	0xFFA0, 0x0070, 0xFFB0, 0xFF80, 0xFFB0, 0xFFA0, 0xFFB0, 0xFFB0, 
	0xFFB0, 0xFFD0, 0xFFB0, 0xFFF0, 0xFFB0, 0x0000, 0xFFB0, 0x0060, 
	0xFFB0, 0x0070, 0xFFC0, 0xFF80, 0xFFC0, 0xFFA0, 0xFFC0, 0xFFB0, 
	0xFFC0, 0xFFD0, 0xFFC0, 0xFFF0, 0xFFC0, 0x0000, 0xFFC0, 0x0060, 
	0xFFC0, 0x0070, 0xFFD0, 0xFF80, 0xFFD0, 0xFFA0, 0xFFD0, 0xFFB0, 
	0xFFD0, 0xFFD0, 0xFFD0, 0xFFF0, 0xFFD0, 0x0000, 0xFFD0, 0x0060, 
	0xFFD0, 0x0070, 0xFFE0, 0xFF80, 0xFFE0, 0xFFA0, 0xFFE0, 0xFFB0, 
	0xFFE0, 0xFFD0, 0xFFE0, 0xFFF0, 0xFFE0, 0x0000, 0xFFE0, 0x0060, 
	0xFFE0, 0x0070, 0xFFF0, 0xFF80, 0xFFF0, 0xFFA0, 0xFFF0, 0xFFB0, 
	0xFFF0, 0xFFD0, 0xFFF0, 0xFFF0, 0xFFF0, 0x0000, 0xFFF0, 0x0060, 
	0xFFF0, 0x0070, 0x0000, 0xFF80, 0x0000, 0xFFA0, 0x0000, 0xFFB0, 
	0x0000, 0xFFD0, 0x0000, 0xFFF0, 0x0000, 0x0000, 0x0000, 0x0060, 
	0x0000, 0x0070, 0x0010, 0xFF80, 0x0010, 0xFFA0, 0x0010, 0xFFB0, 
	0x0010, 0xFFD0, 0x0010, 0xFFF0, 0x0010, 0x0000, 0x0010, 0x0060, 
	0x0010, 0x0070, 0x0020, 0xFF80, 0x0020, 0xFFA0, 0x0020, 0xFFB0, 
	0x0020, 0xFFD0, 0x0020, 0xFFF0, 0x0020, 0x0000, 0x0020, 0x0060, 
	0x0020, 0x0070, 0x0030, 0xFF80, 0x0030, 0xFFA0, 0x0030, 0xFFB0, 
	0x0030, 0xFFD0, 0x0030, 0xFFF0, 0x0030, 0x0000, 0x0030, 0x0060, 
	0x0030, 0x0070, 0x0040, 0xFF80, 0x0040, 0xFFA0, 0x0040, 0xFFB0, 
	0x0040, 0xFFD0, 0x0040, 0xFFF0, 0x0040, 0x0000, 0x0040, 0x0060, 
	0x0040, 0x0070, 0x0050, 0xFF80, 0x0050, 0xFFA0, 0x0050, 0xFFB0, 
	0x0050, 0xFFD0, 0x0050, 0xFFF0, 0x0050, 0x0000, 0x0050, 0x0060, 
	0x0050, 0x0070, 0x0060, 0xFF80, 0x0060, 0xFFA0, 0x0060, 0xFFB0, 
	0x0060, 0xFFD0, 0x0060, 0xFFF0, 0x0060, 0x0000, 0x0060, 0x0060, 
	0x0060, 0x0070, 0x0070, 0xFF80, 0x0070, 0xFFA0, 0x0070, 0xFFB0, 
	0x0070, 0xFFD0, 0x0070, 0xFFF0, 0x0070, 0x0000, 0x0070, 0x0060, 
	0x0070, 0x0070, 0x0080, 0xFF80, 0x0080, 0xFFA0, 0x0080, 0xFFB0, 
	0x0080, 0xFFD0, 0x0080, 0xFFF0, 0x0080, 0x0000, 0x0080, 0x0060, 
	0x0080, 0x0070, 0x0090, 0xFF80, 0x0090, 0xFFA0, 0x0090, 0xFFB0, 
	0x0090, 0xFFD0, 0x0090, 0xFFF0, 0x0090, 0x0000, 0x0090, 0x0060, 
	0x0090, 0x0070, 0x00A0, 0xFF80, 0x00A0, 0xFFA0, 0x00A0, 0xFFB0, 
	0x00A0, 0xFFD0, 0x00A0, 0xFFF0, 0x00A0, 0x0000, 0x00A0, 0x0060, 
	0x00A0, 0x0070, 0x00B0, 0xFF80, 0x00B0, 0xFFA0, 0x00B0, 0xFFB0, 
	0x00B0, 0xFFD0, 0x00B0, 0xFFF0, 0x00B0, 0x0000, 0x00B0, 0x0060, 
	0x00B0, 0x0070, 0x00C0, 0xFF80, 0x00C0, 0xFFA0, 0x00C0, 0xFFB0, 
	0x00C0, 0xFFD0, 0x00C0, 0xFFF0, 0x00C0, 0x0000, 0x00C0, 0x0060, 
	0x00C0, 0x0070, 0x00D0, 0xFF80, 0x00D0, 0xFFA0, 0x00D0, 0xFFB0, 
	0x00D0, 0xFFD0, 0x00D0, 0xFFF0, 0x00D0, 0x0000, 0x00D0, 0x0060, 
	0x00D0, 0x0070, 0xFFF0, 0xFFF0, 0xFFF0, 0x0000, 0x0000, 0xFFF0, 
	0x0000, 0x0000, 0xFFD0, 0xFFF8, 0xFFE0, 0xFFF8, 0xFFF0, 0xFFF8, 
	0x0000, 0xFFF8, 0x0010, 0xFFF8, 0x0020, 0xFFF8, 0xFFE0, 0xFFF0, 
	0xFFE0, 0x0000, 0xFFF0, 0xFFF0, 0xFFF0, 0x0000, 0x0000, 0xFFF0, 
	0x0000, 0x0000, 0x0010, 0xFFF0, 0x0010, 0x0000, 0x0008, 0x2312, 
	0x0008, 0x2350, 0x0008, 0x23D6, 0x0008, 0x24B0, 0x0008, 0x2512, 
	0x0008, 0x25BC, 0x0008, 0x2642, 0x0008, 0x2668, 0x0008, 0x268E, 
	0x0008, 0x26F0, 0x0008, 0x272E, 0x0008, 0x285C, 0x0008, 0x292A, 
	0x0008, 0x2938, 0x0008, 0x29A6, 0x0008, 0x29C0, 
};
/* END DATA */
static void sub_7ef86(Object *obj, const u16 *tilep, const short *offsets, short x, short y, u16 tiles, u16 attr);     /* 7ee58 obj a1, a0, tilep a2, a3, x d0, y d1, tiles d3 */
static void sub_7efd8(Object *obj, const u16 *tilep, const short *offsets, short x, short y, u16 tiles, u16 attr) ;    /* 7ee58 obj a1, a0, tilep a2, a3, x d0, y d1, tiles d3 */
static void sub_7ef2a(Object *obj, const u16 *tilep, const short *offsets, short x, short y, unsigned short tiles, unsigned short attr);    /* 7ee58 obj a1, a0, tilep a2, a3, x d0, y d1, tiles d3 */
static void sub_7f244 (Object *obj, const u16 tiles_in_image, const Image *image, short x, short y );
static void _draw_sprite(Object *obj, const u16 *tilep, const short *offsets, short x, short y, unsigned short tiles, short attr);
static void sub_7ee58(Object *obj, const u16 *tilep, const short *offsets, short x, short y, unsigned short tiles, unsigned short attr);     /* 7ee58 obj a1, a0, tilep a2, a3, x d0, y d1, tiles d3 */
static void swap_object_buffer(void);	/* 7e610 */
	
static void spritelib_drawall(void);		/* 7e638 */
static void _DSDrawPlayers(void);

inline static void draw_playersprite(Player *ply) ;
static void _DSDrawShadows(void);	/* 7e726 playe shadows */
 
void draw_layer1_grp1(void) {
    while (g.Layer1Grp1Cnt) {
        drawsprite(g.x8e16[--g.Layer1Grp1Cnt]);    
    }
}       
void draw_layer1_grp2(void) {
    while (g.Layer1Grp2Cnt) {
        drawsprite(g.x8e26[--g.Layer1Grp2Cnt]);
    }
}
void draw_layer3_grp1(void) {
    while (g.Layer3Grp1Cnt) {
        drawsprite(g.x8efe[--g.Layer3Grp1Cnt]);
    }
}
void draw_layer3_grp2(void) {
    while (g.Layer3Grp2Cnt) {
        drawsprite(g.x8f76[--g.Layer3Grp2Cnt]);
    }
}
void draw_layer3_grp3(void) {
    while (g.Layer3Grp3Cnt) {
        drawsprite(g.x8fee[--g.Layer3Grp3Cnt]);
    }
}
void draw_layer3_grp4(void) {
    while (g.Layer3Grp4Cnt) {
        drawsprite(g.x9066[--g.Layer3Grp4Cnt]);
    }
}
void draw_layer3_grp5(void) {
    while (g.Layer3Grp5Cnt) {
        drawsprite(g.x90de[--g.Layer3Grp5Cnt]);
    }
}
void draw_layer3_grp6(void) {
    while (g.Layer3Grp6Cnt) {
        drawsprite(g.x9156[--g.Layer3Grp6Cnt]);
    }
}
void draw_layer2_grp1(void) {
    while(g.Layer2Grp1Cnt) {
        drawsprite(g.x8e46[--g.Layer2Grp1Cnt]);
    }
}
void draw_layer2_grp2(void) {
    while (g.Layer2Grp2Cnt) {
        drawsprite(g.x8e66[--g.Layer2Grp2Cnt]);
    }
}
void draw_layer2_grp3(void) {
    while (g.Layer2Grp3Cnt) {
        drawsprite(g.x8e86[--g.Layer2Grp3Cnt]);
    }
}

static void ds_draw_hiragana(void) {		// 7e884
	const static u16 data_7e900[] = {
		0, 0x8197, 0x81b7, 0x8199, 0x819f, 0x81bf, 0x81cf, 0x817f, 0x81af,
		0x818f, 0x817e, 0x816f, 0x816e
	};
	
	/*
	0x8180: a ka sa ta na ha ma ya ra wa
	0x8190: i ki shi chi ni hi mi ya ri yo
	0x81a0: u ku su tsu nu fu mu yu ru
	0x81b0: e ke se te ne he me yu re wo
	0x81c0: o ko so to no ho mo yo ro n
	*/

	u16 *img;
	short count;
	
	if (g.x5dfe.exists) {
		img = (u16 *)g.x5dfe.ActionScript;
		if (*img < g.ObjTileBudget) {
			g.ObjTileBudget -= *img;
			g_tilecount -= *img;
			count = *img -1;
			
			// todo hiragana draw loop
			
		}
	}
}
static void sub_7e6b8(void) {	// cousin of spritelib_drawall
	draw_layer3_grp2();
	draw_layer3_grp1();
	draw_layer2_grp2();
	draw_layer3_grp3();
	draw_layer3_grp4();
	ds_draw_hiragana();
}


/* The CPS uses double-buffered tile maps. Since RedHammer takes care of all that, we patch it out here.
 * Haven't found any side effects so far */

/* 7e4dc 910300 160 */
void sub_7e4dc(void) {		
	debughook(5);
//	if(g.DisableDblBuf == 0 && g.CPS.ObjBase & 0x80) {
//		OBJ_CURSOR_SET( DSObjCur_g, 0x20c0 ); /* 0x918300 */
//	} else {
		OBJ_CURSOR_SET( DSObjCur_g, 0x0060 ); /* 0x910300 */
//	}
	g_tilecount=160;	// first 96 reserved
	g.ObjTileBudget = g_tilecount;
	spritelib_drawall();
	WRITE_END_TAG;
	swap_object_buffer();
}		

/* 7e510 910280 176 */
void DSDrawAll_176(void) {		
    debughook(5);
//	if(g.DisableDblBuf == 0 && g.CPS.ObjBase & 0x80) {
//		OBJ_CURSOR_SET( DSObjCur_g, 0x1050 ); /* 0x918280 */
//	} else {
		OBJ_CURSOR_SET( DSObjCur_g, 0x0050 ); /* 0x910280 */
//	}
	g_tilecount=176;
	g.ObjTileBudget = g_tilecount;
	spritelib_drawall();
	WRITE_END_TAG;
	swap_object_buffer();
}

/* 7e544 9100d0 230 not used yet */
void sub_7e544(void) {			
	debughook(5);
//	if(g.DisableDblBuf == 0 && g.CPS.ObjBase & 0x80) {
//		OBJ_CURSOR_SET( DSObjCur_g, 0x101a ); /* 0x9180d0 */
//	} else {
		OBJ_CURSOR_SET( DSObjCur_g, 0x001a ); /* 0x9100d0 */
//	}
	g_tilecount=230;
	g.ObjTileBudget = g_tilecount;
	spritelib_drawall();
    WRITE_END_TAG;
	swap_object_buffer();
}

/* 7e578 184 */
void DSDrawAllMain(void) {		/* 7e578 library, main draw all sprite routine */
	/* was: draw_all_sprites() */
	
	debughook(5);
//	if(g.DisableDblBuf == 0 && g.CPS.ObjBase & 0x80) {
//		OBJ_CURSOR_SET( DSObjCur_g, 0x1090 ); 
//	} else {
		OBJ_CURSOR_SET( DSObjCur_g, 0x48 );		//0x910240
//	}
	g_tilecount = 184;
	g.ObjTileBudget = g_tilecount;
	spritelib_drawall();
    WRITE_END_TAG;
	swap_object_buffer();
}

/* 7e5ac 910240 184 draws hiragana instead of all sprites */
void DSDrawAll_Hira(void) {		/* library */	
	debughook(5);
//	if(g.DisableDblBuf == 0 && g.CPS.ObjBase & 0x80) {
//		OBJ_CURSOR_SET( DSObjCur_g, 0x1090 ); /* 0x918240 */
//	} else {
		OBJ_CURSOR_SET( DSObjCur_g, 0x48 ); /* 0x910240 */
//	}
	g_tilecount=184;		// first 72 reserved
	g.ObjTileBudget=g_tilecount;
	sub_7e6b8();
    WRITE_END_TAG;
	swap_object_buffer();
}

static void swap_object_buffer(void) {	/* 7e610 */
	if (g.DisableDblBuf) {
		g.CPS.ObjBase = 0x9100;	/* flip bit in DblBuffer emu */
		return;
	}
	g.CPS.ObjBase ^= 0x80;	/* swap buffers */
}
static void _DSDrawUpperLayers(void) {
	draw_layer3_grp2();
	draw_layer3_grp1();
	draw_layer1_grp1();
	_DSDrawPlayers();
}	
static void _DSDrawLowerLayers(void) {
	draw_layer3_grp3();
	draw_layer2_grp1();
	draw_layer3_grp4();
}

static void spritelib_drawall(void) {		/* 7e638 */
	switch (g.CurrentStage) {
		case STAGE_BONUS_BARRELS:		
			_DSDrawUpperLayers();
			draw_layer2_grp2();
			_DSDrawShadows();
			_DSDrawLowerLayers();
			break;
		case STAGE_BONUS_DRUMS:
			_DSDrawUpperLayers();
			_DSDrawShadows();
			draw_layer2_grp3();		/* special one */
			draw_layer2_grp2();
			_DSDrawLowerLayers();
			break;
		default:
			_DSDrawUpperLayers();
			_DSDrawShadows();
			draw_layer2_grp2();
			_DSDrawLowerLayers();
			break;
	}
}

const short *sub_7f224(u16 dim) {  /* 7f224 lookup for an array of tile coords pairs of short */
	if(dim >= 128) {
		return &data_81c32[ (data_81c1e[dim - 128] - 0x14) / 2];
	} else {
		return &data_7f7f2[data_7f6f2[dim]];
	}
}

static void draw_player_extrasprite(Player *ply) {	/* 7ed04 */
	short coordpair[2];
	const short *coordpointer;
	u16 attr;
	short tiles_in_image;
	if (ply->exists && ply->flag1 && ply->ExtraSpriteEna && ply->VegaHasClaw) {
		g.DSOffsetX = -ply->Draw_OffsetX;
		g.DSOffsetY = -ply->Draw_OffsetY;
		sprite_coords((Object *)ply, coordpair);
		
		if (!(tiles_in_image = ply->Image2->TileCount))   { return; }
		if (g.ObjTileBudget < tiles_in_image)			 { return; }
		g.ObjTileBudget -= tiles_in_image;
		attr = ply->Image2->Attr;
		if (attr & 0xff00) {
			/* tiled sprite */
			tiles_in_image = 1;
		}
		coordpointer=sub_7f224(ply->Image2->Dimensions);
		if (ply->ActionScript->FlipBits & 0x3) {
			attr ^= ((ply->ActionScript->FlipBits & 0x3) << 5);
			g.DSOffsetY += (ply->ActionScript->YOffset & 0x00ff);
		}
		g.DSOffsetX -= ply->DSOffsetX;
        attr ^= ((((struct image *)RHCODE16(ply->ActionScript->Image))->Attr & 3) << 5);
        
		_draw_sprite((Object *)ply, ply->Image2->Tiles, coordpointer, coordpair[0], coordpair[1], tiles_in_image, attr);

	}
}

/* draw the players depending on g.PlyDrawOrder, ExtraSprite, and layer3grp[5,6] */

static void _DSDrawPlayers(void) {
	if (g.PlyDrawOrder) {
		draw_layer3_grp6();		/* P2 is drawn first */
		draw_player_extrasprite(PLAYER2);
		draw_playersprite(PLAYER2);
		
		draw_layer3_grp5();
		draw_player_extrasprite(PLAYER1);
		draw_playersprite(PLAYER1);
	} else {
		draw_layer3_grp5();
		draw_player_extrasprite(PLAYER1);
		draw_playersprite(PLAYER1);		
		
		draw_layer3_grp6();
		draw_player_extrasprite(PLAYER2);
		draw_playersprite(PLAYER2);
	}
}

inline static void draw_playersprite(Player *ply) {
	if (ply->exists && ply->flag1) {
		ply->OnGround = ply->Airborne;
		drawsprite((Object *)ply);
	}
    
}

static void _DSDrawShadows(void) {
	/* 7e726 player shadows */
	/* was drawsprites_118a 
	 
	 not to be confused with public DSDrawShadows at 7bc00 
	 */
	

	if (g.Ply1Shadow.exists && g.Ply1Shadow.flag1) {
		drawsprite(&g.Ply1Shadow);
	}
	if (g.Ply2Shadow.exists && g.Ply2Shadow.flag1) {
		drawsprite(&g.Ply2Shadow);
	}
}

void DSDrawShadows(void) {			/* 7bc00 */
    draw_shadow(PLAYER1, &g.Ply1Shadow);
    draw_shadow(PLAYER2, &g.Ply2Shadow);    
}

void draw_shadow(Player *ply, Object *obj) {    //7bc14
    if (obj->mode0 == 0) {
        obj->mode0  = 2;
        obj->exists = TRUE;
        obj->LocalTimer  = ply->Side;		//???
    }
    if (ply->exists == FALSE) {
        obj->exists = FALSE;
        return;
    }
    obj->flag1 = TRUE;
    obj->XPI   = ply->XPI;
    obj->YPI   = 40;  
    obj->Flip  = ply->Flip;
    if (ply->ActionScript->Shadow == 0) {
        obj->exists = FALSE;
    }
    RHSetActionList(obj, RHCODE(0x7bc66), ply->ActionScript->Shadow & 0x7f);
}

#pragma mark DrawSprite et al.

static void _draw_sprite(Object *obj, const u16 *tilep, const short *offsets, 
					  short x, short y, unsigned short tiles, short attr) {
	//7ee2c
	if (obj->Draw1 > 0) {
        attr &= 0xffe0;						/* mask out the palette bits */
        attr |= obj->Draw2.part.integer;    /* OR in a replacement palette */
    }
	
    attr ^= (obj->Flip & 3) << 5;
    if (attr & ATTR_X_FLIP) {	
        sub_7ef2a(obj, tilep, offsets, x, y, tiles, attr);
    } else  if (attr & ATTR_Y_FLIP) {
        sub_7ef86(obj, tilep, offsets, x, y, tiles, attr);
    } else {
        sub_7ee58(obj, tilep, offsets, x, y, tiles, attr);
    }
}

static void sprite_coords(Object *obj, short *coordpair) {		// 7f160
	int temp;
	if (obj->Scroll < 0) {
		coordpair[0]=obj->XPI;
		coordpair[1]=obj->YPI;
	} else {
		switch (obj->Scroll) {
			case SCROLL_2:
				if(obj->ZDepth == 0) {
					coordpair[0]=obj->XPI - gstate_Scroll2.position.x.part.integer;
					coordpair[1]=obj->YPI - gstate_Scroll2.position.y.part.integer;
					if(obj->OnGround) {	/* move with screen wobble */
						coordpair[1] += g.ScreenWobbleMagnitude;
					}
				} else {
					temp = g.x02be[1024 - (obj->ZDepth + 1)];
					coordpair[0]=obj->XPI - (temp-192) - gstate_Scroll2.position.x.part.integer;
					coordpair[1]=obj->YPI - gstate_Scroll2.position.y.part.integer;
				}
				break;
			case SCROLL_1:
				coordpair[0]=obj->XPI - gstate_Scroll1.position.x.part.integer;
				coordpair[1]=obj->YPI - gstate_Scroll1.position.y.part.integer;
				break;
			case SCROLL_3:
				coordpair[0]=obj->XPI - gstate_Scroll3.position.x.part.integer;
				coordpair[1]=obj->YPI - gstate_Scroll3.position.y.part.integer;
				break;
			FATALDEFAULT;
		}
        if(obj->OnGround) {	/* move with screen wobble */
            coordpair[1] += g.ScreenWobbleMagnitude;
        }
	}
    coordpair[0] += 64;
    coordpair[1] ^= 0xff;
    coordpair[1] += 1;
}
void drawsprite(Object *obj) {         /* 7edaa */
    const struct image *image;
    u16 tiles_in_image;
    int attr;
    const short *coordlist;
    short coordpair[2];
    

    //if(g.Debug && g.JPCost & JP_DBGSLEEP) {
    //	dbg_draw_hitboxes((Player *)obj);
    //}
    sprite_coords(obj, coordpair);	/* set coords in d0 and d1 to follow scroll X */

    image = (const struct image *)RHCODE(RHSwapLong(obj->ActionScript->Image));

    if (image == NULL) {
        return;
    }
    tiles_in_image = RHSwapWord(image->TileCount);
    
    if (tiles_in_image == 0) { return; }
    if (tiles_in_image & IMAGE_ATTR) {
        sub_7f244(obj, tiles_in_image, image, coordpair[0], coordpair[1]);
        /* tiles are in tile,attr pairs */
        return;
    }
    if (tiles_in_image > g.ObjTileBudget) {
        //printf("Over Tile Budget!\n");
        return;
    }
    g.ObjTileBudget -= tiles_in_image;
    attr = RHSwapWord(image->Attr);
    
    /* this used to be after the Block image check but we do the Block sprites in software now */
    g_tilecount -= tiles_in_image;
    
    if (attr & 0xff00) {
        tiles_in_image = 1;
    }
    coordlist = sub_7f224(RHSwapWord(image->Dimensions));        /* set a3 from Image->Dimensions */
    
    g.DSOffsetX = RHSwapWord(image->OffsetX);
    g.DSOffsetY = RHSwapWord(image->OffsetY);
    
    if (obj->ActionScript->FlipBits & 0x3) {
        attr ^= ((obj->ActionScript->FlipBits & 0x3) << 5);      /* apply flips */
        g.DSOffsetY += obj->ActionScript->YOffset;
    }
    g.DSOffsetX -= obj->DSOffsetX;   /* ply->x0052 */
    
    if (obj->Sel == 2 && obj->Sel == 7) {
        DEBUG_GEN("Sel 0x%x SubSel 0x%x dim 0x%x tiles %d\n", obj->Sel, obj->SubSel, image->Dimensions, tiles_in_image);
    }
    _draw_sprite(obj, image->Tiles, coordlist, coordpair[0], coordpair[1], tiles_in_image, attr);
}
/*!
 @abstract draw an object
 @param obj The object to draw (%a1)
 @param tilep Tilemap to draw into (%a0)
 @param offsets Sprite tile position array (%a2)
 @param x X coordinate (%d0)
 @param y Y coordinate (%d1)
 @param tiles number of tiles to draw (%d3)
 @param attr attributes to draw tiles with (%??)
 @discussion sf2ua:0x7eea2
 */
static void sub_7eea2(Object *obj, const u16 *tilep, const short *offsets, short x, short y, 
					  unsigned short tiles, unsigned short attr) {     
	/* 7eea2 obj a1, a0, tilep a2, a3, x d0, y d1, tiles d3 */

	int sx,sy;
	const short *transform = data_trig[obj->Step];
	while (tiles > 0) {
		if (RHSwapWord(*tilep) != 0) {
			g.x8b0e = (((offsets[0]+8) * obj->Draw2.full) / 16) + offsets[0] + 8;
			sx = ((transform[0] * g.x8b0e) / 256) + x;
			sy = ((transform[1] * g.x8b0e) / 256) + y;
			g.x8b10 = (((offsets[1]+8) * obj->Draw2.full) / 16) + offsets[1] + 8;
			sx +=((transform[2] * g.x8b10) / 256) - 8;
			sy +=((transform[3] * g.x8b10) / 256) - 8;
			OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, RHSwapWord(*tilep), attr);
			OBJ_CURSOR_BUMP(DSObjCur_g);
			
		}
		offsets += 2;
		++tilep;

		--tiles;
	}
}

static void sub_7f244 (Object *obj, u16 tiles_in_image, const Image *image, short x, short y ) {
	u16 tile;
	u16 attr;
	const u16 *tilep;
	short i, sx, sy, sysin, sycos, sxsin, sxcos;
	const short *offsets;
	
	tiles_in_image &= (IMAGE_ATTR - 1);
	if (g.ObjTileBudget < tiles_in_image)		{ return; }
	g.ObjTileBudget -= tiles_in_image;
	g_tilecount -= tiles_in_image;
	attr = RHSwapWord(image->Attr) & 0xe0;	/* Only flips */
	offsets = sub_7f224(RHSwapWord(image->Dimensions));
	g.DSOffsetX = RHSwapWord(image->OffsetX);
	g.DSOffsetY = RHSwapWord(image->OffsetY);
	if (obj->ActionScript->FlipBits & 0x3) {
        attr ^= (obj->ActionScript->FlipBits & 0x3) << 5;      /* apply flips */
        g.DSOffsetY += (obj->ActionScript->YOffset & 0xff);
    }
	g.DSOffsetX += obj->DSOffsetX;
	if (obj->Draw1 > 0) {
		attr &= 0xffe0;
		attr |= obj->Draw2.part.integer;
	}
	tilep = &image->Tiles[0];
	
	attr ^= ((obj->Flip & 0x3) << 5);
	if (attr & ATTR_X_FLIP) {
		/* 7f3a2() */
		if (attr & ATTR_Y_FLIP) {
			/* 7f446 utter rascal, both flips */
			x += g.DSOffsetX;
			y -= g.DSOffsetY;
			for (i=0; i<tiles_in_image; i++) {
				if (RHSwapWord(*tilep) == 0) {
					tilep+=2;
					offsets+=2;
					continue;
				}
				sx = x  - (*offsets++) - 16;
				if (sx<0 || sx > 0x200) {
					offsets++;
					tilep += 2;
					g.ObjTileBudget++;
					g_tilecount++;
					continue;
				}
				sy = (y - *offsets++ -16) & 0x1ff;
				tile = RHSwapWord(*tilep++);
				OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr ^ (RHSwapWord(*tilep++)));
				OBJ_CURSOR_BUMP(DSObjCur_g);
			}
		} else {
			x += g.DSOffsetX;
			y += g.DSOffsetY;
			/* 7f3b2 */
			for (i=0; i<tiles_in_image; i++) {
				if (RHSwapWord(*tilep) == 0) {
					tilep+=2;
					offsets+=2;
					continue;
				}
				sx = x - (*offsets++) - 16;
				if (sx<0 || sx > 0x200) {
					offsets++;
					tilep += 2;
					g.ObjTileBudget++;
					g_tilecount++;
					continue;
				}
				sy = (y + *offsets++) & 0x1ff;
				tile = RHSwapWord(*tilep++);
				OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr ^ (RHSwapWord(*tilep++)));
				OBJ_CURSOR_BUMP(DSObjCur_g);
			}
		}
	}
	else if (attr & ATTR_Y_FLIP) {
		/* sub_7f3f8(); */
		x -= g.DSOffsetX;
		y -= g.DSOffsetY;
		
		for (i=0; i < tiles_in_image; i++) {
			if (RHSwapWord(*tilep) == 0) {
				tilep += 2;
				offsets += 2;
				continue;
			}
			sx = x + *offsets++;
			if(sx < 0 || sx > 0x200) {
				offsets++;
				tilep+=2;
				g.ObjTileBudget++;
				g_tilecount++;
				continue;
			}
			sy = (y + *offsets++) & 0x1ff;
			tile = RHSwapWord(*tilep++);
			OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr ^ (RHSwapWord(*tilep++)));
			OBJ_CURSOR_BUMP(DSObjCur_g);
		}		
	} else {
		/* 7f2c6 */
		x -= g.DSOffsetX;
		y += g.DSOffsetY;
		if(obj->Draw1 < 0) {
			/* sub_7f316()  sincos */
			for (i=0; i<tiles_in_image; i++) {
				if (RHSwapWord(*tilep) == 0) {
					tilep+=2;
					offsets+=2;
				} else {
					sx = (((*offsets) + 8) * obj->Draw2.full / 16)+ *offsets + 8;
					/* g.x8b0e g.8b10 unused */
					sxcos = x + (sx * data_trig[obj->Step][0] / 256);		/* cos */
					sxsin = y + (sx * data_trig[obj->Step][1] / 256);
					offsets++;
					sy = (((*offsets) + 8) * obj->Draw2.full / 16)+ *offsets + 8;
					sysin = sy * data_trig[obj->Step][2] / 256;
					sycos = sx * data_trig[obj->Step][3] / 256;
					
					tile = RHSwapWord(*tilep++);
					OBJECT_DRAW_SINGLE(DSObjCur_g, sxcos + sysin - 8, sxsin + sycos - 8,
									   tile, attr ^ (RHSwapWord(*tilep++)));
					OBJ_CURSOR_BUMP(DSObjCur_g);
				}
			}
			return;
		}
		for (i = 0; i < tiles_in_image; i++) {
			if (RHSwapWord(*tilep) == 0) {
				tilep   += 2;
				offsets += 2;
			} else {
				sx = x + offsets[0];
				if(sx < 0 || sx > 0x200) {
					offsets += 2;
					tilep += 2;
					g.ObjTileBudget++;
					g_tilecount++;
					continue;
				}
				sy = (y + offsets[1]) & 0x1ff;
				tile = RHSwapWord(*tilep++);
				OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr ^ (RHSwapWord(*tilep++)));
				OBJ_CURSOR_BUMP(DSObjCur_g);
				offsets += 2;
			}
		}
	}
}

/* 7ee58 Object with No Flip */
static void sub_7ee58(Object *obj, const u16 *tilep, const short *offsets, short x, short y, unsigned short tiles, unsigned short attr) {     /* 7ee58 obj a1, a0, tilep a2, a3, x d0, y d1, tiles d3 */
	short sx, sy;
//	short bx, by;
	u16 tile;
	
    x -= g.DSOffsetX;
	y += g.DSOffsetY;
		
	if (obj->Draw1 < 0) {
		sub_7eea2(obj, tilep, offsets, x, y, tiles, attr);			/* draw with sin,cos effect */
	} else {
		while(tiles) {
			tile = RHSwapWord(*tilep++);

            if(tile == 0) {
				offsets += 2;
				continue;
			}
			sx = x + *offsets;
			offsets++;
			if(sx > 512 || sx < 0) {
				offsets++;
				g.ObjTileBudget++;
				g_tilecount -= 1;
                tiles--;
				continue;
			}
			/* 7ee86 */
			sy = (y + *offsets) & 0x1ff;
			offsets++;
			OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr); 
			OBJ_CURSOR_BUMP(DSObjCur_g);
			--tiles;
		}	// while(tiles)
	}
}

/* 7ef2a object with X Flip */
static void sub_7ef2a(Object *obj, const u16 *tilep, const short *offsets, 
					  short x, short y, u16 tiles, u16 attr) {    
	short sx, sy;
	u16 tile;
	int d5 = ((attr & 0xf00) >> 4) + 16;
	int count = 0;
	if (attr & ATTR_Y_FLIP) {
		sub_7efd8(obj,tilep,offsets,x,y,tiles,attr);
		return;
	}
	
    x += g.DSOffsetX;
	y += g.DSOffsetY;
	
	while(tiles > 0) {
        tile = RHSwapWord(*tilep++);

        if(tile == 0) {
			offsets +=2;
			continue;
		}
		++count;
		sx = x - (*offsets + d5);
		offsets++;
		if(sx > 512 || sx < 0) {
			offsets++;
			g.ObjTileBudget++;
			g_tilecount -= 1;
            tiles--;
			continue;
		}
		sy = (y + *offsets) & 0x1ff;
		offsets++;
		OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr); 
		OBJ_CURSOR_BUMP(DSObjCur_g);
		
		tiles--;
	}
}

/* 7ef86 object with Y Flip */
static void sub_7ef86(Object *obj, const u16 *tilep, const short *offsets, 
					  short x, short y, u16 tiles, u16 attr) {     
	short sx, sy;
	u16 tile;
	int d6 = ((attr & 0xf000) >> 8) + 0x10;
	
	int count = 0;
	
    x -= g.DSOffsetX;
	y -= g.DSOffsetY;
	
	while(tiles > 0) {
        tile = RHSwapWord(*tilep++);

        if(tile == 0) {
			offsets +=2;
			continue;
		}
		
		++count;
		sx= x + *offsets;
		offsets++;
		if(sx < 0 || sx > 512) {
			offsets++;
			g.ObjTileBudget++;
			g_tilecount -= 1;
            tiles--;
			continue;
		}
		sy = (y - *offsets - d6) & 0x1ff;
		offsets++;
		OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr); 
		OBJ_CURSOR_BUMP(DSObjCur_g);
		
		tiles--;
	}
}

/* 7efd8 object with X and Y Flip */
static void sub_7efd8(Object *obj, const u16 *tilep, const short *offsets, 
					  short x, short y, u16 tiles, u16 attr) {

	short sx, sy;
	u16 tile;
	int d6 = ((attr & 0xf000) >> 8) + 0x10;
	int d5 = ((attr & 0xf00) >> 4) + 0x10;

	int count = 0;
	
    x += g.DSOffsetX;
	y -= g.DSOffsetY;
	
	while(tiles>0) {
        tile = RHSwapWord(*tilep++);

        if(tile == 0) {
			offsets +=2;
			continue;
		}
		++count;
		sx= x - *offsets - d5;
		offsets++;
		if(sx < 0 || sx>512) {
			offsets++;
			g.ObjTileBudget++;
			g_tilecount -= 1;
            tiles--;
			continue;
		}
		sy = (y - *offsets - d6) & 0x1ff;
		offsets++;
		OBJECT_DRAW_SINGLE(DSObjCur_g, sx, sy, tile, attr); 
		OBJ_CURSOR_BUMP(DSObjCur_g);
		tiles--;
	}
}


#pragma mark HitBox Debugging

static void dbg_draw_hitbox(Player *ply, short *hb) {		// 7f672
	short x1,x2,y1,y2,d1,d2;
	
	if (g.ObjTileBudget > 3) {
		g.ObjTileBudget -= 4;
	} else { return; }
	
	g_tilecount -= 4;
	d2 = (ply->Flip & 1) ? -hb[0] : hb[0] ;
	d2 += g.hitbox_center_x;
	d1 = (ply->Flip & 2) ? hb[1] : -hb[1] ;
	d1 += g.hitbox_center_y;
	
	x1 = d2 + hb[2] - 16;
	x2 = d2 - hb[2];
	y1 = d1 - hb[3];
	y2 = d1 + hb[3] - 16;
	
	OBJECT_DRAW_SINGLE(DSObjCur_g, x2, y2, 0x801aL, g.DBGPalette);
	OBJ_CURSOR_BUMP(DSObjCur_g);
	OBJECT_DRAW_SINGLE(DSObjCur_g, x2, y1, 0x8019L, g.DBGPalette);
	OBJ_CURSOR_BUMP(DSObjCur_g);
	OBJECT_DRAW_SINGLE(DSObjCur_g, x1, y1, 0x801bL, g.DBGPalette);
	OBJ_CURSOR_BUMP(DSObjCur_g);
	OBJECT_DRAW_SINGLE(DSObjCur_g, x1, y2, 0x801cL, g.DBGPalette);
	OBJ_CURSOR_BUMP(DSObjCur_g);
}
	
static short *sub_7f600(const HitBoxAct *hb) {		//7f600
	if (hb->Shove >= 0) {
		g.GPHitBoxCopy[0] = hb->X;
	} else {
		g.GPHitBoxCopy[0] = -hb->Shove;
	}

	g.GPHitBoxCopy[1] = hb->Y;
	g.GPHitBoxCopy[2] = hb->width;
	g.GPHitBoxCopy[3] = hb->height;
	return g.GPHitBoxCopy;	
}
static short *sub_7f60c(const HitBox *hb) {			//7f60c
	/* sign extend hitbox up to 16 bit */
	g.GPHitBoxCopy[0] = hb->X;
	g.GPHitBoxCopy[1] = hb->Y;
	g.GPHitBoxCopy[2] = hb->width;
	g.GPHitBoxCopy[3] = hb->height;
	return g.GPHitBoxCopy;
}

// Six hitboxes
static void sub_7f4f2(Player *ply, const FBAction *act) {		// 7f4f2 Pushbox
	if (act->HB_Push) {
		g.DBGPalette = 7;
		dbg_draw_hitbox(ply, sub_7f60c(&ply->HitBoxes->push[act->HB_Push]));
	}
}
static void sub_7f51e(Player *ply, const FBAction *act) {		// 7f51e Active Hitbox
	if (act->HB_Active) {
		g.DBGPalette = 7;
		dbg_draw_hitbox(ply, sub_7f600(&ply->HitBoxes->active[act->HB_Active]));
	}
}
static void sub_7f550(Player *ply, const FBAction *act) {		// Weak
	if (act->HB_Weak) {
		g.DBGPalette = 1;
		dbg_draw_hitbox(ply, sub_7f60c(&ply->HitBoxes->weak[act->HB_Weak]));
	}
}
static void sub_7f57c(Player *ply, const FBAction *act) {		// Foot
	if (act->HB_Foot) {
		g.DBGPalette = 7;
		dbg_draw_hitbox(ply, sub_7f60c(&ply->HitBoxes->foot[act->HB_Foot]));
	}
}
static void sub_7f5a8(Player *ply, const FBAction *act) {		// Body
	if (act->HB_Body) {
		g.DBGPalette = 1;
		dbg_draw_hitbox(ply, sub_7f60c(&ply->HitBoxes->body[act->HB_Body]));
	}
}
static void sub_7f5d4(Player *ply, const FBAction *act) {		// Head
	if (act->HB_Head) {
		g.DBGPalette = 0;
		dbg_draw_hitbox(ply, sub_7f60c(&ply->HitBoxes->head[act->HB_Head]));
	}
}

void debug_spr_crosshair(void) { /* 7f63a */
	if (g.ObjTileBudget > 0) {
		g.ObjTileBudget--;
	}
	g_tilecount -= 1;
	if(g.VictimLeftEdge) {
		OBJECT_DRAW_SINGLE(DSObjCur_g, g.hitbox_center_x-8, g.hitbox_center_y-8, 0x801d, 1);
	} else {
		OBJECT_DRAW_SINGLE(DSObjCur_g, g.hitbox_center_x-8, g.hitbox_center_y-8, 0x801d, 0);
	}
	OBJ_CURSOR_BUMP(DSObjCur_g);
}
void dbg_draw_hitboxes(Player *ply) {		// 7f49a 
	short coordpair[2];
	const FBAction *act = ply->ActionScript;
	sprite_coords((Object *)ply, coordpair);
	g.hitbox_center_x = coordpair[0];
	g.hitbox_center_y = coordpair[1] + 512;		// XXX
	
	sub_7f5d4(ply, act);
	sub_7f5a8(ply, act);
	sub_7f57c(ply, act);
	sub_7f550(ply, act);
	sub_7f51e(ply, act);
	sub_7f4f2(ply, act);
	debug_spr_crosshair();
}

