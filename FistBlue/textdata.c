
/* #S#S#S */
const u16 data_8e2cc[] = { 
	0x0098, 
	0x0058, 0x0090, 0x0003, 0x0000, 0x0000, 0x83ab, 0x83ab, 0x83ab, 
};
/* #S#S#S#S#S#S */
const u16 data_8e2de[] = { 
	0x0098, 
	0x0068, 0x0080, 0x0006, 0x0000, 0x0000, 0x8500, 0x8500, 0x8500, 0x8500, 0x8500, 0x8500, 
};
/* #S#S#S#S#S#S#S#S#S */
const u16 data_8e2f6[] = { 
	0x0098, 
	0x0068, 0x0050, 0x0009, 0x0000, 0x0000, 0x86fb, 0x86fb, 0x86fb, 0x86fb, 0x86fb, 0x86fb, 0x86fb, 0x86fb, 0x86fb, 
};
/* #S#S#S */
const u16 data_8e314[] = { 
	0x0098, 
	0x0070, 0x0090, 0x0003, 0x0000, 0x0000, 0x83aa, 0x83aa, 0x83aa, 
};
/* #S#S#S#S#S */
const u16 data_8e328[] = { 
	0x0098, 
	0x0068, 0x0090, 0x0005, 0x0000, 0x0000, 0x81ca, 0x81ca, 0x81ca, 0x81ca, 0x81ca, 
};
/* #S#S#S */
const u16 data_8e340[] = { 
	0x0098, 
	0x0058, 0x0080, 0x0003, 0x0000, 0x0000, 0x819c, 0x819c, 0x819c, 
};
/* #S#S#S#S */
const u16 data_8e352[] = { 
	0x0098, 
	0x0070, 0x0090, 0x0004, 0x0000, 0x0000, 0x83bc, 0x83bc, 0x83bc, 0x83bc, 
};
/* #S#S#S#S#S#S */
const u16 data_8e366[] = { 
	0x0098, 
	0x0048, 0x0080, 0x0006, 0x0000, 0x0000, 0x8117, 0x8117, 0x8117, 0x8117, 0x8117, 0x8117, 
};
/* #S#S#S#S#S */
const u16 data_8e37e[] = { 
	0x00c8, 
	0x0098, 0x0050, 0x0005, 0x0000, 0x0000, 0x7e78, 0x7e78, 0x7e78, 0x7e78, 0x7e78, 
};
/* #S#S#S#S#S#S */
const u16 data_8e396[] = { 
	0x00b0, 
	0x0070, 0x0050, 0x0006, 0x0000, 0x0000, 0x7eb9, 0x7eb9, 0x7eb9, 0x7eb9, 0x7eb9, 0x7eb9, 
};
/* #S#S#S#S#S#S */
const u16 data_8e3ae[] = { 
	0x00b0, 
	0x0070, 0x0050, 0x0006, 0x0000, 0x0000, 0x7ec9, 0x7ec9, 0x7ec9, 0x7ec9, 0x7ec9, 0x7ec9, 
};
/* #S#S */
const u16 data_8e3c6[] = { 
	0x00e0, 
	0x0048, 0x0050, 0x0002, 0x0000, 0x0000, 0x8020, 0x8020, 
};
/* #S#S */
const u16 data_8e3d6[] = { 
	0x00e0, 
	0x0048, 0x0050, 0x0002, 0x0000, 0x0000, 0x8020, 0x8020, 
};
/* #S#S */
const u16 data_8e3e6[] = { 
	0x00e0, 
	0x0048, 0x0050, 0x0002, 0x0000, 0x0000, 0x7e58, 0x7e58, 
};
/* #S#S#S#S#S#S#S#S#S */
const u16 data_8e3f6[] = { 
	0x00b0, 
	0x0048, 0x0050, 0x0009, 0x0000, 0x0000, 0x83a3, 0x83a3, 0x83a3, 0x83a3, 0x83a3, 0x83a3, 0x83a3, 0x83a3, 0x83a3, 
};
/* #S#S#S#S#S#S#S#S#S */
const u16 data_8e414[] = { 
	0x00b0, 
	0x0048, 0x0050, 0x0009, 0x0000, 0x0000, 0x83a7, 0x83a7, 0x83a7, 0x83a7, 0x83a7, 0x83a7, 0x83a7, 0x83a7, 0x83a7, 
};
const u16 *data_8e2ac[]={
	data_8e2cc,
	data_8e2de,
	data_8e2f6,
	data_8e314,
	data_8e328,
	data_8e340,
	data_8e352,
	data_8e366,
	data_8e37e,
	data_8e396,
	data_8e3ae,
	data_8e3c6,
	data_8e3d6,
	data_8e3e6,
	data_8e3f6,
	data_8e414,
};


/*                 WARNING               
 This game is for use in Japan only.  
 Sales, export or operation outside this 
 country may be construed as copyright and
 trademark infringement and is strictly
 prohibited.
 Violator are subject to severe penalties
 and will be prosecuted to the full extent
 of the law. */
const u8 data_8d2ec[] = { 
	0x00, 0x00, 
	0x02, 0x1c, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x24, 0x00, 0x20, 0x54, 0x68, 0x69, 0x73, 0x20, 0x67, 0x61, 0x6d, 0x65, 0x20, 0x69, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x75, 0x73, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x4a, 0x61, 0x70, 0x61, 0x6e, 0x20, 0x6f, 0x6e, 0x6c, 0x79, 0x2e, 0x20, 0x20, 0x2f, 
	0x02, 0x2c, 0x00, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x2c, 0x20, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x6f, 0x72, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x75, 0x74, 0x73, 0x69, 0x64, 0x65, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x2f, 
	0x02, 0x34, 0x00, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x65, 0x64, 0x20, 0x61, 0x73, 0x20, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x20, 0x61, 0x6e, 0x64, 0x2f, 
	0x02, 0x3c, 0x00, 0x74, 0x72, 0x61, 0x64, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x20, 0x69, 0x6e, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x69, 0x73, 0x20, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6c, 0x79, 0x2f, 
	0x02, 0x44, 0x00, 0x70, 0x72, 0x6f, 0x68, 0x69, 0x62, 0x69, 0x74, 0x65, 0x64, 0x2e, 0x2f, 
	0x02, 0x4c, 0x00, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x20, 0x61, 0x72, 0x65, 0x20, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x20, 0x74, 0x6f, 0x20, 0x73, 0x65, 0x76, 0x65, 0x72, 0x65, 0x20, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x69, 0x65, 0x73, 0x2f, 
	0x02, 0x54, 0x00, 0x61, 0x6e, 0x64, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x20, 0x62, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20, 0x66, 0x75, 0x6c, 0x6c, 0x20, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x2f, 
	0x02, 0x5c, 0x00, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x6c, 0x61, 0x77, 0x2e, 0x00, 
};
/*                  WARNING                  
 This game is for use in the United states
 of America, Canada and Mexico only.       
 Sales, export or operation outside these  
 countries may be construed as copyright   
 and trademark infringement and is strictly
 prohibited.
 Violators are subject to severe penalties
 and will be prosecuted to the full extent
 of the law. */
const u8 data_8d43c[] = { 
	0x00, 0x00, 
	0x02, 0x1c, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x24, 0x00, 0x20, 0x54, 0x68, 0x69, 0x73, 0x20, 0x67, 0x61, 0x6d, 0x65, 0x20, 0x69, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x75, 0x73, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x55, 0x6e, 0x69, 0x74, 0x65, 0x64, 0x20, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x2f, 
	0x02, 0x2c, 0x00, 0x6f, 0x66, 0x20, 0x41, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x61, 0x2c, 0x20, 0x43, 0x61, 0x6e, 0x61, 0x64, 0x61, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x4d, 0x65, 0x78, 0x69, 0x63, 0x6f, 0x20, 0x6f, 0x6e, 0x6c, 0x79, 0x2e, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x34, 0x00, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x2c, 0x20, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x6f, 0x72, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x75, 0x74, 0x73, 0x69, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x73, 0x65, 0x20, 0x20, 0x2f, 
	0x02, 0x3c, 0x00, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x65, 0x64, 0x20, 0x61, 0x73, 0x20, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x44, 0x00, 0x61, 0x6e, 0x64, 0x20, 0x74, 0x72, 0x61, 0x64, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x20, 0x69, 0x6e, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x69, 0x73, 0x20, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6c, 0x79, 0x2f, 
	0x02, 0x4c, 0x00, 0x70, 0x72, 0x6f, 0x68, 0x69, 0x62, 0x69, 0x74, 0x65, 0x64, 0x2e, 0x2f, 
	0x02, 0x54, 0x00, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x20, 0x61, 0x72, 0x65, 0x20, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x20, 0x74, 0x6f, 0x20, 0x73, 0x65, 0x76, 0x65, 0x72, 0x65, 0x20, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x69, 0x65, 0x73, 0x2f, 
	0x02, 0x5c, 0x00, 0x61, 0x6e, 0x64, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x20, 0x62, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20, 0x66, 0x75, 0x6c, 0x6c, 0x20, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x2f, 
	0x02, 0x64, 0x00, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x6c, 0x61, 0x77, 0x2e, 0x00, 
};
/*                  WARNING                  
 This game is for use in all countries    
 excluding the United States of America,   
 Canada, Mexico and Japan.             
 Sales, export or operation inside these
 countries may be construed as copyright
 and trademark infringement and is strictly
 prohibited.
 Violators are subject to severe penalties
 and will be prosecuted to the full extent
 of the law. */
const u8 data_8d5ca[] = { 
	0x00, 0x00, 
	0x02, 0x1c, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x24, 0x00, 0x20, 0x54, 0x68, 0x69, 0x73, 0x20, 0x67, 0x61, 0x6d, 0x65, 0x20, 0x69, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x75, 0x73, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x61, 0x6c, 0x6c, 0x20, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x20, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x2c, 0x00, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x74, 0x68, 0x65, 0x20, 0x55, 0x6e, 0x69, 0x74, 0x65, 0x64, 0x20, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x41, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x61, 0x2c, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x34, 0x00, 0x43, 0x61, 0x6e, 0x61, 0x64, 0x61, 0x2c, 0x20, 0x4d, 0x65, 0x78, 0x69, 0x63, 0x6f, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x4a, 0x61, 0x70, 0x61, 0x6e, 0x2e, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2f, 
	0x02, 0x3c, 0x00, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x2c, 0x20, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x6f, 0x72, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x73, 0x65, 0x2f, 
	0x02, 0x44, 0x00, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x65, 0x64, 0x20, 0x61, 0x73, 0x20, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x2f, 
	0x02, 0x4c, 0x00, 0x61, 0x6e, 0x64, 0x20, 0x74, 0x72, 0x61, 0x64, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x20, 0x69, 0x6e, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x69, 0x73, 0x20, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6c, 0x79, 0x2f, 
	0x02, 0x54, 0x00, 0x70, 0x72, 0x6f, 0x68, 0x69, 0x62, 0x69, 0x74, 0x65, 0x64, 0x2e, 0x2f, 
	0x02, 0x5c, 0x00, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x20, 0x61, 0x72, 0x65, 0x20, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x20, 0x74, 0x6f, 0x20, 0x73, 0x65, 0x76, 0x65, 0x72, 0x65, 0x20, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x69, 0x65, 0x73, 0x2f, 
	0x02, 0x64, 0x00, 0x61, 0x6e, 0x64, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x20, 0x62, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x73, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20, 0x66, 0x75, 0x6c, 0x6c, 0x20, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x2f, 
	0x02, 0x6c, 0x00, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x6c, 0x61, 0x77, 0x2e, 0x00, 
};
/* DEMO */
const u8 data_8d77c[] = { 
	0x00, 0x00, 
	0x16, 0x40, 0x00, 0x44, 0x45, 0x4d, 0x4f, 0x00, 
};
/* TITLE. */
const u8 data_8d786[] = { 
	0x00, 0x00, 
	0x15, 0x40, 0x00, 0x54, 0x49, 0x54, 0x4c, 0x45, 0x2e, 0x00, 
};
/* PLAY DEMO. */
const u8 data_8d792[] = { 
	0x00, 0x00, 
	0x11, 0x40, 0x00, 0x50, 0x4c, 0x41, 0x59, 0x20, 0x44, 0x45, 0x4d, 0x4f, 0x2e, 0x00, 
};
/* RANKING */
const u8 data_8d7a2[] = { 
	0x00, 0x00, 
	0x13, 0x40, 0x00, 0x52, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x00, 
};
/* CREDIT= */
const u8 data_8d7b0[] = { 
	0x00, 0x00, 
	0x25, 0x70, 0x00, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x3d, 0x00, 
};
/*   COIN= */
const u8 data_8d7be[] = { 
	0x00, 0x00, 
	0x25, 0x70, 0x00, 0x20, 0x20, 0x43, 0x4f, 0x49, 0x4e, 0x3d, 0x00, 
};
/* INSERT COIN. */
const u8 data_8d7cc[] = { 
	0x00, 0x00, 
	0x12, 0x5c, 0x00, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x54, 0x20, 0x43, 0x4f, 0x49, 0x4e, 0x2e, 0x00, 
};
/*  FREE PLAY.  */
const u8 data_8d7de[] = { 
	0x00, 0x00, 
	0x12, 0x5c, 0x00, 0x20, 0x46, 0x52, 0x45, 0x45, 0x20, 0x50, 0x4c, 0x41, 0x59, 0x2e, 0x20, 0x00, 
};
/*       PUSH 1P START.        */
const u8 data_8d7f0[] = { 
	0x00, 0x00, 
	0x0b, 0x5c, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x50, 0x55, 0x53, 0x48, 0x20, 0x31, 0x50, 0x20, 0x53, 0x54, 0x41, 0x52, 0x54, 0x2e, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x00, 
};
/*    PUSH 1P OR 2P START.   
 */
const u8 data_8d812[] = { 
	0x00, 0x00, 
	0x0b, 0x5c, 0x00, 0x20, 0x20, 0x20, 0x50, 0x55, 0x53, 0x48, 0x20, 0x31, 0x50, 0x20, 0x4f, 0x52, 0x20, 0x32, 0x50, 0x20, 0x53, 0x54, 0x41, 0x52, 0x54, 0x2e, 0x20, 0x20, 0x20, 0x2f, 
	0x0b, 0x60, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x00, 
};
/* INSERT ADDITONAL COIN. */
const u8 data_8d850[] = { 
	0x00, 0x00, 
	0x0c, 0x5c, 0x00, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x54, 0x20, 0x41, 0x44, 0x44, 0x49, 0x54, 0x4f, 0x4e, 0x41, 0x4c, 0x20, 0x43, 0x4f, 0x49, 0x4e, 0x2e, 0x00, 
};
/*      PUSH 1P START OR     
 2P INSERT ADDITIONAL COIN. */
const u8 data_8d86c[] = { 
	0x00, 0x00, 
	0x0b, 0x5c, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x50, 0x55, 0x53, 0x48, 0x20, 0x31, 0x50, 0x20, 0x53, 0x54, 0x41, 0x52, 0x54, 0x20, 0x4f, 0x52, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2f, 
	0x0b, 0x60, 0x00, 0x32, 0x50, 0x20, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x54, 0x20, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x20, 0x43, 0x4f, 0x49, 0x4e, 0x2e, 0x00, 
};
/* PLAYER SELECT
 0
 1
 2
 3
 4
 5
 6
 7 */
const u8 data_8d8aa[] = { 
	0x00, 0x00, 
	0x11, 0x0c, 0x00, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x20, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x2f, 
	0x0c, 0x18, 0x00, 0x30, 0x2f, 
	0x14, 0x18, 0x00, 0x31, 0x2f, 
	0x1c, 0x18, 0x00, 0x32, 0x2f, 
	0x24, 0x18, 0x00, 0x33, 0x2f, 
	0x0c, 0x38, 0x00, 0x34, 0x2f, 
	0x14, 0x38, 0x00, 0x35, 0x2f, 
	0x1c, 0x38, 0x00, 0x36, 0x2f, 
	0x24, 0x38, 0x00, 0x37, 0x00, 
};
/* SANKA. */
const u8 data_8d8e6[] = { 
	0x02, 0x00, 
	0x13, 0x34, 0x0d, 0x53, 0x41, 0x4e, 0x4b, 0x41, 0x2e, 0x00, 
};
/* S T R E E T   F I G H T E R  2
 9 1 0 2 0 6
 J A P A N */
const u8 data_8d8f2[] = { 
	0x00, 0x00, 
	0x0a, 0x34, 0x08, 0x53, 0x20, 0x54, 0x20, 0x52, 0x20, 0x45, 0x20, 0x45, 0x20, 0x54, 0x20, 0x20, 0x20, 0x46, 0x20, 0x49, 0x20, 0x47, 0x20, 0x48, 0x20, 0x54, 0x20, 0x45, 0x20, 0x52, 0x20, 0x20, 0x32, 0x2f, 
	0x0a, 0x3c, 0x08, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x39, 0x20, 0x31, 0x20, 0x30, 0x20, 0x32, 0x20, 0x30, 0x20, 0x36, 0x2f, 
	0x0a, 0x44, 0x08, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x4a, 0x20, 0x41, 0x20, 0x50, 0x20, 0x41, 0x20, 0x4e, 0x00, 
};
/* S T R E E T   F I G H T E R  2
 9 1 0 2 0 6
 U S A */
const u8 data_8d948[] = { 
	0x00, 0x00, 
	0x0a, 0x34, 0x0f, 0x53, 0x20, 0x54, 0x20, 0x52, 0x20, 0x45, 0x20, 0x45, 0x20, 0x54, 0x20, 0x20, 0x20, 0x46, 0x20, 0x49, 0x20, 0x47, 0x20, 0x48, 0x20, 0x54, 0x20, 0x45, 0x20, 0x52, 0x20, 0x20, 0x32, 0x2f, 
	0x0a, 0x3c, 0x0f, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x39, 0x20, 0x31, 0x20, 0x30, 0x20, 0x32, 0x20, 0x30, 0x20, 0x36, 0x2f, 
	0x0a, 0x44, 0x0f, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x55, 0x20, 0x53, 0x20, 0x41, 0x00, 
};
/* S T R E E T   F I G H T E R  2
 9 1 0 2 0 6
 E T C    */
const u8 data_8d99c[] = { 
	0x00, 0x00, 
	0x0a, 0x34, 0x0e, 0x53, 0x20, 0x54, 0x20, 0x52, 0x20, 0x45, 0x20, 0x45, 0x20, 0x54, 0x20, 0x20, 0x20, 0x46, 0x20, 0x49, 0x20, 0x47, 0x20, 0x48, 0x20, 0x54, 0x20, 0x45, 0x20, 0x52, 0x20, 0x20, 0x32, 0x2f, 
	0x0a, 0x3c, 0x0e, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x39, 0x20, 0x31, 0x20, 0x30, 0x20, 0x32, 0x20, 0x30, 0x20, 0x36, 0x2f, 
	0x0a, 0x44, 0x0e, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x45, 0x20, 0x54, 0x20, 0x43, 0x20, 0x20, 0x20, 0x00, 
};
/* BATTLE */
const u8 data_8d9f2[] = { 
	0x02, 0x00, 
	0x13, 0x44, 0x0d, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x00, 
};
/* PLANNER.
 NIN
 AKIMAN
 CHARACTER DESIGNER
 S.Y
 IKUSAN.Z
 SHO
 ERICHAN
 PIGMON
 KATURAGI */
const u8 data_8d9fe[] = { 
	0x02, 0x00, 
	0x20, 0x10, 0x06, 0x50, 0x4c, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x2e, 0x2f, 
	0x1f, 0x18, 0x0e, 0x4e, 0x49, 0x4e, 0x2f, 
	0x23, 0x24, 0x0e, 0x41, 0x4b, 0x49, 0x4d, 0x41, 0x4e, 0x2f, 
	0x1b, 0x30, 0x06, 0x43, 0x48, 0x41, 0x52, 0x41, 0x43, 0x54, 0x45, 0x52, 0x20, 0x44, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x52, 0x2f, 
	0x1f, 0x38, 0x0e, 0x53, 0x2e, 0x59, 0x2f, 
	0x21, 0x40, 0x0e, 0x49, 0x4b, 0x55, 0x53, 0x41, 0x4e, 0x2e, 0x5a, 0x2f, 
	0x1f, 0x4c, 0x0e, 0x53, 0x48, 0x4f, 0x2f, 
	0x22, 0x54, 0x0e, 0x45, 0x52, 0x49, 0x43, 0x48, 0x41, 0x4e, 0x2f, 
	0x1f, 0x60, 0x0e, 0x50, 0x49, 0x47, 0x4d, 0x4f, 0x4e, 0x2f, 
	0x21, 0x68, 0x0e, 0x4b, 0x41, 0x54, 0x55, 0x52, 0x41, 0x47, 0x49, 0x00, 
};
/* MAK!!
 BALLBOY
 Q KYOKU
 TANUKI
 S'TAING
 MANBOU
 KURISAN
 MIKIMAN
 YAMACHAN
 NISSUI */
const u8 data_8da6e[] = { 
	0x02, 0x00, 
	0x1f, 0x0c, 0x0e, 0x4d, 0x41, 0x4b, 0x21, 0x21, 0x2f, 
	0x1f, 0x20, 0x0e, 0x42, 0x41, 0x4c, 0x4c, 0x42, 0x4f, 0x59, 0x2f, 
	0x1f, 0x34, 0x0e, 0x51, 0x20, 0x4b, 0x59, 0x4f, 0x4b, 0x55, 0x2f, 
	0x1f, 0x48, 0x0e, 0x54, 0x41, 0x4e, 0x55, 0x4b, 0x49, 0x2f, 
	0x1f, 0x5c, 0x0e, 0x53, 0x27, 0x54, 0x41, 0x49, 0x4e, 0x47, 0x2f, 
	0x23, 0x14, 0x0e, 0x4d, 0x41, 0x4e, 0x42, 0x4f, 0x55, 0x2f, 
	0x22, 0x28, 0x0e, 0x4b, 0x55, 0x52, 0x49, 0x53, 0x41, 0x4e, 0x2f, 
	0x22, 0x3c, 0x0e, 0x4d, 0x49, 0x4b, 0x49, 0x4d, 0x41, 0x4e, 0x2f, 
	0x21, 0x50, 0x0e, 0x59, 0x41, 0x4d, 0x41, 0x43, 0x48, 0x41, 0x4e, 0x2f, 
	0x24, 0x68, 0x0e, 0x4e, 0x49, 0x53, 0x53, 0x55, 0x49, 0x00, 
};
/* BUPPO
 ZUMMY
 OKAZAKI
 ZIGGY
 NAKAMURA
 PROGRAMMER
 SHIN.
 MACCHAN
 MARINA
 ECCHRO!! */
const u8 data_8dada[] = { 
	0x02, 0x00, 
	0x1f, 0x10, 0x0e, 0x42, 0x55, 0x50, 0x50, 0x4f, 0x2f, 
	0x1f, 0x20, 0x0e, 0x5a, 0x55, 0x4d, 0x4d, 0x59, 0x2f, 
	0x1f, 0x34, 0x0e, 0x4f, 0x4b, 0x41, 0x5a, 0x41, 0x4b, 0x49, 0x2f, 
	0x24, 0x18, 0x0e, 0x5a, 0x49, 0x47, 0x47, 0x59, 0x2f, 
	0x21, 0x28, 0x0e, 0x4e, 0x41, 0x4b, 0x41, 0x4d, 0x55, 0x52, 0x41, 0x2f, 
	0x20, 0x48, 0x06, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x4d, 0x45, 0x52, 0x2f, 
	0x1f, 0x54, 0x0e, 0x53, 0x48, 0x49, 0x4e, 0x2e, 0x2f, 
	0x1f, 0x68, 0x0e, 0x4d, 0x41, 0x43, 0x43, 0x48, 0x41, 0x4e, 0x2f, 
	0x23, 0x5c, 0x0e, 0x4d, 0x41, 0x52, 0x49, 0x4e, 0x41, 0x2f, 
	0x23, 0x70, 0x0e, 0x45, 0x43, 0x43, 0x48, 0x52, 0x4f, 0x21, 0x21, 0x00, 
};
/* SOUND
 SHIMO-P.
 OYAJI-
 OYAJI
 SPECIAL THANKS.
 CBX
 AND
 POO
 KANEKON
 SHONO. */
const u8 data_8db46[] = { 
	0x02, 0x00, 
	0x22, 0x0c, 0x06, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2f, 
	0x1f, 0x14, 0x0e, 0x53, 0x48, 0x49, 0x4d, 0x4f, 0x2d, 0x50, 0x2e, 0x2f, 
	0x23, 0x1c, 0x0e, 0x4f, 0x59, 0x41, 0x4a, 0x49, 0x2d, 0x2f, 
	0x24, 0x20, 0x0e, 0x4f, 0x59, 0x41, 0x4a, 0x49, 0x2f, 
	0x1d, 0x2c, 0x06, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x20, 0x54, 0x48, 0x41, 0x4e, 0x4b, 0x53, 0x2e, 0x2f, 
	0x20, 0x34, 0x0e, 0x43, 0x42, 0x58, 0x2f, 
	0x21, 0x4c, 0x06, 0x41, 0x4e, 0x44, 0x2f, 
	0x25, 0x50, 0x0e, 0x50, 0x4f, 0x4f, 0x2f, 
	0x25, 0x54, 0x0e, 0x4b, 0x41, 0x4e, 0x45, 0x4b, 0x4f, 0x4e, 0x2f, 
	0x25, 0x58, 0x0e, 0x53, 0x48, 0x4f, 0x4e, 0x4f, 0x2e, 0x00, 
};
/* PRESENTED BY   
 */
const u8 data_8dbae[] = { 
	0x02, 0x00, 
	0x0f, 0x30, 0x06, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x20, 0x42, 0x59, 0x20, 0x20, 0x20, 0x2f, 
	0x00, 0x00, 0x40, 0x00, 
};

// see syslib04 for enums
const u8 *data_8d2ac[]={
	data_8d2ec,	data_8d43c,	data_8d5ca,	data_8d77c,		
	data_8d786,	data_8d792, data_8d7a2, data_8d7b0,
	data_8d7be,	data_8d7cc, data_8d7de, data_8d7f0,
	data_8d812,	data_8d850, data_8d86c, data_8d8aa,
	data_8d8e6,	data_8d8f2,	data_8d948,	data_8d99c,	
	data_8d9f2,	data_8d9fe, data_8da6e, data_8dada,
	data_8db46,	data_8dbae,
};

/* 1P */
const u8 data_8dc04[] = { 
	0x00, 0xd0, 
	0x00, 0x04, 0x00, 0xf0, 0x0d, 0x31, 0x50, 0x00, 
};
/* HI */
const u8 data_8dc0e[] = { 
	0x00, 0x80, 
	0x00, 0x88, 0x00, 0xf0, 0x0d, 0x48, 0x49, 0x00, 
};
/* 2P */
const u8 data_8dc18[] = { 
	0x01, 0x18, 
	0x01, 0x0c, 0x00, 0xf0, 0x0d, 0x32, 0x50, 0x00, 
};
/* 1ST */
const u8 data_8dc22[] = { 
	0x00, 0x00, 
	0x00, 0x04, 0x00, 0xf0, 0x00, 0x31, 0x53, 0x54, 0x00, 
};
/* 1ST */
const u8 data_8dc2e[] = { 
	0x00, 0x38, 
	0x01, 0x0c, 0x00, 0xf0, 0x00, 0x31, 0x53, 0x54, 0x00, 
};
/* 2ND */
const u8 data_8dc3a[] = { 
	0x00, 0x00, 
	0x00, 0x04, 0x00, 0xf0, 0x00, 0x32, 0x4e, 0x44, 0x00, 
};
/* 2ND */
const u8 data_8dc46[] = { 
	0x00, 0x38, 
	0x01, 0x0c, 0x00, 0xf0, 0x00, 0x32, 0x4e, 0x44, 0x00, 
};
/* 3RD */
const u8 data_8dc52[] = { 
	0x00, 0x00, 
	0x00, 0x04, 0x00, 0xf0, 0x00, 0x33, 0x52, 0x44, 0x00, 
};
/* 3RD */
const u8 data_8dc5e[] = { 
	0x00, 0x38, 
	0x01, 0x0c, 0x00, 0xf0, 0x00, 0x33, 0x52, 0x44, 0x00, 
};
/* 4TH */
const u8 data_8dc6a[] = { 
	0x00, 0x00, 
	0x00, 0x04, 0x00, 0xf0, 0x00, 0x34, 0x54, 0x48, 0x00, 
};
/* 4TH */
const u8 data_8dc76[] = { 
	0x00, 0x38, 
	0x01, 0x0c, 0x00, 0xf0, 0x00, 0x34, 0x54, 0x48, 0x00, 
};
/* 5TH */
const u8 data_8dc82[] = { 
	0x00, 0x00, 
	0x00, 0x04, 0x00, 0xf0, 0x00, 0x35, 0x54, 0x48, 0x00, 
};
/* 5TH */
const u8 data_8dc8e[] = { 
	0x00, 0x38, 
	0x01, 0x0c, 0x00, 0xf0, 0x00, 0x35, 0x54, 0x48, 0x00, 
};
/* 6TH */
const u8 data_8dc9a[] = { 
	0x00, 0x00, 
	0x00, 0x04, 0x00, 0xf0, 0x00, 0x36, 0x54, 0x48, 0x00, 
};
/* 6TH */
const u8 data_8dca6[] = { 
	0x00, 0x38, 
	0x01, 0x0c, 0x00, 0xf0, 0x00, 0x36, 0x54, 0x48, 0x00, 
};
/* RANKING
 1ST
 2ND
 3RD
 4TH
 5TH  */
const u8 data_8dcb2[] = { 
	0x00, 0x70, 
	0x00, 0x98, 0x00, 0xe0, 0x00, 0x52, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x2f, 
	0x00, 0x40, 0x00, 0xc0, 0x00, 0x31, 0x53, 0x54, 0x2f, 
	0x00, 0x40, 0x00, 0xa0, 0x00, 0x32, 0x4e, 0x44, 0x2f, 
	0x00, 0x40, 0x00, 0x80, 0x00, 0x33, 0x52, 0x44, 0x2f, 
	0x00, 0x40, 0x00, 0x60, 0x00, 0x34, 0x54, 0x48, 0x2f, 
	0x00, 0x40, 0x00, 0x40, 0x00, 0x35, 0x54, 0x48, 0x20, 0x00, 
};
/* YOU MUST DEFEAT SHENG LONG
 TO STAND A CHANCE. */
const u8 data_8dcf0[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x59, 0x4f, 0x55, 0x20, 0x4d, 0x55, 0x53, 0x54, 0x20, 0x44, 0x45, 0x46, 0x45, 0x41, 0x54, 0x20, 0x53, 0x48, 0x45, 0x4e, 0x47, 0x20, 0x4c, 0x4f, 0x4e, 0x47, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x54, 0x4f, 0x20, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x20, 0x41, 0x20, 0x43, 0x48, 0x41, 0x4e, 0x43, 0x45, 0x2e, 0x00, 
};
/* CAN'T YOU DO BETTER THAN THAT? */
const u8 data_8dd2a[] = { 
	0x00, 0xa0, 
	0x00, 0x18, 0x00, 0x40, 0x0d, 0x43, 0x41, 0x4e, 0x27, 0x54, 0x20, 0x59, 0x4f, 0x55, 0x20, 0x44, 0x4f, 0x20, 0x42, 0x45, 0x54, 0x54, 0x45, 0x52, 0x20, 0x54, 0x48, 0x41, 0x4e, 0x20, 0x54, 0x48, 0x41, 0x54, 0x3f, 0x00, 
};
/* SEEING YOU IN ACTION IS A
 JOKE. */
const u8 data_8dd50[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x53, 0x45, 0x45, 0x49, 0x4e, 0x47, 0x20, 0x59, 0x4f, 0x55, 0x20, 0x49, 0x4e, 0x20, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x20, 0x49, 0x53, 0x20, 0x41, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x4a, 0x4f, 0x4b, 0x45, 0x2e, 0x00, 
};
/* ARE YOU MAN ENOUGH TO FIGHT
 WITH ME? */
const u8 data_8dd7c[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x41, 0x52, 0x45, 0x20, 0x59, 0x4f, 0x55, 0x20, 0x4d, 0x41, 0x4e, 0x20, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x20, 0x54, 0x4f, 0x20, 0x46, 0x49, 0x47, 0x48, 0x54, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x57, 0x49, 0x54, 0x48, 0x20, 0x4d, 0x45, 0x3f, 0x00, 
};
/* ATTACK ME IF YOU DARE, I WILL
 CRUSH YOU. */
const u8 data_8ddae[] = { 
	0x00, 0xa0, 
	0x00, 0x1c, 0x00, 0x40, 0x0d, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x20, 0x4d, 0x45, 0x20, 0x49, 0x46, 0x20, 0x59, 0x4f, 0x55, 0x20, 0x44, 0x41, 0x52, 0x45, 0x2c, 0x20, 0x49, 0x20, 0x57, 0x49, 0x4c, 0x4c, 0x2f, 
	0x00, 0x1c, 0x00, 0x30, 0x0d, 0x43, 0x52, 0x55, 0x53, 0x48, 0x20, 0x59, 0x4f, 0x55, 0x2e, 0x00, 
};
/* I'M THE STRONGEST WOMAN
 IN THE WORLD. */
const u8 data_8dde4[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x49, 0x27, 0x4d, 0x20, 0x54, 0x48, 0x45, 0x20, 0x53, 0x54, 0x52, 0x4f, 0x4e, 0x47, 0x45, 0x53, 0x54, 0x20, 0x57, 0x4f, 0x4d, 0x41, 0x4e, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x49, 0x4e, 0x20, 0x54, 0x48, 0x45, 0x20, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x2e, 0x00, 
};
/* MY STRENGTH IS MUCH GREATER
 THAN YOURS. */
const u8 data_8de16[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x4d, 0x59, 0x20, 0x53, 0x54, 0x52, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x20, 0x49, 0x53, 0x20, 0x4d, 0x55, 0x43, 0x48, 0x20, 0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x54, 0x48, 0x41, 0x4e, 0x20, 0x59, 0x4f, 0x55, 0x52, 0x53, 0x2e, 0x00, 
};
/* I WILL MEDITATE AND THEN
 DESTROY YOU. */
const u8 data_8de4a[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x49, 0x20, 0x57, 0x49, 0x4c, 0x4c, 0x20, 0x4d, 0x45, 0x44, 0x49, 0x54, 0x41, 0x54, 0x45, 0x20, 0x41, 0x4e, 0x44, 0x20, 0x54, 0x48, 0x45, 0x4e, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x44, 0x45, 0x53, 0x54, 0x52, 0x4f, 0x59, 0x20, 0x59, 0x4f, 0x55, 0x2e, 0x00, 
};
/* GET LOSE, YOU CAN'T COMPARE
 WITH MY POWERS. */
const u8 data_8de7c[] = { 
	0x00, 0xa0, 
	0x00, 0x20, 0x00, 0x40, 0x0d, 0x47, 0x45, 0x54, 0x20, 0x4c, 0x4f, 0x53, 0x45, 0x2c, 0x20, 0x59, 0x4f, 0x55, 0x20, 0x43, 0x41, 0x4e, 0x27, 0x54, 0x20, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x52, 0x45, 0x2f, 
	0x00, 0x20, 0x00, 0x30, 0x0d, 0x57, 0x49, 0x54, 0x48, 0x20, 0x4d, 0x59, 0x20, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x53, 0x2e, 0x00, 
};
/* YOU ARE NOT A WARRIOR,
 YOU'RE A BEGINNER. */
const u8 data_8deb4[] = { 
	0x00, 0xa0, 
	0x00, 0x40, 0x00, 0x40, 0x0d, 0x59, 0x4f, 0x55, 0x20, 0x41, 0x52, 0x45, 0x20, 0x4e, 0x4f, 0x54, 0x20, 0x41, 0x20, 0x57, 0x41, 0x52, 0x52, 0x49, 0x4f, 0x52, 0x2c, 0x2f, 
	0x00, 0x40, 0x00, 0x30, 0x0d, 0x59, 0x4f, 0x55, 0x27, 0x52, 0x45, 0x20, 0x41, 0x20, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x4e, 0x45, 0x52, 0x2e, 0x00, 
};
/* MY FISTS HAVE YOUR BLOOD
 OF THEM. */
const u8 data_8deea[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x4d, 0x59, 0x20, 0x46, 0x49, 0x53, 0x54, 0x53, 0x20, 0x48, 0x41, 0x56, 0x45, 0x20, 0x59, 0x4f, 0x55, 0x52, 0x20, 0x42, 0x4c, 0x4f, 0x4f, 0x44, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x4f, 0x46, 0x20, 0x54, 0x48, 0x45, 0x4d, 0x2e, 0x00, 
};
/* HANDSOME FIGHTERS NEVER
 LOSE BATTLES. */
const u8 data_8df18[] = { 
	0x00, 0xa0, 
	0x00, 0x30, 0x00, 0x40, 0x0d, 0x48, 0x41, 0x4e, 0x44, 0x53, 0x4f, 0x4d, 0x45, 0x20, 0x46, 0x49, 0x47, 0x48, 0x54, 0x45, 0x52, 0x53, 0x20, 0x4e, 0x45, 0x56, 0x45, 0x52, 0x2f, 
	0x00, 0x30, 0x00, 0x30, 0x0d, 0x4c, 0x4f, 0x53, 0x45, 0x20, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x53, 0x2e, 0x00, 
};
/* CAPCOM. */
const u8 data_8df4a[] = { 
	0x00, 0x70, 
	0x00, 0x9c, 0x00, 0x90, 0x0d, 0x43, 0x41, 0x50, 0x43, 0x4f, 0x4d, 0x2e, 0x00, 
};
/* TEST DATA */
const u8 data_8df5a[] = { 
	0x00, 0x70, 
	0x00, 0x30, 0x00, 0x50, 0x0d, 0x54, 0x45, 0x53, 0x54, 0x20, 0x44, 0x41, 0x54, 0x41, 0x00, 
};

const u8 *data_8dbc4[]={
	data_8dc04,
	data_8dc0e,
	data_8dc18,
	data_8dc22,
	data_8dc2e,
	data_8dc3a,
	data_8dc46,
	data_8dc52,
	data_8dc5e,
	data_8dc6a,
	data_8dc76,
	data_8dc82,
	data_8dc8e,
	data_8dc9a,
	data_8dca6,
	data_8dcb2,
	data_8dcf0,
	data_8dd2a,
	data_8dd50,
	data_8dd7c,
	data_8ddae,
	data_8dde4,
	data_8de16,
	data_8de4a,
	data_8de7c,
	data_8deb4,
	data_8deea,
	data_8df18,
	data_8df4a,
	data_8df5a,
	data_8df5a,
	data_8df5a,
};

