// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		6C6F0DE81C349FAA004CD78E /* redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = 6CF004551C2A01C9008EA965 /* redhammer.c */; };
		6C6F0DE91C349FED004CD78E /* sf2_06.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004581C2A128C008EA965 /* sf2_06.bin */; };
		6C6F0DEA1C349FED004CD78E /* sf2_07.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004591C2A128C008EA965 /* sf2_07.bin */; };
		6C6F0DEB1C349FED004CD78E /* sf2_08.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0045A1C2A128C008EA965 /* sf2_08.bin */; };
		6C6F0DEC1C349FED004CD78E /* sf2_09.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0045B1C2A128C008EA965 /* sf2_09.bin */; };
		6C6F0DED1C349FED004CD78E /* sf2_14.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0045C1C2A128C008EA965 /* sf2_14.bin */; };
		6C6F0DEE1C349FED004CD78E /* sf2_15.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0045D1C2A128C008EA965 /* sf2_15.bin */; };
		6C6F0DEF1C349FED004CD78E /* sf2_16.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0045E1C2A128C008EA965 /* sf2_16.bin */; };
		6C6F0DF01C349FED004CD78E /* sf2_17.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0045F1C2A128C008EA965 /* sf2_17.bin */; };
		6C6F0DF11C349FED004CD78E /* sf2_18.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004601C2A128C008EA965 /* sf2_18.bin */; };
		6C6F0DF21C349FED004CD78E /* sf2_19.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004611C2A128C008EA965 /* sf2_19.bin */; };
		6C6F0DF31C349FED004CD78E /* sf2_24.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004621C2A128C008EA965 /* sf2_24.bin */; };
		6C6F0DF41C349FED004CD78E /* sf2_25.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004631C2A128C008EA965 /* sf2_25.bin */; };
		6C6F0DF51C349FED004CD78E /* sf2_26.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004641C2A128C008EA965 /* sf2_26.bin */; };
		6C6F0DF61C349FED004CD78E /* sf2_27.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004651C2A128C008EA965 /* sf2_27.bin */; };
		6C6F0DF71C349FED004CD78E /* sf2_29a.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004661C2A128C008EA965 /* sf2_29a.bin */; };
		6C6F0DF81C349FED004CD78E /* sf2_36a.bin in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004671C2A128C008EA965 /* sf2_36a.bin */; };
		6C6F0DF91C349FED004CD78E /* sf2u.37a in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004681C2A128C008EA965 /* sf2u.37a */; };
		6C6F0DFA1C349FED004CD78E /* sf2u.35a in Resources */ = {isa = PBXBuildFile; fileRef = 6CF004691C2A128C008EA965 /* sf2u.35a */; };
		6C6F0DFB1C349FED004CD78E /* sf2u.31a in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0046A1C2A128C008EA965 /* sf2u.31a */; };
		6C6F0DFC1C349FED004CD78E /* sf2u.30a in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0046B1C2A128C008EA965 /* sf2u.30a */; };
		6C6F0DFD1C349FED004CD78E /* sf2u.28a in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0046C1C2A128C008EA965 /* sf2u.28a */; };
		6C6F0DFE1C349FED004CD78E /* sf2u.38a in Resources */ = {isa = PBXBuildFile; fileRef = 6CF0046D1C2A128C008EA965 /* sf2u.38a */; };
		6C8C65261C37717200635FDB /* barrels.c in Sources */ = {isa = PBXBuildFile; fileRef = 6C8C65241C37717200635FDB /* barrels.c */; };
		6C8C65271C37717200635FDB /* barrels.c in Sources */ = {isa = PBXBuildFile; fileRef = 6C8C65241C37717200635FDB /* barrels.c */; };
		6C8C65281C37717200635FDB /* barrels.c in Sources */ = {isa = PBXBuildFile; fileRef = 6C8C65241C37717200635FDB /* barrels.c */; };
		6C8C65291C37717200635FDB /* barrels.c in Sources */ = {isa = PBXBuildFile; fileRef = 6C8C65241C37717200635FDB /* barrels.c */; };
		6CF004561C2A01C9008EA965 /* redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = 6CF004551C2A01C9008EA965 /* redhammer.c */; };
		8D11072B0486CEB800E47090 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 089C165CFE840E0CC02AAC07 /* InfoPlist.strings */; };
		8D11072F0486CEB800E47090 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */; };
		B51533F2147EEB4F004A87B0 /* car.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F1147EEB4F004A87B0 /* car.c */; };
		B51533F5147EEBDB004A87B0 /* drums.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F4147EEBDB004A87B0 /* drums.c */; };
		B517DD592543E8E000649292 /* tileshader.vs in Resources */ = {isa = PBXBuildFile; fileRef = B517DD582543E8E000649292 /* tileshader.vs */; };
		B517DD612543E8F300649292 /* tileshader.fs in Resources */ = {isa = PBXBuildFile; fileRef = B517DD602543E8F300649292 /* tileshader.fs */; };
		B519C69F253C039E0084B88D /* glwimp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB87013B2B59A00905DFE /* glwimp.c */; };
		B519C6A6253C03BB0084B88D /* blanka_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC6F156B444F00DC7BAD /* blanka_human.c */; };
		B519C6AD253C03CF0084B88D /* trackball.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7B013B2B38400905DFE /* trackball.c */; };
		B519C714253C27020084B88D /* PointSprite.swift in Sources */ = {isa = PBXBuildFile; fileRef = B519C713253C27020084B88D /* PointSprite.swift */; };
		B519C723253C92EC0084B88D /* gfx_glcore.c in Sources */ = {isa = PBXBuildFile; fileRef = B519C722253C92EC0084B88D /* gfx_glcore.c */; };
		B51B92A12D4DDF8100C243EB /* allroms.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B7EF232D4D7A5C00D7D54F /* allroms.bin */; };
		B51B92A22D4DDF8500C243EB /* sf2gfx.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B39B69157832F200494662 /* sf2gfx.bin */; };
		B51D1C99253BE07100CEB195 /* redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = 6CF004551C2A01C9008EA965 /* redhammer.c */; };
		B51D1CA1253BE0B500CEB195 /* GLUT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7BD13B2B3BE00905DFE /* GLUT.framework */; };
		B51D1CA9253BE0C300CEB195 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7B913B2B3B300905DFE /* OpenGL.framework */; };
		B51D1CB1253BE0E600CEB195 /* barrels.c in Sources */ = {isa = PBXBuildFile; fileRef = 6C8C65241C37717200635FDB /* barrels.c */; };
		B51D1CEF253BE9B800CEB195 /* redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = 6CF004551C2A01C9008EA965 /* redhammer.c */; };
		B51D1CF5253BE9B800CEB195 /* redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = 6CF004551C2A01C9008EA965 /* redhammer.c */; };
		B51D1D1A253BF34100CEB195 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = B51D1D19253BF34100CEB195 /* AppDelegate.swift */; };
		B51D1D1C253BF34300CEB195 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B51D1D1B253BF34300CEB195 /* Assets.xcassets */; };
		B51D1D1F253BF34300CEB195 /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = B51D1D1D253BF34300CEB195 /* MainMenu.xib */; };
		B51D1D2C253BF3A300CEB195 /* MyNSOpenGLView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B51D1D2B253BF3A300CEB195 /* MyNSOpenGLView.swift */; };
		B51D1D64253BF67A00CEB195 /* Matrix4.swift in Sources */ = {isa = PBXBuildFile; fileRef = B51D1D63253BF67A00CEB195 /* Matrix4.swift */; };
		B51D1D6C253BF79100CEB195 /* ShaderProgram.swift in Sources */ = {isa = PBXBuildFile; fileRef = B51D1D6B253BF79100CEB195 /* ShaderProgram.swift */; };
		B51D1D73253BFA3C00CEB195 /* sf2gfx.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B39B69157832F200494662 /* sf2gfx.bin */; };
		B51D1D8D253BFC7700CEB195 /* pointshader.fs in Resources */ = {isa = PBXBuildFile; fileRef = B51D1D85253BFC2500CEB195 /* pointshader.fs */; };
		B51D1D8E253BFC7700CEB195 /* pointshader.vs in Resources */ = {isa = PBXBuildFile; fileRef = B51D1D86253BFC2500CEB195 /* pointshader.vs */; };
		B51D1D96253BFD0D00CEB195 /* Game.swift in Sources */ = {isa = PBXBuildFile; fileRef = B51D1D95253BFD0D00CEB195 /* Game.swift */; };
		B51D1D9D253BFF1300CEB195 /* gfx_glut.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82213B2B57D00905DFE /* gfx_glut.c */; };
		B51D1DA4253BFF1500CEB195 /* gemu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82013B2B57D00905DFE /* gemu.c */; };
		B51D1DAB253BFF1A00CEB195 /* pthreads.c in Sources */ = {isa = PBXBuildFile; fileRef = B5871ED61408E4F4004A45F5 /* pthreads.c */; };
		B51D1DB2253BFF2700CEB195 /* redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = 6CF004551C2A01C9008EA965 /* redhammer.c */; };
		B51D1DB9253BFF5600CEB195 /* collision.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81713B2B57D00905DFE /* collision.c */; };
		B51D1DC0253BFF5900CEB195 /* coll_projectile.c in Sources */ = {isa = PBXBuildFile; fileRef = B55891421485AB2A00BBB0BA /* coll_projectile.c */; };
		B51D1DC7253BFF5C00CEB195 /* coll_bonus.c in Sources */ = {isa = PBXBuildFile; fileRef = B558918E1485B04A00BBB0BA /* coll_bonus.c */; };
		B51D1DCE253BFF7400CEB195 /* car.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F1147EEB4F004A87B0 /* car.c */; };
		B51D1DD5253BFF7700CEB195 /* drums.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F4147EEBDB004A87B0 /* drums.c */; };
		B51D1DDC253BFF7B00CEB195 /* barrels.c in Sources */ = {isa = PBXBuildFile; fileRef = 6C8C65241C37717200635FDB /* barrels.c */; };
		B51D1DE3253BFF7F00CEB195 /* actions.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E413B2B57D00905DFE /* actions.c */; };
		B51D1DEA253BFF8300CEB195 /* actions_198a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E613B2B57D00905DFE /* actions_198a.c */; };
		B51D1DF1253BFF8500CEB195 /* actions_530a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E813B2B57D00905DFE /* actions_530a.c */; };
		B51D1DF8253BFF8C00CEB195 /* reels.c in Sources */ = {isa = PBXBuildFile; fileRef = B5EB38E0149816D5007100DB /* reels.c */; };
		B51D1DFF253BFF8F00CEB195 /* act02_bicycleriders.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0763C155489B5009D301D /* act02_bicycleriders.c */; };
		B51D1E06253BFF9100CEB195 /* act07_elephants.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0764515548B28009D301D /* act07_elephants.c */; };
		B51D1E0D253BFF9400CEB195 /* act17.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC7F155F91F900581C8D /* act17.c */; };
		B51D1E14253BFF9700CEB195 /* act1e_worldflags.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0761515548649009D301D /* act1e_worldflags.c */; };
		B51D1E1B253BFF9A00CEB195 /* act29_wwlogo.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC58155F86F400581C8D /* act29_wwlogo.c */; };
		B51D1E22253BFF9C00CEB195 /* act2e_plane.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B075B21554803E009D301D /* act2e_plane.c */; };
		B51D1E29253BFFA000CEB195 /* act3e_capcomlogos.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */; };
		B51D1E30253BFFA200CEB195 /* act_3f.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC6D155F8C0600581C8D /* act_3f.c */; };
		B51D1E37253BFFA500CEB195 /* act16.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC83155F927300581C8D /* act16.c */; };
		B51D1E3E253BFFAB00CEB195 /* ai.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7ED13B2B57D00905DFE /* ai.c */; };
		B51D1E45253BFFB400CEB195 /* balrog.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E2138B1568950900D96161 /* balrog.c */; };
		B51D1E4C253BFFB700CEB195 /* blanka.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7FD13B2B57D00905DFE /* blanka.c */; };
		B51D1E53253BFFC800CEB195 /* guile_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4C156EE0FF0029278E /* guile_comp.c */; };
		B51D1E54253BFFC800CEB195 /* blanka_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC67156B43C500DC7BAD /* blanka_comp.c */; };
		B51D1E55253BFFC800CEB195 /* sagat.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E21388156894FA00D96161 /* sagat.c */; };
		B51D1E56253BFFC800CEB195 /* chunli_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE6156F1B6E0029278E /* chunli_human.c */; };
		B51D1E57253BFFC800CEB195 /* ehonda.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80613B2B57D00905DFE /* ehonda.c */; };
		B51D1E58253BFFC800CEB195 /* dhalsim.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80313B2B57D00905DFE /* dhalsim.c */; };
		B51D1E59253BFFC800CEB195 /* ryuken_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC8E156B48F600DC7BAD /* ryuken_human.c */; };
		B51D1E5A253BFFC800CEB195 /* chunli_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE4156F1B610029278E /* chunli_comp.c */; };
		B51D1E5B253BFFC800CEB195 /* guile.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80913B2B57D00905DFE /* guile.c */; };
		B51D1E5C253BFFC800CEB195 /* guile_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4A156EE0F50029278E /* guile_human.c */; };
		B51D1E5D253BFFC800CEB195 /* ehonda_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A25156EDE260029278E /* ehonda_comp.c */; };
		B51D1E5E253BFFC800CEB195 /* ryu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81113B2B57D00905DFE /* ryu.c */; };
		B51D1E5F253BFFC800CEB195 /* ryuken_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */; };
		B51D1E60253BFFC800CEB195 /* zangeif.c in Sources */ = {isa = PBXBuildFile; fileRef = B5F335371562FFA4005A4637 /* zangeif.c */; };
		B51D1E61253BFFC800CEB195 /* mbison.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80D13B2B57D00905DFE /* mbison.c */; };
		B51D1E62253BFFC800CEB195 /* ehonda_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A27156EDE3C0029278E /* ehonda_human.c */; };
		B51D1E63253BFFC800CEB195 /* chunli.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80013B2B57D00905DFE /* chunli.c */; };
		B51D1E64253BFFC800CEB195 /* vega.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81413B2B57D00905DFE /* vega.c */; };
		B51D1E6B253BFFD400CEB195 /* fightgfx.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81E13B2B57D00905DFE /* fightgfx.c */; };
		B51D1E6C253BFFD400CEB195 /* computer.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81A13B2B57D00905DFE /* computer.c */; };
		B51D1E6D253BFFD400CEB195 /* coinage.c in Sources */ = {isa = PBXBuildFile; fileRef = B551553414773E8A00A8BBD9 /* coinage.c */; };
		B51D1E6E253BFFD400CEB195 /* effects.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81C13B2B57D00905DFE /* effects.c */; };
		B51D1E6F253BFFD400CEB195 /* game.c in Sources */ = {isa = PBXBuildFile; fileRef = B56A718B1411B0D300DF7F02 /* game.c */; };
		B51D1E70253BFFD400CEB195 /* demo.c in Sources */ = {isa = PBXBuildFile; fileRef = B5FD9F7E1488260A00DDF4D9 /* demo.c */; };
		B51D1E77253C001900CEB195 /* gfxlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82913B2B57D00905DFE /* gfxlib.c */; };
		B51D1E7E253C001C00CEB195 /* gstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82B13B2B57D00905DFE /* gstate.c */; };
		B51D1E85253C001F00CEB195 /* lib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82D13B2B57E00905DFE /* lib.c */; };
		B51D1E8C253C002100CEB195 /* particle.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83013B2B57E00905DFE /* particle.c */; };
		B51D1E93253C002400CEB195 /* player.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83213B2B57E00905DFE /* player.c */; };
		B51D1E9A253C002700CEB195 /* playerselect.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83413B2B57E00905DFE /* playerselect.c */; };
		B51D1EA1253C002A00CEB195 /* playerstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83613B2B57E00905DFE /* playerstate.c */; };
		B51D1EA8253C002C00CEB195 /* projectiles.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83813B2B57E00905DFE /* projectiles.c */; };
		B51D1EAF253C002F00CEB195 /* reactmode.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83A13B2B57E00905DFE /* reactmode.c */; };
		B51D1EB6253C003100CEB195 /* rules.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83C13B2B57E00905DFE /* rules.c */; };
		B51D1EBD253C003500CEB195 /* sm.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84113B2B57E00905DFE /* sm.c */; };
		B51D1EC4253C003800CEB195 /* sound.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84313B2B57E00905DFE /* sound.c */; };
		B51D1ECB253C003A00CEB195 /* sprite.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84513B2B57E00905DFE /* sprite.c */; };
		B51D1ED2253C003D00CEB195 /* task.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84913B2B57E00905DFE /* task.c */; };
		B51D1ED9253C004000CEB195 /* text.c in Sources */ = {isa = PBXBuildFile; fileRef = B5613C3A155F9917001E127A /* text.c */; };
		B51D1EE0253C004F00CEB195 /* endings.c in Sources */ = {isa = PBXBuildFile; fileRef = B550DEA216BBA1BF00983A54 /* endings.c */; };
		B53EBC59155F86F400581C8D /* act29_wwlogo.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC58155F86F400581C8D /* act29_wwlogo.c */; };
		B53EBC62155F8A4600581C8D /* act3e_capcomlogos.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */; };
		B53EBC6E155F8C0600581C8D /* act_3f.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC6D155F8C0600581C8D /* act_3f.c */; };
		B53EBC80155F91F900581C8D /* act17.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC7F155F91F900581C8D /* act17.c */; };
		B53EBC84155F927300581C8D /* act16.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC83155F927300581C8D /* act16.c */; };
		B54FDCC92543AB940092E183 /* scroll_maint.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCC82543AB940092E183 /* scroll_maint.c */; };
		B54FDCCA2543AB950092E183 /* scroll_maint.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCC82543AB940092E183 /* scroll_maint.c */; };
		B54FDCCB2543AB950092E183 /* scroll_maint.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCC82543AB940092E183 /* scroll_maint.c */; };
		B54FDCCC2543AB950092E183 /* scroll_maint.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCC82543AB940092E183 /* scroll_maint.c */; };
		B54FDCCD2543AB950092E183 /* scroll_maint.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCC82543AB940092E183 /* scroll_maint.c */; };
		B54FDCCE2543AB950092E183 /* scroll_maint.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCC82543AB940092E183 /* scroll_maint.c */; };
		B54FDCD72543AD230092E183 /* parallax.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCD62543AD230092E183 /* parallax.c */; };
		B54FDCD82543AD230092E183 /* parallax.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCD62543AD230092E183 /* parallax.c */; };
		B54FDCD92543AD230092E183 /* parallax.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCD62543AD230092E183 /* parallax.c */; };
		B54FDCDA2543AD230092E183 /* parallax.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCD62543AD230092E183 /* parallax.c */; };
		B54FDCDB2543AD230092E183 /* parallax.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCD62543AD230092E183 /* parallax.c */; };
		B54FDCDC2543AD230092E183 /* parallax.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCD62543AD230092E183 /* parallax.c */; };
		B54FDCE52543ADC10092E183 /* scroll_util.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCE42543ADC10092E183 /* scroll_util.c */; };
		B54FDCE62543ADC10092E183 /* scroll_util.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCE42543ADC10092E183 /* scroll_util.c */; };
		B54FDCE72543ADC10092E183 /* scroll_util.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCE42543ADC10092E183 /* scroll_util.c */; };
		B54FDCE82543ADC10092E183 /* scroll_util.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCE42543ADC10092E183 /* scroll_util.c */; };
		B54FDCE92543ADC10092E183 /* scroll_util.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCE42543ADC10092E183 /* scroll_util.c */; };
		B54FDCEA2543ADC10092E183 /* scroll_util.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCE42543ADC10092E183 /* scroll_util.c */; };
		B54FDCF32543ADF20092E183 /* scroll_data.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCF22543ADF20092E183 /* scroll_data.c */; };
		B54FDCF42543ADF20092E183 /* scroll_data.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCF22543ADF20092E183 /* scroll_data.c */; };
		B54FDCF52543ADF20092E183 /* scroll_data.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCF22543ADF20092E183 /* scroll_data.c */; };
		B54FDCF62543ADF20092E183 /* scroll_data.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCF22543ADF20092E183 /* scroll_data.c */; };
		B54FDCF72543ADF20092E183 /* scroll_data.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCF22543ADF20092E183 /* scroll_data.c */; };
		B54FDCF82543ADF20092E183 /* scroll_data.c in Sources */ = {isa = PBXBuildFile; fileRef = B54FDCF22543ADF20092E183 /* scroll_data.c */; };
		B550DEA316BBA1BF00983A54 /* endings.c in Sources */ = {isa = PBXBuildFile; fileRef = B550DEA216BBA1BF00983A54 /* endings.c */; };
		B551553514773E8A00A8BBD9 /* coinage.c in Sources */ = {isa = PBXBuildFile; fileRef = B551553414773E8A00A8BBD9 /* coinage.c */; };
		B55891431485AB2A00BBB0BA /* coll_projectile.c in Sources */ = {isa = PBXBuildFile; fileRef = B55891421485AB2A00BBB0BA /* coll_projectile.c */; };
		B558918F1485B04A00BBB0BA /* coll_bonus.c in Sources */ = {isa = PBXBuildFile; fileRef = B558918E1485B04A00BBB0BA /* coll_bonus.c */; };
		B5613C3B155F9917001E127A /* text.c in Sources */ = {isa = PBXBuildFile; fileRef = B5613C3A155F9917001E127A /* text.c */; };
		B56A718C1411B0D300DF7F02 /* game.c in Sources */ = {isa = PBXBuildFile; fileRef = B56A718B1411B0D300DF7F02 /* game.c */; };
		B56BFC68156B43C500DC7BAD /* blanka_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC67156B43C500DC7BAD /* blanka_comp.c */; };
		B56BFC70156B444F00DC7BAD /* blanka_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC6F156B444F00DC7BAD /* blanka_human.c */; };
		B56BFC8F156B48F600DC7BAD /* ryuken_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC8E156B48F600DC7BAD /* ryuken_human.c */; };
		B56BFC92156B48FD00DC7BAD /* ryuken_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */; };
		B57AE5DE19D7D1610065D862 /* strings.c in Sources */ = {isa = PBXBuildFile; fileRef = B57AE5DC19D7D1610065D862 /* strings.c */; };
		B57AE5DF19D7DFEF0065D862 /* strings.c in Sources */ = {isa = PBXBuildFile; fileRef = B57AE5DC19D7D1610065D862 /* strings.c */; };
		B57E3B46296A7DFE007F6872 /* main.c in Sources */ = {isa = PBXBuildFile; fileRef = B57E3B45296A7DFE007F6872 /* main.c */; };
		B57E3B4B296A7E3D007F6872 /* libcmocka.0.7.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B57E3B4A296A7E3D007F6872 /* libcmocka.0.7.0.dylib */; };
		B57E3B50296A8BE4007F6872 /* test_redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = B57E3B4F296A8BE4007F6872 /* test_redhammer.c */; };
		B57E3B53296A8F9A007F6872 /* test_sf2types.c in Sources */ = {isa = PBXBuildFile; fileRef = B57E3B52296A8F9A007F6872 /* test_sf2types.c */; };
		B57E3B6B296A978F007F6872 /* redhammer.c in Sources */ = {isa = PBXBuildFile; fileRef = 6CF004551C2A01C9008EA965 /* redhammer.c */; };
		B5871ED71408E4F4004A45F5 /* pthreads.c in Sources */ = {isa = PBXBuildFile; fileRef = B5871ED61408E4F4004A45F5 /* pthreads.c */; };
		B5948A26156EDE260029278E /* ehonda_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A25156EDE260029278E /* ehonda_comp.c */; };
		B5948A28156EDE3C0029278E /* ehonda_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A27156EDE3C0029278E /* ehonda_human.c */; };
		B5948A4B156EE0F50029278E /* guile_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4A156EE0F50029278E /* guile_human.c */; };
		B5948A4D156EE0FF0029278E /* guile_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4C156EE0FF0029278E /* guile_comp.c */; };
		B5948BE5156F1B610029278E /* chunli_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE4156F1B610029278E /* chunli_comp.c */; };
		B5948BE7156F1B6E0029278E /* chunli_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE6156F1B6E0029278E /* chunli_human.c */; };
		B5AC69E71550AC9C00C9339E /* aitests.c in Sources */ = {isa = PBXBuildFile; fileRef = B527F61415508A0800BC70D8 /* aitests.c */; };
		B5AC6A611550BDBB00C9339E /* testlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5AC6A601550BDBB00C9339E /* testlib.c */; };
		B5ADB7A913B2B2EA00905DFE /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = B5ADB7A813B2B2EA00905DFE /* MainMenu.xib */; };
		B5ADB7B213B2B38400905DFE /* trackball.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7B013B2B38400905DFE /* trackball.c */; };
		B5ADB7BA13B2B3B300905DFE /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7B913B2B3B300905DFE /* OpenGL.framework */; };
		B5ADB7BE13B2B3BE00905DFE /* GLUT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7BD13B2B3BE00905DFE /* GLUT.framework */; };
		B5ADB84E13B2B57E00905DFE /* actions_198a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E613B2B57D00905DFE /* actions_198a.c */; };
		B5ADB84F13B2B57E00905DFE /* actions_530a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E813B2B57D00905DFE /* actions_530a.c */; };
		B5ADB85113B2B57E00905DFE /* ai.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7ED13B2B57D00905DFE /* ai.c */; };
		B5ADB85213B2B57E00905DFE /* blanka.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7FD13B2B57D00905DFE /* blanka.c */; };
		B5ADB85313B2B57E00905DFE /* chunli.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80013B2B57D00905DFE /* chunli.c */; };
		B5ADB85413B2B57E00905DFE /* dhalsim.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80313B2B57D00905DFE /* dhalsim.c */; };
		B5ADB85513B2B57E00905DFE /* ehonda.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80613B2B57D00905DFE /* ehonda.c */; };
		B5ADB85613B2B57E00905DFE /* guile.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80913B2B57D00905DFE /* guile.c */; };
		B5ADB85713B2B57E00905DFE /* mbison.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80D13B2B57D00905DFE /* mbison.c */; };
		B5ADB85813B2B57E00905DFE /* ryu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81113B2B57D00905DFE /* ryu.c */; };
		B5ADB85913B2B57E00905DFE /* vega.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81413B2B57D00905DFE /* vega.c */; };
		B5ADB85A13B2B57E00905DFE /* collision.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81713B2B57D00905DFE /* collision.c */; };
		B5ADB85B13B2B57E00905DFE /* computer.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81A13B2B57D00905DFE /* computer.c */; };
		B5ADB85C13B2B57E00905DFE /* effects.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81C13B2B57D00905DFE /* effects.c */; };
		B5ADB85D13B2B57E00905DFE /* fightgfx.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81E13B2B57D00905DFE /* fightgfx.c */; };
		B5ADB85E13B2B57E00905DFE /* gemu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82013B2B57D00905DFE /* gemu.c */; };
		B5ADB85F13B2B57E00905DFE /* gfx_glut.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82213B2B57D00905DFE /* gfx_glut.c */; };
		B5ADB86113B2B57E00905DFE /* gfxlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82913B2B57D00905DFE /* gfxlib.c */; };
		B5ADB86213B2B57E00905DFE /* gstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82B13B2B57D00905DFE /* gstate.c */; };
		B5ADB86313B2B57E00905DFE /* lib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82D13B2B57E00905DFE /* lib.c */; };
		B5ADB86413B2B57E00905DFE /* particle.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83013B2B57E00905DFE /* particle.c */; };
		B5ADB86513B2B57E00905DFE /* player.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83213B2B57E00905DFE /* player.c */; };
		B5ADB86613B2B57E00905DFE /* playerselect.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83413B2B57E00905DFE /* playerselect.c */; };
		B5ADB86713B2B57E00905DFE /* playerstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83613B2B57E00905DFE /* playerstate.c */; };
		B5ADB86813B2B57E00905DFE /* projectiles.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83813B2B57E00905DFE /* projectiles.c */; };
		B5ADB86913B2B57E00905DFE /* reactmode.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83A13B2B57E00905DFE /* reactmode.c */; };
		B5ADB86A13B2B57E00905DFE /* rules.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83C13B2B57E00905DFE /* rules.c */; };
		B5ADB86B13B2B57E00905DFE /* sm.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84113B2B57E00905DFE /* sm.c */; };
		B5ADB86C13B2B57E00905DFE /* sound.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84313B2B57E00905DFE /* sound.c */; };
		B5ADB86D13B2B57E00905DFE /* sprite.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84513B2B57E00905DFE /* sprite.c */; };
		B5ADB86E13B2B57E00905DFE /* task.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84913B2B57E00905DFE /* task.c */; };
		B5ADB87213B2B59A00905DFE /* glwimp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB87013B2B59A00905DFE /* glwimp.c */; };
		B5B075B31554803E009D301D /* act2e_plane.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B075B21554803E009D301D /* act2e_plane.c */; };
		B5B0761615548649009D301D /* act1e_worldflags.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0761515548649009D301D /* act1e_worldflags.c */; };
		B5B0763D155489B5009D301D /* act02_bicycleriders.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0763C155489B5009D301D /* act02_bicycleriders.c */; };
		B5B0764615548B28009D301D /* act07_elephants.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0764515548B28009D301D /* act07_elephants.c */; };
		B5B39CFC1578370D00494662 /* MT2AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 256AC3D90F4B6AC300CF3369 /* MT2AppDelegate.m */; };
		B5B39CFD1578370E00494662 /* OGLView.m in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7AC13B2B34900905DFE /* OGLView.m */; };
		B5B39CFF1578371400494662 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 29B97316FDCFA39411CA2CEA /* main.m */; };
		B5B7EF242D4D7A7200D7D54F /* allroms.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B7EF232D4D7A5C00D7D54F /* allroms.bin */; };
		B5B7EF252D4D7A7200D7D54F /* allroms.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B7EF232D4D7A5C00D7D54F /* allroms.bin */; };
		B5B7EF262D4D7A7300D7D54F /* allroms.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B7EF232D4D7A5C00D7D54F /* allroms.bin */; };
		B5B7EF272D4D7A7400D7D54F /* allroms.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B7EF232D4D7A5C00D7D54F /* allroms.bin */; };
		B5BA3ACE147F0EBC0069ACD3 /* actions.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E413B2B57D00905DFE /* actions.c */; };
		B5C5742C1772782600496283 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 089C165CFE840E0CC02AAC07 /* InfoPlist.strings */; };
		B5C5742D1772782600496283 /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = B5ADB7A813B2B2EA00905DFE /* MainMenu.xib */; };
		B5C5742F1772782600496283 /* sf2gfx.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B39B69157832F200494662 /* sf2gfx.bin */; };
		B5C574311772782600496283 /* trackball.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7B013B2B38400905DFE /* trackball.c */; };
		B5C574321772782600496283 /* actions_198a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E613B2B57D00905DFE /* actions_198a.c */; };
		B5C574331772782600496283 /* actions_530a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E813B2B57D00905DFE /* actions_530a.c */; };
		B5C574341772782600496283 /* ai.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7ED13B2B57D00905DFE /* ai.c */; };
		B5C574351772782600496283 /* blanka.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7FD13B2B57D00905DFE /* blanka.c */; };
		B5C574361772782600496283 /* chunli.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80013B2B57D00905DFE /* chunli.c */; };
		B5C574371772782600496283 /* dhalsim.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80313B2B57D00905DFE /* dhalsim.c */; };
		B5C574381772782600496283 /* ehonda.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80613B2B57D00905DFE /* ehonda.c */; };
		B5C574391772782600496283 /* guile.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80913B2B57D00905DFE /* guile.c */; };
		B5C5743A1772782600496283 /* mbison.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80D13B2B57D00905DFE /* mbison.c */; };
		B5C5743B1772782600496283 /* ryu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81113B2B57D00905DFE /* ryu.c */; };
		B5C5743C1772782600496283 /* vega.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81413B2B57D00905DFE /* vega.c */; };
		B5C5743D1772782600496283 /* collision.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81713B2B57D00905DFE /* collision.c */; };
		B5C5743E1772782600496283 /* computer.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81A13B2B57D00905DFE /* computer.c */; };
		B5C5743F1772782600496283 /* effects.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81C13B2B57D00905DFE /* effects.c */; };
		B5C574401772782600496283 /* fightgfx.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81E13B2B57D00905DFE /* fightgfx.c */; };
		B5C574411772782600496283 /* gemu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82013B2B57D00905DFE /* gemu.c */; };
		B5C574421772782600496283 /* gfx_glut.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82213B2B57D00905DFE /* gfx_glut.c */; };
		B5C574441772782600496283 /* gfxlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82913B2B57D00905DFE /* gfxlib.c */; };
		B5C574451772782600496283 /* gstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82B13B2B57D00905DFE /* gstate.c */; };
		B5C574461772782600496283 /* lib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82D13B2B57E00905DFE /* lib.c */; };
		B5C574471772782600496283 /* particle.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83013B2B57E00905DFE /* particle.c */; };
		B5C574481772782600496283 /* player.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83213B2B57E00905DFE /* player.c */; };
		B5C574491772782600496283 /* playerselect.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83413B2B57E00905DFE /* playerselect.c */; };
		B5C5744A1772782600496283 /* playerstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83613B2B57E00905DFE /* playerstate.c */; };
		B5C5744B1772782600496283 /* projectiles.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83813B2B57E00905DFE /* projectiles.c */; };
		B5C5744C1772782600496283 /* reactmode.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83A13B2B57E00905DFE /* reactmode.c */; };
		B5C5744D1772782600496283 /* rules.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83C13B2B57E00905DFE /* rules.c */; };
		B5C5744E1772782600496283 /* sm.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84113B2B57E00905DFE /* sm.c */; };
		B5C5744F1772782600496283 /* sound.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84313B2B57E00905DFE /* sound.c */; };
		B5C574501772782600496283 /* sprite.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84513B2B57E00905DFE /* sprite.c */; };
		B5C574511772782600496283 /* task.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84913B2B57E00905DFE /* task.c */; };
		B5C574521772782600496283 /* glwimp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB87013B2B59A00905DFE /* glwimp.c */; };
		B5C574531772782600496283 /* pthreads.c in Sources */ = {isa = PBXBuildFile; fileRef = B5871ED61408E4F4004A45F5 /* pthreads.c */; };
		B5C574541772782600496283 /* game.c in Sources */ = {isa = PBXBuildFile; fileRef = B56A718B1411B0D300DF7F02 /* game.c */; };
		B5C574551772782600496283 /* coinage.c in Sources */ = {isa = PBXBuildFile; fileRef = B551553414773E8A00A8BBD9 /* coinage.c */; };
		B5C574561772782600496283 /* car.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F1147EEB4F004A87B0 /* car.c */; };
		B5C574571772782600496283 /* drums.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F4147EEBDB004A87B0 /* drums.c */; };
		B5C574581772782600496283 /* actions.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E413B2B57D00905DFE /* actions.c */; };
		B5C5745A1772782600496283 /* coll_projectile.c in Sources */ = {isa = PBXBuildFile; fileRef = B55891421485AB2A00BBB0BA /* coll_projectile.c */; };
		B5C5745B1772782600496283 /* coll_bonus.c in Sources */ = {isa = PBXBuildFile; fileRef = B558918E1485B04A00BBB0BA /* coll_bonus.c */; };
		B5C5745C1772782600496283 /* demo.c in Sources */ = {isa = PBXBuildFile; fileRef = B5FD9F7E1488260A00DDF4D9 /* demo.c */; };
		B5C5745D1772782600496283 /* reels.c in Sources */ = {isa = PBXBuildFile; fileRef = B5EB38E0149816D5007100DB /* reels.c */; };
		B5C5745E1772782600496283 /* aitests.c in Sources */ = {isa = PBXBuildFile; fileRef = B527F61415508A0800BC70D8 /* aitests.c */; };
		B5C5745F1772782600496283 /* testlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5AC6A601550BDBB00C9339E /* testlib.c */; };
		B5C574601772782600496283 /* act2e_plane.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B075B21554803E009D301D /* act2e_plane.c */; };
		B5C574611772782600496283 /* act1e_worldflags.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0761515548649009D301D /* act1e_worldflags.c */; };
		B5C574621772782600496283 /* act02_bicycleriders.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0763C155489B5009D301D /* act02_bicycleriders.c */; };
		B5C574631772782600496283 /* act07_elephants.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0764515548B28009D301D /* act07_elephants.c */; };
		B5C574641772782600496283 /* act29_wwlogo.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC58155F86F400581C8D /* act29_wwlogo.c */; };
		B5C574651772782600496283 /* act3e_capcomlogos.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */; };
		B5C574661772782600496283 /* act_3f.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC6D155F8C0600581C8D /* act_3f.c */; };
		B5C574671772782600496283 /* act17.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC7F155F91F900581C8D /* act17.c */; };
		B5C574681772782600496283 /* act16.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC83155F927300581C8D /* act16.c */; };
		B5C574691772782600496283 /* text.c in Sources */ = {isa = PBXBuildFile; fileRef = B5613C3A155F9917001E127A /* text.c */; };
		B5C5746A1772782600496283 /* zangeif.c in Sources */ = {isa = PBXBuildFile; fileRef = B5F335371562FFA4005A4637 /* zangeif.c */; };
		B5C5746B1772782600496283 /* sagat.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E21388156894FA00D96161 /* sagat.c */; };
		B5C5746C1772782600496283 /* balrog.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E2138B1568950900D96161 /* balrog.c */; };
		B5C5746D1772782600496283 /* blanka_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC67156B43C500DC7BAD /* blanka_comp.c */; };
		B5C5746E1772782600496283 /* blanka_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC6F156B444F00DC7BAD /* blanka_human.c */; };
		B5C5746F1772782600496283 /* ryuken_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC8E156B48F600DC7BAD /* ryuken_human.c */; };
		B5C574701772782600496283 /* ryuken_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */; };
		B5C574711772782600496283 /* ehonda_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A25156EDE260029278E /* ehonda_comp.c */; };
		B5C574721772782600496283 /* ehonda_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A27156EDE3C0029278E /* ehonda_human.c */; };
		B5C574731772782600496283 /* guile_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4A156EE0F50029278E /* guile_human.c */; };
		B5C574741772782600496283 /* guile_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4C156EE0FF0029278E /* guile_comp.c */; };
		B5C574751772782600496283 /* chunli_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE4156F1B610029278E /* chunli_comp.c */; };
		B5C574761772782600496283 /* chunli_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE6156F1B6E0029278E /* chunli_human.c */; };
		B5C574771772782600496283 /* MT2AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 256AC3D90F4B6AC300CF3369 /* MT2AppDelegate.m */; };
		B5C574781772782600496283 /* OGLView.m in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7AC13B2B34900905DFE /* OGLView.m */; };
		B5C5747A1772782600496283 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 29B97316FDCFA39411CA2CEA /* main.m */; };
		B5C5747B1772782600496283 /* endings.c in Sources */ = {isa = PBXBuildFile; fileRef = B550DEA216BBA1BF00983A54 /* endings.c */; };
		B5C5747D1772782600496283 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */; };
		B5C5747E1772782600496283 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7B913B2B3B300905DFE /* OpenGL.framework */; };
		B5C5747F1772782600496283 /* GLUT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7BD13B2B3BE00905DFE /* GLUT.framework */; };
		B5E0EFBA17754D8800F50790 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 089C165CFE840E0CC02AAC07 /* InfoPlist.strings */; };
		B5E0EFBB17754D8800F50790 /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = B5ADB7A813B2B2EA00905DFE /* MainMenu.xib */; };
		B5E0EFBD17754D8800F50790 /* sf2gfx.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B39B69157832F200494662 /* sf2gfx.bin */; };
		B5E0EFBF17754D8800F50790 /* trackball.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7B013B2B38400905DFE /* trackball.c */; };
		B5E0EFC017754D8800F50790 /* actions_198a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E613B2B57D00905DFE /* actions_198a.c */; };
		B5E0EFC117754D8800F50790 /* actions_530a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E813B2B57D00905DFE /* actions_530a.c */; };
		B5E0EFC217754D8800F50790 /* ai.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7ED13B2B57D00905DFE /* ai.c */; };
		B5E0EFC317754D8800F50790 /* blanka.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7FD13B2B57D00905DFE /* blanka.c */; };
		B5E0EFC417754D8800F50790 /* chunli.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80013B2B57D00905DFE /* chunli.c */; };
		B5E0EFC517754D8800F50790 /* dhalsim.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80313B2B57D00905DFE /* dhalsim.c */; };
		B5E0EFC617754D8800F50790 /* ehonda.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80613B2B57D00905DFE /* ehonda.c */; };
		B5E0EFC717754D8800F50790 /* guile.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80913B2B57D00905DFE /* guile.c */; };
		B5E0EFC817754D8800F50790 /* mbison.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80D13B2B57D00905DFE /* mbison.c */; };
		B5E0EFC917754D8800F50790 /* ryu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81113B2B57D00905DFE /* ryu.c */; };
		B5E0EFCA17754D8800F50790 /* vega.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81413B2B57D00905DFE /* vega.c */; };
		B5E0EFCB17754D8800F50790 /* collision.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81713B2B57D00905DFE /* collision.c */; };
		B5E0EFCC17754D8800F50790 /* computer.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81A13B2B57D00905DFE /* computer.c */; };
		B5E0EFCD17754D8800F50790 /* effects.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81C13B2B57D00905DFE /* effects.c */; };
		B5E0EFCE17754D8800F50790 /* fightgfx.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81E13B2B57D00905DFE /* fightgfx.c */; };
		B5E0EFCF17754D8800F50790 /* gemu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82013B2B57D00905DFE /* gemu.c */; };
		B5E0EFD017754D8800F50790 /* gfx_glut.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82213B2B57D00905DFE /* gfx_glut.c */; };
		B5E0EFD217754D8800F50790 /* gfxlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82913B2B57D00905DFE /* gfxlib.c */; };
		B5E0EFD317754D8800F50790 /* gstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82B13B2B57D00905DFE /* gstate.c */; };
		B5E0EFD417754D8800F50790 /* lib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82D13B2B57E00905DFE /* lib.c */; };
		B5E0EFD517754D8800F50790 /* particle.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83013B2B57E00905DFE /* particle.c */; };
		B5E0EFD617754D8800F50790 /* player.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83213B2B57E00905DFE /* player.c */; };
		B5E0EFD717754D8800F50790 /* playerselect.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83413B2B57E00905DFE /* playerselect.c */; };
		B5E0EFD817754D8800F50790 /* playerstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83613B2B57E00905DFE /* playerstate.c */; };
		B5E0EFD917754D8800F50790 /* projectiles.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83813B2B57E00905DFE /* projectiles.c */; };
		B5E0EFDA17754D8800F50790 /* reactmode.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83A13B2B57E00905DFE /* reactmode.c */; };
		B5E0EFDB17754D8800F50790 /* rules.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83C13B2B57E00905DFE /* rules.c */; };
		B5E0EFDC17754D8800F50790 /* sm.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84113B2B57E00905DFE /* sm.c */; };
		B5E0EFDD17754D8800F50790 /* sound.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84313B2B57E00905DFE /* sound.c */; };
		B5E0EFDE17754D8800F50790 /* sprite.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84513B2B57E00905DFE /* sprite.c */; };
		B5E0EFDF17754D8800F50790 /* task.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84913B2B57E00905DFE /* task.c */; };
		B5E0EFE017754D8800F50790 /* glwimp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB87013B2B59A00905DFE /* glwimp.c */; };
		B5E0EFE117754D8800F50790 /* pthreads.c in Sources */ = {isa = PBXBuildFile; fileRef = B5871ED61408E4F4004A45F5 /* pthreads.c */; };
		B5E0EFE217754D8800F50790 /* game.c in Sources */ = {isa = PBXBuildFile; fileRef = B56A718B1411B0D300DF7F02 /* game.c */; };
		B5E0EFE317754D8800F50790 /* coinage.c in Sources */ = {isa = PBXBuildFile; fileRef = B551553414773E8A00A8BBD9 /* coinage.c */; };
		B5E0EFE417754D8800F50790 /* car.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F1147EEB4F004A87B0 /* car.c */; };
		B5E0EFE517754D8800F50790 /* drums.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F4147EEBDB004A87B0 /* drums.c */; };
		B5E0EFE617754D8800F50790 /* actions.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E413B2B57D00905DFE /* actions.c */; };
		B5E0EFE817754D8800F50790 /* coll_projectile.c in Sources */ = {isa = PBXBuildFile; fileRef = B55891421485AB2A00BBB0BA /* coll_projectile.c */; };
		B5E0EFE917754D8800F50790 /* coll_bonus.c in Sources */ = {isa = PBXBuildFile; fileRef = B558918E1485B04A00BBB0BA /* coll_bonus.c */; };
		B5E0EFEA17754D8800F50790 /* demo.c in Sources */ = {isa = PBXBuildFile; fileRef = B5FD9F7E1488260A00DDF4D9 /* demo.c */; };
		B5E0EFEB17754D8800F50790 /* reels.c in Sources */ = {isa = PBXBuildFile; fileRef = B5EB38E0149816D5007100DB /* reels.c */; };
		B5E0EFEC17754D8800F50790 /* aitests.c in Sources */ = {isa = PBXBuildFile; fileRef = B527F61415508A0800BC70D8 /* aitests.c */; };
		B5E0EFED17754D8800F50790 /* testlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5AC6A601550BDBB00C9339E /* testlib.c */; };
		B5E0EFEE17754D8800F50790 /* act2e_plane.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B075B21554803E009D301D /* act2e_plane.c */; };
		B5E0EFEF17754D8800F50790 /* act1e_worldflags.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0761515548649009D301D /* act1e_worldflags.c */; };
		B5E0EFF017754D8800F50790 /* act02_bicycleriders.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0763C155489B5009D301D /* act02_bicycleriders.c */; };
		B5E0EFF117754D8800F50790 /* act07_elephants.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0764515548B28009D301D /* act07_elephants.c */; };
		B5E0EFF217754D8800F50790 /* act29_wwlogo.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC58155F86F400581C8D /* act29_wwlogo.c */; };
		B5E0EFF317754D8800F50790 /* act3e_capcomlogos.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */; };
		B5E0EFF417754D8800F50790 /* act_3f.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC6D155F8C0600581C8D /* act_3f.c */; };
		B5E0EFF517754D8800F50790 /* act17.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC7F155F91F900581C8D /* act17.c */; };
		B5E0EFF617754D8800F50790 /* act16.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC83155F927300581C8D /* act16.c */; };
		B5E0EFF717754D8800F50790 /* text.c in Sources */ = {isa = PBXBuildFile; fileRef = B5613C3A155F9917001E127A /* text.c */; };
		B5E0EFF817754D8800F50790 /* zangeif.c in Sources */ = {isa = PBXBuildFile; fileRef = B5F335371562FFA4005A4637 /* zangeif.c */; };
		B5E0EFF917754D8800F50790 /* sagat.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E21388156894FA00D96161 /* sagat.c */; };
		B5E0EFFA17754D8800F50790 /* balrog.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E2138B1568950900D96161 /* balrog.c */; };
		B5E0EFFB17754D8800F50790 /* blanka_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC67156B43C500DC7BAD /* blanka_comp.c */; };
		B5E0EFFC17754D8800F50790 /* blanka_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC6F156B444F00DC7BAD /* blanka_human.c */; };
		B5E0EFFD17754D8800F50790 /* ryuken_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC8E156B48F600DC7BAD /* ryuken_human.c */; };
		B5E0EFFE17754D8800F50790 /* ryuken_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */; };
		B5E0EFFF17754D8800F50790 /* ehonda_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A25156EDE260029278E /* ehonda_comp.c */; };
		B5E0F00017754D8800F50790 /* ehonda_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A27156EDE3C0029278E /* ehonda_human.c */; };
		B5E0F00117754D8800F50790 /* guile_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4A156EE0F50029278E /* guile_human.c */; };
		B5E0F00217754D8800F50790 /* guile_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4C156EE0FF0029278E /* guile_comp.c */; };
		B5E0F00317754D8800F50790 /* chunli_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE4156F1B610029278E /* chunli_comp.c */; };
		B5E0F00417754D8800F50790 /* chunli_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE6156F1B6E0029278E /* chunli_human.c */; };
		B5E0F00517754D8800F50790 /* MT2AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 256AC3D90F4B6AC300CF3369 /* MT2AppDelegate.m */; };
		B5E0F00617754D8800F50790 /* OGLView.m in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7AC13B2B34900905DFE /* OGLView.m */; };
		B5E0F00817754D8800F50790 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 29B97316FDCFA39411CA2CEA /* main.m */; };
		B5E0F00917754D8800F50790 /* endings.c in Sources */ = {isa = PBXBuildFile; fileRef = B550DEA216BBA1BF00983A54 /* endings.c */; };
		B5E0F00B17754D8800F50790 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */; };
		B5E0F00C17754D8800F50790 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7B913B2B3B300905DFE /* OpenGL.framework */; };
		B5E0F00D17754D8800F50790 /* GLUT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7BD13B2B3BE00905DFE /* GLUT.framework */; };
		B5E0F01517754D9500F50790 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 089C165CFE840E0CC02AAC07 /* InfoPlist.strings */; };
		B5E0F01617754D9500F50790 /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = B5ADB7A813B2B2EA00905DFE /* MainMenu.xib */; };
		B5E0F01817754D9500F50790 /* sf2gfx.bin in Resources */ = {isa = PBXBuildFile; fileRef = B5B39B69157832F200494662 /* sf2gfx.bin */; };
		B5E0F01A17754D9500F50790 /* trackball.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7B013B2B38400905DFE /* trackball.c */; };
		B5E0F01B17754D9500F50790 /* actions_198a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E613B2B57D00905DFE /* actions_198a.c */; };
		B5E0F01C17754D9500F50790 /* actions_530a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E813B2B57D00905DFE /* actions_530a.c */; };
		B5E0F01D17754D9500F50790 /* ai.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7ED13B2B57D00905DFE /* ai.c */; };
		B5E0F01E17754D9500F50790 /* blanka.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7FD13B2B57D00905DFE /* blanka.c */; };
		B5E0F01F17754D9500F50790 /* chunli.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80013B2B57D00905DFE /* chunli.c */; };
		B5E0F02017754D9500F50790 /* dhalsim.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80313B2B57D00905DFE /* dhalsim.c */; };
		B5E0F02117754D9500F50790 /* ehonda.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80613B2B57D00905DFE /* ehonda.c */; };
		B5E0F02217754D9500F50790 /* guile.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80913B2B57D00905DFE /* guile.c */; };
		B5E0F02317754D9500F50790 /* mbison.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80D13B2B57D00905DFE /* mbison.c */; };
		B5E0F02417754D9500F50790 /* ryu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81113B2B57D00905DFE /* ryu.c */; };
		B5E0F02517754D9500F50790 /* vega.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81413B2B57D00905DFE /* vega.c */; };
		B5E0F02617754D9500F50790 /* collision.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81713B2B57D00905DFE /* collision.c */; };
		B5E0F02717754D9500F50790 /* computer.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81A13B2B57D00905DFE /* computer.c */; };
		B5E0F02817754D9500F50790 /* effects.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81C13B2B57D00905DFE /* effects.c */; };
		B5E0F02917754D9500F50790 /* fightgfx.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81E13B2B57D00905DFE /* fightgfx.c */; };
		B5E0F02A17754D9500F50790 /* gemu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82013B2B57D00905DFE /* gemu.c */; };
		B5E0F02B17754D9500F50790 /* gfx_glut.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82213B2B57D00905DFE /* gfx_glut.c */; };
		B5E0F02D17754D9500F50790 /* gfxlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82913B2B57D00905DFE /* gfxlib.c */; };
		B5E0F02E17754D9500F50790 /* gstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82B13B2B57D00905DFE /* gstate.c */; };
		B5E0F02F17754D9500F50790 /* lib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82D13B2B57E00905DFE /* lib.c */; };
		B5E0F03017754D9500F50790 /* particle.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83013B2B57E00905DFE /* particle.c */; };
		B5E0F03117754D9500F50790 /* player.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83213B2B57E00905DFE /* player.c */; };
		B5E0F03217754D9500F50790 /* playerselect.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83413B2B57E00905DFE /* playerselect.c */; };
		B5E0F03317754D9500F50790 /* playerstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83613B2B57E00905DFE /* playerstate.c */; };
		B5E0F03417754D9500F50790 /* projectiles.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83813B2B57E00905DFE /* projectiles.c */; };
		B5E0F03517754D9500F50790 /* reactmode.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83A13B2B57E00905DFE /* reactmode.c */; };
		B5E0F03617754D9500F50790 /* rules.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83C13B2B57E00905DFE /* rules.c */; };
		B5E0F03717754D9500F50790 /* sm.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84113B2B57E00905DFE /* sm.c */; };
		B5E0F03817754D9500F50790 /* sound.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84313B2B57E00905DFE /* sound.c */; };
		B5E0F03917754D9500F50790 /* sprite.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84513B2B57E00905DFE /* sprite.c */; };
		B5E0F03A17754D9500F50790 /* task.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84913B2B57E00905DFE /* task.c */; };
		B5E0F03B17754D9500F50790 /* glwimp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB87013B2B59A00905DFE /* glwimp.c */; };
		B5E0F03C17754D9500F50790 /* pthreads.c in Sources */ = {isa = PBXBuildFile; fileRef = B5871ED61408E4F4004A45F5 /* pthreads.c */; };
		B5E0F03D17754D9500F50790 /* game.c in Sources */ = {isa = PBXBuildFile; fileRef = B56A718B1411B0D300DF7F02 /* game.c */; };
		B5E0F03E17754D9500F50790 /* coinage.c in Sources */ = {isa = PBXBuildFile; fileRef = B551553414773E8A00A8BBD9 /* coinage.c */; };
		B5E0F03F17754D9500F50790 /* car.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F1147EEB4F004A87B0 /* car.c */; };
		B5E0F04017754D9500F50790 /* drums.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F4147EEBDB004A87B0 /* drums.c */; };
		B5E0F04117754D9500F50790 /* actions.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E413B2B57D00905DFE /* actions.c */; };
		B5E0F04317754D9500F50790 /* coll_projectile.c in Sources */ = {isa = PBXBuildFile; fileRef = B55891421485AB2A00BBB0BA /* coll_projectile.c */; };
		B5E0F04417754D9500F50790 /* coll_bonus.c in Sources */ = {isa = PBXBuildFile; fileRef = B558918E1485B04A00BBB0BA /* coll_bonus.c */; };
		B5E0F04517754D9500F50790 /* demo.c in Sources */ = {isa = PBXBuildFile; fileRef = B5FD9F7E1488260A00DDF4D9 /* demo.c */; };
		B5E0F04617754D9500F50790 /* reels.c in Sources */ = {isa = PBXBuildFile; fileRef = B5EB38E0149816D5007100DB /* reels.c */; };
		B5E0F04717754D9500F50790 /* aitests.c in Sources */ = {isa = PBXBuildFile; fileRef = B527F61415508A0800BC70D8 /* aitests.c */; };
		B5E0F04817754D9500F50790 /* testlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5AC6A601550BDBB00C9339E /* testlib.c */; };
		B5E0F04917754D9500F50790 /* act2e_plane.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B075B21554803E009D301D /* act2e_plane.c */; };
		B5E0F04A17754D9500F50790 /* act1e_worldflags.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0761515548649009D301D /* act1e_worldflags.c */; };
		B5E0F04B17754D9500F50790 /* act02_bicycleriders.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0763C155489B5009D301D /* act02_bicycleriders.c */; };
		B5E0F04C17754D9500F50790 /* act07_elephants.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0764515548B28009D301D /* act07_elephants.c */; };
		B5E0F04D17754D9500F50790 /* act29_wwlogo.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC58155F86F400581C8D /* act29_wwlogo.c */; };
		B5E0F04E17754D9500F50790 /* act3e_capcomlogos.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */; };
		B5E0F04F17754D9500F50790 /* act_3f.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC6D155F8C0600581C8D /* act_3f.c */; };
		B5E0F05017754D9500F50790 /* act17.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC7F155F91F900581C8D /* act17.c */; };
		B5E0F05117754D9500F50790 /* act16.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC83155F927300581C8D /* act16.c */; };
		B5E0F05217754D9500F50790 /* text.c in Sources */ = {isa = PBXBuildFile; fileRef = B5613C3A155F9917001E127A /* text.c */; };
		B5E0F05317754D9500F50790 /* zangeif.c in Sources */ = {isa = PBXBuildFile; fileRef = B5F335371562FFA4005A4637 /* zangeif.c */; };
		B5E0F05417754D9500F50790 /* sagat.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E21388156894FA00D96161 /* sagat.c */; };
		B5E0F05517754D9500F50790 /* balrog.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E2138B1568950900D96161 /* balrog.c */; };
		B5E0F05617754D9500F50790 /* blanka_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC67156B43C500DC7BAD /* blanka_comp.c */; };
		B5E0F05717754D9500F50790 /* blanka_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC6F156B444F00DC7BAD /* blanka_human.c */; };
		B5E0F05817754D9500F50790 /* ryuken_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC8E156B48F600DC7BAD /* ryuken_human.c */; };
		B5E0F05917754D9500F50790 /* ryuken_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */; };
		B5E0F05A17754D9500F50790 /* ehonda_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A25156EDE260029278E /* ehonda_comp.c */; };
		B5E0F05B17754D9500F50790 /* ehonda_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A27156EDE3C0029278E /* ehonda_human.c */; };
		B5E0F05C17754D9500F50790 /* guile_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4A156EE0F50029278E /* guile_human.c */; };
		B5E0F05D17754D9500F50790 /* guile_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4C156EE0FF0029278E /* guile_comp.c */; };
		B5E0F05E17754D9500F50790 /* chunli_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE4156F1B610029278E /* chunli_comp.c */; };
		B5E0F05F17754D9500F50790 /* chunli_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE6156F1B6E0029278E /* chunli_human.c */; };
		B5E0F06017754D9500F50790 /* MT2AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 256AC3D90F4B6AC300CF3369 /* MT2AppDelegate.m */; };
		B5E0F06117754D9500F50790 /* OGLView.m in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7AC13B2B34900905DFE /* OGLView.m */; };
		B5E0F06317754D9500F50790 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 29B97316FDCFA39411CA2CEA /* main.m */; };
		B5E0F06417754D9500F50790 /* endings.c in Sources */ = {isa = PBXBuildFile; fileRef = B550DEA216BBA1BF00983A54 /* endings.c */; };
		B5E0F06617754D9500F50790 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */; };
		B5E0F06717754D9500F50790 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7B913B2B3B300905DFE /* OpenGL.framework */; };
		B5E0F06817754D9500F50790 /* GLUT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5ADB7BD13B2B3BE00905DFE /* GLUT.framework */; };
		B5E21389156894FA00D96161 /* sagat.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E21388156894FA00D96161 /* sagat.c */; };
		B5E2138C1568950900D96161 /* balrog.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E2138B1568950900D96161 /* balrog.c */; };
		B5E8FD121776C7D400C6060E /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5E8FD111776C7D400C6060E /* CoreFoundation.framework */; };
		B5E8FD171776C7D400C6060E /* MT2_GLUT.1 in CopyFiles */ = {isa = PBXBuildFile; fileRef = B5E8FD161776C7D400C6060E /* MT2_GLUT.1 */; };
		B5E8FD1B1776C7E600C6060E /* glutBasics.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B39BB01578345600494662 /* glutBasics.c */; };
		B5E8FD1C1776C7EC00C6060E /* gfx_glut.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82213B2B57D00905DFE /* gfx_glut.c */; };
		B5E8FD1D1776C7F300C6060E /* pthreads.c in Sources */ = {isa = PBXBuildFile; fileRef = B5871ED61408E4F4004A45F5 /* pthreads.c */; };
		B5E8FD1E1776C7F700C6060E /* gemu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82013B2B57D00905DFE /* gemu.c */; };
		B5E8FD1F1776C81D00C6060E /* collision.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81713B2B57D00905DFE /* collision.c */; };
		B5E8FD201776C82000C6060E /* coll_projectile.c in Sources */ = {isa = PBXBuildFile; fileRef = B55891421485AB2A00BBB0BA /* coll_projectile.c */; };
		B5E8FD211776C82300C6060E /* coll_bonus.c in Sources */ = {isa = PBXBuildFile; fileRef = B558918E1485B04A00BBB0BA /* coll_bonus.c */; };
		B5E8FD221776C83300C6060E /* car.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F1147EEB4F004A87B0 /* car.c */; };
		B5E8FD231776C83700C6060E /* drums.c in Sources */ = {isa = PBXBuildFile; fileRef = B51533F4147EEBDB004A87B0 /* drums.c */; };
		B5E8FD241776C86700C6060E /* actions.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E413B2B57D00905DFE /* actions.c */; };
		B5E8FD251776C86700C6060E /* actions_198a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E613B2B57D00905DFE /* actions_198a.c */; };
		B5E8FD261776C86700C6060E /* actions_530a.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7E813B2B57D00905DFE /* actions_530a.c */; };
		B5E8FD281776C86700C6060E /* reels.c in Sources */ = {isa = PBXBuildFile; fileRef = B5EB38E0149816D5007100DB /* reels.c */; };
		B5E8FD291776C86700C6060E /* act02_bicycleriders.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0763C155489B5009D301D /* act02_bicycleriders.c */; };
		B5E8FD2A1776C86700C6060E /* act07_elephants.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0764515548B28009D301D /* act07_elephants.c */; };
		B5E8FD2B1776C86700C6060E /* act17.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC7F155F91F900581C8D /* act17.c */; };
		B5E8FD2C1776C86700C6060E /* act1e_worldflags.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B0761515548649009D301D /* act1e_worldflags.c */; };
		B5E8FD2D1776C86700C6060E /* act29_wwlogo.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC58155F86F400581C8D /* act29_wwlogo.c */; };
		B5E8FD2E1776C86700C6060E /* act2e_plane.c in Sources */ = {isa = PBXBuildFile; fileRef = B5B075B21554803E009D301D /* act2e_plane.c */; };
		B5E8FD2F1776C86700C6060E /* act3e_capcomlogos.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */; };
		B5E8FD301776C86700C6060E /* act_3f.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC6D155F8C0600581C8D /* act_3f.c */; };
		B5E8FD311776C86700C6060E /* act16.c in Sources */ = {isa = PBXBuildFile; fileRef = B53EBC83155F927300581C8D /* act16.c */; };
		B5E8FD321776C87400C6060E /* ai.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7ED13B2B57D00905DFE /* ai.c */; };
		B5E8FD331776C89400C6060E /* balrog.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E2138B1568950900D96161 /* balrog.c */; };
		B5E8FD341776C89400C6060E /* blanka.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7FD13B2B57D00905DFE /* blanka.c */; };
		B5E8FD351776C89400C6060E /* blanka_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC67156B43C500DC7BAD /* blanka_comp.c */; };
		B5E8FD361776C89400C6060E /* chunli.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80013B2B57D00905DFE /* chunli.c */; };
		B5E8FD371776C89400C6060E /* dhalsim.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80313B2B57D00905DFE /* dhalsim.c */; };
		B5E8FD381776C89400C6060E /* ehonda.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80613B2B57D00905DFE /* ehonda.c */; };
		B5E8FD391776C89400C6060E /* ehonda_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A25156EDE260029278E /* ehonda_comp.c */; };
		B5E8FD3A1776C89400C6060E /* ehonda_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A27156EDE3C0029278E /* ehonda_human.c */; };
		B5E8FD3B1776C89400C6060E /* guile.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80913B2B57D00905DFE /* guile.c */; };
		B5E8FD3C1776C89400C6060E /* guile_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4A156EE0F50029278E /* guile_human.c */; };
		B5E8FD3D1776C89400C6060E /* guile_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948A4C156EE0FF0029278E /* guile_comp.c */; };
		B5E8FD3E1776C89400C6060E /* mbison.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB80D13B2B57D00905DFE /* mbison.c */; };
		B5E8FD3F1776C89400C6060E /* ryu.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81113B2B57D00905DFE /* ryu.c */; };
		B5E8FD401776C89400C6060E /* ryuken_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC8E156B48F600DC7BAD /* ryuken_human.c */; };
		B5E8FD411776C89400C6060E /* ryuken_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */; };
		B5E8FD421776C89400C6060E /* sagat.c in Sources */ = {isa = PBXBuildFile; fileRef = B5E21388156894FA00D96161 /* sagat.c */; };
		B5E8FD431776C89400C6060E /* vega.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81413B2B57D00905DFE /* vega.c */; };
		B5E8FD441776C89400C6060E /* zangeif.c in Sources */ = {isa = PBXBuildFile; fileRef = B5F335371562FFA4005A4637 /* zangeif.c */; };
		B5E8FD451776C89400C6060E /* chunli_comp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE4156F1B610029278E /* chunli_comp.c */; };
		B5E8FD461776C89400C6060E /* chunli_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B5948BE6156F1B6E0029278E /* chunli_human.c */; };
		B5E8FD471776C89D00C6060E /* coinage.c in Sources */ = {isa = PBXBuildFile; fileRef = B551553414773E8A00A8BBD9 /* coinage.c */; };
		B5E8FD481776C89D00C6060E /* computer.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81A13B2B57D00905DFE /* computer.c */; };
		B5E8FD491776C89D00C6060E /* demo.c in Sources */ = {isa = PBXBuildFile; fileRef = B5FD9F7E1488260A00DDF4D9 /* demo.c */; };
		B5E8FD4A1776C89D00C6060E /* effects.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81C13B2B57D00905DFE /* effects.c */; };
		B5E8FD4B1776C89D00C6060E /* fightgfx.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB81E13B2B57D00905DFE /* fightgfx.c */; };
		B5E8FD4C1776C89D00C6060E /* game.c in Sources */ = {isa = PBXBuildFile; fileRef = B56A718B1411B0D300DF7F02 /* game.c */; };
		B5E8FD4E1776C8A600C6060E /* graphics.c in Sources */ = {isa = PBXBuildFile; fileRef = B5869A8413B561F1001844D9 /* graphics.c */; };
		B5E8FD4F1776C8BF00C6060E /* gfxlib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82913B2B57D00905DFE /* gfxlib.c */; };
		B5E8FD501776C8BF00C6060E /* gstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82B13B2B57D00905DFE /* gstate.c */; };
		B5E8FD511776C8BF00C6060E /* lib.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB82D13B2B57E00905DFE /* lib.c */; };
		B5E8FD521776C8BF00C6060E /* particle.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83013B2B57E00905DFE /* particle.c */; };
		B5E8FD531776C8BF00C6060E /* player.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83213B2B57E00905DFE /* player.c */; };
		B5E8FD541776C8BF00C6060E /* playerselect.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83413B2B57E00905DFE /* playerselect.c */; };
		B5E8FD551776C8BF00C6060E /* playerstate.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83613B2B57E00905DFE /* playerstate.c */; };
		B5E8FD561776C8BF00C6060E /* projectiles.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83813B2B57E00905DFE /* projectiles.c */; };
		B5E8FD571776C8BF00C6060E /* reactmode.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83A13B2B57E00905DFE /* reactmode.c */; };
		B5E8FD581776C8BF00C6060E /* rules.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB83C13B2B57E00905DFE /* rules.c */; };
		B5E8FD591776C8BF00C6060E /* sm.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84113B2B57E00905DFE /* sm.c */; };
		B5E8FD5A1776C8BF00C6060E /* sound.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84313B2B57E00905DFE /* sound.c */; };
		B5E8FD5B1776C8BF00C6060E /* sprite.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84513B2B57E00905DFE /* sprite.c */; };
		B5E8FD5C1776C8BF00C6060E /* task.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB84913B2B57E00905DFE /* task.c */; };
		B5E8FD5D1776C8BF00C6060E /* text.c in Sources */ = {isa = PBXBuildFile; fileRef = B5613C3A155F9917001E127A /* text.c */; };
		B5E8FD5F1776C8BF00C6060E /* endings.c in Sources */ = {isa = PBXBuildFile; fileRef = B550DEA216BBA1BF00983A54 /* endings.c */; };
		B5E8FD601776C8C900C6060E /* trackball.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB7B013B2B38400905DFE /* trackball.c */; };
		B5E8FD631776C9DD00C6060E /* glwimp.c in Sources */ = {isa = PBXBuildFile; fileRef = B5ADB87013B2B59A00905DFE /* glwimp.c */; };
		B5E8FD641776CA1A00C6060E /* blanka_human.c in Sources */ = {isa = PBXBuildFile; fileRef = B56BFC6F156B444F00DC7BAD /* blanka_human.c */; };
		B5E8FD681776DF3A00C6060E /* sf2gfx.bin in CopyFiles */ = {isa = PBXBuildFile; fileRef = B5B39B69157832F200494662 /* sf2gfx.bin */; };
		B5EB38E1149816D5007100DB /* reels.c in Sources */ = {isa = PBXBuildFile; fileRef = B5EB38E0149816D5007100DB /* reels.c */; };
		B5F335381562FFA4005A4637 /* zangeif.c in Sources */ = {isa = PBXBuildFile; fileRef = B5F335371562FFA4005A4637 /* zangeif.c */; };
		B5FD9F801488260A00DDF4D9 /* demo.c in Sources */ = {isa = PBXBuildFile; fileRef = B5FD9F7E1488260A00DDF4D9 /* demo.c */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		B57E3B41296A7DFC007F6872 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = /usr/share/man/man1/;
			dstSubfolderSpec = 0;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 1;
		};
		B5E8FD0E1776C7D400C6060E /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = /usr/share/man/man1/;
			dstSubfolderSpec = 0;
			files = (
				B5E8FD171776C7D400C6060E /* MT2_GLUT.1 in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 1;
		};
		B5E8FD671776DF2900C6060E /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				B5E8FD681776DF3A00C6060E /* sf2gfx.bin in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		089C165DFE840E0CC02AAC07 /* English */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.strings; name = English; path = English.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = /System/Library/Frameworks/Cocoa.framework; sourceTree = "<absolute>"; };
		13E42FB307B3F0F600E4EEF1 /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = /System/Library/Frameworks/CoreData.framework; sourceTree = "<absolute>"; };
		256AC3D80F4B6AC300CF3369 /* MT2AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MT2AppDelegate.h; sourceTree = "<group>"; };
		256AC3D90F4B6AC300CF3369 /* MT2AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MT2AppDelegate.m; sourceTree = "<group>"; };
		256AC3F00F4B6AF500CF3369 /* MT2_Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MT2_Prefix.pch; sourceTree = "<group>"; };
		29B97316FDCFA39411CA2CEA /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		29B97324FDCFA39411CA2CEA /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = /System/Library/Frameworks/AppKit.framework; sourceTree = "<absolute>"; };
		29B97325FDCFA39411CA2CEA /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = /System/Library/Frameworks/Foundation.framework; sourceTree = "<absolute>"; };
		6C8C65241C37717200635FDB /* barrels.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = barrels.c; sourceTree = "<group>"; };
		6C8C65251C37717200635FDB /* barrels.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = barrels.h; sourceTree = "<group>"; };
		6CF004551C2A01C9008EA965 /* redhammer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = redhammer.c; sourceTree = "<group>"; };
		6CF004571C2A128C008EA965 /* sf2_05.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_05.bin; sourceTree = "<group>"; };
		6CF004581C2A128C008EA965 /* sf2_06.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_06.bin; sourceTree = "<group>"; };
		6CF004591C2A128C008EA965 /* sf2_07.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_07.bin; sourceTree = "<group>"; };
		6CF0045A1C2A128C008EA965 /* sf2_08.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_08.bin; sourceTree = "<group>"; };
		6CF0045B1C2A128C008EA965 /* sf2_09.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_09.bin; sourceTree = "<group>"; };
		6CF0045C1C2A128C008EA965 /* sf2_14.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_14.bin; sourceTree = "<group>"; };
		6CF0045D1C2A128C008EA965 /* sf2_15.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_15.bin; sourceTree = "<group>"; };
		6CF0045E1C2A128C008EA965 /* sf2_16.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_16.bin; sourceTree = "<group>"; };
		6CF0045F1C2A128C008EA965 /* sf2_17.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_17.bin; sourceTree = "<group>"; };
		6CF004601C2A128C008EA965 /* sf2_18.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_18.bin; sourceTree = "<group>"; };
		6CF004611C2A128C008EA965 /* sf2_19.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_19.bin; sourceTree = "<group>"; };
		6CF004621C2A128C008EA965 /* sf2_24.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_24.bin; sourceTree = "<group>"; };
		6CF004631C2A128C008EA965 /* sf2_25.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_25.bin; sourceTree = "<group>"; };
		6CF004641C2A128C008EA965 /* sf2_26.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_26.bin; sourceTree = "<group>"; };
		6CF004651C2A128C008EA965 /* sf2_27.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_27.bin; sourceTree = "<group>"; };
		6CF004661C2A128C008EA965 /* sf2_29a.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_29a.bin; sourceTree = "<group>"; };
		6CF004671C2A128C008EA965 /* sf2_36a.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2_36a.bin; sourceTree = "<group>"; };
		6CF004681C2A128C008EA965 /* sf2u.37a */ = {isa = PBXFileReference; lastKnownFileType = file; path = sf2u.37a; sourceTree = "<group>"; };
		6CF004691C2A128C008EA965 /* sf2u.35a */ = {isa = PBXFileReference; lastKnownFileType = file; path = sf2u.35a; sourceTree = "<group>"; };
		6CF0046A1C2A128C008EA965 /* sf2u.31a */ = {isa = PBXFileReference; lastKnownFileType = file; path = sf2u.31a; sourceTree = "<group>"; };
		6CF0046B1C2A128C008EA965 /* sf2u.30a */ = {isa = PBXFileReference; lastKnownFileType = file; path = sf2u.30a; sourceTree = "<group>"; };
		6CF0046C1C2A128C008EA965 /* sf2u.28a */ = {isa = PBXFileReference; lastKnownFileType = file; path = sf2u.28a; sourceTree = "<group>"; };
		6CF0046D1C2A128C008EA965 /* sf2u.38a */ = {isa = PBXFileReference; lastKnownFileType = file; path = sf2u.38a; sourceTree = "<group>"; };
		8D1107310486CEB800E47090 /* MT2-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "MT2-Info.plist"; sourceTree = "<group>"; };
		8D1107320486CEB800E47090 /* MT2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MT2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B50BCA161C040AF300C7A6F4 /* cps_tile.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = cps_tile.h; sourceTree = "<group>"; };
		B51533F0147EEB4F004A87B0 /* car.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = car.h; sourceTree = "<group>"; };
		B51533F1147EEB4F004A87B0 /* car.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = car.c; sourceTree = "<group>"; };
		B51533F3147EEBDB004A87B0 /* drums.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = drums.h; sourceTree = "<group>"; };
		B51533F4147EEBDB004A87B0 /* drums.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = drums.c; sourceTree = "<group>"; };
		B517DD582543E8E000649292 /* tileshader.vs */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.glsl; path = tileshader.vs; sourceTree = "<group>"; };
		B517DD602543E8F300649292 /* tileshader.fs */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.glsl; path = tileshader.fs; sourceTree = "<group>"; };
		B519C6B4253C04A60084B88D /* game-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "game-Bridging-Header.h"; sourceTree = "<group>"; };
		B519C713253C27020084B88D /* PointSprite.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointSprite.swift; sourceTree = "<group>"; };
		B519C721253C92EC0084B88D /* gfx_glcore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = gfx_glcore.h; sourceTree = "<group>"; };
		B519C722253C92EC0084B88D /* gfx_glcore.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = gfx_glcore.c; sourceTree = "<group>"; };
		B51D1CC8253BE4C400CEB195 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/OpenGLES.framework; sourceTree = DEVELOPER_DIR; };
		B51D1D17253BF34100CEB195 /* MT2-GLcore.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "MT2-GLcore.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B51D1D19253BF34100CEB195 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		B51D1D1B253BF34300CEB195 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		B51D1D1E253BF34300CEB195 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/MainMenu.xib; sourceTree = "<group>"; };
		B51D1D20253BF34300CEB195 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B51D1D21253BF34300CEB195 /* MT2_GLcore.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = MT2_GLcore.entitlements; sourceTree = "<group>"; };
		B51D1D2B253BF3A300CEB195 /* MyNSOpenGLView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyNSOpenGLView.swift; sourceTree = "<group>"; };
		B51D1D63253BF67A00CEB195 /* Matrix4.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Matrix4.swift; sourceTree = "<group>"; };
		B51D1D6B253BF79100CEB195 /* ShaderProgram.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShaderProgram.swift; sourceTree = "<group>"; };
		B51D1D85253BFC2500CEB195 /* pointshader.fs */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.glsl; path = pointshader.fs; sourceTree = "<group>"; };
		B51D1D86253BFC2500CEB195 /* pointshader.vs */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.glsl; path = pointshader.vs; sourceTree = "<group>"; };
		B51D1D95253BFD0D00CEB195 /* Game.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Game.swift; sourceTree = "<group>"; };
		B52124AD18D65A6D00D2F649 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		B52124AF18D65A6D00D2F649 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		B527F61315508A0800BC70D8 /* aitests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aitests.h; sourceTree = "<group>"; };
		B527F61415508A0800BC70D8 /* aitests.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = aitests.c; sourceTree = "<group>"; };
		B529FCEC15606B0A005EE9B7 /* textdata.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = textdata.c; sourceTree = "<group>"; };
		B53EBC57155F86F400581C8D /* act29_wwlogo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act29_wwlogo.h; sourceTree = "<group>"; };
		B53EBC58155F86F400581C8D /* act29_wwlogo.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act29_wwlogo.c; sourceTree = "<group>"; };
		B53EBC60155F8A4600581C8D /* act3e_capcomlogos.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act3e_capcomlogos.h; sourceTree = "<group>"; };
		B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act3e_capcomlogos.c; sourceTree = "<group>"; };
		B53EBC6C155F8C0600581C8D /* act_3f.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act_3f.h; sourceTree = "<group>"; };
		B53EBC6D155F8C0600581C8D /* act_3f.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act_3f.c; sourceTree = "<group>"; };
		B53EBC7E155F91F900581C8D /* act17.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act17.h; sourceTree = "<group>"; };
		B53EBC7F155F91F900581C8D /* act17.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act17.c; sourceTree = "<group>"; };
		B53EBC82155F927300581C8D /* act16.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act16.h; sourceTree = "<group>"; };
		B53EBC83155F927300581C8D /* act16.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act16.c; sourceTree = "<group>"; };
		B54FDCC72543AB940092E183 /* scroll_maint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = scroll_maint.h; sourceTree = "<group>"; };
		B54FDCC82543AB940092E183 /* scroll_maint.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = scroll_maint.c; sourceTree = "<group>"; };
		B54FDCD52543AD230092E183 /* parallax.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = parallax.h; sourceTree = "<group>"; };
		B54FDCD62543AD230092E183 /* parallax.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = parallax.c; sourceTree = "<group>"; };
		B54FDCE32543ADC10092E183 /* scroll_util.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = scroll_util.h; sourceTree = "<group>"; };
		B54FDCE42543ADC10092E183 /* scroll_util.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = scroll_util.c; sourceTree = "<group>"; };
		B54FDCF12543ADF20092E183 /* scroll_data.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = scroll_data.h; sourceTree = "<group>"; };
		B54FDCF22543ADF20092E183 /* scroll_data.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = scroll_data.c; sourceTree = "<group>"; };
		B54FDCFF2543C8B20092E183 /* scroll.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = scroll.h; sourceTree = "<group>"; };
		B550DEA116BBA1BF00983A54 /* endings.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = endings.h; sourceTree = "<group>"; };
		B550DEA216BBA1BF00983A54 /* endings.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = endings.c; sourceTree = "<group>"; };
		B551553314773E8A00A8BBD9 /* coinage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = coinage.h; sourceTree = "<group>"; };
		B551553414773E8A00A8BBD9 /* coinage.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = coinage.c; sourceTree = "<group>"; };
		B55196BB148B0385002300A3 /* sf2io.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sf2io.h; sourceTree = "<group>"; };
		B55891411485AB2A00BBB0BA /* coll_projectile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = coll_projectile.h; sourceTree = "<group>"; };
		B55891421485AB2A00BBB0BA /* coll_projectile.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = coll_projectile.c; sourceTree = "<group>"; };
		B558918D1485B04A00BBB0BA /* coll_bonus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = coll_bonus.h; sourceTree = "<group>"; };
		B558918E1485B04A00BBB0BA /* coll_bonus.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = coll_bonus.c; sourceTree = "<group>"; };
		B5613C3A155F9917001E127A /* text.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = text.c; sourceTree = "<group>"; };
		B56A715D1411AA1000DF7F02 /* io.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = io.h; sourceTree = "<group>"; };
		B56A718A1411B0D300DF7F02 /* game.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = game.h; sourceTree = "<group>"; };
		B56A718B1411B0D300DF7F02 /* game.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = game.c; sourceTree = "<group>"; };
		B56BBD5314859AA100FF84CB /* fbtests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = fbtests.h; sourceTree = "<group>"; };
		B56BBD5414859AA100FF84CB /* fbtests.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = fbtests.c; sourceTree = "<group>"; };
		B56BFC67156B43C500DC7BAD /* blanka_comp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = blanka_comp.c; sourceTree = "<group>"; };
		B56BFC6F156B444F00DC7BAD /* blanka_human.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = blanka_human.c; sourceTree = "<group>"; };
		B56BFC8E156B48F600DC7BAD /* ryuken_human.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ryuken_human.c; sourceTree = "<group>"; };
		B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ryuken_comp.c; sourceTree = "<group>"; };
		B56E0DC913B95EB600FC837B /* actions_198a_data.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = actions_198a_data.h; path = FistBlue/gfxdata/actions_198a_data.h; sourceTree = SOURCE_ROOT; };
		B572140C19DE70AB00160398 /* sf2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = sf2.h; sourceTree = "<group>"; };
		B57AE5DC19D7D1610065D862 /* strings.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = strings.c; sourceTree = "<group>"; };
		B57AE5DD19D7D1610065D862 /* strings.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = strings.h; sourceTree = "<group>"; };
		B57E3B43296A7DFC007F6872 /* FBTests */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = FBTests; sourceTree = BUILT_PRODUCTS_DIR; };
		B57E3B45296A7DFE007F6872 /* main.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = main.c; sourceTree = "<group>"; };
		B57E3B4A296A7E3D007F6872 /* libcmocka.0.7.0.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libcmocka.0.7.0.dylib; path = ../../../../../usr/local/Cellar/cmocka/1.1.5/lib/libcmocka.0.7.0.dylib; sourceTree = "<group>"; };
		B57E3B4C296A7EB6007F6872 /* FBTests.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = FBTests.entitlements; sourceTree = "<group>"; };
		B57E3B4E296A8BE4007F6872 /* test_redhammer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = test_redhammer.h; sourceTree = "<group>"; };
		B57E3B4F296A8BE4007F6872 /* test_redhammer.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = test_redhammer.c; sourceTree = "<group>"; };
		B57E3B51296A8F9A007F6872 /* test_sf2types.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = test_sf2types.h; sourceTree = "<group>"; };
		B57E3B52296A8F9A007F6872 /* test_sf2types.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = test_sf2types.c; sourceTree = "<group>"; };
		B5869A8313B561F1001844D9 /* graphics.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = graphics.h; sourceTree = "<group>"; };
		B5869A8413B561F1001844D9 /* graphics.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = graphics.c; sourceTree = "<group>"; };
		B5871ED51408E4F4004A45F5 /* pthreads.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pthreads.h; sourceTree = "<group>"; };
		B5871ED61408E4F4004A45F5 /* pthreads.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pthreads.c; sourceTree = "<group>"; };
		B5948A25156EDE260029278E /* ehonda_comp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ehonda_comp.c; sourceTree = "<group>"; };
		B5948A27156EDE3C0029278E /* ehonda_human.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ehonda_human.c; sourceTree = "<group>"; };
		B5948A4A156EE0F50029278E /* guile_human.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = guile_human.c; sourceTree = "<group>"; };
		B5948A4C156EE0FF0029278E /* guile_comp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = guile_comp.c; sourceTree = "<group>"; };
		B5948BE4156F1B610029278E /* chunli_comp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = chunli_comp.c; sourceTree = "<group>"; };
		B5948BE6156F1B6E0029278E /* chunli_human.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = chunli_human.c; sourceTree = "<group>"; };
		B59F4C931A2992B4003699C1 /* redhammer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = redhammer.h; sourceTree = "<group>"; };
		B5AC6A5F1550BDBB00C9339E /* testlib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = testlib.h; sourceTree = "<group>"; };
		B5AC6A601550BDBB00C9339E /* testlib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testlib.c; sourceTree = "<group>"; };
		B5ADB7A813B2B2EA00905DFE /* MainMenu.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MainMenu.xib; sourceTree = "<group>"; };
		B5ADB7AB13B2B34900905DFE /* OGLView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OGLView.h; sourceTree = "<group>"; };
		B5ADB7AC13B2B34900905DFE /* OGLView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = OGLView.m; sourceTree = "<group>"; };
		B5ADB7B013B2B38400905DFE /* trackball.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = trackball.c; sourceTree = "<group>"; };
		B5ADB7B113B2B38400905DFE /* trackball.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = trackball.h; sourceTree = "<group>"; };
		B5ADB7B913B2B3B300905DFE /* OpenGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGL.framework; path = System/Library/Frameworks/OpenGL.framework; sourceTree = SDKROOT; };
		B5ADB7BD13B2B3BE00905DFE /* GLUT.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLUT.framework; path = System/Library/Frameworks/GLUT.framework; sourceTree = SDKROOT; };
		B5ADB7E313B2B57D00905DFE /* actiondata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = actiondata.h; sourceTree = "<group>"; };
		B5ADB7E413B2B57D00905DFE /* actions.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = actions.c; sourceTree = "<group>"; };
		B5ADB7E513B2B57D00905DFE /* actions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = actions.h; sourceTree = "<group>"; };
		B5ADB7E613B2B57D00905DFE /* actions_198a.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = actions_198a.c; sourceTree = "<group>"; };
		B5ADB7E713B2B57D00905DFE /* actions_198a.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = actions_198a.h; sourceTree = "<group>"; };
		B5ADB7E813B2B57D00905DFE /* actions_530a.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = actions_530a.c; sourceTree = "<group>"; };
		B5ADB7E913B2B57D00905DFE /* actions_530a.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = actions_530a.h; sourceTree = "<group>"; };
		B5ADB7EA13B2B57D00905DFE /* actions_7c000.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = actions_7c000.c; sourceTree = "<group>"; };
		B5ADB7EB13B2B57D00905DFE /* actions_7c000.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = actions_7c000.h; sourceTree = "<group>"; };
		B5ADB7ED13B2B57D00905DFE /* ai.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ai.c; sourceTree = "<group>"; };
		B5ADB7EE13B2B57D00905DFE /* ai.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ai.h; sourceTree = "<group>"; };
		B5ADB7EF13B2B57D00905DFE /* aibyte_balrog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_balrog.h; sourceTree = "<group>"; };
		B5ADB7F013B2B57D00905DFE /* aibyte_blanka.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_blanka.h; sourceTree = "<group>"; };
		B5ADB7F113B2B57D00905DFE /* aibyte_chunli.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_chunli.h; sourceTree = "<group>"; };
		B5ADB7F213B2B57D00905DFE /* aibyte_dhalsim.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_dhalsim.h; sourceTree = "<group>"; };
		B5ADB7F313B2B57D00905DFE /* aibyte_ehonda.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_ehonda.h; sourceTree = "<group>"; };
		B5ADB7F413B2B57D00905DFE /* aibyte_guile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_guile.h; sourceTree = "<group>"; };
		B5ADB7F513B2B57D00905DFE /* aibyte_ken.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_ken.h; sourceTree = "<group>"; };
		B5ADB7F613B2B57D00905DFE /* aibyte_mbison.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_mbison.h; sourceTree = "<group>"; };
		B5ADB7F713B2B57D00905DFE /* aibyte_ryu.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_ryu.h; sourceTree = "<group>"; };
		B5ADB7F813B2B57D00905DFE /* aibyte_sagat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_sagat.h; sourceTree = "<group>"; };
		B5ADB7F913B2B57D00905DFE /* aibyte_vega.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_vega.h; sourceTree = "<group>"; };
		B5ADB7FA13B2B57D00905DFE /* aibyte_zangeif.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aibyte_zangeif.h; sourceTree = "<group>"; };
		B5ADB7FB13B2B57D00905DFE /* aidata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aidata.h; sourceTree = "<group>"; };
		B5ADB7FD13B2B57D00905DFE /* blanka.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = blanka.c; sourceTree = "<group>"; };
		B5ADB7FE13B2B57D00905DFE /* blanka.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = blanka.h; sourceTree = "<group>"; };
		B5ADB80013B2B57D00905DFE /* chunli.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = chunli.c; sourceTree = "<group>"; };
		B5ADB80113B2B57D00905DFE /* chunli.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = chunli.h; sourceTree = "<group>"; };
		B5ADB80313B2B57D00905DFE /* dhalsim.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = dhalsim.c; sourceTree = "<group>"; };
		B5ADB80413B2B57D00905DFE /* dhalsim.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dhalsim.h; sourceTree = "<group>"; };
		B5ADB80613B2B57D00905DFE /* ehonda.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ehonda.c; sourceTree = "<group>"; };
		B5ADB80713B2B57D00905DFE /* ehonda.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ehonda.h; sourceTree = "<group>"; };
		B5ADB80913B2B57D00905DFE /* guile.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = guile.c; sourceTree = "<group>"; };
		B5ADB80A13B2B57D00905DFE /* guile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = guile.h; sourceTree = "<group>"; };
		B5ADB80D13B2B57D00905DFE /* mbison.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mbison.c; sourceTree = "<group>"; };
		B5ADB80E13B2B57D00905DFE /* mbison.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mbison.h; sourceTree = "<group>"; };
		B5ADB81013B2B57D00905DFE /* playerdata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = playerdata.h; sourceTree = "<group>"; };
		B5ADB81113B2B57D00905DFE /* ryu.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ryu.c; sourceTree = "<group>"; };
		B5ADB81213B2B57D00905DFE /* ryu.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ryu.h; sourceTree = "<group>"; };
		B5ADB81413B2B57D00905DFE /* vega.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = vega.c; sourceTree = "<group>"; };
		B5ADB81513B2B57D00905DFE /* vega.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vega.h; sourceTree = "<group>"; };
		B5ADB81713B2B57D00905DFE /* collision.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = collision.c; sourceTree = "<group>"; };
		B5ADB81813B2B57D00905DFE /* collision.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = collision.h; sourceTree = "<group>"; };
		B5ADB81913B2B57D00905DFE /* compdata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = compdata.h; sourceTree = "<group>"; };
		B5ADB81A13B2B57D00905DFE /* computer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = computer.c; sourceTree = "<group>"; };
		B5ADB81B13B2B57D00905DFE /* computer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = computer.h; sourceTree = "<group>"; };
		B5ADB81C13B2B57D00905DFE /* effects.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = effects.c; sourceTree = "<group>"; };
		B5ADB81D13B2B57D00905DFE /* effects.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = effects.h; sourceTree = "<group>"; };
		B5ADB81E13B2B57D00905DFE /* fightgfx.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = fightgfx.c; sourceTree = "<group>"; };
		B5ADB81F13B2B57D00905DFE /* fightgfx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = fightgfx.h; sourceTree = "<group>"; };
		B5ADB82013B2B57D00905DFE /* gemu.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = gemu.c; path = ../FistBlue/gemu.c; sourceTree = "<group>"; };
		B5ADB82113B2B57D00905DFE /* gemu.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = gemu.h; path = ../FistBlue/gemu.h; sourceTree = "<group>"; };
		B5ADB82213B2B57D00905DFE /* gfx_glut.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = gfx_glut.c; path = ../FistBlue/gfx_glut.c; sourceTree = "<group>"; };
		B5ADB82313B2B57D00905DFE /* gfx_glut.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = gfx_glut.h; path = ../FistBlue/gfx_glut.h; sourceTree = "<group>"; };
		B5ADB82613B2B57D00905DFE /* projdata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = projdata.h; sourceTree = "<group>"; };
		B5ADB82813B2B57D00905DFE /* stagegfx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = stagegfx.h; sourceTree = "<group>"; };
		B5ADB82913B2B57D00905DFE /* gfxlib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = gfxlib.c; sourceTree = "<group>"; };
		B5ADB82A13B2B57D00905DFE /* gfxlib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = gfxlib.h; sourceTree = "<group>"; };
		B5ADB82B13B2B57D00905DFE /* gstate.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = gstate.c; sourceTree = "<group>"; };
		B5ADB82C13B2B57D00905DFE /* gstate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = gstate.h; sourceTree = "<group>"; };
		B5ADB82D13B2B57E00905DFE /* lib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = lib.c; sourceTree = "<group>"; };
		B5ADB82E13B2B57E00905DFE /* lib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lib.h; sourceTree = "<group>"; };
		B5ADB82F13B2B57E00905DFE /* libdata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = libdata.h; sourceTree = "<group>"; };
		B5ADB83013B2B57E00905DFE /* particle.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = particle.c; sourceTree = "<group>"; };
		B5ADB83113B2B57E00905DFE /* particle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = particle.h; sourceTree = "<group>"; };
		B5ADB83213B2B57E00905DFE /* player.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = player.c; sourceTree = "<group>"; };
		B5ADB83313B2B57E00905DFE /* player.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = player.h; sourceTree = "<group>"; };
		B5ADB83413B2B57E00905DFE /* playerselect.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = playerselect.c; sourceTree = "<group>"; };
		B5ADB83513B2B57E00905DFE /* playerselect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = playerselect.h; sourceTree = "<group>"; };
		B5ADB83613B2B57E00905DFE /* playerstate.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = playerstate.c; sourceTree = "<group>"; };
		B5ADB83713B2B57E00905DFE /* playerstate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = playerstate.h; sourceTree = "<group>"; };
		B5ADB83813B2B57E00905DFE /* projectiles.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = projectiles.c; sourceTree = "<group>"; };
		B5ADB83913B2B57E00905DFE /* projectiles.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = projectiles.h; sourceTree = "<group>"; };
		B5ADB83A13B2B57E00905DFE /* reactmode.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = reactmode.c; sourceTree = "<group>"; };
		B5ADB83B13B2B57E00905DFE /* reactmode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = reactmode.h; sourceTree = "<group>"; };
		B5ADB83C13B2B57E00905DFE /* rules.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = rules.c; sourceTree = "<group>"; };
		B5ADB83D13B2B57E00905DFE /* rules.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rules.h; sourceTree = "<group>"; };
		B5ADB83E13B2B57E00905DFE /* sf2const.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sf2const.h; sourceTree = "<group>"; };
		B5ADB83F13B2B57E00905DFE /* sf2macros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sf2macros.h; sourceTree = "<group>"; };
		B5ADB84013B2B57E00905DFE /* sf2types.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sf2types.h; sourceTree = "<group>"; };
		B5ADB84113B2B57E00905DFE /* sm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sm.c; sourceTree = "<group>"; };
		B5ADB84213B2B57E00905DFE /* sm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sm.h; sourceTree = "<group>"; };
		B5ADB84313B2B57E00905DFE /* sound.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sound.c; sourceTree = "<group>"; };
		B5ADB84413B2B57E00905DFE /* sound.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sound.h; sourceTree = "<group>"; };
		B5ADB84513B2B57E00905DFE /* sprite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sprite.c; sourceTree = "<group>"; };
		B5ADB84613B2B57E00905DFE /* sprite.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sprite.h; sourceTree = "<group>"; };
		B5ADB84713B2B57E00905DFE /* std_object.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = std_object.h; sourceTree = "<group>"; };
		B5ADB84813B2B57E00905DFE /* structs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = structs.h; sourceTree = "<group>"; };
		B5ADB84913B2B57E00905DFE /* task.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = task.c; sourceTree = "<group>"; };
		B5ADB84A13B2B57E00905DFE /* task.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = task.h; sourceTree = "<group>"; };
		B5ADB84B13B2B57E00905DFE /* text.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = text.h; sourceTree = "<group>"; };
		B5ADB84C13B2B57E00905DFE /* workarounds.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = workarounds.h; sourceTree = "<group>"; };
		B5ADB87013B2B59A00905DFE /* glwimp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = glwimp.c; sourceTree = "<group>"; };
		B5ADB87113B2B59A00905DFE /* glwimp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = glwimp.h; sourceTree = "<group>"; };
		B5AEA9D81405A46600DD9798 /* cpstests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cpstests.h; sourceTree = "<group>"; };
		B5AEA9D91405A46600DD9798 /* cpstests.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = cpstests.c; sourceTree = "<group>"; };
		B5B075B11554803E009D301D /* act2e_plane.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act2e_plane.h; sourceTree = "<group>"; };
		B5B075B21554803E009D301D /* act2e_plane.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act2e_plane.c; sourceTree = "<group>"; };
		B5B0761415548649009D301D /* act1e_worldflags.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act1e_worldflags.h; sourceTree = "<group>"; };
		B5B0761515548649009D301D /* act1e_worldflags.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act1e_worldflags.c; sourceTree = "<group>"; };
		B5B0763B155489B5009D301D /* act02_bicycleriders.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act02_bicycleriders.h; sourceTree = "<group>"; };
		B5B0763C155489B5009D301D /* act02_bicycleriders.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act02_bicycleriders.c; sourceTree = "<group>"; };
		B5B0764415548B28009D301D /* act07_elephants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = act07_elephants.h; sourceTree = "<group>"; };
		B5B0764515548B28009D301D /* act07_elephants.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = act07_elephants.c; sourceTree = "<group>"; };
		B5B39B69157832F200494662 /* sf2gfx.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = sf2gfx.bin; sourceTree = "<group>"; };
		B5B39BB01578345600494662 /* glutBasics.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = glutBasics.c; sourceTree = SOURCE_ROOT; };
		B5B39CF9157836E100494662 /* MT2-Info copy.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "MT2-Info copy.plist"; sourceTree = "<group>"; };
		B5B7EF222D4D5BD300D7D54F /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		B5B7EF232D4D7A5C00D7D54F /* allroms.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = allroms.bin; sourceTree = "<group>"; };
		B5C574171772781A00496283 /* MT2_tests-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "MT2_tests-Info.plist"; sourceTree = "<group>"; };
		B5C574191772781A00496283 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		B5C5741B1772781A00496283 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		B5C5741D1772781A00496283 /* MT2_tests-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MT2_tests-Prefix.pch"; sourceTree = "<group>"; };
		B5C5741F1772781A00496283 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.rtf; name = en; path = en.lproj/Credits.rtf; sourceTree = "<group>"; };
		B5C574211772781A00496283 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		B5C574221772781A00496283 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		B5C574251772781A00496283 /* en */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = en; path = en.lproj/MainMenu.xib; sourceTree = "<group>"; };
		B5C574831772782600496283 /* MT2 copy.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "MT2 copy.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B5C574841772782800496283 /* MT2 JAP-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "MT2 JAP-Info.plist"; sourceTree = SOURCE_ROOT; };
		B5E0F01117754D8800F50790 /* MT2 JAP.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "MT2 JAP.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B5E0F01217754D8A00F50790 /* MT2 copy-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "MT2 copy-Info.plist"; sourceTree = SOURCE_ROOT; };
		B5E0F06C17754D9500F50790 /* MT2 ETC.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "MT2 ETC.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B5E0F06D17754D9700F50790 /* MT2 ETC-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "MT2 ETC-Info.plist"; sourceTree = SOURCE_ROOT; };
		B5E21387156894FA00D96161 /* sagat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sagat.h; sourceTree = "<group>"; };
		B5E21388156894FA00D96161 /* sagat.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sagat.c; sourceTree = "<group>"; };
		B5E2138A1568950900D96161 /* balrog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = balrog.h; sourceTree = "<group>"; };
		B5E2138B1568950900D96161 /* balrog.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = balrog.c; sourceTree = "<group>"; };
		B5E8FD101776C7D400C6060E /* MT2 GLUT */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = "MT2 GLUT"; sourceTree = BUILT_PRODUCTS_DIR; };
		B5E8FD111776C7D400C6060E /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = System/Library/Frameworks/CoreFoundation.framework; sourceTree = SDKROOT; };
		B5E8FD161776C7D400C6060E /* MT2_GLUT.1 */ = {isa = PBXFileReference; lastKnownFileType = text.man; path = MT2_GLUT.1; sourceTree = "<group>"; };
		B5EB38DF149816D5007100DB /* reels.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = reels.h; sourceTree = "<group>"; };
		B5EB38E0149816D5007100DB /* reels.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = reels.c; sourceTree = "<group>"; };
		B5F335361562FFA4005A4637 /* zangeif.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = zangeif.h; sourceTree = "<group>"; };
		B5F335371562FFA4005A4637 /* zangeif.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = zangeif.c; sourceTree = "<group>"; };
		B5FD9F7E1488260A00DDF4D9 /* demo.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = demo.c; sourceTree = "<group>"; };
		B5FD9F7F1488260A00DDF4D9 /* demo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = demo.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8D11072E0486CEB800E47090 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D11072F0486CEB800E47090 /* Cocoa.framework in Frameworks */,
				B5ADB7BA13B2B3B300905DFE /* OpenGL.framework in Frameworks */,
				B5ADB7BE13B2B3BE00905DFE /* GLUT.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B51D1D14253BF34100CEB195 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B57E3B40296A7DFC007F6872 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B57E3B4B296A7E3D007F6872 /* libcmocka.0.7.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5C5747C1772782600496283 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5C5747D1772782600496283 /* Cocoa.framework in Frameworks */,
				B5C5747E1772782600496283 /* OpenGL.framework in Frameworks */,
				B5C5747F1772782600496283 /* GLUT.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E0F00A17754D8800F50790 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5E0F00B17754D8800F50790 /* Cocoa.framework in Frameworks */,
				B5E0F00C17754D8800F50790 /* OpenGL.framework in Frameworks */,
				B5E0F00D17754D8800F50790 /* GLUT.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E0F06517754D9500F50790 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5E0F06617754D9500F50790 /* Cocoa.framework in Frameworks */,
				B5E0F06717754D9500F50790 /* OpenGL.framework in Frameworks */,
				B5E0F06817754D9500F50790 /* GLUT.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E8FD0D1776C7D400C6060E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B51D1CA9253BE0C300CEB195 /* OpenGL.framework in Frameworks */,
				B51D1CA1253BE0B500CEB195 /* GLUT.framework in Frameworks */,
				B5E8FD121776C7D400C6060E /* CoreFoundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		080E96DDFE201D6D7F000001 /* Classes */ = {
			isa = PBXGroup;
			children = (
				B5ADB7AB13B2B34900905DFE /* OGLView.h */,
				B5ADB7AC13B2B34900905DFE /* OGLView.m */,
				256AC3D80F4B6AC300CF3369 /* MT2AppDelegate.h */,
				256AC3D90F4B6AC300CF3369 /* MT2AppDelegate.m */,
			);
			name = Classes;
			sourceTree = "<group>";
		};
		1058C7A0FEA54F0111CA2CBB /* Linked Frameworks */ = {
			isa = PBXGroup;
			children = (
				1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */,
			);
			name = "Linked Frameworks";
			sourceTree = "<group>";
		};
		1058C7A2FEA54F0111CA2CBB /* Other Frameworks */ = {
			isa = PBXGroup;
			children = (
				29B97324FDCFA39411CA2CEA /* AppKit.framework */,
				13E42FB307B3F0F600E4EEF1 /* CoreData.framework */,
				29B97325FDCFA39411CA2CEA /* Foundation.framework */,
			);
			name = "Other Frameworks";
			sourceTree = "<group>";
		};
		19C28FACFE9D520D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				8D1107320486CEB800E47090 /* MT2.app */,
				B5C574831772782600496283 /* MT2 copy.app */,
				B5E0F01117754D8800F50790 /* MT2 JAP.app */,
				B5E0F06C17754D9500F50790 /* MT2 ETC.app */,
				B5E8FD101776C7D400C6060E /* MT2 GLUT */,
				B51D1D17253BF34100CEB195 /* MT2-GLcore.app */,
				B57E3B43296A7DFC007F6872 /* FBTests */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEA /* MT2 */ = {
			isa = PBXGroup;
			children = (
				B5B7EF222D4D5BD300D7D54F /* README.md */,
				080E96DDFE201D6D7F000001 /* Classes */,
				29B97315FDCFA39411CA2CEA /* Other Sources */,
				29B97317FDCFA39411CA2CEA /* Resources */,
				B5C574151772781900496283 /* MT2_tests */,
				B5E8FD131776C7D400C6060E /* MT2 GLUT */,
				B51D1D18253BF34100CEB195 /* MT2-GLcore */,
				B57E3B44296A7DFE007F6872 /* FBTests */,
				29B97323FDCFA39411CA2CEA /* Frameworks */,
				19C28FACFE9D520D11CA2CBB /* Products */,
				B5C574841772782800496283 /* MT2 JAP-Info.plist */,
				B5E0F01217754D8A00F50790 /* MT2 copy-Info.plist */,
				B5E0F06D17754D9700F50790 /* MT2 ETC-Info.plist */,
			);
			name = MT2;
			sourceTree = "<group>";
		};
		29B97315FDCFA39411CA2CEA /* Other Sources */ = {
			isa = PBXGroup;
			children = (
				B56A715B1411A9D500DF7F02 /* SiennaBird */,
				B5ADB8B913B2BB5E00905DFE /* RedHammer */,
				B5ADB86F13B2B59A00905DFE /* SwiftBeam */,
				B5ADB7E113B2B57D00905DFE /* FistBlue */,
				B5ADB7B013B2B38400905DFE /* trackball.c */,
				B5ADB7B113B2B38400905DFE /* trackball.h */,
				256AC3F00F4B6AF500CF3369 /* MT2_Prefix.pch */,
				29B97316FDCFA39411CA2CEA /* main.m */,
			);
			name = "Other Sources";
			sourceTree = "<group>";
		};
		29B97317FDCFA39411CA2CEA /* Resources */ = {
			isa = PBXGroup;
			children = (
				B5B7EF232D4D7A5C00D7D54F /* allroms.bin */,
				6CF004571C2A128C008EA965 /* sf2_05.bin */,
				6CF004581C2A128C008EA965 /* sf2_06.bin */,
				6CF004591C2A128C008EA965 /* sf2_07.bin */,
				6CF0045A1C2A128C008EA965 /* sf2_08.bin */,
				6CF0045B1C2A128C008EA965 /* sf2_09.bin */,
				6CF0045C1C2A128C008EA965 /* sf2_14.bin */,
				6CF0045D1C2A128C008EA965 /* sf2_15.bin */,
				6CF0045E1C2A128C008EA965 /* sf2_16.bin */,
				6CF0045F1C2A128C008EA965 /* sf2_17.bin */,
				6CF004601C2A128C008EA965 /* sf2_18.bin */,
				6CF004611C2A128C008EA965 /* sf2_19.bin */,
				6CF004621C2A128C008EA965 /* sf2_24.bin */,
				6CF004631C2A128C008EA965 /* sf2_25.bin */,
				6CF004641C2A128C008EA965 /* sf2_26.bin */,
				6CF004651C2A128C008EA965 /* sf2_27.bin */,
				6CF004661C2A128C008EA965 /* sf2_29a.bin */,
				6CF004671C2A128C008EA965 /* sf2_36a.bin */,
				6CF004681C2A128C008EA965 /* sf2u.37a */,
				6CF004691C2A128C008EA965 /* sf2u.35a */,
				6CF0046A1C2A128C008EA965 /* sf2u.31a */,
				6CF0046B1C2A128C008EA965 /* sf2u.30a */,
				6CF0046C1C2A128C008EA965 /* sf2u.28a */,
				6CF0046D1C2A128C008EA965 /* sf2u.38a */,
				B5B39B69157832F200494662 /* sf2gfx.bin */,
				B5ADB7A813B2B2EA00905DFE /* MainMenu.xib */,
				8D1107310486CEB800E47090 /* MT2-Info.plist */,
				089C165CFE840E0CC02AAC07 /* InfoPlist.strings */,
				B5B39CF9157836E100494662 /* MT2-Info copy.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B57E3B4A296A7E3D007F6872 /* libcmocka.0.7.0.dylib */,
				B51D1CC8253BE4C400CEB195 /* OpenGLES.framework */,
				1058C7A0FEA54F0111CA2CBB /* Linked Frameworks */,
				B5E8FD111776C7D400C6060E /* CoreFoundation.framework */,
				B52124AD18D65A6D00D2F649 /* Foundation.framework */,
				B52124AF18D65A6D00D2F649 /* CoreGraphics.framework */,
				1058C7A2FEA54F0111CA2CBB /* Other Frameworks */,
				B5ADB7B913B2B3B300905DFE /* OpenGL.framework */,
				B5ADB7BD13B2B3BE00905DFE /* GLUT.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B51533EF147EEB1A004A87B0 /* actions_b */ = {
			isa = PBXGroup;
			children = (
				B51533F0147EEB4F004A87B0 /* car.h */,
				B51533F1147EEB4F004A87B0 /* car.c */,
				B51533F3147EEBDB004A87B0 /* drums.h */,
				B51533F4147EEBDB004A87B0 /* drums.c */,
				6C8C65241C37717200635FDB /* barrels.c */,
				6C8C65251C37717200635FDB /* barrels.h */,
			);
			name = actions_b;
			sourceTree = "<group>";
		};
		B51D1D18253BF34100CEB195 /* MT2-GLcore */ = {
			isa = PBXGroup;
			children = (
				B519C713253C27020084B88D /* PointSprite.swift */,
				B51D1D84253BFC0E00CEB195 /* Shaders */,
				B51D1D63253BF67A00CEB195 /* Matrix4.swift */,
				B51D1D19253BF34100CEB195 /* AppDelegate.swift */,
				B51D1D1B253BF34300CEB195 /* Assets.xcassets */,
				B51D1D1D253BF34300CEB195 /* MainMenu.xib */,
				B51D1D20253BF34300CEB195 /* Info.plist */,
				B51D1D21253BF34300CEB195 /* MT2_GLcore.entitlements */,
				B51D1D2B253BF3A300CEB195 /* MyNSOpenGLView.swift */,
				B51D1D6B253BF79100CEB195 /* ShaderProgram.swift */,
				B51D1D95253BFD0D00CEB195 /* Game.swift */,
				B519C6B4253C04A60084B88D /* game-Bridging-Header.h */,
			);
			path = "MT2-GLcore";
			sourceTree = "<group>";
		};
		B51D1D84253BFC0E00CEB195 /* Shaders */ = {
			isa = PBXGroup;
			children = (
				B51D1D85253BFC2500CEB195 /* pointshader.fs */,
				B51D1D86253BFC2500CEB195 /* pointshader.vs */,
				B517DD582543E8E000649292 /* tileshader.vs */,
				B517DD602543E8F300649292 /* tileshader.fs */,
			);
			path = Shaders;
			sourceTree = "<group>";
		};
		B54FDCC62543AB680092E183 /* scrolls */ = {
			isa = PBXGroup;
			children = (
				B5ADB82B13B2B57D00905DFE /* gstate.c */,
				B5ADB82C13B2B57D00905DFE /* gstate.h */,
				B54FDCC72543AB940092E183 /* scroll_maint.h */,
				B54FDCC82543AB940092E183 /* scroll_maint.c */,
				B54FDCD52543AD230092E183 /* parallax.h */,
				B54FDCD62543AD230092E183 /* parallax.c */,
				B54FDCE32543ADC10092E183 /* scroll_util.h */,
				B54FDCE42543ADC10092E183 /* scroll_util.c */,
				B54FDCF12543ADF20092E183 /* scroll_data.h */,
				B54FDCF22543ADF20092E183 /* scroll_data.c */,
				B54FDCFF2543C8B20092E183 /* scroll.h */,
			);
			path = scrolls;
			sourceTree = "<group>";
		};
		B55891401485AB1B00BBB0BA /* coll */ = {
			isa = PBXGroup;
			children = (
				B5ADB81713B2B57D00905DFE /* collision.c */,
				B5ADB81813B2B57D00905DFE /* collision.h */,
				B55891411485AB2A00BBB0BA /* coll_projectile.h */,
				B55891421485AB2A00BBB0BA /* coll_projectile.c */,
				B558918D1485B04A00BBB0BA /* coll_bonus.h */,
				B558918E1485B04A00BBB0BA /* coll_bonus.c */,
			);
			name = coll;
			sourceTree = "<group>";
		};
		B56A715B1411A9D500DF7F02 /* SiennaBird */ = {
			isa = PBXGroup;
			children = (
				B56A715C1411A9FF00DF7F02 /* cps */,
			);
			path = SiennaBird;
			sourceTree = "<group>";
		};
		B56A715C1411A9FF00DF7F02 /* cps */ = {
			isa = PBXGroup;
			children = (
				B56A715D1411AA1000DF7F02 /* io.h */,
			);
			name = cps;
			sourceTree = "<group>";
		};
		B572140D19DF32D000160398 /* GLUT App */ = {
			isa = PBXGroup;
			children = (
				B5B39BB01578345600494662 /* glutBasics.c */,
			);
			name = "GLUT App";
			sourceTree = "<group>";
		};
		B57E3B44296A7DFE007F6872 /* FBTests */ = {
			isa = PBXGroup;
			children = (
				B57E3B4C296A7EB6007F6872 /* FBTests.entitlements */,
				B57E3B45296A7DFE007F6872 /* main.c */,
				B57E3B4E296A8BE4007F6872 /* test_redhammer.h */,
				B57E3B4F296A8BE4007F6872 /* test_redhammer.c */,
				B57E3B51296A8F9A007F6872 /* test_sf2types.h */,
				B57E3B52296A8F9A007F6872 /* test_sf2types.c */,
			);
			path = FBTests;
			sourceTree = "<group>";
		};
		B5ADB7E113B2B57D00905DFE /* FistBlue */ = {
			isa = PBXGroup;
			children = (
				B54FDCC62543AB680092E183 /* scrolls */,
				B55891401485AB1B00BBB0BA /* coll */,
				B5AEA9D61405A44200DD9798 /* tests */,
				B5ADB7E213B2B57D00905DFE /* actions */,
				B5ADB7EC13B2B57D00905DFE /* ai */,
				B5ADB7FC13B2B57D00905DFE /* avatars */,
				B551553314773E8A00A8BBD9 /* coinage.h */,
				B551553414773E8A00A8BBD9 /* coinage.c */,
				B5ADB81913B2B57D00905DFE /* compdata.h */,
				B5ADB81A13B2B57D00905DFE /* computer.c */,
				B5ADB81B13B2B57D00905DFE /* computer.h */,
				B5FD9F7E1488260A00DDF4D9 /* demo.c */,
				B5FD9F7F1488260A00DDF4D9 /* demo.h */,
				B5ADB81C13B2B57D00905DFE /* effects.c */,
				B5ADB81D13B2B57D00905DFE /* effects.h */,
				B5ADB81E13B2B57D00905DFE /* fightgfx.c */,
				B5ADB81F13B2B57D00905DFE /* fightgfx.h */,
				B56A718A1411B0D300DF7F02 /* game.h */,
				B56A718B1411B0D300DF7F02 /* game.c */,
				B5ADB82413B2B57D00905DFE /* gfxdata */,
				B5ADB82913B2B57D00905DFE /* gfxlib.c */,
				B5ADB82A13B2B57D00905DFE /* gfxlib.h */,
				B5ADB82D13B2B57E00905DFE /* lib.c */,
				B5ADB82E13B2B57E00905DFE /* lib.h */,
				B5ADB82F13B2B57E00905DFE /* libdata.h */,
				B5ADB83013B2B57E00905DFE /* particle.c */,
				B5ADB83113B2B57E00905DFE /* particle.h */,
				B5ADB83213B2B57E00905DFE /* player.c */,
				B5ADB83313B2B57E00905DFE /* player.h */,
				B5ADB83413B2B57E00905DFE /* playerselect.c */,
				B5ADB83513B2B57E00905DFE /* playerselect.h */,
				B5ADB83613B2B57E00905DFE /* playerstate.c */,
				B5ADB83713B2B57E00905DFE /* playerstate.h */,
				B5ADB83813B2B57E00905DFE /* projectiles.c */,
				B5ADB83913B2B57E00905DFE /* projectiles.h */,
				B5ADB83A13B2B57E00905DFE /* reactmode.c */,
				B5ADB83B13B2B57E00905DFE /* reactmode.h */,
				B5ADB83C13B2B57E00905DFE /* rules.c */,
				B5ADB83D13B2B57E00905DFE /* rules.h */,
				B5ADB83E13B2B57E00905DFE /* sf2const.h */,
				B55196BB148B0385002300A3 /* sf2io.h */,
				B5ADB83F13B2B57E00905DFE /* sf2macros.h */,
				B5ADB84013B2B57E00905DFE /* sf2types.h */,
				B5ADB84113B2B57E00905DFE /* sm.c */,
				B5ADB84213B2B57E00905DFE /* sm.h */,
				B5ADB84313B2B57E00905DFE /* sound.c */,
				B5ADB84413B2B57E00905DFE /* sound.h */,
				B5ADB84513B2B57E00905DFE /* sprite.c */,
				B5ADB84613B2B57E00905DFE /* sprite.h */,
				B5ADB84713B2B57E00905DFE /* std_object.h */,
				B5ADB84813B2B57E00905DFE /* structs.h */,
				B5ADB84913B2B57E00905DFE /* task.c */,
				B5ADB84A13B2B57E00905DFE /* task.h */,
				B5613C3A155F9917001E127A /* text.c */,
				B5ADB84B13B2B57E00905DFE /* text.h */,
				B529FCEC15606B0A005EE9B7 /* textdata.c */,
				B5ADB84C13B2B57E00905DFE /* workarounds.h */,
				B550DEA116BBA1BF00983A54 /* endings.h */,
				B550DEA216BBA1BF00983A54 /* endings.c */,
				B572140C19DE70AB00160398 /* sf2.h */,
			);
			path = FistBlue;
			sourceTree = "<group>";
		};
		B5ADB7E213B2B57D00905DFE /* actions */ = {
			isa = PBXGroup;
			children = (
				B51533EF147EEB1A004A87B0 /* actions_b */,
				B56E0DC913B95EB600FC837B /* actions_198a_data.h */,
				B5ADB7E313B2B57D00905DFE /* actiondata.h */,
				B5ADB7E413B2B57D00905DFE /* actions.c */,
				B5ADB7E513B2B57D00905DFE /* actions.h */,
				B5ADB7E613B2B57D00905DFE /* actions_198a.c */,
				B5ADB7E713B2B57D00905DFE /* actions_198a.h */,
				B5ADB7E813B2B57D00905DFE /* actions_530a.c */,
				B5ADB7E913B2B57D00905DFE /* actions_530a.h */,
				B5ADB7EA13B2B57D00905DFE /* actions_7c000.c */,
				B5ADB7EB13B2B57D00905DFE /* actions_7c000.h */,
				B5EB38DF149816D5007100DB /* reels.h */,
				B5EB38E0149816D5007100DB /* reels.c */,
				B5B0763B155489B5009D301D /* act02_bicycleriders.h */,
				B5B0763C155489B5009D301D /* act02_bicycleriders.c */,
				B5B0764415548B28009D301D /* act07_elephants.h */,
				B5B0764515548B28009D301D /* act07_elephants.c */,
				B53EBC7E155F91F900581C8D /* act17.h */,
				B53EBC7F155F91F900581C8D /* act17.c */,
				B5B0761415548649009D301D /* act1e_worldflags.h */,
				B5B0761515548649009D301D /* act1e_worldflags.c */,
				B53EBC57155F86F400581C8D /* act29_wwlogo.h */,
				B53EBC58155F86F400581C8D /* act29_wwlogo.c */,
				B5B075B11554803E009D301D /* act2e_plane.h */,
				B5B075B21554803E009D301D /* act2e_plane.c */,
				B53EBC60155F8A4600581C8D /* act3e_capcomlogos.h */,
				B53EBC61155F8A4600581C8D /* act3e_capcomlogos.c */,
				B53EBC6C155F8C0600581C8D /* act_3f.h */,
				B53EBC6D155F8C0600581C8D /* act_3f.c */,
				B53EBC82155F927300581C8D /* act16.h */,
				B53EBC83155F927300581C8D /* act16.c */,
			);
			path = actions;
			sourceTree = "<group>";
		};
		B5ADB7EC13B2B57D00905DFE /* ai */ = {
			isa = PBXGroup;
			children = (
				B5ADB7ED13B2B57D00905DFE /* ai.c */,
				B5ADB7EE13B2B57D00905DFE /* ai.h */,
				B5ADB7EF13B2B57D00905DFE /* aibyte_balrog.h */,
				B5ADB7F013B2B57D00905DFE /* aibyte_blanka.h */,
				B5ADB7F113B2B57D00905DFE /* aibyte_chunli.h */,
				B5ADB7F213B2B57D00905DFE /* aibyte_dhalsim.h */,
				B5ADB7F313B2B57D00905DFE /* aibyte_ehonda.h */,
				B5ADB7F413B2B57D00905DFE /* aibyte_guile.h */,
				B5ADB7F513B2B57D00905DFE /* aibyte_ken.h */,
				B5ADB7F613B2B57D00905DFE /* aibyte_mbison.h */,
				B5ADB7F713B2B57D00905DFE /* aibyte_ryu.h */,
				B5ADB7F813B2B57D00905DFE /* aibyte_sagat.h */,
				B5ADB7F913B2B57D00905DFE /* aibyte_vega.h */,
				B5ADB7FA13B2B57D00905DFE /* aibyte_zangeif.h */,
				B5ADB7FB13B2B57D00905DFE /* aidata.h */,
			);
			path = ai;
			sourceTree = "<group>";
		};
		B5ADB7FC13B2B57D00905DFE /* avatars */ = {
			isa = PBXGroup;
			children = (
				B5E2138B1568950900D96161 /* balrog.c */,
				B5E2138A1568950900D96161 /* balrog.h */,
				B5ADB7FD13B2B57D00905DFE /* blanka.c */,
				B5ADB7FE13B2B57D00905DFE /* blanka.h */,
				B56BFC67156B43C500DC7BAD /* blanka_comp.c */,
				B56BFC6F156B444F00DC7BAD /* blanka_human.c */,
				B5ADB80013B2B57D00905DFE /* chunli.c */,
				B5ADB80113B2B57D00905DFE /* chunli.h */,
				B5ADB80313B2B57D00905DFE /* dhalsim.c */,
				B5ADB80413B2B57D00905DFE /* dhalsim.h */,
				B5ADB80613B2B57D00905DFE /* ehonda.c */,
				B5ADB80713B2B57D00905DFE /* ehonda.h */,
				B5948A25156EDE260029278E /* ehonda_comp.c */,
				B5948A27156EDE3C0029278E /* ehonda_human.c */,
				B5ADB80913B2B57D00905DFE /* guile.c */,
				B5ADB80A13B2B57D00905DFE /* guile.h */,
				B5948A4A156EE0F50029278E /* guile_human.c */,
				B5948A4C156EE0FF0029278E /* guile_comp.c */,
				B5ADB80D13B2B57D00905DFE /* mbison.c */,
				B5ADB80E13B2B57D00905DFE /* mbison.h */,
				B5ADB81013B2B57D00905DFE /* playerdata.h */,
				B5ADB81113B2B57D00905DFE /* ryu.c */,
				B5ADB81213B2B57D00905DFE /* ryu.h */,
				B56BFC8E156B48F600DC7BAD /* ryuken_human.c */,
				B56BFC91156B48FD00DC7BAD /* ryuken_comp.c */,
				B5E21387156894FA00D96161 /* sagat.h */,
				B5E21388156894FA00D96161 /* sagat.c */,
				B5ADB81413B2B57D00905DFE /* vega.c */,
				B5ADB81513B2B57D00905DFE /* vega.h */,
				B5F335361562FFA4005A4637 /* zangeif.h */,
				B5F335371562FFA4005A4637 /* zangeif.c */,
				B5948BE4156F1B610029278E /* chunli_comp.c */,
				B5948BE6156F1B6E0029278E /* chunli_human.c */,
			);
			path = avatars;
			sourceTree = "<group>";
		};
		B5ADB82413B2B57D00905DFE /* gfxdata */ = {
			isa = PBXGroup;
			children = (
				B5ADB82613B2B57D00905DFE /* projdata.h */,
				B5ADB82813B2B57D00905DFE /* stagegfx.h */,
				B5869A8313B561F1001844D9 /* graphics.h */,
				B5869A8413B561F1001844D9 /* graphics.c */,
			);
			path = gfxdata;
			sourceTree = "<group>";
		};
		B5ADB86F13B2B59A00905DFE /* SwiftBeam */ = {
			isa = PBXGroup;
			children = (
				B5ADB87013B2B59A00905DFE /* glwimp.c */,
				B5ADB87113B2B59A00905DFE /* glwimp.h */,
			);
			path = SwiftBeam;
			sourceTree = "<group>";
		};
		B5ADB8B913B2BB5E00905DFE /* RedHammer */ = {
			isa = PBXGroup;
			children = (
				B572140D19DF32D000160398 /* GLUT App */,
				B5ADB82213B2B57D00905DFE /* gfx_glut.c */,
				B5ADB82313B2B57D00905DFE /* gfx_glut.h */,
				B5ADB82013B2B57D00905DFE /* gemu.c */,
				B5ADB82113B2B57D00905DFE /* gemu.h */,
				B5871ED51408E4F4004A45F5 /* pthreads.h */,
				B5871ED61408E4F4004A45F5 /* pthreads.c */,
				B57AE5DC19D7D1610065D862 /* strings.c */,
				B57AE5DD19D7D1610065D862 /* strings.h */,
				B59F4C931A2992B4003699C1 /* redhammer.h */,
				B50BCA161C040AF300C7A6F4 /* cps_tile.h */,
				6CF004551C2A01C9008EA965 /* redhammer.c */,
				B519C721253C92EC0084B88D /* gfx_glcore.h */,
				B519C722253C92EC0084B88D /* gfx_glcore.c */,
			);
			path = RedHammer;
			sourceTree = "<group>";
		};
		B5AEA9D61405A44200DD9798 /* tests */ = {
			isa = PBXGroup;
			children = (
				B5AEA9D81405A46600DD9798 /* cpstests.h */,
				B5AEA9D91405A46600DD9798 /* cpstests.c */,
				B56BBD5314859AA100FF84CB /* fbtests.h */,
				B56BBD5414859AA100FF84CB /* fbtests.c */,
				B527F61315508A0800BC70D8 /* aitests.h */,
				B527F61415508A0800BC70D8 /* aitests.c */,
				B5AC6A5F1550BDBB00C9339E /* testlib.h */,
				B5AC6A601550BDBB00C9339E /* testlib.c */,
			);
			path = tests;
			sourceTree = "<group>";
		};
		B5C574151772781900496283 /* MT2_tests */ = {
			isa = PBXGroup;
			children = (
				B5C574211772781A00496283 /* AppDelegate.h */,
				B5C574221772781A00496283 /* AppDelegate.m */,
				B5C574241772781A00496283 /* MainMenu.xib */,
				B5C574161772781A00496283 /* Supporting Files */,
			);
			path = MT2_tests;
			sourceTree = "<group>";
		};
		B5C574161772781A00496283 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				B5C574171772781A00496283 /* MT2_tests-Info.plist */,
				B5C574181772781A00496283 /* InfoPlist.strings */,
				B5C5741B1772781A00496283 /* main.m */,
				B5C5741D1772781A00496283 /* MT2_tests-Prefix.pch */,
				B5C5741E1772781A00496283 /* Credits.rtf */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		B5E8FD131776C7D400C6060E /* MT2 GLUT */ = {
			isa = PBXGroup;
			children = (
				B5E8FD161776C7D400C6060E /* MT2_GLUT.1 */,
			);
			path = "MT2 GLUT";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8D1107260486CEB800E47090 /* MT2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C01FCF4A08A954540054247B /* Build configuration list for PBXNativeTarget "MT2" */;
			buildPhases = (
				8D1107290486CEB800E47090 /* Resources */,
				8D11072C0486CEB800E47090 /* Sources */,
				8D11072E0486CEB800E47090 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MT2;
			productInstallPath = "$(HOME)/Applications";
			productName = MT2;
			productReference = 8D1107320486CEB800E47090 /* MT2.app */;
			productType = "com.apple.product-type.application";
		};
		B51D1D16253BF34100CEB195 /* MT2-GLcore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B51D1D24253BF34300CEB195 /* Build configuration list for PBXNativeTarget "MT2-GLcore" */;
			buildPhases = (
				B51D1D13253BF34100CEB195 /* Sources */,
				B51D1D14253BF34100CEB195 /* Frameworks */,
				B51D1D15253BF34100CEB195 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MT2-GLcore";
			productName = "MT2-GLcore";
			productReference = B51D1D17253BF34100CEB195 /* MT2-GLcore.app */;
			productType = "com.apple.product-type.application";
		};
		B57E3B42296A7DFC007F6872 /* FBTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B57E3B49296A7DFE007F6872 /* Build configuration list for PBXNativeTarget "FBTests" */;
			buildPhases = (
				B57E3B3F296A7DFC007F6872 /* Sources */,
				B57E3B40296A7DFC007F6872 /* Frameworks */,
				B57E3B41296A7DFC007F6872 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FBTests;
			productName = FBTests;
			productReference = B57E3B43296A7DFC007F6872 /* FBTests */;
			productType = "com.apple.product-type.tool";
		};
		B5C5742A1772782600496283 /* MT2 tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5C574801772782600496283 /* Build configuration list for PBXNativeTarget "MT2 tests" */;
			buildPhases = (
				B5C5742B1772782600496283 /* Resources */,
				B5C574301772782600496283 /* Sources */,
				B5C5747C1772782600496283 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MT2 tests";
			productInstallPath = "$(HOME)/Applications";
			productName = MT2;
			productReference = B5C574831772782600496283 /* MT2 copy.app */;
			productType = "com.apple.product-type.application";
		};
		B5E0EFB817754D8800F50790 /* MT2 JAP */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5E0F00E17754D8800F50790 /* Build configuration list for PBXNativeTarget "MT2 JAP" */;
			buildPhases = (
				B5E0EFB917754D8800F50790 /* Resources */,
				B5E0EFBE17754D8800F50790 /* Sources */,
				B5E0F00A17754D8800F50790 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MT2 JAP";
			productInstallPath = "$(HOME)/Applications";
			productName = MT2;
			productReference = B5E0F01117754D8800F50790 /* MT2 JAP.app */;
			productType = "com.apple.product-type.application";
		};
		B5E0F01317754D9500F50790 /* MT2 ETC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5E0F06917754D9500F50790 /* Build configuration list for PBXNativeTarget "MT2 ETC" */;
			buildPhases = (
				B5E0F01417754D9500F50790 /* Resources */,
				B5E0F01917754D9500F50790 /* Sources */,
				B5E0F06517754D9500F50790 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MT2 ETC";
			productInstallPath = "$(HOME)/Applications";
			productName = MT2;
			productReference = B5E0F06C17754D9500F50790 /* MT2 ETC.app */;
			productType = "com.apple.product-type.application";
		};
		B5E8FD0F1776C7D400C6060E /* MT2 GLUT */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5E8FD1A1776C7D400C6060E /* Build configuration list for PBXNativeTarget "MT2 GLUT" */;
			buildPhases = (
				B5E8FD0C1776C7D400C6060E /* Sources */,
				B5E8FD0D1776C7D400C6060E /* Frameworks */,
				B5E8FD0E1776C7D400C6060E /* CopyFiles */,
				B5E8FD671776DF2900C6060E /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MT2 GLUT";
			productName = "MT2 GLUT";
			productReference = B5E8FD101776C7D400C6060E /* MT2 GLUT */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1200;
				LastUpgradeCheck = 1200;
				TargetAttributes = {
					B51D1D16253BF34100CEB195 = {
						CreatedOnToolsVersion = 12.0;
						DevelopmentTeam = L2D42QR3GV;
						ProvisioningStyle = Automatic;
					};
					B57E3B42296A7DFC007F6872 = {
						CreatedOnToolsVersion = 14.2;
						DevelopmentTeam = L2D42QR3GV;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "MT2" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
				en,
				Base,
			);
			mainGroup = 29B97314FDCFA39411CA2CEA /* MT2 */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8D1107260486CEB800E47090 /* MT2 */,
				B5C5742A1772782600496283 /* MT2 tests */,
				B5E0EFB817754D8800F50790 /* MT2 JAP */,
				B5E0F01317754D9500F50790 /* MT2 ETC */,
				B5E8FD0F1776C7D400C6060E /* MT2 GLUT */,
				B51D1D16253BF34100CEB195 /* MT2-GLcore */,
				B57E3B42296A7DFC007F6872 /* FBTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8D1107290486CEB800E47090 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B51B92A12D4DDF8100C243EB /* allroms.bin in Resources */,
				B51B92A22D4DDF8500C243EB /* sf2gfx.bin in Resources */,
				8D11072B0486CEB800E47090 /* InfoPlist.strings in Resources */,
				B5ADB7A913B2B2EA00905DFE /* MainMenu.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B51D1D15253BF34100CEB195 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B51D1D8D253BFC7700CEB195 /* pointshader.fs in Resources */,
				B51D1D8E253BFC7700CEB195 /* pointshader.vs in Resources */,
				B51D1D73253BFA3C00CEB195 /* sf2gfx.bin in Resources */,
				B517DD592543E8E000649292 /* tileshader.vs in Resources */,
				B5B7EF272D4D7A7400D7D54F /* allroms.bin in Resources */,
				B51D1D1C253BF34300CEB195 /* Assets.xcassets in Resources */,
				B517DD612543E8F300649292 /* tileshader.fs in Resources */,
				B51D1D1F253BF34300CEB195 /* MainMenu.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5C5742B1772782600496283 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5B7EF262D4D7A7300D7D54F /* allroms.bin in Resources */,
				6C6F0DF51C349FED004CD78E /* sf2_26.bin in Resources */,
				6C6F0DF71C349FED004CD78E /* sf2_29a.bin in Resources */,
				B5C5742C1772782600496283 /* InfoPlist.strings in Resources */,
				6C6F0DF31C349FED004CD78E /* sf2_24.bin in Resources */,
				6C6F0DFE1C349FED004CD78E /* sf2u.38a in Resources */,
				6C6F0DF41C349FED004CD78E /* sf2_25.bin in Resources */,
				6C6F0DEE1C349FED004CD78E /* sf2_15.bin in Resources */,
				6C6F0DEB1C349FED004CD78E /* sf2_08.bin in Resources */,
				6C6F0DF01C349FED004CD78E /* sf2_17.bin in Resources */,
				6C6F0DE91C349FED004CD78E /* sf2_06.bin in Resources */,
				6C6F0DED1C349FED004CD78E /* sf2_14.bin in Resources */,
				6C6F0DFC1C349FED004CD78E /* sf2u.30a in Resources */,
				6C6F0DFB1C349FED004CD78E /* sf2u.31a in Resources */,
				6C6F0DFD1C349FED004CD78E /* sf2u.28a in Resources */,
				6C6F0DEF1C349FED004CD78E /* sf2_16.bin in Resources */,
				6C6F0DEA1C349FED004CD78E /* sf2_07.bin in Resources */,
				6C6F0DF11C349FED004CD78E /* sf2_18.bin in Resources */,
				6C6F0DF21C349FED004CD78E /* sf2_19.bin in Resources */,
				6C6F0DF91C349FED004CD78E /* sf2u.37a in Resources */,
				6C6F0DEC1C349FED004CD78E /* sf2_09.bin in Resources */,
				B5C5742D1772782600496283 /* MainMenu.xib in Resources */,
				6C6F0DFA1C349FED004CD78E /* sf2u.35a in Resources */,
				6C6F0DF61C349FED004CD78E /* sf2_27.bin in Resources */,
				6C6F0DF81C349FED004CD78E /* sf2_36a.bin in Resources */,
				B5C5742F1772782600496283 /* sf2gfx.bin in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E0EFB917754D8800F50790 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5B7EF252D4D7A7200D7D54F /* allroms.bin in Resources */,
				B5E0EFBA17754D8800F50790 /* InfoPlist.strings in Resources */,
				B5E0EFBB17754D8800F50790 /* MainMenu.xib in Resources */,
				B5E0EFBD17754D8800F50790 /* sf2gfx.bin in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E0F01417754D9500F50790 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5B7EF242D4D7A7200D7D54F /* allroms.bin in Resources */,
				B5E0F01517754D9500F50790 /* InfoPlist.strings in Resources */,
				B5E0F01617754D9500F50790 /* MainMenu.xib in Resources */,
				B5E0F01817754D9500F50790 /* sf2gfx.bin in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8D11072C0486CEB800E47090 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5ADB7B213B2B38400905DFE /* trackball.c in Sources */,
				B5ADB84E13B2B57E00905DFE /* actions_198a.c in Sources */,
				B5ADB84F13B2B57E00905DFE /* actions_530a.c in Sources */,
				B5ADB85113B2B57E00905DFE /* ai.c in Sources */,
				B5ADB85213B2B57E00905DFE /* blanka.c in Sources */,
				B5ADB85313B2B57E00905DFE /* chunli.c in Sources */,
				B5ADB85413B2B57E00905DFE /* dhalsim.c in Sources */,
				B5ADB85513B2B57E00905DFE /* ehonda.c in Sources */,
				B5ADB85613B2B57E00905DFE /* guile.c in Sources */,
				B5ADB85713B2B57E00905DFE /* mbison.c in Sources */,
				B5ADB85813B2B57E00905DFE /* ryu.c in Sources */,
				B5ADB85913B2B57E00905DFE /* vega.c in Sources */,
				B54FDCE52543ADC10092E183 /* scroll_util.c in Sources */,
				B5ADB85A13B2B57E00905DFE /* collision.c in Sources */,
				B5ADB85B13B2B57E00905DFE /* computer.c in Sources */,
				B5ADB85C13B2B57E00905DFE /* effects.c in Sources */,
				B5ADB85D13B2B57E00905DFE /* fightgfx.c in Sources */,
				B5ADB85E13B2B57E00905DFE /* gemu.c in Sources */,
				6CF004561C2A01C9008EA965 /* redhammer.c in Sources */,
				B5ADB85F13B2B57E00905DFE /* gfx_glut.c in Sources */,
				B5ADB86113B2B57E00905DFE /* gfxlib.c in Sources */,
				B5ADB86213B2B57E00905DFE /* gstate.c in Sources */,
				B5ADB86313B2B57E00905DFE /* lib.c in Sources */,
				B5ADB86413B2B57E00905DFE /* particle.c in Sources */,
				B5ADB86513B2B57E00905DFE /* player.c in Sources */,
				B5ADB86613B2B57E00905DFE /* playerselect.c in Sources */,
				B5ADB86713B2B57E00905DFE /* playerstate.c in Sources */,
				B5ADB86813B2B57E00905DFE /* projectiles.c in Sources */,
				B5ADB86913B2B57E00905DFE /* reactmode.c in Sources */,
				B5ADB86A13B2B57E00905DFE /* rules.c in Sources */,
				6C8C65261C37717200635FDB /* barrels.c in Sources */,
				B5ADB86B13B2B57E00905DFE /* sm.c in Sources */,
				B5ADB86C13B2B57E00905DFE /* sound.c in Sources */,
				B5ADB86D13B2B57E00905DFE /* sprite.c in Sources */,
				B54FDCF32543ADF20092E183 /* scroll_data.c in Sources */,
				B5ADB86E13B2B57E00905DFE /* task.c in Sources */,
				B5ADB87213B2B59A00905DFE /* glwimp.c in Sources */,
				B5871ED71408E4F4004A45F5 /* pthreads.c in Sources */,
				B56A718C1411B0D300DF7F02 /* game.c in Sources */,
				B551553514773E8A00A8BBD9 /* coinage.c in Sources */,
				B54FDCC92543AB940092E183 /* scroll_maint.c in Sources */,
				B51533F2147EEB4F004A87B0 /* car.c in Sources */,
				B51533F5147EEBDB004A87B0 /* drums.c in Sources */,
				B5BA3ACE147F0EBC0069ACD3 /* actions.c in Sources */,
				B55891431485AB2A00BBB0BA /* coll_projectile.c in Sources */,
				B558918F1485B04A00BBB0BA /* coll_bonus.c in Sources */,
				B5FD9F801488260A00DDF4D9 /* demo.c in Sources */,
				B57AE5DE19D7D1610065D862 /* strings.c in Sources */,
				B5EB38E1149816D5007100DB /* reels.c in Sources */,
				B5AC69E71550AC9C00C9339E /* aitests.c in Sources */,
				B5AC6A611550BDBB00C9339E /* testlib.c in Sources */,
				B5B075B31554803E009D301D /* act2e_plane.c in Sources */,
				B5B0761615548649009D301D /* act1e_worldflags.c in Sources */,
				B5B0763D155489B5009D301D /* act02_bicycleriders.c in Sources */,
				B5B0764615548B28009D301D /* act07_elephants.c in Sources */,
				B53EBC59155F86F400581C8D /* act29_wwlogo.c in Sources */,
				B54FDCD72543AD230092E183 /* parallax.c in Sources */,
				B53EBC62155F8A4600581C8D /* act3e_capcomlogos.c in Sources */,
				B53EBC6E155F8C0600581C8D /* act_3f.c in Sources */,
				B53EBC80155F91F900581C8D /* act17.c in Sources */,
				B53EBC84155F927300581C8D /* act16.c in Sources */,
				B5613C3B155F9917001E127A /* text.c in Sources */,
				B5F335381562FFA4005A4637 /* zangeif.c in Sources */,
				B5E21389156894FA00D96161 /* sagat.c in Sources */,
				B5E2138C1568950900D96161 /* balrog.c in Sources */,
				B56BFC68156B43C500DC7BAD /* blanka_comp.c in Sources */,
				B56BFC70156B444F00DC7BAD /* blanka_human.c in Sources */,
				B56BFC8F156B48F600DC7BAD /* ryuken_human.c in Sources */,
				B56BFC92156B48FD00DC7BAD /* ryuken_comp.c in Sources */,
				B5948A26156EDE260029278E /* ehonda_comp.c in Sources */,
				B5948A28156EDE3C0029278E /* ehonda_human.c in Sources */,
				B5948A4B156EE0F50029278E /* guile_human.c in Sources */,
				B5948A4D156EE0FF0029278E /* guile_comp.c in Sources */,
				B5948BE5156F1B610029278E /* chunli_comp.c in Sources */,
				B5948BE7156F1B6E0029278E /* chunli_human.c in Sources */,
				B5B39CFC1578370D00494662 /* MT2AppDelegate.m in Sources */,
				B5B39CFD1578370E00494662 /* OGLView.m in Sources */,
				B5B39CFF1578371400494662 /* main.m in Sources */,
				B550DEA316BBA1BF00983A54 /* endings.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B51D1D13253BF34100CEB195 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B519C6A6253C03BB0084B88D /* blanka_human.c in Sources */,
				B51D1E0D253BFF9400CEB195 /* act17.c in Sources */,
				B51D1E53253BFFC800CEB195 /* guile_comp.c in Sources */,
				B51D1DF1253BFF8500CEB195 /* actions_530a.c in Sources */,
				B51D1EB6253C003100CEB195 /* rules.c in Sources */,
				B54FDCEA2543ADC10092E183 /* scroll_util.c in Sources */,
				B51D1E85253C001F00CEB195 /* lib.c in Sources */,
				B519C6AD253C03CF0084B88D /* trackball.c in Sources */,
				B51D1DF8253BFF8C00CEB195 /* reels.c in Sources */,
				B51D1DDC253BFF7B00CEB195 /* barrels.c in Sources */,
				B51D1E29253BFFA000CEB195 /* act3e_capcomlogos.c in Sources */,
				B51D1E58253BFFC800CEB195 /* dhalsim.c in Sources */,
				B51D1E30253BFFA200CEB195 /* act_3f.c in Sources */,
				B51D1E56253BFFC800CEB195 /* chunli_human.c in Sources */,
				B51D1E57253BFFC800CEB195 /* ehonda.c in Sources */,
				B51D1DFF253BFF8F00CEB195 /* act02_bicycleriders.c in Sources */,
				B51D1E64253BFFC800CEB195 /* vega.c in Sources */,
				B51D1E6B253BFFD400CEB195 /* fightgfx.c in Sources */,
				B51D1E59253BFFC800CEB195 /* ryuken_human.c in Sources */,
				B51D1DC0253BFF5900CEB195 /* coll_projectile.c in Sources */,
				B51D1EAF253C002F00CEB195 /* reactmode.c in Sources */,
				B51D1E4C253BFFB700CEB195 /* blanka.c in Sources */,
				B51D1E55253BFFC800CEB195 /* sagat.c in Sources */,
				B51D1E5D253BFFC800CEB195 /* ehonda_comp.c in Sources */,
				B51D1E5A253BFFC800CEB195 /* chunli_comp.c in Sources */,
				B51D1DD5253BFF7700CEB195 /* drums.c in Sources */,
				B51D1DB9253BFF5600CEB195 /* collision.c in Sources */,
				B51D1E45253BFFB400CEB195 /* balrog.c in Sources */,
				B51D1E1B253BFF9A00CEB195 /* act29_wwlogo.c in Sources */,
				B51D1D2C253BF3A300CEB195 /* MyNSOpenGLView.swift in Sources */,
				B51D1D96253BFD0D00CEB195 /* Game.swift in Sources */,
				B51D1DB2253BFF2700CEB195 /* redhammer.c in Sources */,
				B51D1E9A253C002700CEB195 /* playerselect.c in Sources */,
				B51D1D6C253BF79100CEB195 /* ShaderProgram.swift in Sources */,
				B51D1EE0253C004F00CEB195 /* endings.c in Sources */,
				B51D1E60253BFFC800CEB195 /* zangeif.c in Sources */,
				B51D1D1A253BF34100CEB195 /* AppDelegate.swift in Sources */,
				B51D1E8C253C002100CEB195 /* particle.c in Sources */,
				B54FDCF82543ADF20092E183 /* scroll_data.c in Sources */,
				B51D1EA1253C002A00CEB195 /* playerstate.c in Sources */,
				B51D1E77253C001900CEB195 /* gfxlib.c in Sources */,
				B51D1EC4253C003800CEB195 /* sound.c in Sources */,
				B51D1ED2253C003D00CEB195 /* task.c in Sources */,
				B51D1E5C253BFFC800CEB195 /* guile_human.c in Sources */,
				B51D1E70253BFFD400CEB195 /* demo.c in Sources */,
				B51D1ED9253C004000CEB195 /* text.c in Sources */,
				B519C714253C27020084B88D /* PointSprite.swift in Sources */,
				B51D1D64253BF67A00CEB195 /* Matrix4.swift in Sources */,
				B51D1E5E253BFFC800CEB195 /* ryu.c in Sources */,
				B51D1E3E253BFFAB00CEB195 /* ai.c in Sources */,
				B51D1E7E253C001C00CEB195 /* gstate.c in Sources */,
				B51D1E61253BFFC800CEB195 /* mbison.c in Sources */,
				B51D1ECB253C003A00CEB195 /* sprite.c in Sources */,
				B51D1E5F253BFFC800CEB195 /* ryuken_comp.c in Sources */,
				B51D1DEA253BFF8300CEB195 /* actions_198a.c in Sources */,
				B51D1DCE253BFF7400CEB195 /* car.c in Sources */,
				B54FDCDC2543AD230092E183 /* parallax.c in Sources */,
				B51D1E6E253BFFD400CEB195 /* effects.c in Sources */,
				B51D1DE3253BFF7F00CEB195 /* actions.c in Sources */,
				B51D1EA8253C002C00CEB195 /* projectiles.c in Sources */,
				B51D1E5B253BFFC800CEB195 /* guile.c in Sources */,
				B51D1E62253BFFC800CEB195 /* ehonda_human.c in Sources */,
				B51D1E6D253BFFD400CEB195 /* coinage.c in Sources */,
				B51D1E22253BFF9C00CEB195 /* act2e_plane.c in Sources */,
				B51D1D9D253BFF1300CEB195 /* gfx_glut.c in Sources */,
				B51D1E6C253BFFD400CEB195 /* computer.c in Sources */,
				B51D1EBD253C003500CEB195 /* sm.c in Sources */,
				B51D1E6F253BFFD400CEB195 /* game.c in Sources */,
				B519C723253C92EC0084B88D /* gfx_glcore.c in Sources */,
				B51D1E14253BFF9700CEB195 /* act1e_worldflags.c in Sources */,
				B51D1E54253BFFC800CEB195 /* blanka_comp.c in Sources */,
				B51D1E06253BFF9100CEB195 /* act07_elephants.c in Sources */,
				B519C69F253C039E0084B88D /* glwimp.c in Sources */,
				B51D1DC7253BFF5C00CEB195 /* coll_bonus.c in Sources */,
				B51D1E63253BFFC800CEB195 /* chunli.c in Sources */,
				B51D1DAB253BFF1A00CEB195 /* pthreads.c in Sources */,
				B51D1DA4253BFF1500CEB195 /* gemu.c in Sources */,
				B51D1E93253C002400CEB195 /* player.c in Sources */,
				B54FDCCE2543AB950092E183 /* scroll_maint.c in Sources */,
				B51D1E37253BFFA500CEB195 /* act16.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B57E3B3F296A7DFC007F6872 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B57E3B46296A7DFE007F6872 /* main.c in Sources */,
				B57E3B6B296A978F007F6872 /* redhammer.c in Sources */,
				B57E3B50296A8BE4007F6872 /* test_redhammer.c in Sources */,
				B57E3B53296A8F9A007F6872 /* test_sf2types.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5C574301772782600496283 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5C574311772782600496283 /* trackball.c in Sources */,
				B5C574321772782600496283 /* actions_198a.c in Sources */,
				B5C574331772782600496283 /* actions_530a.c in Sources */,
				B5C574341772782600496283 /* ai.c in Sources */,
				B5C574351772782600496283 /* blanka.c in Sources */,
				B5C574361772782600496283 /* chunli.c in Sources */,
				B5C574371772782600496283 /* dhalsim.c in Sources */,
				B5C574381772782600496283 /* ehonda.c in Sources */,
				B5C574391772782600496283 /* guile.c in Sources */,
				B5C5743A1772782600496283 /* mbison.c in Sources */,
				B5C5743B1772782600496283 /* ryu.c in Sources */,
				B5C5743C1772782600496283 /* vega.c in Sources */,
				B5C5743D1772782600496283 /* collision.c in Sources */,
				B5C5743E1772782600496283 /* computer.c in Sources */,
				B5C5743F1772782600496283 /* effects.c in Sources */,
				B5C574401772782600496283 /* fightgfx.c in Sources */,
				B5C574411772782600496283 /* gemu.c in Sources */,
				B5C574421772782600496283 /* gfx_glut.c in Sources */,
				B5C574441772782600496283 /* gfxlib.c in Sources */,
				B5C574451772782600496283 /* gstate.c in Sources */,
				B5C574461772782600496283 /* lib.c in Sources */,
				B5C574471772782600496283 /* particle.c in Sources */,
				B5C574481772782600496283 /* player.c in Sources */,
				B5C574491772782600496283 /* playerselect.c in Sources */,
				B5C5744A1772782600496283 /* playerstate.c in Sources */,
				B5C5744B1772782600496283 /* projectiles.c in Sources */,
				B5C5744C1772782600496283 /* reactmode.c in Sources */,
				B5C5744D1772782600496283 /* rules.c in Sources */,
				B54FDCCA2543AB950092E183 /* scroll_maint.c in Sources */,
				B5C5744E1772782600496283 /* sm.c in Sources */,
				B5C5744F1772782600496283 /* sound.c in Sources */,
				B5C574501772782600496283 /* sprite.c in Sources */,
				B5C574511772782600496283 /* task.c in Sources */,
				B5C574521772782600496283 /* glwimp.c in Sources */,
				B5C574531772782600496283 /* pthreads.c in Sources */,
				B5C574541772782600496283 /* game.c in Sources */,
				B54FDCF42543ADF20092E183 /* scroll_data.c in Sources */,
				B5C574551772782600496283 /* coinage.c in Sources */,
				6C6F0DE81C349FAA004CD78E /* redhammer.c in Sources */,
				B5C574561772782600496283 /* car.c in Sources */,
				B5C574571772782600496283 /* drums.c in Sources */,
				B5C574581772782600496283 /* actions.c in Sources */,
				B5C5745A1772782600496283 /* coll_projectile.c in Sources */,
				B5C5745B1772782600496283 /* coll_bonus.c in Sources */,
				B5C5745C1772782600496283 /* demo.c in Sources */,
				B5C5745D1772782600496283 /* reels.c in Sources */,
				B5C5745E1772782600496283 /* aitests.c in Sources */,
				B5C5745F1772782600496283 /* testlib.c in Sources */,
				B5C574601772782600496283 /* act2e_plane.c in Sources */,
				B54FDCD82543AD230092E183 /* parallax.c in Sources */,
				B5C574611772782600496283 /* act1e_worldflags.c in Sources */,
				6C8C65271C37717200635FDB /* barrels.c in Sources */,
				B5C574621772782600496283 /* act02_bicycleriders.c in Sources */,
				B5C574631772782600496283 /* act07_elephants.c in Sources */,
				B5C574641772782600496283 /* act29_wwlogo.c in Sources */,
				B5C574651772782600496283 /* act3e_capcomlogos.c in Sources */,
				B5C574661772782600496283 /* act_3f.c in Sources */,
				B5C574671772782600496283 /* act17.c in Sources */,
				B5C574681772782600496283 /* act16.c in Sources */,
				B54FDCE62543ADC10092E183 /* scroll_util.c in Sources */,
				B5C574691772782600496283 /* text.c in Sources */,
				B5C5746A1772782600496283 /* zangeif.c in Sources */,
				B5C5746B1772782600496283 /* sagat.c in Sources */,
				B5C5746C1772782600496283 /* balrog.c in Sources */,
				B5C5746D1772782600496283 /* blanka_comp.c in Sources */,
				B5C5746E1772782600496283 /* blanka_human.c in Sources */,
				B5C5746F1772782600496283 /* ryuken_human.c in Sources */,
				B5C574701772782600496283 /* ryuken_comp.c in Sources */,
				B5C574711772782600496283 /* ehonda_comp.c in Sources */,
				B5C574721772782600496283 /* ehonda_human.c in Sources */,
				B5C574731772782600496283 /* guile_human.c in Sources */,
				B5C574741772782600496283 /* guile_comp.c in Sources */,
				B5C574751772782600496283 /* chunli_comp.c in Sources */,
				B5C574761772782600496283 /* chunli_human.c in Sources */,
				B5C574771772782600496283 /* MT2AppDelegate.m in Sources */,
				B5C574781772782600496283 /* OGLView.m in Sources */,
				B5C5747A1772782600496283 /* main.m in Sources */,
				B5C5747B1772782600496283 /* endings.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E0EFBE17754D8800F50790 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5E0EFBF17754D8800F50790 /* trackball.c in Sources */,
				B5E0EFC017754D8800F50790 /* actions_198a.c in Sources */,
				B5E0EFC117754D8800F50790 /* actions_530a.c in Sources */,
				B5E0EFC217754D8800F50790 /* ai.c in Sources */,
				B5E0EFC317754D8800F50790 /* blanka.c in Sources */,
				B5E0EFC417754D8800F50790 /* chunli.c in Sources */,
				B5E0EFC517754D8800F50790 /* dhalsim.c in Sources */,
				B5E0EFC617754D8800F50790 /* ehonda.c in Sources */,
				B5E0EFC717754D8800F50790 /* guile.c in Sources */,
				B5E0EFC817754D8800F50790 /* mbison.c in Sources */,
				B5E0EFC917754D8800F50790 /* ryu.c in Sources */,
				B5E0EFCA17754D8800F50790 /* vega.c in Sources */,
				B5E0EFCB17754D8800F50790 /* collision.c in Sources */,
				B5E0EFCC17754D8800F50790 /* computer.c in Sources */,
				B5E0EFCD17754D8800F50790 /* effects.c in Sources */,
				6C8C65281C37717200635FDB /* barrels.c in Sources */,
				B5E0EFCE17754D8800F50790 /* fightgfx.c in Sources */,
				B5E0EFCF17754D8800F50790 /* gemu.c in Sources */,
				B5E0EFD017754D8800F50790 /* gfx_glut.c in Sources */,
				B5E0EFD217754D8800F50790 /* gfxlib.c in Sources */,
				B5E0EFD317754D8800F50790 /* gstate.c in Sources */,
				B5E0EFD417754D8800F50790 /* lib.c in Sources */,
				B5E0EFD517754D8800F50790 /* particle.c in Sources */,
				B51D1CF5253BE9B800CEB195 /* redhammer.c in Sources */,
				B5E0EFD617754D8800F50790 /* player.c in Sources */,
				B5E0EFD717754D8800F50790 /* playerselect.c in Sources */,
				B5E0EFD817754D8800F50790 /* playerstate.c in Sources */,
				B5E0EFD917754D8800F50790 /* projectiles.c in Sources */,
				B54FDCCB2543AB950092E183 /* scroll_maint.c in Sources */,
				B5E0EFDA17754D8800F50790 /* reactmode.c in Sources */,
				B5E0EFDB17754D8800F50790 /* rules.c in Sources */,
				B5E0EFDC17754D8800F50790 /* sm.c in Sources */,
				B5E0EFDD17754D8800F50790 /* sound.c in Sources */,
				B5E0EFDE17754D8800F50790 /* sprite.c in Sources */,
				B5E0EFDF17754D8800F50790 /* task.c in Sources */,
				B5E0EFE017754D8800F50790 /* glwimp.c in Sources */,
				B54FDCF52543ADF20092E183 /* scroll_data.c in Sources */,
				B5E0EFE117754D8800F50790 /* pthreads.c in Sources */,
				B5E0EFE217754D8800F50790 /* game.c in Sources */,
				B5E0EFE317754D8800F50790 /* coinage.c in Sources */,
				B5E0EFE417754D8800F50790 /* car.c in Sources */,
				B5E0EFE517754D8800F50790 /* drums.c in Sources */,
				B5E0EFE617754D8800F50790 /* actions.c in Sources */,
				B5E0EFE817754D8800F50790 /* coll_projectile.c in Sources */,
				B5E0EFE917754D8800F50790 /* coll_bonus.c in Sources */,
				B5E0EFEA17754D8800F50790 /* demo.c in Sources */,
				B5E0EFEB17754D8800F50790 /* reels.c in Sources */,
				B5E0EFEC17754D8800F50790 /* aitests.c in Sources */,
				B5E0EFED17754D8800F50790 /* testlib.c in Sources */,
				B54FDCD92543AD230092E183 /* parallax.c in Sources */,
				B5E0EFEE17754D8800F50790 /* act2e_plane.c in Sources */,
				B5E0EFEF17754D8800F50790 /* act1e_worldflags.c in Sources */,
				B5E0EFF017754D8800F50790 /* act02_bicycleriders.c in Sources */,
				B5E0EFF117754D8800F50790 /* act07_elephants.c in Sources */,
				B5E0EFF217754D8800F50790 /* act29_wwlogo.c in Sources */,
				B5E0EFF317754D8800F50790 /* act3e_capcomlogos.c in Sources */,
				B5E0EFF417754D8800F50790 /* act_3f.c in Sources */,
				B5E0EFF517754D8800F50790 /* act17.c in Sources */,
				B5E0EFF617754D8800F50790 /* act16.c in Sources */,
				B54FDCE72543ADC10092E183 /* scroll_util.c in Sources */,
				B5E0EFF717754D8800F50790 /* text.c in Sources */,
				B5E0EFF817754D8800F50790 /* zangeif.c in Sources */,
				B5E0EFF917754D8800F50790 /* sagat.c in Sources */,
				B5E0EFFA17754D8800F50790 /* balrog.c in Sources */,
				B5E0EFFB17754D8800F50790 /* blanka_comp.c in Sources */,
				B5E0EFFC17754D8800F50790 /* blanka_human.c in Sources */,
				B5E0EFFD17754D8800F50790 /* ryuken_human.c in Sources */,
				B5E0EFFE17754D8800F50790 /* ryuken_comp.c in Sources */,
				B5E0EFFF17754D8800F50790 /* ehonda_comp.c in Sources */,
				B5E0F00017754D8800F50790 /* ehonda_human.c in Sources */,
				B5E0F00117754D8800F50790 /* guile_human.c in Sources */,
				B5E0F00217754D8800F50790 /* guile_comp.c in Sources */,
				B5E0F00317754D8800F50790 /* chunli_comp.c in Sources */,
				B5E0F00417754D8800F50790 /* chunli_human.c in Sources */,
				B5E0F00517754D8800F50790 /* MT2AppDelegate.m in Sources */,
				B5E0F00617754D8800F50790 /* OGLView.m in Sources */,
				B5E0F00817754D8800F50790 /* main.m in Sources */,
				B5E0F00917754D8800F50790 /* endings.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E0F01917754D9500F50790 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5E0F01A17754D9500F50790 /* trackball.c in Sources */,
				B5E0F01B17754D9500F50790 /* actions_198a.c in Sources */,
				B5E0F01C17754D9500F50790 /* actions_530a.c in Sources */,
				B5E0F01D17754D9500F50790 /* ai.c in Sources */,
				B5E0F01E17754D9500F50790 /* blanka.c in Sources */,
				B5E0F01F17754D9500F50790 /* chunli.c in Sources */,
				B5E0F02017754D9500F50790 /* dhalsim.c in Sources */,
				B5E0F02117754D9500F50790 /* ehonda.c in Sources */,
				B5E0F02217754D9500F50790 /* guile.c in Sources */,
				B5E0F02317754D9500F50790 /* mbison.c in Sources */,
				B5E0F02417754D9500F50790 /* ryu.c in Sources */,
				B5E0F02517754D9500F50790 /* vega.c in Sources */,
				B5E0F02617754D9500F50790 /* collision.c in Sources */,
				B5E0F02717754D9500F50790 /* computer.c in Sources */,
				B5E0F02817754D9500F50790 /* effects.c in Sources */,
				6C8C65291C37717200635FDB /* barrels.c in Sources */,
				B5E0F02917754D9500F50790 /* fightgfx.c in Sources */,
				B5E0F02A17754D9500F50790 /* gemu.c in Sources */,
				B5E0F02B17754D9500F50790 /* gfx_glut.c in Sources */,
				B5E0F02D17754D9500F50790 /* gfxlib.c in Sources */,
				B5E0F02E17754D9500F50790 /* gstate.c in Sources */,
				B5E0F02F17754D9500F50790 /* lib.c in Sources */,
				B5E0F03017754D9500F50790 /* particle.c in Sources */,
				B51D1CEF253BE9B800CEB195 /* redhammer.c in Sources */,
				B5E0F03117754D9500F50790 /* player.c in Sources */,
				B5E0F03217754D9500F50790 /* playerselect.c in Sources */,
				B5E0F03317754D9500F50790 /* playerstate.c in Sources */,
				B5E0F03417754D9500F50790 /* projectiles.c in Sources */,
				B54FDCCC2543AB950092E183 /* scroll_maint.c in Sources */,
				B5E0F03517754D9500F50790 /* reactmode.c in Sources */,
				B5E0F03617754D9500F50790 /* rules.c in Sources */,
				B5E0F03717754D9500F50790 /* sm.c in Sources */,
				B5E0F03817754D9500F50790 /* sound.c in Sources */,
				B5E0F03917754D9500F50790 /* sprite.c in Sources */,
				B5E0F03A17754D9500F50790 /* task.c in Sources */,
				B5E0F03B17754D9500F50790 /* glwimp.c in Sources */,
				B54FDCF62543ADF20092E183 /* scroll_data.c in Sources */,
				B5E0F03C17754D9500F50790 /* pthreads.c in Sources */,
				B5E0F03D17754D9500F50790 /* game.c in Sources */,
				B5E0F03E17754D9500F50790 /* coinage.c in Sources */,
				B5E0F03F17754D9500F50790 /* car.c in Sources */,
				B5E0F04017754D9500F50790 /* drums.c in Sources */,
				B5E0F04117754D9500F50790 /* actions.c in Sources */,
				B5E0F04317754D9500F50790 /* coll_projectile.c in Sources */,
				B5E0F04417754D9500F50790 /* coll_bonus.c in Sources */,
				B5E0F04517754D9500F50790 /* demo.c in Sources */,
				B5E0F04617754D9500F50790 /* reels.c in Sources */,
				B5E0F04717754D9500F50790 /* aitests.c in Sources */,
				B5E0F04817754D9500F50790 /* testlib.c in Sources */,
				B54FDCDA2543AD230092E183 /* parallax.c in Sources */,
				B5E0F04917754D9500F50790 /* act2e_plane.c in Sources */,
				B5E0F04A17754D9500F50790 /* act1e_worldflags.c in Sources */,
				B5E0F04B17754D9500F50790 /* act02_bicycleriders.c in Sources */,
				B5E0F04C17754D9500F50790 /* act07_elephants.c in Sources */,
				B5E0F04D17754D9500F50790 /* act29_wwlogo.c in Sources */,
				B5E0F04E17754D9500F50790 /* act3e_capcomlogos.c in Sources */,
				B5E0F04F17754D9500F50790 /* act_3f.c in Sources */,
				B5E0F05017754D9500F50790 /* act17.c in Sources */,
				B5E0F05117754D9500F50790 /* act16.c in Sources */,
				B54FDCE82543ADC10092E183 /* scroll_util.c in Sources */,
				B5E0F05217754D9500F50790 /* text.c in Sources */,
				B5E0F05317754D9500F50790 /* zangeif.c in Sources */,
				B5E0F05417754D9500F50790 /* sagat.c in Sources */,
				B5E0F05517754D9500F50790 /* balrog.c in Sources */,
				B5E0F05617754D9500F50790 /* blanka_comp.c in Sources */,
				B5E0F05717754D9500F50790 /* blanka_human.c in Sources */,
				B5E0F05817754D9500F50790 /* ryuken_human.c in Sources */,
				B5E0F05917754D9500F50790 /* ryuken_comp.c in Sources */,
				B5E0F05A17754D9500F50790 /* ehonda_comp.c in Sources */,
				B5E0F05B17754D9500F50790 /* ehonda_human.c in Sources */,
				B5E0F05C17754D9500F50790 /* guile_human.c in Sources */,
				B5E0F05D17754D9500F50790 /* guile_comp.c in Sources */,
				B5E0F05E17754D9500F50790 /* chunli_comp.c in Sources */,
				B5E0F05F17754D9500F50790 /* chunli_human.c in Sources */,
				B5E0F06017754D9500F50790 /* MT2AppDelegate.m in Sources */,
				B5E0F06117754D9500F50790 /* OGLView.m in Sources */,
				B5E0F06317754D9500F50790 /* main.m in Sources */,
				B5E0F06417754D9500F50790 /* endings.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5E8FD0C1776C7D400C6060E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5E8FD1B1776C7E600C6060E /* glutBasics.c in Sources */,
				B5E8FD1F1776C81D00C6060E /* collision.c in Sources */,
				B5E8FD201776C82000C6060E /* coll_projectile.c in Sources */,
				B5E8FD231776C83700C6060E /* drums.c in Sources */,
				B5E8FD1C1776C7EC00C6060E /* gfx_glut.c in Sources */,
				B5E8FD211776C82300C6060E /* coll_bonus.c in Sources */,
				B5E8FD1E1776C7F700C6060E /* gemu.c in Sources */,
				B5E8FD221776C83300C6060E /* car.c in Sources */,
				B5E8FD1D1776C7F300C6060E /* pthreads.c in Sources */,
				B5E8FD241776C86700C6060E /* actions.c in Sources */,
				B5E8FD251776C86700C6060E /* actions_198a.c in Sources */,
				B5E8FD261776C86700C6060E /* actions_530a.c in Sources */,
				B5E8FD281776C86700C6060E /* reels.c in Sources */,
				B5E8FD291776C86700C6060E /* act02_bicycleriders.c in Sources */,
				B5E8FD2A1776C86700C6060E /* act07_elephants.c in Sources */,
				B5E8FD2B1776C86700C6060E /* act17.c in Sources */,
				B5E8FD2C1776C86700C6060E /* act1e_worldflags.c in Sources */,
				B5E8FD2D1776C86700C6060E /* act29_wwlogo.c in Sources */,
				B5E8FD2E1776C86700C6060E /* act2e_plane.c in Sources */,
				B5E8FD2F1776C86700C6060E /* act3e_capcomlogos.c in Sources */,
				B5E8FD301776C86700C6060E /* act_3f.c in Sources */,
				B5E8FD311776C86700C6060E /* act16.c in Sources */,
				B5E8FD321776C87400C6060E /* ai.c in Sources */,
				B5E8FD331776C89400C6060E /* balrog.c in Sources */,
				B5E8FD341776C89400C6060E /* blanka.c in Sources */,
				B5E8FD351776C89400C6060E /* blanka_comp.c in Sources */,
				B5E8FD361776C89400C6060E /* chunli.c in Sources */,
				B5E8FD371776C89400C6060E /* dhalsim.c in Sources */,
				B5E8FD381776C89400C6060E /* ehonda.c in Sources */,
				B5E8FD391776C89400C6060E /* ehonda_comp.c in Sources */,
				B5E8FD3A1776C89400C6060E /* ehonda_human.c in Sources */,
				B5E8FD3B1776C89400C6060E /* guile.c in Sources */,
				B54FDCDB2543AD230092E183 /* parallax.c in Sources */,
				B5E8FD3C1776C89400C6060E /* guile_human.c in Sources */,
				B54FDCCD2543AB950092E183 /* scroll_maint.c in Sources */,
				B5E8FD3D1776C89400C6060E /* guile_comp.c in Sources */,
				B5E8FD3E1776C89400C6060E /* mbison.c in Sources */,
				B5E8FD3F1776C89400C6060E /* ryu.c in Sources */,
				B5E8FD401776C89400C6060E /* ryuken_human.c in Sources */,
				B57AE5DF19D7DFEF0065D862 /* strings.c in Sources */,
				B5E8FD411776C89400C6060E /* ryuken_comp.c in Sources */,
				B5E8FD421776C89400C6060E /* sagat.c in Sources */,
				B5E8FD431776C89400C6060E /* vega.c in Sources */,
				B5E8FD441776C89400C6060E /* zangeif.c in Sources */,
				B5E8FD451776C89400C6060E /* chunli_comp.c in Sources */,
				B5E8FD461776C89400C6060E /* chunli_human.c in Sources */,
				B54FDCF72543ADF20092E183 /* scroll_data.c in Sources */,
				B5E8FD471776C89D00C6060E /* coinage.c in Sources */,
				B51D1CB1253BE0E600CEB195 /* barrels.c in Sources */,
				B54FDCE92543ADC10092E183 /* scroll_util.c in Sources */,
				B5E8FD481776C89D00C6060E /* computer.c in Sources */,
				B5E8FD491776C89D00C6060E /* demo.c in Sources */,
				B5E8FD4A1776C89D00C6060E /* effects.c in Sources */,
				B5E8FD4B1776C89D00C6060E /* fightgfx.c in Sources */,
				B5E8FD4C1776C89D00C6060E /* game.c in Sources */,
				B5E8FD4E1776C8A600C6060E /* graphics.c in Sources */,
				B5E8FD4F1776C8BF00C6060E /* gfxlib.c in Sources */,
				B5E8FD501776C8BF00C6060E /* gstate.c in Sources */,
				B5E8FD511776C8BF00C6060E /* lib.c in Sources */,
				B5E8FD521776C8BF00C6060E /* particle.c in Sources */,
				B5E8FD531776C8BF00C6060E /* player.c in Sources */,
				B5E8FD541776C8BF00C6060E /* playerselect.c in Sources */,
				B5E8FD551776C8BF00C6060E /* playerstate.c in Sources */,
				B5E8FD561776C8BF00C6060E /* projectiles.c in Sources */,
				B5E8FD571776C8BF00C6060E /* reactmode.c in Sources */,
				B5E8FD581776C8BF00C6060E /* rules.c in Sources */,
				B5E8FD591776C8BF00C6060E /* sm.c in Sources */,
				B5E8FD5A1776C8BF00C6060E /* sound.c in Sources */,
				B5E8FD5B1776C8BF00C6060E /* sprite.c in Sources */,
				B5E8FD5C1776C8BF00C6060E /* task.c in Sources */,
				B5E8FD5D1776C8BF00C6060E /* text.c in Sources */,
				B5E8FD5F1776C8BF00C6060E /* endings.c in Sources */,
				B5E8FD601776C8C900C6060E /* trackball.c in Sources */,
				B5E8FD631776C9DD00C6060E /* glwimp.c in Sources */,
				B5E8FD641776CA1A00C6060E /* blanka_human.c in Sources */,
				B51D1C99253BE07100CEB195 /* redhammer.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		089C165CFE840E0CC02AAC07 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				089C165DFE840E0CC02AAC07 /* English */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		B51D1D1D253BF34300CEB195 /* MainMenu.xib */ = {
			isa = PBXVariantGroup;
			children = (
				B51D1D1E253BF34300CEB195 /* Base */,
			);
			name = MainMenu.xib;
			sourceTree = "<group>";
		};
		B5C574181772781A00496283 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				B5C574191772781A00496283 /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		B5C5741E1772781A00496283 /* Credits.rtf */ = {
			isa = PBXVariantGroup;
			children = (
				B5C5741F1772781A00496283 /* en */,
			);
			name = Credits.rtf;
			sourceTree = "<group>";
		};
		B5C574241772781A00496283 /* MainMenu.xib */ = {
			isa = PBXVariantGroup;
			children = (
				B5C574251772781A00496283 /* en */,
			);
			name = MainMenu.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		B51D1D22253BF34300CEB195 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "MT2-GLcore/MT2_GLcore.entitlements";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L2D42QR3GV;
				ENABLE_HARDENED_RUNTIME = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"GL_SILENCE_DEPRECATION=1",
					"DEBUG=1",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "MT2-GLcore/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sf2platinum.MT2-GLcore";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OBJC_BRIDGING_HEADER = "MT2-GLcore/game-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		B51D1D23253BF34300CEB195 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "MT2-GLcore/MT2_GLcore.entitlements";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L2D42QR3GV;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_PREPROCESSOR_DEFINITIONS = "GL_SILENCE_DEPRECATION=1";
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				INFOPLIST_FILE = "MT2-GLcore/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sf2platinum.MT2-GLcore";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "MT2-GLcore/game-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		B57E3B47296A7DFE007F6872 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = FBTests/FBTests.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L2D42QR3GV;
				ENABLE_HARDENED_RUNTIME = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				HEADER_SEARCH_PATHS = /usr/local/include;
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					/usr/local/Cellar/cmocka/1.1.5/lib,
				);
				MACOSX_DEPLOYMENT_TARGET = 13.1;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		B57E3B48296A7DFE007F6872 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = FBTests/FBTests.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L2D42QR3GV;
				ENABLE_HARDENED_RUNTIME = NO;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				HEADER_SEARCH_PATHS = /usr/local/include;
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					/usr/local/Cellar/cmocka/1.1.5/lib,
				);
				MACOSX_DEPLOYMENT_TARGET = 13.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		B5C574811772782600496283 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = FISTBLUE_TESTS;
				INFOPLIST_FILE = "MT2 copy-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "MT2 copy";
				SDKROOT = "";
			};
			name = Debug;
		};
		B5C574821772782600496283 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = FISTBLUE_TESTS;
				INFOPLIST_FILE = "MT2 copy-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "MT2 copy";
				SDKROOT = "";
			};
			name = Release;
		};
		B5E0F00F17754D8800F50790 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = FISTBLUE_SF2UA_JAP;
				INFOPLIST_FILE = "MT2 JAP-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "MT2 JAP";
				SDKROOT = "";
			};
			name = Debug;
		};
		B5E0F01017754D8800F50790 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = FISTBLUE_SF2UA_JAP;
				INFOPLIST_FILE = "MT2 JAP-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "MT2 JAP";
				SDKROOT = "";
			};
			name = Release;
		};
		B5E0F06A17754D9500F50790 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = FISTBLUE_SF2UA_ETC;
				INFOPLIST_FILE = "MT2 ETC-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "MT2 ETC";
				SDKROOT = "";
			};
			name = Debug;
		};
		B5E0F06B17754D9500F50790 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = FISTBLUE_SF2UA_ETC;
				INFOPLIST_FILE = "MT2 ETC-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "MT2 ETC";
				SDKROOT = "";
			};
			name = Release;
		};
		B5E8FD181776C7D400C6060E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"REDHAMMER=1",
					"DEBUG=1",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
			};
			name = Debug;
		};
		B5E8FD191776C7D400C6060E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = "REDHAMMER=1";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
			};
			name = Release;
		};
		C01FCF4B08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = "GL_SILENCE_DEPRECATION=1";
				INFOPLIST_FILE = "MT2-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = MT2;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		C01FCF4C08A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = MT2_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = "GL_SILENCE_DEPRECATION=1";
				INFOPLIST_FILE = "MT2-Info.plist";
				INSTALL_PATH = "$(HOME)/Applications";
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = MT2;
				SDKROOT = macosx;
			};
			name = Release;
		};
		C01FCF4F08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		C01FCF5008A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				SDKROOT = macosx;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B51D1D24253BF34300CEB195 /* Build configuration list for PBXNativeTarget "MT2-GLcore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B51D1D22253BF34300CEB195 /* Debug */,
				B51D1D23253BF34300CEB195 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B57E3B49296A7DFE007F6872 /* Build configuration list for PBXNativeTarget "FBTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B57E3B47296A7DFE007F6872 /* Debug */,
				B57E3B48296A7DFE007F6872 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5C574801772782600496283 /* Build configuration list for PBXNativeTarget "MT2 tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5C574811772782600496283 /* Debug */,
				B5C574821772782600496283 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5E0F00E17754D8800F50790 /* Build configuration list for PBXNativeTarget "MT2 JAP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5E0F00F17754D8800F50790 /* Debug */,
				B5E0F01017754D8800F50790 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5E0F06917754D9500F50790 /* Build configuration list for PBXNativeTarget "MT2 ETC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5E0F06A17754D9500F50790 /* Debug */,
				B5E0F06B17754D9500F50790 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5E8FD1A1776C7D400C6060E /* Build configuration list for PBXNativeTarget "MT2 GLUT" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5E8FD181776C7D400C6060E /* Debug */,
				B5E8FD191776C7D400C6060E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4A08A954540054247B /* Build configuration list for PBXNativeTarget "MT2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4B08A954540054247B /* Debug */,
				C01FCF4C08A954540054247B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "MT2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4F08A954540054247B /* Debug */,
				C01FCF5008A954540054247B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEA /* Project object */;
}
