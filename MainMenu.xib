<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="7.10">
	<data>
		<int key="IBDocument.SystemTarget">1060</int>
		<string key="IBDocument.SystemVersion">12E55</string>
		<string key="IBDocument.InterfaceBuilderVersion">3084</string>
		<string key="IBDocument.AppKitVersion">1187.39</string>
		<string key="IBDocument.HIToolboxVersion">626.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin</string>
			<string key="NS.object.0">3084</string>
		</object>
		<object class="NSArray" key="IBDocument.IntegratedClassDependencies">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<string>NSCustomObject</string>
			<string>NSMenu</string>
			<string>NSMenuItem</string>
			<string>NSOpenGLView</string>
			<string>NSView</string>
			<string>NSWindowTemplate</string>
		</object>
		<object class="NSArray" key="IBDocument.PluginDependencies">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<string key="NS.key.0">PluginDependencyRecalculationVersion</string>
			<integer value="1" key="NS.object.0"/>
		</object>
		<object class="NSMutableArray" key="IBDocument.RootObjects" id="1048">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSCustomObject" id="1021">
				<string key="NSClassName">NSApplication</string>
			</object>
			<object class="NSCustomObject" id="1014">
				<string key="NSClassName">FirstResponder</string>
			</object>
			<object class="NSCustomObject" id="1050">
				<string key="NSClassName">NSApplication</string>
			</object>
			<object class="NSMenu" id="649796088">
				<string key="NSTitle">AMainMenu</string>
				<object class="NSMutableArray" key="NSMenuItems">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="NSMenuItem" id="694149608">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">MT2</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<object class="NSCustomResource" key="NSOnImage" id="35465992">
							<string key="NSClassName">NSImage</string>
							<string key="NSResourceName">NSMenuCheckmark</string>
						</object>
						<object class="NSCustomResource" key="NSMixedImage" id="502551668">
							<string key="NSClassName">NSImage</string>
							<string key="NSResourceName">NSMenuMixedState</string>
						</object>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="110575045">
							<string key="NSTitle">MT2</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="238522557">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">About MT2</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="304266470">
									<reference key="NSMenu" ref="110575045"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="609285721">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">Preferences…</string>
									<string key="NSKeyEquiv">,</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="481834944">
									<reference key="NSMenu" ref="110575045"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="1046388886">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">Services</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="752062318">
										<string key="NSTitle">Services</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
										</object>
										<string key="NSName">_NSServicesMenu</string>
									</object>
								</object>
								<object class="NSMenuItem" id="646227648">
									<reference key="NSMenu" ref="110575045"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="755159360">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">Hide MT2</string>
									<string key="NSKeyEquiv">h</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="342932134">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">Hide Others</string>
									<string key="NSKeyEquiv">h</string>
									<int key="NSKeyEquivModMask">1572864</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="908899353">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">Show All</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="1056857174">
									<reference key="NSMenu" ref="110575045"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="632727374">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">Quit MT2</string>
									<string key="NSKeyEquiv">q</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
							</object>
							<string key="NSName">_NSAppleMenu</string>
						</object>
					</object>
					<object class="NSMenuItem" id="379814623">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">File</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="502551668"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="720053764">
							<string key="NSTitle">File</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="705341025">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">New</string>
									<string key="NSKeyEquiv">n</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="722745758">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Open…</string>
									<string key="NSKeyEquiv">o</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="1025936716">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Open Recent</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="1065607017">
										<string key="NSTitle">Open Recent</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="759406840">
												<reference key="NSMenu" ref="1065607017"/>
												<string key="NSTitle">Clear Menu</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
										</object>
										<string key="NSName">_NSRecentDocumentsMenu</string>
									</object>
								</object>
								<object class="NSMenuItem" id="425164168">
									<reference key="NSMenu" ref="720053764"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="776162233">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Close</string>
									<string key="NSKeyEquiv">w</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="1023925487">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Save</string>
									<string key="NSKeyEquiv">s</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="117038363">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Save As…</string>
									<string key="NSKeyEquiv">S</string>
									<int key="NSKeyEquivModMask">1179648</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="579971712">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Revert to Saved</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="1010469920">
									<reference key="NSMenu" ref="720053764"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="294629803">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Page Setup...</string>
									<string key="NSKeyEquiv">P</string>
									<int key="NSKeyEquivModMask">1179648</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSToolTip"/>
								</object>
								<object class="NSMenuItem" id="49223823">
									<reference key="NSMenu" ref="720053764"/>
									<string key="NSTitle">Print…</string>
									<string key="NSKeyEquiv">p</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
							</object>
						</object>
					</object>
					<object class="NSMenuItem" id="952259628">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Edit</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="502551668"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="789758025">
							<string key="NSTitle">Edit</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="1058277027">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Undo</string>
									<string key="NSKeyEquiv">z</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="790794224">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Redo</string>
									<string key="NSKeyEquiv">Z</string>
									<int key="NSKeyEquivModMask">1179648</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="1040322652">
									<reference key="NSMenu" ref="789758025"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="296257095">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Cut</string>
									<string key="NSKeyEquiv">x</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="860595796">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Copy</string>
									<string key="NSKeyEquiv">c</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="29853731">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Paste</string>
									<string key="NSKeyEquiv">v</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="82994268">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Paste and Match Style</string>
									<string key="NSKeyEquiv">V</string>
									<int key="NSKeyEquivModMask">1572864</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="437104165">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Delete</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="583158037">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Select All</string>
									<string key="NSKeyEquiv">a</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="212016141">
									<reference key="NSMenu" ref="789758025"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="892235320">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Find</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="963351320">
										<string key="NSTitle">Find</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="447796847">
												<reference key="NSMenu" ref="963351320"/>
												<string key="NSTitle">Find…</string>
												<string key="NSKeyEquiv">f</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
												<int key="NSTag">1</int>
											</object>
											<object class="NSMenuItem" id="326711663">
												<reference key="NSMenu" ref="963351320"/>
												<string key="NSTitle">Find Next</string>
												<string key="NSKeyEquiv">g</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
												<int key="NSTag">2</int>
											</object>
											<object class="NSMenuItem" id="270902937">
												<reference key="NSMenu" ref="963351320"/>
												<string key="NSTitle">Find Previous</string>
												<string key="NSKeyEquiv">G</string>
												<int key="NSKeyEquivModMask">1179648</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
												<int key="NSTag">3</int>
											</object>
											<object class="NSMenuItem" id="159080638">
												<reference key="NSMenu" ref="963351320"/>
												<string key="NSTitle">Use Selection for Find</string>
												<string key="NSKeyEquiv">e</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
												<int key="NSTag">7</int>
											</object>
											<object class="NSMenuItem" id="88285865">
												<reference key="NSMenu" ref="963351320"/>
												<string key="NSTitle">Jump to Selection</string>
												<string key="NSKeyEquiv">j</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
										</object>
									</object>
								</object>
								<object class="NSMenuItem" id="972420730">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Spelling and Grammar</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="769623530">
										<string key="NSTitle">Spelling and Grammar</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="679648819">
												<reference key="NSMenu" ref="769623530"/>
												<string key="NSTitle">Show Spelling and Grammar</string>
												<string key="NSKeyEquiv">:</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="96193923">
												<reference key="NSMenu" ref="769623530"/>
												<string key="NSTitle">Check Document Now</string>
												<string key="NSKeyEquiv">;</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="859480356">
												<reference key="NSMenu" ref="769623530"/>
												<bool key="NSIsDisabled">YES</bool>
												<bool key="NSIsSeparator">YES</bool>
												<string key="NSTitle"/>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="948374510">
												<reference key="NSMenu" ref="769623530"/>
												<string key="NSTitle">Check Spelling While Typing</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="967646866">
												<reference key="NSMenu" ref="769623530"/>
												<string key="NSTitle">Check Grammar With Spelling</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="795346622">
												<reference key="NSMenu" ref="769623530"/>
												<string key="NSTitle">Correct Spelling Automatically</string>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
										</object>
									</object>
								</object>
								<object class="NSMenuItem" id="507821607">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Substitutions</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="698887838">
										<string key="NSTitle">Substitutions</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="65139061">
												<reference key="NSMenu" ref="698887838"/>
												<string key="NSTitle">Show Substitutions</string>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="19036812">
												<reference key="NSMenu" ref="698887838"/>
												<bool key="NSIsDisabled">YES</bool>
												<bool key="NSIsSeparator">YES</bool>
												<string key="NSTitle"/>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="605118523">
												<reference key="NSMenu" ref="698887838"/>
												<string key="NSTitle">Smart Copy/Paste</string>
												<string key="NSKeyEquiv">f</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
												<int key="NSTag">1</int>
											</object>
											<object class="NSMenuItem" id="197661976">
												<reference key="NSMenu" ref="698887838"/>
												<string key="NSTitle">Smart Quotes</string>
												<string key="NSKeyEquiv">g</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
												<int key="NSTag">2</int>
											</object>
											<object class="NSMenuItem" id="672708820">
												<reference key="NSMenu" ref="698887838"/>
												<string key="NSTitle">Smart Dashes</string>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="708854459">
												<reference key="NSMenu" ref="698887838"/>
												<string key="NSTitle">Smart Links</string>
												<string key="NSKeyEquiv">G</string>
												<int key="NSKeyEquivModMask">1179648</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
												<int key="NSTag">3</int>
											</object>
											<object class="NSMenuItem" id="537092702">
												<reference key="NSMenu" ref="698887838"/>
												<string key="NSTitle">Text Replacement</string>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
										</object>
									</object>
								</object>
								<object class="NSMenuItem" id="288088188">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Transformations</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="579392910">
										<string key="NSTitle">Transformations</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="1060694897">
												<reference key="NSMenu" ref="579392910"/>
												<string key="NSTitle">Make Upper Case</string>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="879586729">
												<reference key="NSMenu" ref="579392910"/>
												<string key="NSTitle">Make Lower Case</string>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="56570060">
												<reference key="NSMenu" ref="579392910"/>
												<string key="NSTitle">Capitalize</string>
												<string key="NSKeyEquiv"/>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
										</object>
									</object>
								</object>
								<object class="NSMenuItem" id="676164635">
									<reference key="NSMenu" ref="789758025"/>
									<string key="NSTitle">Speech</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="785027613">
										<string key="NSTitle">Speech</string>
										<object class="NSMutableArray" key="NSMenuItems">
											<bool key="EncodedWithXMLCoder">YES</bool>
											<object class="NSMenuItem" id="731782645">
												<reference key="NSMenu" ref="785027613"/>
												<string key="NSTitle">Start Speaking</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
											<object class="NSMenuItem" id="680220178">
												<reference key="NSMenu" ref="785027613"/>
												<string key="NSTitle">Stop Speaking</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="35465992"/>
												<reference key="NSMixedImage" ref="502551668"/>
											</object>
										</object>
									</object>
								</object>
							</object>
						</object>
					</object>
					<object class="NSMenuItem" id="586577488">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">View</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="502551668"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="466310130">
							<string key="NSTitle">View</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="102151532">
									<reference key="NSMenu" ref="466310130"/>
									<string key="NSTitle">Show Toolbar</string>
									<string key="NSKeyEquiv">t</string>
									<int key="NSKeyEquivModMask">1572864</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="237841660">
									<reference key="NSMenu" ref="466310130"/>
									<string key="NSTitle">Customize Toolbar…</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
							</object>
						</object>
					</object>
					<object class="NSMenuItem" id="929916212">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Game</string>
						<string key="NSKeyEquiv"/>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="502551668"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="210653850">
							<string key="NSTitle">Menu</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="248959056">
									<reference key="NSMenu" ref="210653850"/>
									<string key="NSTitle">Reset</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
							</object>
						</object>
					</object>
					<object class="NSMenuItem" id="713487014">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Window</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="502551668"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="835318025">
							<string key="NSTitle">Window</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="1011231497">
									<reference key="NSMenu" ref="835318025"/>
									<string key="NSTitle">Minimize</string>
									<string key="NSKeyEquiv">m</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="575023229">
									<reference key="NSMenu" ref="835318025"/>
									<string key="NSTitle">Zoom</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="299356726">
									<reference key="NSMenu" ref="835318025"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
								<object class="NSMenuItem" id="625202149">
									<reference key="NSMenu" ref="835318025"/>
									<string key="NSTitle">Bring All to Front</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
							</object>
							<string key="NSName">_NSWindowsMenu</string>
						</object>
					</object>
					<object class="NSMenuItem" id="448692316">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Help</string>
						<string key="NSKeyEquiv"/>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="502551668"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="992780483">
							<string key="NSTitle">Help</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="105068016">
									<reference key="NSMenu" ref="992780483"/>
									<string key="NSTitle">MT2 Help</string>
									<string key="NSKeyEquiv">?</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="502551668"/>
								</object>
							</object>
							<string key="NSName">_NSHelpMenu</string>
						</object>
					</object>
				</object>
				<string key="NSName">_NSMainMenu</string>
			</object>
			<object class="NSWindowTemplate" id="972006081">
				<int key="NSWindowStyleMask">15</int>
				<int key="NSWindowBacking">2</int>
				<string key="NSWindowRect">{{472, 310}, {900, 600}}</string>
				<int key="NSWTFlags">1685585920</int>
				<string key="NSWindowTitle">MT2</string>
				<string key="NSWindowClass">NSWindow</string>
				<nil key="NSViewClass"/>
				<nil key="NSUserInterfaceItemIdentifier"/>
				<object class="NSView" key="NSWindowView" id="439893737">
					<reference key="NSNextResponder"/>
					<int key="NSvFlags">256</int>
					<object class="NSMutableArray" key="NSSubviews">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSOpenGLView" id="362349794">
							<reference key="NSNextResponder" ref="439893737"/>
							<int key="NSvFlags">1298</int>
							<string key="NSFrameSize">{900, 600}</string>
							<reference key="NSSuperview" ref="439893737"/>
							<reference key="NSNextKeyView"/>
							<object class="NSOpenGLPixelFormat" key="NSPixelFormat">
								<object class="NSMutableData" key="NSPixelAttributes">
									<bytes key="NS.bytes">AAAAYAAAAAwAAAAQAAAAAA</bytes>
								</object>
							</object>
						</object>
					</object>
					<string key="NSFrameSize">{900, 600}</string>
					<reference key="NSSuperview"/>
					<reference key="NSNextKeyView" ref="362349794"/>
				</object>
				<string key="NSScreenRect">{{0, 0}, {1920, 1058}}</string>
				<string key="NSMaxSize">{10000000000000, 10000000000000}</string>
				<bool key="NSWindowIsRestorable">YES</bool>
			</object>
			<object class="NSCustomObject" id="976324537">
				<string key="NSClassName">MT2AppDelegate</string>
			</object>
			<object class="NSCustomObject" id="755631768">
				<string key="NSClassName">NSFontManager</string>
			</object>
		</object>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<object class="NSMutableArray" key="connectionRecords">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">terminate:</string>
						<reference key="source" ref="1050"/>
						<reference key="destination" ref="632727374"/>
					</object>
					<int key="connectionID">449</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">orderFrontStandardAboutPanel:</string>
						<reference key="source" ref="1021"/>
						<reference key="destination" ref="238522557"/>
					</object>
					<int key="connectionID">142</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">delegate</string>
						<reference key="source" ref="1021"/>
						<reference key="destination" ref="976324537"/>
					</object>
					<int key="connectionID">495</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performMiniaturize:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="1011231497"/>
					</object>
					<int key="connectionID">37</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">arrangeInFront:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="625202149"/>
					</object>
					<int key="connectionID">39</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">print:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="49223823"/>
					</object>
					<int key="connectionID">86</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">runPageLayout:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="294629803"/>
					</object>
					<int key="connectionID">87</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">clearRecentDocuments:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="759406840"/>
					</object>
					<int key="connectionID">127</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performClose:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="776162233"/>
					</object>
					<int key="connectionID">193</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleContinuousSpellChecking:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="948374510"/>
					</object>
					<int key="connectionID">222</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">undo:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="1058277027"/>
					</object>
					<int key="connectionID">223</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">copy:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="860595796"/>
					</object>
					<int key="connectionID">224</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">checkSpelling:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="96193923"/>
					</object>
					<int key="connectionID">225</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">paste:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="29853731"/>
					</object>
					<int key="connectionID">226</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">stopSpeaking:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="680220178"/>
					</object>
					<int key="connectionID">227</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">cut:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="296257095"/>
					</object>
					<int key="connectionID">228</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showGuessPanel:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="679648819"/>
					</object>
					<int key="connectionID">230</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">redo:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="790794224"/>
					</object>
					<int key="connectionID">231</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">selectAll:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="583158037"/>
					</object>
					<int key="connectionID">232</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">startSpeaking:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="731782645"/>
					</object>
					<int key="connectionID">233</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">delete:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="437104165"/>
					</object>
					<int key="connectionID">235</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performZoom:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="575023229"/>
					</object>
					<int key="connectionID">240</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="447796847"/>
					</object>
					<int key="connectionID">241</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">centerSelectionInVisibleArea:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="88285865"/>
					</object>
					<int key="connectionID">245</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleGrammarChecking:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="967646866"/>
					</object>
					<int key="connectionID">347</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleSmartInsertDelete:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="605118523"/>
					</object>
					<int key="connectionID">355</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleAutomaticQuoteSubstitution:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="197661976"/>
					</object>
					<int key="connectionID">356</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleAutomaticLinkDetection:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="708854459"/>
					</object>
					<int key="connectionID">357</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">saveDocument:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="1023925487"/>
					</object>
					<int key="connectionID">362</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">saveDocumentAs:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="117038363"/>
					</object>
					<int key="connectionID">363</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">revertDocumentToSaved:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="579971712"/>
					</object>
					<int key="connectionID">364</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">runToolbarCustomizationPalette:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="237841660"/>
					</object>
					<int key="connectionID">365</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleToolbarShown:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="102151532"/>
					</object>
					<int key="connectionID">366</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">hide:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="755159360"/>
					</object>
					<int key="connectionID">367</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">hideOtherApplications:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="342932134"/>
					</object>
					<int key="connectionID">368</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">unhideAllApplications:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="908899353"/>
					</object>
					<int key="connectionID">370</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">newDocument:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="705341025"/>
					</object>
					<int key="connectionID">373</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">openDocument:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="722745758"/>
					</object>
					<int key="connectionID">374</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleAutomaticSpellingCorrection:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="795346622"/>
					</object>
					<int key="connectionID">456</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">orderFrontSubstitutionsPanel:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="65139061"/>
					</object>
					<int key="connectionID">458</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleAutomaticDashSubstitution:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="672708820"/>
					</object>
					<int key="connectionID">461</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleAutomaticTextReplacement:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="537092702"/>
					</object>
					<int key="connectionID">463</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">uppercaseWord:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="1060694897"/>
					</object>
					<int key="connectionID">464</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">capitalizeWord:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="56570060"/>
					</object>
					<int key="connectionID">467</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">lowercaseWord:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="879586729"/>
					</object>
					<int key="connectionID">468</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">pasteAsPlainText:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="82994268"/>
					</object>
					<int key="connectionID">486</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="326711663"/>
					</object>
					<int key="connectionID">487</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="270902937"/>
					</object>
					<int key="connectionID">488</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="159080638"/>
					</object>
					<int key="connectionID">489</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showHelp:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="105068016"/>
					</object>
					<int key="connectionID">493</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">window</string>
						<reference key="source" ref="976324537"/>
						<reference key="destination" ref="972006081"/>
					</object>
					<int key="connectionID">532</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">resetGame:</string>
						<reference key="source" ref="362349794"/>
						<reference key="destination" ref="248959056"/>
					</object>
					<int key="connectionID">538</int>
				</object>
			</object>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<object class="NSArray" key="orderedObjects">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<object class="NSArray" key="object" id="0">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="children" ref="1048"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="1021"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="1014"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">First Responder</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-3</int>
						<reference key="object" ref="1050"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">Application</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">29</int>
						<reference key="object" ref="649796088"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="713487014"/>
							<reference ref="694149608"/>
							<reference ref="952259628"/>
							<reference ref="379814623"/>
							<reference ref="586577488"/>
							<reference ref="448692316"/>
							<reference ref="929916212"/>
						</object>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">19</int>
						<reference key="object" ref="713487014"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="835318025"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">56</int>
						<reference key="object" ref="694149608"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="110575045"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">217</int>
						<reference key="object" ref="952259628"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="789758025"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">83</int>
						<reference key="object" ref="379814623"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="720053764"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">81</int>
						<reference key="object" ref="720053764"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1023925487"/>
							<reference ref="117038363"/>
							<reference ref="49223823"/>
							<reference ref="722745758"/>
							<reference ref="705341025"/>
							<reference ref="1025936716"/>
							<reference ref="294629803"/>
							<reference ref="776162233"/>
							<reference ref="425164168"/>
							<reference ref="579971712"/>
							<reference ref="1010469920"/>
						</object>
						<reference key="parent" ref="379814623"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">75</int>
						<reference key="object" ref="1023925487"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">80</int>
						<reference key="object" ref="117038363"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">78</int>
						<reference key="object" ref="49223823"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">72</int>
						<reference key="object" ref="722745758"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">82</int>
						<reference key="object" ref="705341025"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">124</int>
						<reference key="object" ref="1025936716"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1065607017"/>
						</object>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">77</int>
						<reference key="object" ref="294629803"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">73</int>
						<reference key="object" ref="776162233"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">79</int>
						<reference key="object" ref="425164168"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">112</int>
						<reference key="object" ref="579971712"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">74</int>
						<reference key="object" ref="1010469920"/>
						<reference key="parent" ref="720053764"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">125</int>
						<reference key="object" ref="1065607017"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="759406840"/>
						</object>
						<reference key="parent" ref="1025936716"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">126</int>
						<reference key="object" ref="759406840"/>
						<reference key="parent" ref="1065607017"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">205</int>
						<reference key="object" ref="789758025"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="437104165"/>
							<reference ref="583158037"/>
							<reference ref="1058277027"/>
							<reference ref="212016141"/>
							<reference ref="296257095"/>
							<reference ref="29853731"/>
							<reference ref="860595796"/>
							<reference ref="1040322652"/>
							<reference ref="790794224"/>
							<reference ref="892235320"/>
							<reference ref="972420730"/>
							<reference ref="676164635"/>
							<reference ref="507821607"/>
							<reference ref="288088188"/>
							<reference ref="82994268"/>
						</object>
						<reference key="parent" ref="952259628"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">202</int>
						<reference key="object" ref="437104165"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">198</int>
						<reference key="object" ref="583158037"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">207</int>
						<reference key="object" ref="1058277027"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">214</int>
						<reference key="object" ref="212016141"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">199</int>
						<reference key="object" ref="296257095"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">203</int>
						<reference key="object" ref="29853731"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">197</int>
						<reference key="object" ref="860595796"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">206</int>
						<reference key="object" ref="1040322652"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">215</int>
						<reference key="object" ref="790794224"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">218</int>
						<reference key="object" ref="892235320"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="963351320"/>
						</object>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">216</int>
						<reference key="object" ref="972420730"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="769623530"/>
						</object>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">200</int>
						<reference key="object" ref="769623530"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="948374510"/>
							<reference ref="96193923"/>
							<reference ref="679648819"/>
							<reference ref="967646866"/>
							<reference ref="859480356"/>
							<reference ref="795346622"/>
						</object>
						<reference key="parent" ref="972420730"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">219</int>
						<reference key="object" ref="948374510"/>
						<reference key="parent" ref="769623530"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">201</int>
						<reference key="object" ref="96193923"/>
						<reference key="parent" ref="769623530"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">204</int>
						<reference key="object" ref="679648819"/>
						<reference key="parent" ref="769623530"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">220</int>
						<reference key="object" ref="963351320"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="270902937"/>
							<reference ref="88285865"/>
							<reference ref="159080638"/>
							<reference ref="326711663"/>
							<reference ref="447796847"/>
						</object>
						<reference key="parent" ref="892235320"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">213</int>
						<reference key="object" ref="270902937"/>
						<reference key="parent" ref="963351320"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">210</int>
						<reference key="object" ref="88285865"/>
						<reference key="parent" ref="963351320"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">221</int>
						<reference key="object" ref="159080638"/>
						<reference key="parent" ref="963351320"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">208</int>
						<reference key="object" ref="326711663"/>
						<reference key="parent" ref="963351320"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">209</int>
						<reference key="object" ref="447796847"/>
						<reference key="parent" ref="963351320"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">57</int>
						<reference key="object" ref="110575045"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="238522557"/>
							<reference ref="755159360"/>
							<reference ref="908899353"/>
							<reference ref="632727374"/>
							<reference ref="646227648"/>
							<reference ref="609285721"/>
							<reference ref="481834944"/>
							<reference ref="304266470"/>
							<reference ref="1046388886"/>
							<reference ref="1056857174"/>
							<reference ref="342932134"/>
						</object>
						<reference key="parent" ref="694149608"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">58</int>
						<reference key="object" ref="238522557"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">134</int>
						<reference key="object" ref="755159360"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">150</int>
						<reference key="object" ref="908899353"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">136</int>
						<reference key="object" ref="632727374"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">144</int>
						<reference key="object" ref="646227648"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">129</int>
						<reference key="object" ref="609285721"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">143</int>
						<reference key="object" ref="481834944"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">236</int>
						<reference key="object" ref="304266470"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">131</int>
						<reference key="object" ref="1046388886"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="752062318"/>
						</object>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">149</int>
						<reference key="object" ref="1056857174"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">145</int>
						<reference key="object" ref="342932134"/>
						<reference key="parent" ref="110575045"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">130</int>
						<reference key="object" ref="752062318"/>
						<reference key="parent" ref="1046388886"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">24</int>
						<reference key="object" ref="835318025"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="299356726"/>
							<reference ref="625202149"/>
							<reference ref="575023229"/>
							<reference ref="1011231497"/>
						</object>
						<reference key="parent" ref="713487014"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">92</int>
						<reference key="object" ref="299356726"/>
						<reference key="parent" ref="835318025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">5</int>
						<reference key="object" ref="625202149"/>
						<reference key="parent" ref="835318025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">239</int>
						<reference key="object" ref="575023229"/>
						<reference key="parent" ref="835318025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">23</int>
						<reference key="object" ref="1011231497"/>
						<reference key="parent" ref="835318025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">295</int>
						<reference key="object" ref="586577488"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="466310130"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">296</int>
						<reference key="object" ref="466310130"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="102151532"/>
							<reference ref="237841660"/>
						</object>
						<reference key="parent" ref="586577488"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">297</int>
						<reference key="object" ref="102151532"/>
						<reference key="parent" ref="466310130"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">298</int>
						<reference key="object" ref="237841660"/>
						<reference key="parent" ref="466310130"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">211</int>
						<reference key="object" ref="676164635"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="785027613"/>
						</object>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">212</int>
						<reference key="object" ref="785027613"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="680220178"/>
							<reference ref="731782645"/>
						</object>
						<reference key="parent" ref="676164635"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">195</int>
						<reference key="object" ref="680220178"/>
						<reference key="parent" ref="785027613"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">196</int>
						<reference key="object" ref="731782645"/>
						<reference key="parent" ref="785027613"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">346</int>
						<reference key="object" ref="967646866"/>
						<reference key="parent" ref="769623530"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">348</int>
						<reference key="object" ref="507821607"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="698887838"/>
						</object>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">349</int>
						<reference key="object" ref="698887838"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="605118523"/>
							<reference ref="197661976"/>
							<reference ref="708854459"/>
							<reference ref="65139061"/>
							<reference ref="19036812"/>
							<reference ref="672708820"/>
							<reference ref="537092702"/>
						</object>
						<reference key="parent" ref="507821607"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">350</int>
						<reference key="object" ref="605118523"/>
						<reference key="parent" ref="698887838"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">351</int>
						<reference key="object" ref="197661976"/>
						<reference key="parent" ref="698887838"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">354</int>
						<reference key="object" ref="708854459"/>
						<reference key="parent" ref="698887838"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">371</int>
						<reference key="object" ref="972006081"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="439893737"/>
						</object>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">372</int>
						<reference key="object" ref="439893737"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="362349794"/>
						</object>
						<reference key="parent" ref="972006081"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">420</int>
						<reference key="object" ref="755631768"/>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">450</int>
						<reference key="object" ref="288088188"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="579392910"/>
						</object>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">451</int>
						<reference key="object" ref="579392910"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="1060694897"/>
							<reference ref="879586729"/>
							<reference ref="56570060"/>
						</object>
						<reference key="parent" ref="288088188"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">452</int>
						<reference key="object" ref="1060694897"/>
						<reference key="parent" ref="579392910"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">453</int>
						<reference key="object" ref="859480356"/>
						<reference key="parent" ref="769623530"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">454</int>
						<reference key="object" ref="795346622"/>
						<reference key="parent" ref="769623530"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">457</int>
						<reference key="object" ref="65139061"/>
						<reference key="parent" ref="698887838"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">459</int>
						<reference key="object" ref="19036812"/>
						<reference key="parent" ref="698887838"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">460</int>
						<reference key="object" ref="672708820"/>
						<reference key="parent" ref="698887838"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">462</int>
						<reference key="object" ref="537092702"/>
						<reference key="parent" ref="698887838"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">465</int>
						<reference key="object" ref="879586729"/>
						<reference key="parent" ref="579392910"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">466</int>
						<reference key="object" ref="56570060"/>
						<reference key="parent" ref="579392910"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">485</int>
						<reference key="object" ref="82994268"/>
						<reference key="parent" ref="789758025"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">490</int>
						<reference key="object" ref="448692316"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="992780483"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">491</int>
						<reference key="object" ref="992780483"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="105068016"/>
						</object>
						<reference key="parent" ref="448692316"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">492</int>
						<reference key="object" ref="105068016"/>
						<reference key="parent" ref="992780483"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">494</int>
						<reference key="object" ref="976324537"/>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">533</int>
						<reference key="object" ref="362349794"/>
						<reference key="parent" ref="439893737"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">535</int>
						<reference key="object" ref="929916212"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="210653850"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">536</int>
						<reference key="object" ref="210653850"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="248959056"/>
						</object>
						<reference key="parent" ref="929916212"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">537</int>
						<reference key="object" ref="248959056"/>
						<reference key="parent" ref="210653850"/>
					</object>
				</object>
			</object>
			<object class="NSMutableDictionary" key="flattenedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="NSArray" key="dict.sortedKeys">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>-1.IBPluginDependency</string>
					<string>-2.IBPluginDependency</string>
					<string>-3.IBPluginDependency</string>
					<string>112.IBPluginDependency</string>
					<string>124.IBPluginDependency</string>
					<string>125.IBPluginDependency</string>
					<string>126.IBPluginDependency</string>
					<string>129.IBPluginDependency</string>
					<string>130.IBPluginDependency</string>
					<string>131.IBPluginDependency</string>
					<string>134.IBPluginDependency</string>
					<string>136.IBPluginDependency</string>
					<string>143.IBPluginDependency</string>
					<string>144.IBPluginDependency</string>
					<string>145.IBPluginDependency</string>
					<string>149.IBPluginDependency</string>
					<string>150.IBPluginDependency</string>
					<string>19.IBPluginDependency</string>
					<string>195.IBPluginDependency</string>
					<string>196.IBPluginDependency</string>
					<string>197.IBPluginDependency</string>
					<string>198.IBPluginDependency</string>
					<string>199.IBPluginDependency</string>
					<string>200.IBPluginDependency</string>
					<string>201.IBPluginDependency</string>
					<string>202.IBPluginDependency</string>
					<string>203.IBPluginDependency</string>
					<string>204.IBPluginDependency</string>
					<string>205.IBPluginDependency</string>
					<string>206.IBPluginDependency</string>
					<string>207.IBPluginDependency</string>
					<string>208.IBPluginDependency</string>
					<string>209.IBPluginDependency</string>
					<string>210.IBPluginDependency</string>
					<string>211.IBPluginDependency</string>
					<string>212.IBPluginDependency</string>
					<string>213.IBPluginDependency</string>
					<string>214.IBPluginDependency</string>
					<string>215.IBPluginDependency</string>
					<string>216.IBPluginDependency</string>
					<string>217.IBPluginDependency</string>
					<string>218.IBPluginDependency</string>
					<string>219.IBPluginDependency</string>
					<string>220.IBPluginDependency</string>
					<string>221.IBPluginDependency</string>
					<string>23.IBPluginDependency</string>
					<string>236.IBPluginDependency</string>
					<string>239.IBPluginDependency</string>
					<string>24.IBPluginDependency</string>
					<string>29.IBPluginDependency</string>
					<string>295.IBPluginDependency</string>
					<string>296.IBPluginDependency</string>
					<string>297.IBPluginDependency</string>
					<string>298.IBPluginDependency</string>
					<string>346.IBPluginDependency</string>
					<string>348.IBPluginDependency</string>
					<string>349.IBPluginDependency</string>
					<string>350.IBPluginDependency</string>
					<string>351.IBPluginDependency</string>
					<string>354.IBPluginDependency</string>
					<string>371.IBPluginDependency</string>
					<string>371.IBWindowTemplateEditedContentRect</string>
					<string>371.NSWindowTemplate.visibleAtLaunch</string>
					<string>372.IBPluginDependency</string>
					<string>420.IBPluginDependency</string>
					<string>450.IBPluginDependency</string>
					<string>451.IBPluginDependency</string>
					<string>452.IBPluginDependency</string>
					<string>453.IBPluginDependency</string>
					<string>454.IBPluginDependency</string>
					<string>457.IBPluginDependency</string>
					<string>459.IBPluginDependency</string>
					<string>460.IBPluginDependency</string>
					<string>462.IBPluginDependency</string>
					<string>465.IBPluginDependency</string>
					<string>466.IBPluginDependency</string>
					<string>485.IBPluginDependency</string>
					<string>490.IBPluginDependency</string>
					<string>491.IBPluginDependency</string>
					<string>492.IBPluginDependency</string>
					<string>494.IBPluginDependency</string>
					<string>5.IBPluginDependency</string>
					<string>533.CustomClassName</string>
					<string>533.IBPluginDependency</string>
					<string>535.IBPluginDependency</string>
					<string>536.IBPluginDependency</string>
					<string>537.IBPluginDependency</string>
					<string>56.IBPluginDependency</string>
					<string>57.IBPluginDependency</string>
					<string>58.IBPluginDependency</string>
					<string>72.IBPluginDependency</string>
					<string>73.IBPluginDependency</string>
					<string>74.IBPluginDependency</string>
					<string>75.IBPluginDependency</string>
					<string>77.IBPluginDependency</string>
					<string>78.IBPluginDependency</string>
					<string>79.IBPluginDependency</string>
					<string>80.IBPluginDependency</string>
					<string>81.IBPluginDependency</string>
					<string>82.IBPluginDependency</string>
					<string>83.IBPluginDependency</string>
					<string>92.IBPluginDependency</string>
				</object>
				<object class="NSArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>{{216, 112}, {900, 600}}</string>
					<integer value="1"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>OGLView</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
				</object>
			</object>
			<object class="NSMutableDictionary" key="unlocalizedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<reference key="dict.values" ref="0"/>
			</object>
			<nil key="activeLocalization"/>
			<object class="NSMutableDictionary" key="localizations">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<reference key="dict.values" ref="0"/>
			</object>
			<nil key="sourceID"/>
			<int key="maxID">538</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes"/>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaFramework</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDependencyDefaults">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.macosx</string>
			<integer value="1060" key="NS.object.0"/>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDevelopmentDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.InterfaceBuilder3</string>
			<integer value="3000" key="NS.object.0"/>
		</object>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<object class="NSMutableDictionary" key="IBDocument.LastKnownImageSizes">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSArray" key="dict.sortedKeys">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<string>NSMenuCheckmark</string>
				<string>NSMenuMixedState</string>
			</object>
			<object class="NSArray" key="dict.values">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<string>{11, 11}</string>
				<string>{10, 3}</string>
			</object>
		</object>
	</data>
</archive>
