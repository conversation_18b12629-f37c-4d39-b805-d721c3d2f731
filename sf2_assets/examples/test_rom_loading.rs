//! # ROM Loading Test Example
//! 
//! This example demonstrates the ROM loading and asset extraction system.
//! 
//! ## Usage
//! 
//! ```bash
//! # Test with default ROM directory (./roms)
//! cargo run --example test_rom_loading
//! 
//! # Test with custom ROM directory
//! SF2_ROM_DIR=/path/to/roms cargo run --example test_rom_loading
//! 
//! # Test with verbose logging
//! RUST_LOG=debug cargo run --example test_rom_loading
//! ```

use sf2_assets::{run_rom_system_test, run_quick_rom_test, RomTestApp, TestResults};
use std::env;
use std::path::PathBuf;
use log::{info, warn, error};

fn main() {
    // Initialize simple logging
    println!("=== SF2 ROM Loading Test ===");
    
    // Get ROM directory from environment or use default
    let rom_directory = env::var("SF2_ROM_DIR")
        .ok()
        .map(PathBuf::from)
        .or_else(|| {
            let default_path = PathBuf::from("./roms");
            if default_path.exists() {
                Some(default_path)
            } else {
                None
            }
        });
    
    if let Some(ref rom_dir) = rom_directory {
        println!("Using ROM directory: {}", rom_dir.display());
    } else {
        println!("No ROM directory specified or found. Testing without ROM files.");
        println!("To test with ROM files, set SF2_ROM_DIR environment variable or place ROM files in ./roms/");
    }

    // Run quick validation test first
    println!("\n--- Running Quick ROM Validation Test ---");
    let quick_test_passed = run_quick_rom_test(rom_directory.clone());

    if quick_test_passed {
        println!("✓ Quick ROM test passed");
    } else {
        println!("✗ Quick ROM test failed");
    }

    // Run comprehensive test
    println!("\n--- Running Comprehensive ROM System Test ---");
    let results = run_rom_system_test(rom_directory.clone());
    
    // Print detailed results
    print_detailed_results(&results);
    
    // Run interactive test if ROM files are available
    if rom_directory.is_some() && results.rom_loaded {
        println!("\n--- Running Interactive Test ---");
        run_interactive_test(rom_directory.clone());
    }

    // Run performance validation
    println!("\n--- Running Performance Validation ---");
    run_performance_validation(rom_directory.clone());

    // Create mock ROM data for testing if no real ROMs available
    if rom_directory.is_none() {
        println!("\n--- Creating Mock ROM Data for Testing ---");
        create_mock_rom_data();

        // Test with mock data
        println!("\n--- Testing with Mock ROM Data ---");
        let mock_results = run_rom_system_test(Some(PathBuf::from("./mock_roms")));
        print_detailed_results(&mock_results);
    }

    // Final summary
    println!("\n=== Test Summary ===");
    if results.is_successful() {
        println!("🎉 All tests PASSED! ROM loading and asset extraction working correctly.");
    } else {
        println!("❌ Some tests FAILED. Check the logs above for details.");
    }

    println!("Test completed.");
}

fn print_detailed_results(results: &TestResults) {
    println!("\n--- Detailed Test Results ---");

    // ROM Loading
    if results.rom_loaded {
        println!("✓ ROM Loading: SUCCESS");
        if let Some(frame) = results.rom_load_frame {
            println!("  - Loaded at frame: {}", frame);
        }
    } else {
        println!("✗ ROM Loading: FAILED");
        if let Some(ref error) = results.rom_error {
            println!("  - Error: {}", error);
        }
    }

    // Asset Extraction
    if results.extraction_complete {
        println!("✓ Asset Extraction: SUCCESS");
        if let Some(frame) = results.extraction_complete_frame {
            println!("  - Completed at frame: {}", frame);
        }
    } else {
        println!("⚠ Asset Extraction: INCOMPLETE");
    }

    // Sprite Data
    println!("📊 Sprite Data:");
    println!("  - Sprites extracted: {}/{}", results.sprites_extracted, results.total_sprites);
    println!("  - Total sprite objects: {}", results.sprite_count);
    println!("  - Color palettes: {}", results.palette_count);

    // Audio Data
    println!("🔊 Audio Data:");
    println!("  - Audio samples extracted: {}/{}", results.audio_extracted, results.total_audio);
    println!("  - Total audio samples: {}", results.audio_sample_count);

    // Audio System
    if results.audio_test_performed {
        println!("✓ Audio System: TESTED");
    } else {
        println!("⚠ Audio System: NOT TESTED");
    }
}

fn run_interactive_test(rom_directory: Option<PathBuf>) {
    info!("Starting interactive test with real-time monitoring...");
    
    let mut test_app = RomTestApp::new(rom_directory);
    let mut last_sprites = 0;
    let mut last_audio = 0;
    
    // Run for 10 seconds, monitoring progress
    for frame in 0..600 {
        test_app.update();
        
        // Monitor progress every 60 frames (1 second)
        if frame % 60 == 0 {
            let progress = test_app.get_extraction_progress();
            let sprites = test_app.get_extracted_sprites();
            let audio = test_app.get_extracted_audio();
            
            if let Some(progress) = progress {
                let sprite_count = sprites.map(|s| s.sprites.len()).unwrap_or(0);
                let audio_count = audio.map(|a| a.samples.len()).unwrap_or(0);
                
                if sprite_count != last_sprites || audio_count != last_audio {
                    info!("Frame {}: Sprites: {}, Audio: {}, Progress: {:.1}%", 
                          frame, 
                          sprite_count, 
                          audio_count,
                          (progress.sprites_extracted + progress.audio_extracted) as f32 /
                          (progress.total_sprites + progress.total_audio) as f32 * 100.0);
                    
                    last_sprites = sprite_count;
                    last_audio = audio_count;
                }
                
                if progress.is_complete {
                    info!("✓ Asset extraction completed at frame {}", frame);
                    break;
                }
            }
        }
    }
    
    // Final status
    if let Some(rom_data) = test_app.get_rom_data() {
        if let Some(ref loaded_roms) = rom_data.loaded_roms {
            info!("📁 ROM File Sizes:");
            info!("  - Graphics ROM: {} bytes", loaded_roms.graphics_rom.len());
            info!("  - Code ROM: {} bytes", loaded_roms.code_rom.len());
            if let Some(ref audio_rom) = loaded_roms.audio_rom {
                info!("  - Audio ROM: {} bytes", audio_rom.len());
            }
            if let Some(ref sound_rom) = loaded_roms.sound_rom {
                info!("  - Sound ROM: {} bytes", sound_rom.len());
            }
        }
    }
    
    info!("Interactive test completed.");
}

fn run_performance_validation(rom_directory: Option<PathBuf>) {
    info!("Starting performance validation...");

    let mut test_app = RomTestApp::new(rom_directory);
    let start_time = std::time::Instant::now();
    let mut frame_times = Vec::new();
    let mut max_frame_time = std::time::Duration::ZERO;
    let mut min_frame_time = std::time::Duration::from_secs(1);

    // Run for 300 frames (5 seconds at 60 FPS)
    for frame in 0..300 {
        let frame_start = std::time::Instant::now();
        test_app.update();
        let frame_duration = frame_start.elapsed();

        frame_times.push(frame_duration);
        max_frame_time = max_frame_time.max(frame_duration);
        min_frame_time = min_frame_time.min(frame_duration);

        // Log performance every 60 frames
        if frame % 60 == 0 && frame > 0 {
            let avg_frame_time: std::time::Duration = frame_times.iter().sum::<std::time::Duration>() / frame_times.len() as u32;
            let fps = 1.0 / avg_frame_time.as_secs_f64();

            info!("Frame {}: Avg FPS: {:.1}, Avg frame time: {:.2}ms",
                  frame, fps, avg_frame_time.as_millis());

            // Check if we're maintaining 60 FPS target
            if fps < 55.0 {
                warn!("⚠ Performance warning: FPS dropped below 55 ({:.1})", fps);
            }
        }
    }

    let total_time = start_time.elapsed();
    let avg_frame_time: std::time::Duration = frame_times.iter().sum::<std::time::Duration>() / frame_times.len() as u32;
    let avg_fps = 1.0 / avg_frame_time.as_secs_f64();

    info!("\n📊 Performance Validation Results:");
    info!("  - Total test time: {:.2}s", total_time.as_secs_f64());
    info!("  - Average FPS: {:.1}", avg_fps);
    info!("  - Average frame time: {:.2}ms", avg_frame_time.as_millis());
    info!("  - Min frame time: {:.2}ms", min_frame_time.as_millis());
    info!("  - Max frame time: {:.2}ms", max_frame_time.as_millis());

    // Performance assessment
    if avg_fps >= 58.0 {
        info!("✅ Performance: EXCELLENT (>= 58 FPS)");
    } else if avg_fps >= 50.0 {
        info!("✅ Performance: GOOD (>= 50 FPS)");
    } else if avg_fps >= 30.0 {
        warn!("⚠ Performance: ACCEPTABLE (>= 30 FPS)");
    } else {
        error!("❌ Performance: POOR (< 30 FPS)");
    }

    // Frame time consistency check
    let frame_time_variance = frame_times.iter()
        .map(|&t| (t.as_secs_f64() - avg_frame_time.as_secs_f64()).powi(2))
        .sum::<f64>() / frame_times.len() as f64;
    let frame_time_std_dev = frame_time_variance.sqrt();

    info!("  - Frame time std dev: {:.2}ms", frame_time_std_dev * 1000.0);

    if frame_time_std_dev < 0.005 {
        info!("✅ Frame timing: VERY CONSISTENT");
    } else if frame_time_std_dev < 0.010 {
        info!("✅ Frame timing: CONSISTENT");
    } else {
        warn!("⚠ Frame timing: INCONSISTENT (high variance)");
    }
}

fn create_mock_rom_data() {
    use std::fs;

    info!("Creating mock ROM data for testing...");

    // Create mock ROM directory
    let mock_dir = PathBuf::from("./mock_roms");
    if let Err(e) = fs::create_dir_all(&mock_dir) {
        error!("Failed to create mock ROM directory: {}", e);
        return;
    }

    // Create mock ROM files with realistic sizes and patterns
    create_mock_rom_file(&mock_dir.join("sf2.03c"), 0x80000, 0xAA); // Graphics ROM - 512KB
    create_mock_rom_file(&mock_dir.join("sf2.30"), 0x20000, 0x55);  // Code ROM - 128KB
    create_mock_rom_file(&mock_dir.join("sf2.01"), 0x40000, 0xCC);  // Audio ROM - 256KB
    create_mock_rom_file(&mock_dir.join("sf2.02"), 0x40000, 0x33);  // Sound ROM - 256KB

    info!("✅ Mock ROM files created in: {}", mock_dir.display());
}

fn create_mock_rom_file(path: &PathBuf, size: usize, pattern: u8) {
    use std::fs::File;
    use std::io::Write;

    match File::create(path) {
        Ok(mut file) => {
            // Create realistic ROM data with patterns
            let mut data = Vec::with_capacity(size);

            for i in 0..size {
                // Create a pattern that simulates real ROM data
                let byte = match i % 4 {
                    0 => pattern,
                    1 => pattern.wrapping_add((i / 256) as u8),
                    2 => pattern.wrapping_sub((i / 512) as u8),
                    3 => pattern ^ ((i / 1024) as u8),
                    _ => pattern,
                };
                data.push(byte);
            }

            if let Err(e) = file.write_all(&data) {
                error!("Failed to write mock ROM file {}: {}", path.display(), e);
            } else {
                info!("  Created: {} ({} bytes)", path.display(), size);
            }
        }
        Err(e) => {
            error!("Failed to create mock ROM file {}: {}", path.display(), e);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_example_runs_without_panic() {
        // Test that the example can run without panicking
        // This is a basic smoke test
        let rom_directory = None; // No ROM files for CI
        let results = run_quick_rom_test(rom_directory);
        
        // Should not panic, even without ROM files
        // The test may fail, but it shouldn't crash
        println!("Quick test result: {}", results);
    }
}
