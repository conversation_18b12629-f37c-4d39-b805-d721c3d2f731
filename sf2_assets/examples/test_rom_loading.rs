//! # ROM Loading Test Example
//! 
//! This example demonstrates the ROM loading and asset extraction system.
//! 
//! ## Usage
//! 
//! ```bash
//! # Test with default ROM directory (./roms)
//! cargo run --example test_rom_loading
//! 
//! # Test with custom ROM directory
//! SF2_ROM_DIR=/path/to/roms cargo run --example test_rom_loading
//! 
//! # Test with verbose logging
//! RUST_LOG=debug cargo run --example test_rom_loading
//! ```

use sf2_assets::{run_rom_system_test, run_quick_rom_test, RomTestApp, TestResults};
use std::env;
use std::path::PathBuf;
use log::{info, warn, error};

fn main() {
    // Initialize simple logging
    println!("=== SF2 ROM Loading Test ===");
    
    info!("=== SF2 ROM Loading Test ===");
    
    // Get ROM directory from environment or use default
    let rom_directory = env::var("SF2_ROM_DIR")
        .ok()
        .map(PathBuf::from)
        .or_else(|| {
            let default_path = PathBuf::from("./roms");
            if default_path.exists() {
                Some(default_path)
            } else {
                None
            }
        });
    
    if let Some(ref rom_dir) = rom_directory {
        info!("Using ROM directory: {}", rom_dir.display());
    } else {
        warn!("No ROM directory specified or found. Testing without ROM files.");
        info!("To test with ROM files, set SF2_ROM_DIR environment variable or place ROM files in ./roms/");
    }
    
    // Run quick validation test first
    info!("\n--- Running Quick ROM Validation Test ---");
    let quick_test_passed = run_quick_rom_test(rom_directory.clone());
    
    if quick_test_passed {
        info!("✓ Quick ROM test passed");
    } else {
        warn!("✗ Quick ROM test failed");
    }
    
    // Run comprehensive test
    info!("\n--- Running Comprehensive ROM System Test ---");
    let results = run_rom_system_test(rom_directory.clone());
    
    // Print detailed results
    print_detailed_results(&results);
    
    // Run interactive test if ROM files are available
    if rom_directory.is_some() && results.rom_loaded {
        info!("\n--- Running Interactive Test ---");
        run_interactive_test(rom_directory);
    }
    
    // Final summary
    info!("\n=== Test Summary ===");
    if results.is_successful() {
        info!("🎉 All tests PASSED! ROM loading and asset extraction working correctly.");
    } else {
        error!("❌ Some tests FAILED. Check the logs above for details.");
    }
    
    info!("Test completed.");
}

fn print_detailed_results(results: &TestResults) {
    info!("\n--- Detailed Test Results ---");
    
    // ROM Loading
    if results.rom_loaded {
        info!("✓ ROM Loading: SUCCESS");
        if let Some(frame) = results.rom_load_frame {
            info!("  - Loaded at frame: {}", frame);
        }
    } else {
        error!("✗ ROM Loading: FAILED");
        if let Some(ref error) = results.rom_error {
            error!("  - Error: {}", error);
        }
    }
    
    // Asset Extraction
    if results.extraction_complete {
        info!("✓ Asset Extraction: SUCCESS");
        if let Some(frame) = results.extraction_complete_frame {
            info!("  - Completed at frame: {}", frame);
        }
    } else {
        warn!("⚠ Asset Extraction: INCOMPLETE");
    }
    
    // Sprite Data
    info!("📊 Sprite Data:");
    info!("  - Sprites extracted: {}/{}", results.sprites_extracted, results.total_sprites);
    info!("  - Total sprite objects: {}", results.sprite_count);
    info!("  - Color palettes: {}", results.palette_count);
    
    // Audio Data
    info!("🔊 Audio Data:");
    info!("  - Audio samples extracted: {}/{}", results.audio_extracted, results.total_audio);
    info!("  - Total audio samples: {}", results.audio_sample_count);
    
    // Audio System
    if results.audio_test_performed {
        info!("✓ Audio System: TESTED");
    } else {
        warn!("⚠ Audio System: NOT TESTED");
    }
}

fn run_interactive_test(rom_directory: Option<PathBuf>) {
    info!("Starting interactive test with real-time monitoring...");
    
    let mut test_app = RomTestApp::new(rom_directory);
    let mut last_sprites = 0;
    let mut last_audio = 0;
    
    // Run for 10 seconds, monitoring progress
    for frame in 0..600 {
        test_app.update();
        
        // Monitor progress every 60 frames (1 second)
        if frame % 60 == 0 {
            let progress = test_app.get_extraction_progress();
            let sprites = test_app.get_extracted_sprites();
            let audio = test_app.get_extracted_audio();
            
            if let Some(progress) = progress {
                let sprite_count = sprites.map(|s| s.sprites.len()).unwrap_or(0);
                let audio_count = audio.map(|a| a.samples.len()).unwrap_or(0);
                
                if sprite_count != last_sprites || audio_count != last_audio {
                    info!("Frame {}: Sprites: {}, Audio: {}, Progress: {:.1}%", 
                          frame, 
                          sprite_count, 
                          audio_count,
                          (progress.sprites_extracted + progress.audio_extracted) as f32 /
                          (progress.total_sprites + progress.total_audio) as f32 * 100.0);
                    
                    last_sprites = sprite_count;
                    last_audio = audio_count;
                }
                
                if progress.is_complete {
                    info!("✓ Asset extraction completed at frame {}", frame);
                    break;
                }
            }
        }
    }
    
    // Final status
    if let Some(rom_data) = test_app.get_rom_data() {
        if let Some(ref loaded_roms) = rom_data.loaded_roms {
            info!("📁 ROM File Sizes:");
            info!("  - Graphics ROM: {} bytes", loaded_roms.graphics_rom.len());
            info!("  - Code ROM: {} bytes", loaded_roms.code_rom.len());
            if let Some(ref audio_rom) = loaded_roms.audio_rom {
                info!("  - Audio ROM: {} bytes", audio_rom.len());
            }
            if let Some(ref sound_rom) = loaded_roms.sound_rom {
                info!("  - Sound ROM: {} bytes", sound_rom.len());
            }
        }
    }
    
    info!("Interactive test completed.");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_example_runs_without_panic() {
        // Test that the example can run without panicking
        // This is a basic smoke test
        let rom_directory = None; // No ROM files for CI
        let results = run_quick_rom_test(rom_directory);
        
        // Should not panic, even without ROM files
        // The test may fail, but it shouldn't crash
        println!("Quick test result: {}", results);
    }
}
