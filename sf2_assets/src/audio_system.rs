//! # Audio System Integration
//! 
//! Integrates extracted ROM audio with <PERSON><PERSON>'s audio system for SF2.

use bevy::prelude::*;
use bevy::audio::{AudioSink, AudioSinkPlayback, Volume, PlaybackMode};
use std::collections::HashMap;
use log::{info, warn};
use crate::AudioSampleManager;

/// Audio configuration for SF2
#[derive(Resource)]
pub struct SF2AudioConfig {
    pub master_volume: f32,
    pub sfx_volume: f32,
    pub music_volume: f32,
    pub positional_audio: bool,
    pub max_concurrent_sounds: usize,
}

impl Default for SF2AudioConfig {
    fn default() -> Self {
        Self {
            master_volume: 1.0,
            sfx_volume: 0.8,
            music_volume: 0.6,
            positional_audio: true,
            max_concurrent_sounds: 32,
        }
    }
}

/// Audio event system for triggering sounds
#[derive(Event)]
pub enum SF2AudioEvent {
    PlaySFX {
        sample_name: String,
        volume: Option<f32>,
        position: Option<Vec3>,
    },
    PlayMusic {
        track_name: String,
        loop_track: bool,
        fade_in_duration: Option<f32>,
    },
    StopMusic {
        fade_out_duration: Option<f32>,
    },
    SetMasterVolume(f32),
    SetSFXVolume(f32),
    SetMusicVolume(f32),
}

/// Active audio tracks manager
#[derive(Resource, Default)]
pub struct ActiveAudioTracks {
    pub sfx_sinks: HashMap<String, AudioSink>,
    pub music_sink: Option<AudioSink>,
    pub current_music_track: Option<String>,
}

/// Audio system plugin for SF2
pub struct SF2AudioSystemPlugin;

impl Plugin for SF2AudioSystemPlugin {
    fn build(&self, app: &mut App) {
        app
            .init_resource::<SF2AudioConfig>()
            .init_resource::<ActiveAudioTracks>()
            .add_event::<SF2AudioEvent>()
            .add_systems(Update, (
                handle_audio_events,
                update_audio_volumes,
                cleanup_finished_audio,
            ));
    }
}

/// System to handle audio events
fn handle_audio_events(
    mut audio_events: EventReader<SF2AudioEvent>,
    mut commands: Commands,
    audio_config: Res<SF2AudioConfig>,
    audio_manager: Res<AudioSampleManager>,
    mut active_tracks: ResMut<ActiveAudioTracks>,
    asset_server: Res<AssetServer>,
) {
    for event in audio_events.read() {
        match event {
            SF2AudioEvent::PlaySFX { sample_name, volume, position } => {
                if let Some(audio_handle) = audio_manager.get_sample(sample_name) {
                    let final_volume = volume.unwrap_or(1.0) * audio_config.sfx_volume * audio_config.master_volume;

                    let audio_player = AudioPlayer::new(audio_handle.clone());
                    let playback_settings = PlaybackSettings {
                        volume: Volume::new(final_volume),
                        ..default()
                    };

                    if let Some(pos) = position {
                        if audio_config.positional_audio {
                            // Spawn positional audio
                            commands.spawn((
                                audio_player,
                                playback_settings,
                                Transform::from_translation(*pos),
                                Visibility::default(),
                            ));
                        } else {
                            commands.spawn((audio_player, playback_settings));
                        }
                    } else {
                        commands.spawn((audio_player, playback_settings));
                    }

                    info!("Playing SFX: {} at volume {:.2}", sample_name, final_volume);
                } else {
                    warn!("Audio sample not found: {}", sample_name);
                }
            }

            SF2AudioEvent::PlayMusic { track_name, loop_track, fade_in_duration } => {
                // Stop current music if playing
                if let Some(ref mut music_sink) = active_tracks.music_sink {
                    if let Some(fade_duration) = fade_in_duration {
                        // TODO: Implement fade out
                        music_sink.stop();
                    } else {
                        music_sink.stop();
                    }
                }

                if let Some(music_handle) = audio_manager.get_sample(track_name) {
                    let final_volume = audio_config.music_volume * audio_config.master_volume;

                    let audio_player = AudioPlayer::new(music_handle.clone());
                    let playback_settings = PlaybackSettings {
                        volume: Volume::new(final_volume),
                        mode: if *loop_track {
                            PlaybackMode::Loop
                        } else {
                            PlaybackMode::Once
                        },
                        ..default()
                    };

                    let music_entity = commands.spawn((audio_player, playback_settings)).id();

                    // TODO: Get AudioSink from entity for control
                    // This requires accessing the AudioSink component after spawning
                    active_tracks.current_music_track = Some(track_name.clone());

                    info!("Playing music: {} (loop: {})", track_name, loop_track);
                } else {
                    warn!("Music track not found: {}", track_name);
                }
            }

            SF2AudioEvent::StopMusic { fade_out_duration } => {
                if let Some(ref mut music_sink) = active_tracks.music_sink {
                    if let Some(_fade_duration) = fade_out_duration {
                        // TODO: Implement fade out
                        music_sink.stop();
                    } else {
                        music_sink.stop();
                    }
                    active_tracks.current_music_track = None;
                    info!("Stopped music playback");
                }
            }

            SF2AudioEvent::SetMasterVolume(volume) => {
                // This would be handled by updating the config resource
                info!("Master volume set to: {:.2}", volume);
            }

            SF2AudioEvent::SetSFXVolume(volume) => {
                info!("SFX volume set to: {:.2}", volume);
            }

            SF2AudioEvent::SetMusicVolume(volume) => {
                info!("Music volume set to: {:.2}", volume);
            }
        }
    }
}

/// System to update audio volumes when config changes
fn update_audio_volumes(
    audio_config: Res<SF2AudioConfig>,
    mut active_tracks: ResMut<ActiveAudioTracks>,
) {
    if audio_config.is_changed() {
        // Update music volume
        if let Some(ref mut music_sink) = active_tracks.music_sink {
            let new_volume = audio_config.music_volume * audio_config.master_volume;
            music_sink.set_volume(new_volume);
        }

        // Update SFX volumes
        for (_name, sink) in &mut active_tracks.sfx_sinks {
            let new_volume = audio_config.sfx_volume * audio_config.master_volume;
            sink.set_volume(new_volume);
        }
    }
}

/// System to cleanup finished audio tracks
fn cleanup_finished_audio(
    mut active_tracks: ResMut<ActiveAudioTracks>,
) {
    // Remove finished SFX sinks
    active_tracks.sfx_sinks.retain(|name, sink| {
        if sink.empty() {
            info!("Cleaned up finished SFX: {}", name);
            false
        } else {
            true
        }
    });

    // Check if music finished (for non-looping tracks)
    if let Some(ref music_sink) = active_tracks.music_sink {
        if music_sink.empty() {
            active_tracks.music_sink = None;
            active_tracks.current_music_track = None;
            info!("Music track finished");
        }
    }
}

/// Helper functions for common audio operations
impl SF2AudioEvent {
    /// Create a simple SFX event
    pub fn play_sfx(sample_name: impl Into<String>) -> Self {
        Self::PlaySFX {
            sample_name: sample_name.into(),
            volume: None,
            position: None,
        }
    }

    /// Create a positional SFX event
    pub fn play_sfx_at(sample_name: impl Into<String>, position: Vec3) -> Self {
        Self::PlaySFX {
            sample_name: sample_name.into(),
            volume: None,
            position: Some(position),
        }
    }

    /// Create a music event
    pub fn play_music(track_name: impl Into<String>, loop_track: bool) -> Self {
        Self::PlayMusic {
            track_name: track_name.into(),
            loop_track,
            fade_in_duration: None,
        }
    }

    /// Stop music with optional fade
    pub fn stop_music() -> Self {
        Self::StopMusic {
            fade_out_duration: None,
        }
    }
}
