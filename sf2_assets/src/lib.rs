//! # SF2 Assets
//!
//! Asset loading and management system for Street Fighter II ROM data.
//!
//! This crate handles loading original ROM files, validating their integrity,
//! and converting them to Bevy-compatible asset formats.

use bevy::prelude::*;
use std::path::PathBuf;
use std::env;

pub mod rom_loader;
pub mod sprite_extractor;
pub mod audio_extractor;
pub mod validation;

pub use rom_loader::*;
pub use sprite_extractor::*;
pub use audio_extractor::*;
pub use validation::*;

/// Plugin for SF2 asset management
pub struct SF2AssetsPlugin {
    pub rom_directory: Option<PathBuf>,
}

impl Default for SF2AssetsPlugin {
    fn default() -> Self {
        Self {
            rom_directory: None,
        }
    }
}

impl SF2AssetsPlugin {
    /// Create plugin with custom ROM directory
    pub fn with_rom_directory<P: Into<PathBuf>>(rom_directory: P) -> Self {
        Self {
            rom_directory: Some(rom_directory.into()),
        }
    }
}

impl Plugin for SF2AssetsPlugin {
    fn build(&self, app: &mut App) {
        // Determine ROM directory
        let rom_dir = self.rom_directory.clone()
            .or_else(|| env::var("SF2_ROM_DIR").ok().map(PathBuf::from))
            .unwrap_or_else(|| PathBuf::from("rom_data"));

        app
            .insert_resource(RomConfig { rom_directory: rom_dir })
            .init_resource::<RomData>()
            .init_resource::<AssetExtractionProgress>()
            .init_resource::<AudioSampleManager>()
            .add_systems(Startup, load_rom_data)
            .add_systems(Update, (monitor_asset_extraction, update_extraction_progress));
    }
}

/// Configuration for ROM loading
#[derive(Resource)]
pub struct RomConfig {
    pub rom_directory: PathBuf,
}

/// Resource containing loaded ROM data
#[derive(Resource, Default)]
pub struct RomData {
    pub loaded_roms: Option<LoadedRoms>,
    pub is_validated: bool,
    pub loading_error: Option<String>,
}

/// Resource tracking asset extraction progress
#[derive(Resource, Default)]
pub struct AssetExtractionProgress {
    pub sprites_extracted: usize,
    pub total_sprites: usize,
    pub audio_extracted: usize,
    pub total_audio: usize,
    pub is_complete: bool,
    pub extraction_started: bool,
}

/// System to load ROM data on startup
fn load_rom_data(
    rom_config: Res<RomConfig>,
    mut rom_data: ResMut<RomData>,
    mut progress: ResMut<AssetExtractionProgress>,
) {
    info!("Loading ROM data from: {}", rom_config.rom_directory.display());

    // Create ROM loader
    let loader = RomLoader::new(&rom_config.rom_directory);

    // Validate ROM files exist
    match loader.validate_rom_files() {
        Ok(()) => {
            info!("ROM file validation passed");
        }
        Err(e) => {
            error!("ROM file validation failed: {}", e);
            rom_data.loading_error = Some(format!("ROM validation failed: {}", e));
            return;
        }
    }

    // Load all ROM files
    match loader.load_all_roms() {
        Ok(loaded_roms) => {
            let stats = loaded_roms.get_stats();
            info!("Successfully loaded ROM data:");
            info!("  Graphics: {} bytes", stats.graphics_size);
            info!("  Code: {} bytes", stats.code_size);
            info!("  Audio: {} bytes", stats.audio_size);
            info!("  Sound: {} bytes", stats.sound_size);
            info!("  Total: {} bytes", stats.total_size);

            // Validate ROM integrity
            match RomValidator::validate_all(&loaded_roms) {
                Ok(()) => {
                    info!("ROM integrity validation passed");
                    rom_data.is_validated = true;
                }
                Err(e) => {
                    warn!("ROM integrity validation failed: {}", e);
                    // Continue anyway for development
                    rom_data.is_validated = false;
                }
            }

            rom_data.loaded_roms = Some(loaded_roms);

            // Initialize extraction progress
            progress.total_sprites = 100; // Placeholder - actual count would come from ROM analysis
            progress.total_audio = 50;    // Placeholder - actual count would come from ROM analysis
            progress.extraction_started = true;

            info!("ROM data loading complete");
        }
        Err(e) => {
            error!("Failed to load ROM data: {}", e);
            rom_data.loading_error = Some(format!("ROM loading failed: {}", e));
        }
    }
}

/// System to monitor asset extraction progress
fn monitor_asset_extraction(progress: Res<AssetExtractionProgress>) {
    if progress.is_changed() && !progress.is_complete && progress.extraction_started {
        let sprite_progress = if progress.total_sprites > 0 {
            (progress.sprites_extracted as f32 / progress.total_sprites as f32) * 100.0
        } else {
            0.0
        };

        let audio_progress = if progress.total_audio > 0 {
            (progress.audio_extracted as f32 / progress.total_audio as f32) * 100.0
        } else {
            0.0
        };

        info!(
            "Asset extraction progress - Sprites: {:.1}% ({}/{}), Audio: {:.1}% ({}/{})",
            sprite_progress, progress.sprites_extracted, progress.total_sprites,
            audio_progress, progress.audio_extracted, progress.total_audio
        );
    }
}

/// System to update extraction progress
fn update_extraction_progress(mut progress: ResMut<AssetExtractionProgress>) {
    if progress.extraction_started && !progress.is_complete {
        // Check if extraction is complete
        if progress.sprites_extracted >= progress.total_sprites &&
           progress.audio_extracted >= progress.total_audio {
            progress.is_complete = true;
            info!("Asset extraction complete!");
        }
    }
}
