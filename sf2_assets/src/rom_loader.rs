//! # ROM Loader
//!
//! Handles loading and validation of Street Fighter II ROM files.
//!
//! This module implements comprehensive ROM loading for SF2 World Warriors,
//! supporting both individual ROM files and combined ROM formats.
//! It handles graphics ROMs, code ROMs, audio ROMs, and provides validation.

use std::path::{Path, PathBuf};
use std::fs;
use thiserror::Error;
use log::{info, warn, error, debug};
use serde::{Serialize, Deserialize};

/// Errors that can occur during ROM loading
#[derive(Error, Debug)]
pub enum RomLoadError {
    #[error("File not found: {path}")]
    FileNotFound { path: String },

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Invalid ROM size: expected {expected}, got {actual}")]
    InvalidSize { expected: usize, actual: usize },

    #[error("Invalid ROM checksum: expected {expected:08x}, got {actual:08x}")]
    InvalidChecksum { expected: u32, actual: u32 },

    #[error("Unsupported ROM version: {version}")]
    UnsupportedVersion { version: String },

    #[error("ROM validation failed: {reason}")]
    ValidationFailed { reason: String },

    #[error("Missing required ROM files: {files:?}")]
    MissingRomFiles { files: Vec<String> },
}

/// Information about a specific ROM file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RomInfo {
    pub name: String,
    pub size: usize,
    pub checksum: Option<u32>, // Optional for development
    pub version: String,
    pub rom_type: RomType,
}

/// Type of ROM file
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RomType {
    Graphics,
    Code,
    Audio,
    Program,
    Sound,
}

/// SF2 ROM file definitions based on MAME ROM set
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SF2RomSet {
    /// Graphics ROMs (sprites and tiles)
    pub graphics_roms: Vec<RomInfo>,
    /// Code/Program ROMs
    pub code_roms: Vec<RomInfo>,
    /// Audio sample ROMs
    pub audio_roms: Vec<RomInfo>,
    /// Sound program ROMs
    pub sound_roms: Vec<RomInfo>,
}

impl SF2RomSet {
    /// Get SF2 World Warriors ROM set definition
    pub fn sf2_world_warriors() -> Self {
        Self {
            graphics_roms: vec![
                // Graphics ROMs - 512KB each, total 6MB
                RomInfo { name: "sf2-1m.3a".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-2m.3c".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-3m.5a".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-4m.5c".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-5m.4a".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-6m.4c".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-7m.6a".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-8m.6c".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-9m.3d".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-11m.5d".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-13m.4d".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
                RomInfo { name: "sf2-15m.6d".to_string(), size: 524288, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Graphics },
            ],
            code_roms: vec![
                // Program ROM
                RomInfo { name: "sf2_9.12a".to_string(), size: 65536, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                // Additional program ROMs
                RomInfo { name: "sf2_29b.10e".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                RomInfo { name: "sf2_36b.10f".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                RomInfo { name: "sf2e_28g.9e".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                RomInfo { name: "sf2e_30g.11e".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                RomInfo { name: "sf2e_31g.12e".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                RomInfo { name: "sf2e_35g.9f".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                RomInfo { name: "sf2e_37g.11f".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
                RomInfo { name: "sf2e_38g.12f".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Code },
            ],
            audio_roms: vec![
                // Audio sample ROMs
                RomInfo { name: "sf2_18.11c".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Audio },
                RomInfo { name: "sf2_19.12c".to_string(), size: 131072, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Audio },
            ],
            sound_roms: vec![
                // Sound program ROMs (small files)
                RomInfo { name: "buf1".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
                RomInfo { name: "c632.ic1".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
                RomInfo { name: "ioa1".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
                RomInfo { name: "iob1.11d".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
                RomInfo { name: "prg1".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
                RomInfo { name: "rom1".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
                RomInfo { name: "sou1".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
                RomInfo { name: "stf29.1a".to_string(), size: 279, checksum: None, version: "World Warriors".to_string(), rom_type: RomType::Sound },
            ],
        }
    }
}

/// ROM loader for Street Fighter II
pub struct RomLoader {
    rom_directory: PathBuf,
    rom_set: SF2RomSet,
}

impl RomLoader {
    /// Create a new ROM loader
    pub fn new<P: AsRef<Path>>(rom_directory: P) -> Self {
        Self {
            rom_directory: rom_directory.as_ref().to_path_buf(),
            rom_set: SF2RomSet::sf2_world_warriors(),
        }
    }

    /// Create ROM loader with custom ROM set
    pub fn with_rom_set<P: AsRef<Path>>(rom_directory: P, rom_set: SF2RomSet) -> Self {
        Self {
            rom_directory: rom_directory.as_ref().to_path_buf(),
            rom_set,
        }
    }
    
    /// Load all ROM files
    pub fn load_all_roms(&self) -> Result<LoadedRoms, RomLoadError> {
        info!("Loading ROM files from: {}", self.rom_directory.display());

        // Load graphics ROMs
        let mut graphics_data = Vec::new();
        for rom_info in &self.rom_set.graphics_roms {
            let data = self.load_rom_file(rom_info)?;
            graphics_data.extend(data);
        }
        info!("Loaded {} graphics ROMs, total size: {} bytes",
              self.rom_set.graphics_roms.len(), graphics_data.len());

        // Load code ROMs
        let mut code_data = Vec::new();
        for rom_info in &self.rom_set.code_roms {
            let data = self.load_rom_file(rom_info)?;
            code_data.extend(data);
        }
        info!("Loaded {} code ROMs, total size: {} bytes",
              self.rom_set.code_roms.len(), code_data.len());

        // Load audio ROMs
        let mut audio_data = Vec::new();
        for rom_info in &self.rom_set.audio_roms {
            let data = self.load_rom_file(rom_info)?;
            audio_data.extend(data);
        }
        info!("Loaded {} audio ROMs, total size: {} bytes",
              self.rom_set.audio_roms.len(), audio_data.len());

        // Load sound ROMs
        let mut sound_data = Vec::new();
        for rom_info in &self.rom_set.sound_roms {
            let data = self.load_rom_file(rom_info)?;
            sound_data.extend(data);
        }
        info!("Loaded {} sound ROMs, total size: {} bytes",
              self.rom_set.sound_roms.len(), sound_data.len());

        info!("Successfully loaded all ROM files");

        Ok(LoadedRoms {
            graphics_rom: graphics_data,
            code_rom: code_data,
            audio_rom: if audio_data.is_empty() { None } else { Some(audio_data) },
            sound_rom: if sound_data.is_empty() { None } else { Some(sound_data) },
        })
    }
    
    /// Load a specific ROM file
    fn load_rom_file(&self, rom_info: &RomInfo) -> Result<Vec<u8>, RomLoadError> {
        let file_path = self.rom_directory.join(&rom_info.name);

        debug!("Loading ROM file: {}", file_path.display());

        if !file_path.exists() {
            return Err(RomLoadError::FileNotFound {
                path: file_path.to_string_lossy().to_string(),
            });
        }

        let data = fs::read(&file_path)?;

        // Validate file size
        if data.len() != rom_info.size {
            warn!(
                "ROM file {} has unexpected size: {} (expected {})",
                rom_info.name, data.len(), rom_info.size
            );
            // Don't fail on size mismatch for now, just warn
        }

        // Calculate and validate checksum if provided
        if let Some(expected_checksum) = rom_info.checksum {
            let checksum = calculate_checksum(&data);
            if checksum != expected_checksum {
                warn!(
                    "ROM file {} has unexpected checksum: {:08x} (expected {:08x})",
                    rom_info.name, checksum, expected_checksum
                );
                // Don't fail on checksum mismatch for now, just warn
                // This allows for different ROM versions during development
            }
        }

        debug!(
            "Loaded ROM file: {} ({} bytes)",
            rom_info.name, data.len()
        );

        Ok(data)
    }

    /// Check if all required ROM files exist
    pub fn validate_rom_files(&self) -> Result<(), RomLoadError> {
        let mut missing_files = Vec::new();

        // Check all ROM files
        for rom_info in self.rom_set.graphics_roms.iter()
            .chain(self.rom_set.code_roms.iter())
            .chain(self.rom_set.audio_roms.iter())
            .chain(self.rom_set.sound_roms.iter()) {

            let file_path = self.rom_directory.join(&rom_info.name);
            if !file_path.exists() {
                missing_files.push(rom_info.name.clone());
            }
        }

        if !missing_files.is_empty() {
            return Err(RomLoadError::MissingRomFiles { files: missing_files });
        }

        info!("All required ROM files found");
        Ok(())
    }
    
    /// Validate ROM integrity
    pub fn validate_roms(&self, roms: &LoadedRoms) -> Result<(), RomLoadError> {
        info!("Validating ROM integrity...");
        
        // Basic validation - check for known patterns or headers
        if !self.validate_graphics_rom(&roms.graphics_rom) {
            error!("Graphics ROM validation failed");
            return Err(RomLoadError::UnsupportedVersion {
                version: "Unknown graphics ROM".to_string(),
            });
        }
        
        if !self.validate_code_rom(&roms.code_rom) {
            error!("Code ROM validation failed");
            return Err(RomLoadError::UnsupportedVersion {
                version: "Unknown code ROM".to_string(),
            });
        }
        
        info!("ROM validation successful");
        Ok(())
    }
    
    /// Validate graphics ROM by checking for known patterns
    fn validate_graphics_rom(&self, data: &[u8]) -> bool {
        // Check for Capcom signature or known tile patterns
        // This is a placeholder - actual validation would check for specific patterns
        data.len() > 1024 && !data.iter().all(|&b| b == 0)
    }
    
    /// Validate code ROM by checking for M68k code patterns
    fn validate_code_rom(&self, data: &[u8]) -> bool {
        // Check for M68k reset vector or known code patterns
        // This is a placeholder - actual validation would check for specific patterns
        data.len() > 1024 && !data.iter().all(|&b| b == 0)
    }
}

/// Container for all loaded ROM data
#[derive(Debug, Clone)]
pub struct LoadedRoms {
    pub graphics_rom: Vec<u8>,
    pub code_rom: Vec<u8>,
    pub audio_rom: Option<Vec<u8>>,
    pub sound_rom: Option<Vec<u8>>,
}

impl LoadedRoms {
    /// Get total size of all loaded ROMs
    pub fn total_size(&self) -> usize {
        self.graphics_rom.len()
            + self.code_rom.len()
            + self.audio_rom.as_ref().map_or(0, |data| data.len())
            + self.sound_rom.as_ref().map_or(0, |data| data.len())
    }

    /// Get ROM statistics
    pub fn get_stats(&self) -> RomStats {
        RomStats {
            graphics_size: self.graphics_rom.len(),
            code_size: self.code_rom.len(),
            audio_size: self.audio_rom.as_ref().map_or(0, |data| data.len()),
            sound_size: self.sound_rom.as_ref().map_or(0, |data| data.len()),
            total_size: self.total_size(),
        }
    }
}

/// ROM loading statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RomStats {
    pub graphics_size: usize,
    pub code_size: usize,
    pub audio_size: usize,
    pub sound_size: usize,
    pub total_size: usize,
}

/// Calculate a simple checksum for ROM validation
fn calculate_checksum(data: &[u8]) -> u32 {
    // Simple CRC32-like checksum
    let mut checksum = 0u32;
    for &byte in data {
        checksum = checksum.wrapping_mul(31).wrapping_add(byte as u32);
    }
    checksum
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::io::Write;
    use tempfile::TempDir;
    
    #[test]
    fn test_checksum_calculation() {
        let data = b"Hello, World!";
        let checksum1 = calculate_checksum(data);
        let checksum2 = calculate_checksum(data);
        assert_eq!(checksum1, checksum2);
        
        let different_data = b"Hello, World?";
        let checksum3 = calculate_checksum(different_data);
        assert_ne!(checksum1, checksum3);
    }
    
    #[test]
    fn test_rom_loader_file_not_found() {
        let temp_dir = TempDir::new().unwrap();
        let loader = RomLoader::new(temp_dir.path());
        
        let result = loader.load_all_roms();
        assert!(matches!(result, Err(RomLoadError::FileNotFound { .. })));
    }
}
