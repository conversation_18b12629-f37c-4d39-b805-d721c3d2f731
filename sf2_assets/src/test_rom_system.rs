//! # ROM System Testing
//! 
//! Comprehensive tests for ROM loading and asset extraction systems.

use bevy::prelude::*;
use std::path::PathBuf;
use log::{info, warn, error};
use crate::{
    SF2AssetsPlugin, SF2AudioEvent, SF2AudioSystemPlugin,
    RomData, AssetExtractionProgress, ExtractedSprites, ExtractedAudio,
    AudioSampleManager, SF2AudioConfig,
};

/// Test application for ROM loading and asset extraction
pub struct RomTestApp {
    app: App,
}

impl RomTestApp {
    /// Create a new test application
    pub fn new(rom_directory: Option<PathBuf>) -> Self {
        let mut app = App::new();
        
        // Add minimal plugins for testing
        app.add_plugins((
            MinimalPlugins,
            AssetPlugin::default(),
        ));

        // Add SF2 asset plugins
        if let Some(rom_dir) = rom_directory {
            app.add_plugins(SF2AssetsPlugin::with_rom_directory(rom_dir));
        } else {
            app.add_plugins(SF2AssetsPlugin::default());
        }

        Self { app }
    }

    /// Update the test application for one frame
    pub fn update(&mut self) {
        self.app.update();
    }

    /// Run the test application for a specified number of frames
    pub fn run_test_frames(&mut self, frames: usize) -> TestResults {
        let mut results = TestResults::default();
        
        info!("Starting ROM system test for {} frames", frames);
        
        for frame in 0..frames {
            self.app.update();
            
            // Check ROM loading status
            if let Some(rom_data) = self.app.world().get_resource::<RomData>() {
                if rom_data.loaded_roms.is_some() && !results.rom_loaded {
                    results.rom_loaded = true;
                    results.rom_load_frame = Some(frame);
                    info!("ROM loaded successfully at frame {}", frame);
                }
                
                if let Some(ref error) = rom_data.loading_error {
                    results.rom_error = Some(error.clone());
                    error!("ROM loading error: {}", error);
                }
            }
            
            // Check asset extraction progress
            if let Some(progress) = self.app.world().get_resource::<AssetExtractionProgress>() {
                results.sprites_extracted = progress.sprites_extracted;
                results.audio_extracted = progress.audio_extracted;
                results.total_sprites = progress.total_sprites;
                results.total_audio = progress.total_audio;
                
                if progress.is_complete && !results.extraction_complete {
                    results.extraction_complete = true;
                    results.extraction_complete_frame = Some(frame);
                    info!("Asset extraction completed at frame {}", frame);
                }
            }
            
            // Check extracted assets
            if let Some(sprites) = self.app.world().get_resource::<ExtractedSprites>() {
                results.sprite_count = sprites.sprites.len();
                results.palette_count = sprites.palettes.len();
            }
            
            if let Some(audio) = self.app.world().get_resource::<ExtractedAudio>() {
                results.audio_sample_count = audio.samples.len();
            }
            
            // Test audio system if extraction is complete
            if results.extraction_complete && !results.audio_test_performed {
                self.test_audio_system(&mut results);
            }
        }
        
        info!("ROM system test completed");
        results.print_summary();
        results
    }

    /// Test the audio system functionality
    fn test_audio_system(&mut self, results: &mut TestResults) {
        info!("Testing audio system functionality");

        // Send test audio events
        if let Some(mut audio_events) = self.app.world_mut().get_resource_mut::<Events<SF2AudioEvent>>() {
            audio_events.send(SF2AudioEvent::play_sfx("test_sound"));
            audio_events.send(SF2AudioEvent::play_music("test_music", true));
            audio_events.send(SF2AudioEvent::SetMasterVolume(0.8));

            results.audio_test_performed = true;
            info!("Audio test events sent successfully");
        } else {
            warn!("Audio event system not available");
        }
    }

    /// Get the current ROM data
    pub fn get_rom_data(&self) -> Option<&RomData> {
        self.app.world().get_resource::<RomData>()
    }

    /// Get the current asset extraction progress
    pub fn get_extraction_progress(&self) -> Option<&AssetExtractionProgress> {
        self.app.world().get_resource::<AssetExtractionProgress>()
    }

    /// Get extracted sprites
    pub fn get_extracted_sprites(&self) -> Option<&ExtractedSprites> {
        self.app.world().get_resource::<ExtractedSprites>()
    }

    /// Get extracted audio
    pub fn get_extracted_audio(&self) -> Option<&ExtractedAudio> {
        self.app.world().get_resource::<ExtractedAudio>()
    }
}

/// Test results for ROM system testing
#[derive(Debug, Default)]
pub struct TestResults {
    pub rom_loaded: bool,
    pub rom_load_frame: Option<usize>,
    pub rom_error: Option<String>,
    pub extraction_complete: bool,
    pub extraction_complete_frame: Option<usize>,
    pub sprites_extracted: usize,
    pub audio_extracted: usize,
    pub total_sprites: usize,
    pub total_audio: usize,
    pub sprite_count: usize,
    pub palette_count: usize,
    pub audio_sample_count: usize,
    pub audio_test_performed: bool,
}

impl TestResults {
    /// Print a summary of the test results
    pub fn print_summary(&self) {
        info!("=== ROM System Test Results ===");
        info!("ROM Loaded: {}", self.rom_loaded);
        if let Some(frame) = self.rom_load_frame {
            info!("ROM Load Frame: {}", frame);
        }
        if let Some(ref error) = self.rom_error {
            error!("ROM Error: {}", error);
        }
        
        info!("Extraction Complete: {}", self.extraction_complete);
        if let Some(frame) = self.extraction_complete_frame {
            info!("Extraction Complete Frame: {}", frame);
        }
        
        info!("Sprites: {}/{} extracted, {} total sprites, {} palettes", 
              self.sprites_extracted, self.total_sprites, 
              self.sprite_count, self.palette_count);
        
        info!("Audio: {}/{} extracted, {} total samples", 
              self.audio_extracted, self.total_audio, self.audio_sample_count);
        
        info!("Audio Test Performed: {}", self.audio_test_performed);
        info!("===============================");
    }

    /// Check if the test was successful
    pub fn is_successful(&self) -> bool {
        self.rom_loaded && 
        self.rom_error.is_none() && 
        self.extraction_complete &&
        self.sprite_count > 0 &&
        self.palette_count > 0 &&
        self.audio_sample_count > 0
    }
}

/// Run a comprehensive ROM system test
pub fn run_rom_system_test(rom_directory: Option<PathBuf>) -> TestResults {
    info!("Starting comprehensive ROM system test");
    
    let mut test_app = RomTestApp::new(rom_directory);
    let results = test_app.run_test_frames(300); // Run for 5 seconds at 60 FPS
    
    if results.is_successful() {
        info!("ROM system test PASSED");
    } else {
        warn!("ROM system test FAILED");
    }
    
    results
}

/// Quick ROM validation test
pub fn run_quick_rom_test(rom_directory: Option<PathBuf>) -> bool {
    info!("Running quick ROM validation test");
    
    let mut test_app = RomTestApp::new(rom_directory);
    let results = test_app.run_test_frames(60); // Run for 1 second
    
    let success = results.rom_loaded && results.rom_error.is_none();
    
    if success {
        info!("Quick ROM test PASSED");
    } else {
        warn!("Quick ROM test FAILED");
    }
    
    success
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;

    #[test]
    fn test_rom_system_initialization() {
        // Test that the ROM system initializes without crashing
        let test_app = RomTestApp::new(None);
        
        // Check that required resources are initialized
        assert!(test_app.app.world().get_resource::<RomData>().is_some());
        assert!(test_app.app.world().get_resource::<AssetExtractionProgress>().is_some());
        assert!(test_app.app.world().get_resource::<ExtractedSprites>().is_some());
        assert!(test_app.app.world().get_resource::<ExtractedAudio>().is_some());
        assert!(test_app.app.world().get_resource::<AudioSampleManager>().is_some());
        assert!(test_app.app.world().get_resource::<SF2AudioConfig>().is_some());
    }

    #[test]
    fn test_audio_event_system() {
        let mut test_app = RomTestApp::new(None);
        
        // Run a few frames to initialize systems
        for _ in 0..10 {
            test_app.app.update();
        }
        
        // Check that audio event system is working
        if let Some(mut audio_events) = test_app.app.world_mut().get_resource_mut::<Events<SF2AudioEvent>>() {
            audio_events.send(SF2AudioEvent::play_sfx("test"));
            // If we get here without panicking, the event system is working
        }
    }

    #[test]
    #[ignore] // Only run when ROM files are available
    fn test_with_real_rom_files() {
        if let Ok(rom_dir) = env::var("SF2_ROM_DIR") {
            let results = run_quick_rom_test(Some(PathBuf::from(rom_dir)));
            assert!(results, "ROM loading should succeed with valid ROM files");
        }
    }
}
