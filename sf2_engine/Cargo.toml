[package]
name = "sf2_engine"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
description = "Core Street Fighter II game engine"

[dependencies]
bevy.workspace = true
sf2_types = { path = "../sf2_types" }
sf2_assets = { path = "../sf2_assets" }
glam.workspace = true
thiserror.workspace = true
anyhow.workspace = true
log.workspace = true
serde.workspace = true
fastrand.workspace = true
serde_json.workspace = true

[dev-dependencies]
criterion.workspace = true
