// AI Behavior Tree Implementation
// High-fidelity port of the original C99 _AIBeginStrategy system with behavior trees

use bevy::prelude::*;
use sf2_types::*;
use crate::ai::ai_core::*;
use crate::ai::ai_strategy::*;
use crate::ai::ai_difficulty::*;
use crate::ai::ai_threat::*;
use crate::Fighter;

/// Behavior tree node types matching C99 AI decision structure
#[derive(Debu<PERSON>, Clone, PartialEq)]
pub enum BehaviorNodeType {
    /// Root node - entry point for AI decision making
    Root,
    /// Selector node - tries children until one succeeds (OR logic)
    Selector,
    /// Sequence node - executes children in order until one fails (AND logic)
    Sequence,
    /// Condition node - evaluates game state conditions
    Condition(AICondition),
    /// Action node - executes specific AI actions
    Action(AIAction),
    /// Strategy node - executes bytecode strategies
    Strategy(AIStrategyType),
    /// Timer node - waits for specified duration
    Timer(u16),
    /// Parallel node - executes multiple children simultaneously
    Parallel,
}

/// AI conditions for behavior tree evaluation
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
pub enum AICondition {
    /// Check if opponent is within attack range
    OpponentInRange(u16),
    /// Check if AI should be aggressive
    ShouldBeAggressive,
    /// Check if AI should be defensive
    ShouldBeDefensive,
    /// Check if opponent is jumping
    OpponentJumping,
    /// Check if opponent is attacking
    OpponentAttacking,
    /// Check if AI has projectile advantage
    HasProjectileAdvantage,
    /// Check if AI is cornered
    IsCornered,
    /// Check if timer has expired
    TimerExpired,
    /// Check difficulty-based random condition
    DifficultyCheck(u8),
    /// Check if AI should go aggressive (matching _AIShouldGoAggressive)
    ShouldGoAggressive,
    /// Check if AI has reached walk target
    HasReachedWalkTarget,
    /// Check if opponent is in block stun
    OpponentInBlockStun,
    /// Check if AI should throw
    ShouldThrow,
}

/// AI actions for behavior tree execution
#[derive(Debug, Clone, PartialEq)]
pub enum AIAction {
    /// Stand still and wait
    StandStill,
    /// Walk towards opponent
    WalkForward,
    /// Walk away from opponent
    WalkBackward,
    /// Jump towards opponent
    JumpForward,
    /// Jump away from opponent
    JumpBackward,
    /// Jump straight up
    JumpUp,
    /// Execute attack
    Attack(AttackType),
    /// Execute special move
    SpecialMove(SpecialMoveId),
    /// Block incoming attacks
    Block,
    /// Throw opponent
    Throw,
    /// Set AI mode
    SetMode(AIMode1, AIMode2),
    /// Reset AI state
    ResetState,
    /// Go aggressive
    GoAggressive,
    /// Go defensive
    GoDefensive,
    /// Restart strategy
    RestartStrategy,
    /// End current strategy
    EndStrategy,
}

/// Behavior tree node structure
#[derive(Debug, Clone)]
pub struct BehaviorNode {
    pub node_type: BehaviorNodeType,
    pub children: Vec<BehaviorNode>,
    pub name: String,
    pub priority: u8,
}

/// Behavior tree execution result
#[derive(Debug, Clone, PartialEq)]
pub enum BehaviorResult {
    /// Node execution succeeded
    Success,
    /// Node execution failed
    Failure,
    /// Node is still running (for async operations)
    Running,
}

/// AI Behavior Tree Component - manages behavior tree execution
#[derive(Component, Debug)]
pub struct AIBehaviorTree {
    /// Root node of the behavior tree
    pub root: BehaviorNode,
    /// Current execution context
    pub execution_context: BehaviorExecutionContext,
    /// Tree execution statistics
    pub stats: BehaviorTreeStats,
    /// Character-specific behavior modifications
    pub character_modifiers: CharacterBehaviorModifiers,
}

/// Behavior tree execution context
#[derive(Debug, Clone)]
pub struct BehaviorExecutionContext {
    /// Currently executing node path
    pub current_path: Vec<usize>,
    /// Execution stack for recursive calls
    pub execution_stack: Vec<BehaviorStackFrame>,
    /// Current frame counter
    pub frame_counter: u64,
    /// Last execution result
    pub last_result: BehaviorResult,
    /// Blackboard for sharing data between nodes
    pub blackboard: BehaviorBlackboard,
}

/// Stack frame for behavior tree execution
#[derive(Debug, Clone)]
pub struct BehaviorStackFrame {
    pub node_index: usize,
    pub child_index: usize,
    pub execution_state: BehaviorExecutionState,
    pub entry_frame: u64,
}

/// Execution state for behavior nodes
#[derive(Debug, Clone, PartialEq)]
pub enum BehaviorExecutionState {
    /// Node is ready to execute
    Ready,
    /// Node is currently executing
    Executing,
    /// Node execution completed
    Completed,
    /// Node execution was interrupted
    Interrupted,
}

/// Blackboard for sharing data between behavior tree nodes
#[derive(Debug, Clone, Default)]
pub struct BehaviorBlackboard {
    /// Opponent distance
    pub opponent_distance: f32,
    /// Opponent direction
    pub opponent_direction: i8,
    /// Current threat level
    pub threat_level: u8,
    /// Last attack time
    pub last_attack_frame: u64,
    /// Walk target position
    pub walk_target: i16,
    /// Strategy parameters
    pub strategy_params: [u8; 4],
    /// Timer values
    pub timers: [u16; 8],
    /// Flags for various AI states
    pub flags: BehaviorFlags,
}

/// Behavior tree flags
#[derive(Debug, Clone, Default)]
pub struct BehaviorFlags {
    pub should_attack: bool,
    pub should_jump: bool,
    pub should_block: bool,
    pub should_throw: bool,
    pub force_defensive: bool,
    pub aggressive_mode: bool,
    pub wall_bounce: bool,
    pub collision_disabled: bool,
}

/// Behavior tree execution statistics
#[derive(Debug, Clone, Default)]
pub struct BehaviorTreeStats {
    pub total_executions: u64,
    pub successful_executions: u64,
    pub failed_executions: u64,
    pub average_execution_time: f32,
    pub max_recursion_depth: u8,
    pub current_recursion_depth: u8,
}

/// Character-specific behavior modifications
#[derive(Debug, Clone)]
pub struct CharacterBehaviorModifiers {
    pub character_id: FighterId,
    pub aggression_modifier: f32,
    pub defense_modifier: f32,
    pub reaction_time_modifier: f32,
    pub special_move_frequency: f32,
    pub throw_frequency: f32,
}

impl Default for CharacterBehaviorModifiers {
    fn default() -> Self {
        Self {
            character_id: FighterId::Ryu,
            aggression_modifier: 1.0,
            defense_modifier: 1.0,
            reaction_time_modifier: 1.0,
            special_move_frequency: 0.1,
            throw_frequency: 0.05,
        }
    }
}

impl AIBehaviorTree {
    /// Create new behavior tree for character
    pub fn new(character_id: FighterId) -> Self {
        let root = Self::create_default_behavior_tree(character_id);
        let character_modifiers = Self::create_character_modifiers(character_id);
        
        Self {
            root,
            execution_context: BehaviorExecutionContext::new(),
            stats: BehaviorTreeStats::default(),
            character_modifiers,
        }
    }
    
    /// Create default behavior tree structure matching C99 AI logic
    fn create_default_behavior_tree(character_id: FighterId) -> BehaviorNode {
        BehaviorNode {
            node_type: BehaviorNodeType::Root,
            name: format!("AI_Root_{:?}", character_id),
            priority: 0,
            children: vec![
                // Main AI decision selector
                BehaviorNode {
                    node_type: BehaviorNodeType::Selector,
                    name: "MainDecisionSelector".to_string(),
                    priority: 10,
                    children: vec![
                        // Defensive behavior sequence
                        Self::create_defensive_sequence(),
                        // Aggressive behavior sequence  
                        Self::create_aggressive_sequence(),
                        // Default behavior sequence
                        Self::create_default_sequence(),
                    ],
                },
            ],
        }
    }
    
    /// Create defensive behavior sequence (matching AIBeginDef)
    fn create_defensive_sequence() -> BehaviorNode {
        BehaviorNode {
            node_type: BehaviorNodeType::Sequence,
            name: "DefensiveSequence".to_string(),
            priority: 20,
            children: vec![
                BehaviorNode {
                    node_type: BehaviorNodeType::Condition(AICondition::ShouldBeDefensive),
                    name: "CheckDefensive".to_string(),
                    priority: 0,
                    children: vec![],
                },
                BehaviorNode {
                    node_type: BehaviorNodeType::Strategy(AIStrategyType::Block),
                    name: "ExecuteDefensiveStrategy".to_string(),
                    priority: 0,
                    children: vec![],
                },
            ],
        }
    }
    
    /// Create aggressive behavior sequence (matching AIBeginAgg0/1)
    fn create_aggressive_sequence() -> BehaviorNode {
        BehaviorNode {
            node_type: BehaviorNodeType::Sequence,
            name: "AggressiveSequence".to_string(),
            priority: 15,
            children: vec![
                BehaviorNode {
                    node_type: BehaviorNodeType::Condition(AICondition::ShouldBeAggressive),
                    name: "CheckAggressive".to_string(),
                    priority: 0,
                    children: vec![],
                },
                BehaviorNode {
                    node_type: BehaviorNodeType::Selector,
                    name: "AggressiveSelector".to_string(),
                    priority: 0,
                    children: vec![
                        BehaviorNode {
                            node_type: BehaviorNodeType::Strategy(AIStrategyType::Special),
                            name: "ExecuteEnhancedAggressive".to_string(),
                            priority: 0,
                            children: vec![],
                        },
                        BehaviorNode {
                            node_type: BehaviorNodeType::Strategy(AIStrategyType::Attack),
                            name: "ExecuteBasicAggressive".to_string(),
                            priority: 0,
                            children: vec![],
                        },
                    ],
                },
            ],
        }
    }
    
    /// Create default behavior sequence
    fn create_default_sequence() -> BehaviorNode {
        BehaviorNode {
            node_type: BehaviorNodeType::Sequence,
            name: "DefaultSequence".to_string(),
            priority: 5,
            children: vec![
                BehaviorNode {
                    node_type: BehaviorNodeType::Action(AIAction::StandStill),
                    name: "DefaultStandStill".to_string(),
                    priority: 0,
                    children: vec![],
                },
            ],
        }
    }
    
    /// Create character-specific behavior modifiers
    fn create_character_modifiers(character_id: FighterId) -> CharacterBehaviorModifiers {
        match character_id {
            FighterId::Ryu => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 1.0,
                defense_modifier: 1.0,
                reaction_time_modifier: 1.0,
                special_move_frequency: 0.15,
                throw_frequency: 0.05,
            },
            FighterId::Ken => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 1.2,
                defense_modifier: 0.9,
                reaction_time_modifier: 0.95,
                special_move_frequency: 0.18,
                throw_frequency: 0.08,
            },
            FighterId::ChunLi => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 0.9,
                defense_modifier: 1.1,
                reaction_time_modifier: 0.9,
                special_move_frequency: 0.12,
                throw_frequency: 0.03,
            },
            FighterId::Guile => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 0.8,
                defense_modifier: 1.3,
                reaction_time_modifier: 1.1,
                special_move_frequency: 0.2,
                throw_frequency: 0.02,
            },
            FighterId::Blanka => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 1.3,
                defense_modifier: 0.8,
                reaction_time_modifier: 0.85,
                special_move_frequency: 0.16,
                throw_frequency: 0.06,
            },
            FighterId::EHonda => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 1.1,
                defense_modifier: 1.2,
                reaction_time_modifier: 1.05,
                special_move_frequency: 0.14,
                throw_frequency: 0.1,
            },
            FighterId::Zangief => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 1.4,
                defense_modifier: 1.0,
                reaction_time_modifier: 1.2,
                special_move_frequency: 0.1,
                throw_frequency: 0.15,
            },
            FighterId::Dhalsim => CharacterBehaviorModifiers {
                character_id,
                aggression_modifier: 0.7,
                defense_modifier: 1.4,
                reaction_time_modifier: 0.8,
                special_move_frequency: 0.25,
                throw_frequency: 0.01,
            },
            _ => CharacterBehaviorModifiers::default(),
        }
    }
}

impl BehaviorExecutionContext {
    /// Create new execution context
    pub fn new() -> Self {
        Self {
            current_path: Vec::new(),
            execution_stack: Vec::new(),
            frame_counter: 0,
            last_result: BehaviorResult::Success,
            blackboard: BehaviorBlackboard::default(),
        }
    }
    
    /// Push new stack frame for recursive execution
    pub fn push_frame(&mut self, node_index: usize) {
        if self.execution_stack.len() >= 20 {
            warn!("AI Behavior tree recursion depth exceeded 20!");
            return;
        }
        
        let frame = BehaviorStackFrame {
            node_index,
            child_index: 0,
            execution_state: BehaviorExecutionState::Ready,
            entry_frame: self.frame_counter,
        };
        
        self.execution_stack.push(frame);
    }
    
    /// Pop current stack frame
    pub fn pop_frame(&mut self) -> Option<BehaviorStackFrame> {
        self.execution_stack.pop()
    }
    
    /// Get current stack depth
    pub fn stack_depth(&self) -> usize {
        self.execution_stack.len()
    }
    
    /// Update frame counter
    pub fn advance_frame(&mut self) {
        self.frame_counter += 1;
    }
}

/// AI Behavior Tree Executor - handles behavior tree execution and integration
#[derive(Component, Debug)]
pub struct AIBehaviorTreeExecutor {
    /// Current execution state
    pub execution_state: BehaviorTreeExecutionState,
    /// Strategy integration
    pub strategy_integration: StrategyIntegration,
    /// Performance metrics
    pub performance_metrics: BehaviorTreePerformance,
}

/// Behavior tree execution state
#[derive(Debug, Clone, PartialEq)]
pub enum BehaviorTreeExecutionState {
    /// Tree is idle, waiting for next execution
    Idle,
    /// Tree is currently executing
    Executing,
    /// Tree execution completed successfully
    Completed,
    /// Tree execution failed
    Failed,
    /// Tree execution was interrupted
    Interrupted,
}

/// Strategy integration for behavior trees
#[derive(Debug, Clone)]
pub struct StrategyIntegration {
    /// Current strategy being executed
    pub current_strategy: Option<AIStrategyType>,
    /// Strategy execution context
    pub strategy_context: StrategyExecutionContext,
    /// Bytecode interpreter state
    pub bytecode_state: BytecodeInterpreterState,
}

/// Strategy execution context
#[derive(Debug, Clone)]
pub struct StrategyExecutionContext {
    /// Strategy parameters
    pub parameters: [u8; 4],
    /// Execution frame
    pub execution_frame: u64,
    /// Strategy timer
    pub timer: u16,
    /// Strategy flags
    pub flags: StrategyFlags,
}

/// Strategy execution flags
#[derive(Debug, Clone, Default)]
pub struct StrategyFlags {
    pub is_recursive: bool,
    pub is_interrupted: bool,
    pub should_restart: bool,
    pub should_end: bool,
}

/// Bytecode interpreter state
#[derive(Debug, Clone)]
pub struct BytecodeInterpreterState {
    /// Current instruction pointer
    pub instruction_pointer: usize,
    /// Bytecode stack
    pub bytecode_stack: Vec<u8>,
    /// Interpreter flags
    pub interpreter_flags: InterpreterFlags,
}

/// Bytecode interpreter flags
#[derive(Debug, Clone, Default)]
pub struct InterpreterFlags {
    pub is_executing: bool,
    pub has_error: bool,
    pub should_halt: bool,
}

/// Behavior tree performance metrics
#[derive(Debug, Clone, Default)]
pub struct BehaviorTreePerformance {
    pub execution_time_ms: f32,
    pub nodes_evaluated: u32,
    pub conditions_checked: u32,
    pub actions_executed: u32,
    pub strategies_invoked: u32,
}

impl Default for StrategyIntegration {
    fn default() -> Self {
        Self {
            current_strategy: None,
            strategy_context: StrategyExecutionContext::default(),
            bytecode_state: BytecodeInterpreterState::default(),
        }
    }
}

impl Default for StrategyExecutionContext {
    fn default() -> Self {
        Self {
            parameters: [0; 4],
            execution_frame: 0,
            timer: 0,
            flags: StrategyFlags::default(),
        }
    }
}

impl Default for BytecodeInterpreterState {
    fn default() -> Self {
        Self {
            instruction_pointer: 0,
            bytecode_stack: Vec::new(),
            interpreter_flags: InterpreterFlags::default(),
        }
    }
}

impl AIBehaviorTreeExecutor {
    /// Create new behavior tree executor
    pub fn new() -> Self {
        Self {
            execution_state: BehaviorTreeExecutionState::Idle,
            strategy_integration: StrategyIntegration::default(),
            performance_metrics: BehaviorTreePerformance::default(),
        }
    }

    /// Execute behavior tree (matching C99 _AIBeginStrategy)
    pub fn execute_tree(
        &mut self,
        tree: &mut AIBehaviorTree,
        ai_core: &mut AICore,
        strategy_executor: &mut AIStrategyExecutor,
    ) -> BehaviorResult {
        self.execution_state = BehaviorTreeExecutionState::Executing;
        tree.execution_context.advance_frame();

        let start_time = std::time::Instant::now();

        // Execute root node
        let result = self.execute_node(
            &tree.root,
            &mut tree.execution_context,
            ai_core,
            strategy_executor,
        );

        // Update performance metrics
        self.performance_metrics.execution_time_ms = start_time.elapsed().as_secs_f32() * 1000.0;

        // Update execution state based on result
        self.execution_state = match result {
            BehaviorResult::Success => BehaviorTreeExecutionState::Completed,
            BehaviorResult::Failure => BehaviorTreeExecutionState::Failed,
            BehaviorResult::Running => BehaviorTreeExecutionState::Executing,
        };

        result
    }

    /// Execute individual behavior tree node
    fn execute_node(
        &mut self,
        node: &BehaviorNode,
        context: &mut BehaviorExecutionContext,
        ai_core: &mut AICore,
        strategy_executor: &mut AIStrategyExecutor,
    ) -> BehaviorResult {
        self.performance_metrics.nodes_evaluated += 1;

        match &node.node_type {
            BehaviorNodeType::Root => {
                self.execute_selector_node(node, context, ai_core, strategy_executor)
            }
            BehaviorNodeType::Selector => {
                self.execute_selector_node(node, context, ai_core, strategy_executor)
            }
            BehaviorNodeType::Sequence => {
                self.execute_sequence_node(node, context, ai_core, strategy_executor)
            }
            BehaviorNodeType::Condition(condition) => {
                self.execute_condition_node(condition, context, ai_core)
            }
            BehaviorNodeType::Action(action) => {
                self.execute_action_node(action, context, ai_core)
            }
            BehaviorNodeType::Strategy(strategy_type) => {
                self.execute_strategy_node(strategy_type, context, ai_core, strategy_executor)
            }
            BehaviorNodeType::Timer(duration) => {
                self.execute_timer_node(*duration, context, ai_core)
            }
            BehaviorNodeType::Parallel => {
                self.execute_parallel_node(node, context, ai_core, strategy_executor)
            }
        }
    }

    /// Execute selector node (OR logic - first success wins)
    fn execute_selector_node(
        &mut self,
        node: &BehaviorNode,
        context: &mut BehaviorExecutionContext,
        ai_core: &mut AICore,
        strategy_executor: &mut AIStrategyExecutor,
    ) -> BehaviorResult {
        for child in &node.children {
            let result = self.execute_node(child, context, ai_core, strategy_executor);
            match result {
                BehaviorResult::Success => return BehaviorResult::Success,
                BehaviorResult::Running => return BehaviorResult::Running,
                BehaviorResult::Failure => continue, // Try next child
            }
        }
        BehaviorResult::Failure
    }

    /// Execute sequence node (AND logic - all must succeed)
    fn execute_sequence_node(
        &mut self,
        node: &BehaviorNode,
        context: &mut BehaviorExecutionContext,
        ai_core: &mut AICore,
        strategy_executor: &mut AIStrategyExecutor,
    ) -> BehaviorResult {
        for child in &node.children {
            let result = self.execute_node(child, context, ai_core, strategy_executor);
            match result {
                BehaviorResult::Success => continue, // Continue to next child
                BehaviorResult::Running => return BehaviorResult::Running,
                BehaviorResult::Failure => return BehaviorResult::Failure,
            }
        }
        BehaviorResult::Success
    }

    /// Execute parallel node (all children execute simultaneously)
    fn execute_parallel_node(
        &mut self,
        node: &BehaviorNode,
        context: &mut BehaviorExecutionContext,
        ai_core: &mut AICore,
        strategy_executor: &mut AIStrategyExecutor,
    ) -> BehaviorResult {
        let mut success_count = 0;
        let mut running_count = 0;
        let mut failure_count = 0;

        for child in &node.children {
            let result = self.execute_node(child, context, ai_core, strategy_executor);
            match result {
                BehaviorResult::Success => success_count += 1,
                BehaviorResult::Running => running_count += 1,
                BehaviorResult::Failure => failure_count += 1,
            }
        }

        // Parallel node succeeds if any child succeeds
        if success_count > 0 {
            BehaviorResult::Success
        } else if running_count > 0 {
            BehaviorResult::Running
        } else {
            BehaviorResult::Failure
        }
    }

    /// Execute condition node
    fn execute_condition_node(
        &mut self,
        condition: &AICondition,
        context: &mut BehaviorExecutionContext,
        ai_core: &AICore,
    ) -> BehaviorResult {
        self.performance_metrics.conditions_checked += 1;

        let result = match condition {
            AICondition::OpponentInRange(range) => {
                context.blackboard.opponent_distance <= *range as f32
            }
            AICondition::ShouldBeAggressive => {
                matches!(ai_core.behavior_state, AIBehaviorState::Aggressive0 | AIBehaviorState::Aggressive1) ||
                self.should_go_aggressive(ai_core, context)
            }
            AICondition::ShouldBeDefensive => {
                matches!(ai_core.behavior_state, AIBehaviorState::Defensive) ||
                self.should_be_defensive(ai_core, context)
            }
            AICondition::OpponentJumping => {
                // Check opponent state from blackboard
                context.blackboard.flags.should_jump
            }
            AICondition::OpponentAttacking => {
                // Check if opponent is in attack state
                context.blackboard.flags.should_attack
            }
            AICondition::HasProjectileAdvantage => {
                // Check projectile advantage logic
                self.has_projectile_advantage(ai_core, context)
            }
            AICondition::IsCornered => {
                // Check if AI is near screen edge
                self.is_cornered(ai_core, context)
            }
            AICondition::TimerExpired => {
                ai_core.timers.main_timer == 0
            }
            AICondition::DifficultyCheck(threshold) => {
                // Use fastrand for difficulty-based random checks
                fastrand::u8(0..=255) < *threshold
            }
            AICondition::ShouldGoAggressive => {
                self.should_go_aggressive(ai_core, context)
            }
            AICondition::HasReachedWalkTarget => {
                self.has_reached_walk_target(ai_core, context)
            }
            AICondition::OpponentInBlockStun => {
                context.blackboard.flags.should_block
            }
            AICondition::ShouldThrow => {
                self.should_throw(ai_core, context)
            }
        };

        if result {
            BehaviorResult::Success
        } else {
            BehaviorResult::Failure
        }
    }

    /// Execute action node
    fn execute_action_node(
        &mut self,
        action: &AIAction,
        context: &mut BehaviorExecutionContext,
        ai_core: &mut AICore,
    ) -> BehaviorResult {
        self.performance_metrics.actions_executed += 1;

        match action {
            AIAction::StandStill => {
                ai_core.movement.walk_direction = 0;
                // Reset control signals
                BehaviorResult::Success
            }
            AIAction::WalkForward => {
                ai_core.movement.walk_direction = 1;
                BehaviorResult::Success
            }
            AIAction::WalkBackward => {
                ai_core.movement.walk_direction = 255; // -1 as u8
                BehaviorResult::Success
            }
            AIAction::JumpForward => {
                ai_core.control_signals.do_jump = true;
                ai_core.movement.walk_direction = 1;
                BehaviorResult::Success
            }
            AIAction::JumpBackward => {
                ai_core.control_signals.do_jump = true;
                ai_core.movement.walk_direction = 255; // -1 as u8
                BehaviorResult::Success
            }
            AIAction::JumpUp => {
                ai_core.control_signals.do_jump = true;
                ai_core.movement.walk_direction = 0;
                BehaviorResult::Success
            }
            AIAction::Attack(attack_type) => {
                // Set attack type in context
                context.blackboard.flags.should_attack = true;
                BehaviorResult::Success
            }
            AIAction::SpecialMove(special_id) => {
                // Set special move in context
                BehaviorResult::Success
            }
            AIAction::Block => {
                ai_core.control_signals.do_block = true;
                context.blackboard.flags.should_block = true;
                BehaviorResult::Success
            }
            AIAction::Throw => {
                ai_core.control_signals.do_throw = true;
                context.blackboard.flags.should_throw = true;
                BehaviorResult::Success
            }
            AIAction::SetMode(mode1, mode2) => {
                ai_core.mode1 = *mode1;
                ai_core.mode2 = *mode2;
                BehaviorResult::Success
            }
            AIAction::ResetState => {
                ai_core.mode1 = AIMode1::Init;
                ai_core.mode2 = AIMode2::Init;
                ai_core.behavior_state = AIBehaviorState::Aggressive0;
                BehaviorResult::Success
            }
            AIAction::GoAggressive => {
                ai_core.behavior_state = AIBehaviorState::Aggressive1;
                BehaviorResult::Success
            }
            AIAction::GoDefensive => {
                ai_core.behavior_state = AIBehaviorState::Defensive;
                BehaviorResult::Success
            }
            AIAction::RestartStrategy => {
                // Signal strategy restart
                context.blackboard.flags.should_attack = false;
                BehaviorResult::Success
            }
            AIAction::EndStrategy => {
                // Signal strategy end
                BehaviorResult::Success
            }
        }
    }

    /// Execute strategy node (integrates with existing strategy system)
    fn execute_strategy_node(
        &mut self,
        strategy_type: &AIStrategyType,
        context: &mut BehaviorExecutionContext,
        ai_core: &mut AICore,
        strategy_executor: &mut AIStrategyExecutor,
    ) -> BehaviorResult {
        self.performance_metrics.strategies_invoked += 1;

        match strategy_type {
            AIStrategyType::Block => {
                strategy_executor.begin_defensive(ai_core);
                if strategy_executor.execute_strategy(ai_core) {
                    BehaviorResult::Running
                } else {
                    BehaviorResult::Success
                }
            }
            AIStrategyType::Attack => {
                strategy_executor.begin_aggressive(ai_core, false);
                if strategy_executor.execute_strategy(ai_core) {
                    BehaviorResult::Running
                } else {
                    BehaviorResult::Success
                }
            }
            AIStrategyType::Special => {
                strategy_executor.begin_aggressive(ai_core, true);
                if strategy_executor.execute_strategy(ai_core) {
                    BehaviorResult::Running
                } else {
                    BehaviorResult::Success
                }
            }
            _ => BehaviorResult::Failure,
        }
    }

    /// Execute timer node
    fn execute_timer_node(
        &mut self,
        duration: u16,
        context: &mut BehaviorExecutionContext,
        ai_core: &mut AICore,
    ) -> BehaviorResult {
        if ai_core.timers.main_timer == 0 {
            ai_core.timers.main_timer = duration as u8;
            BehaviorResult::Running
        } else if ai_core.timers.main_timer > 0 {
            ai_core.timers.main_timer -= 1;
            BehaviorResult::Running
        } else {
            BehaviorResult::Success
        }
    }

    // Helper methods for condition evaluation (matching C99 AI logic)

    /// Check if AI should go aggressive (matching _AIShouldGoAggressive)
    fn should_go_aggressive(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {
        // Simplified logic - can be expanded based on C99 implementation
        !matches!(ai_core.behavior_state, AIBehaviorState::Defensive) &&
        ai_core.timers.agg_timer0 == 0 &&
        context.blackboard.opponent_distance < 100.0
    }

    /// Check if AI should be defensive
    fn should_be_defensive(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {
        matches!(ai_core.behavior_state, AIBehaviorState::Defensive) ||
        context.blackboard.threat_level > 5 ||
        context.blackboard.opponent_distance < 50.0
    }

    /// Check if AI has projectile advantage
    fn has_projectile_advantage(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {
        // Simplified projectile advantage logic
        context.blackboard.opponent_distance > 150.0 &&
        !context.blackboard.flags.should_block
    }

    /// Check if AI is cornered (matching C99 screen edge detection)
    fn is_cornered(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {
        // Simplified corner detection - would need actual position data
        context.blackboard.walk_target < 50 || context.blackboard.walk_target > 350
    }

    /// Check if AI has reached walk target (matching _AIHasReachedWalkTarget)
    fn has_reached_walk_target(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {
        let target_diff = (ai_core.movement.walk_target - context.blackboard.walk_target).abs();
        target_diff <= 4
    }

    /// Check if AI should throw
    fn should_throw(&self, ai_core: &AICore, context: &BehaviorExecutionContext) -> bool {
        context.blackboard.opponent_distance < 30.0 &&
        !context.blackboard.flags.should_block &&
        fastrand::u8(0..=255) < 50 // 50/255 chance
    }
}

// Bevy Systems for AI Behavior Tree Integration

/// System to execute AI behavior trees each frame
pub fn ai_behavior_tree_system(
    mut ai_query: Query<(
        &mut AIBehaviorTree,
        &mut AIBehaviorTreeExecutor,
        &mut AICore,
        &mut AIStrategyExecutor,
    ), With<Fighter>>,
    time: Res<Time>,
) {
    for (mut tree, mut executor, mut ai_core, mut strategy_executor) in ai_query.iter_mut() {
        // Only execute if AI is active
        if ai_core.mode1 != AIMode1::Init {
            let result = executor.execute_tree(
                &mut tree,
                &mut ai_core,
                &mut strategy_executor,
            );

            // Handle execution result
            match result {
                BehaviorResult::Success => {
                    // Tree completed successfully, reset for next frame
                    executor.execution_state = BehaviorTreeExecutionState::Idle;
                }
                BehaviorResult::Failure => {
                    // Tree failed, try fallback behavior
                    ai_core.mode1 = AIMode1::Init;
                    executor.execution_state = BehaviorTreeExecutionState::Idle;
                }
                BehaviorResult::Running => {
                    // Tree is still executing, continue next frame
                }
            }
        }
    }
}

/// System to update behavior tree blackboards with game state
pub fn ai_blackboard_update_system(
    mut ai_query: Query<(
        &mut AIBehaviorTree,
        &AIThreatAssessment,
        &Transform,
    ), With<Fighter>>,
    opponent_query: Query<&Transform, (With<Fighter>, Without<AIBehaviorTree>)>,
) {
    for (mut tree, threat_assessment, transform) in ai_query.iter_mut() {
        // Update opponent distance
        if let Ok(opponent_transform) = opponent_query.get_single() {
            let distance = transform.translation.distance(opponent_transform.translation);
            tree.execution_context.blackboard.opponent_distance = distance;

            // Update opponent direction
            let direction = if opponent_transform.translation.x > transform.translation.x { 1 } else { -1 };
            tree.execution_context.blackboard.opponent_direction = direction;
        }

        // Update threat level from threat assessment
        tree.execution_context.blackboard.threat_level = (threat_assessment.threat_level * 10.0) as u8;

        // Update walk target (simplified)
        tree.execution_context.blackboard.walk_target =
            tree.execution_context.blackboard.opponent_distance as i16;
    }
}

/// System to handle behavior tree performance monitoring
pub fn ai_behavior_tree_performance_system(
    mut ai_query: Query<&mut AIBehaviorTreeExecutor, With<Fighter>>,
    mut performance_events: EventWriter<AIPerformanceEvent>,
) {
    for mut executor in ai_query.iter_mut() {
        // Check for performance issues
        if executor.performance_metrics.execution_time_ms > 5.0 {
            performance_events.send(AIPerformanceEvent::SlowExecution {
                execution_time: executor.performance_metrics.execution_time_ms,
                nodes_evaluated: executor.performance_metrics.nodes_evaluated,
            });
        }

        // Reset performance metrics for next frame
        executor.performance_metrics = BehaviorTreePerformance::default();
    }
}

/// AI Performance Event for monitoring
#[derive(Event, Debug)]
pub enum AIPerformanceEvent {
    SlowExecution {
        execution_time: f32,
        nodes_evaluated: u32,
    },
    RecursionDepthExceeded {
        depth: u8,
    },
    StrategyExecutionFailed {
        strategy_type: AIStrategyType,
    },
}

/// Resource for global behavior tree configuration
#[derive(Resource, Debug)]
pub struct AIBehaviorTreeConfig {
    /// Maximum execution time per frame (ms)
    pub max_execution_time_ms: f32,
    /// Maximum recursion depth
    pub max_recursion_depth: u8,
    /// Enable performance monitoring
    pub enable_performance_monitoring: bool,
    /// Enable debug visualization
    pub enable_debug_visualization: bool,
    /// Behavior tree update frequency (frames)
    pub update_frequency: u8,
}

impl Default for AIBehaviorTreeConfig {
    fn default() -> Self {
        Self {
            max_execution_time_ms: 5.0,
            max_recursion_depth: 20,
            enable_performance_monitoring: true,
            enable_debug_visualization: false,
            update_frequency: 1, // Every frame
        }
    }
}

/// Plugin for AI Behavior Tree system
pub struct AIBehaviorTreePlugin;

impl Plugin for AIBehaviorTreePlugin {
    fn build(&self, app: &mut App) {
        app
            .init_resource::<AIBehaviorTreeConfig>()
            .add_event::<AIPerformanceEvent>()
            .add_systems(Update, (
                ai_behavior_tree_system,
                ai_blackboard_update_system,
                ai_behavior_tree_performance_system,
            ).chain());
    }
}
