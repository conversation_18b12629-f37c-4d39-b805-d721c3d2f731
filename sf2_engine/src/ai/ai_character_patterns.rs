// Character-Specific AI Patterns - individual character AI behaviors and strategies
// Implements character-specific attack patterns, defensive strategies, and special move usage

use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use sf2_types::*;
use sf2_types::character_traits::AIBehaviorPattern;
use crate::ai::ai_core::*;
use crate::ai::ai_strategy::*;
use crate::ai::ai_difficulty::*;
use crate::ai::ai_behavior_tree::AIAction;
use crate::components::Fighter;
use std::collections::HashMap;

/// AI-specific attack type for character patterns
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AIAttackType {
    LightPunch,
    MediumPunch,
    HeavyPunch,
    LightKick,
    MediumKick,
    HeavyKick,
    Throw,
    Special(SpecialMoveId),
}

impl AIAttackType {
    /// Convert to the main AttackType enum used by the game engine
    pub fn to_attack_type(self) -> sf2_types::fighter::AttackType {
        match self {
            AIAttackType::LightPunch => sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Light),
            AIAttackType::MediumPunch => sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Medium),
            AIAttackType::HeavyPunch => sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Heavy),
            AIAttackType::LightKick => sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Light),
            AIAttackType::MediumKick => sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium),
            AIAttackType::HeavyKick => sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Heavy),
            AIAttackType::Throw => sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Heavy), // Throws are handled separately
            AIAttackType::Special(special_id) => sf2_types::fighter::AttackType::Special(special_id),
        }
    }
}

/// Character-specific AI pattern modifiers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterAIModifiers {
    /// Character ID this applies to
    pub character_id: FighterId,
    /// Opponent-specific behavior adjustments
    pub opponent_modifiers: HashMap<FighterId, OpponentSpecificModifier>,
    /// Preferred attack patterns by distance
    pub attack_patterns: AttackPatternPreferences,
    /// Defensive strategy preferences
    pub defensive_patterns: DefensivePatternPreferences,
    /// Special move usage patterns
    pub special_move_patterns: SpecialMovePatternPreferences,
    /// Character-specific AI flags
    pub ai_flags: CharacterAIFlags,
}

/// Opponent-specific behavior modifiers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpponentSpecificModifier {
    /// Aggression adjustment against this opponent
    pub aggression_modifier: f32,
    /// Defense adjustment against this opponent
    pub defense_modifier: f32,
    /// Special move frequency adjustment
    pub special_frequency_modifier: f32,
    /// Preferred distance adjustment
    pub distance_modifier: i16,
    /// Reaction time adjustment (frames)
    pub reaction_time_modifier: i16,
}

/// Attack pattern preferences by distance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttackPatternPreferences {
    /// Close range attack preferences (0-80 pixels)
    pub close_range: Vec<AttackPreference>,
    /// Medium range attack preferences (80-150 pixels)
    pub medium_range: Vec<AttackPreference>,
    /// Long range attack preferences (150+ pixels)
    pub long_range: Vec<AttackPreference>,
    /// Anti-air attack preferences
    pub anti_air: Vec<AttackPreference>,
}

/// Individual attack preference
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttackPreference {
    /// Attack type
    pub attack_type: AIAttackType,
    /// Probability weight (0.0-1.0)
    pub weight: f32,
    /// Minimum difficulty level required
    pub min_difficulty: u8,
    /// Frame advantage requirement
    pub frame_advantage_required: i8,
}

/// Defensive pattern preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DefensivePatternPreferences {
    /// Block patterns by attack type
    pub block_patterns: HashMap<AIAttackType, f32>,
    /// Escape patterns when cornered
    pub escape_patterns: Vec<EscapePattern>,
    /// Counter-attack patterns after blocking
    pub counter_patterns: Vec<CounterPattern>,
    /// Throw escape preferences
    pub throw_escape_weight: f32,
}

/// Escape pattern when cornered
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EscapePattern {
    /// Escape action
    pub action: AIAction,
    /// Weight/probability
    pub weight: f32,
    /// Minimum difficulty required
    pub min_difficulty: u8,
}

/// Counter-attack pattern after blocking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CounterPattern {
    /// Counter action
    pub action: AIAction,
    /// Weight/probability
    pub weight: f32,
    /// Frame window for counter
    pub frame_window: u8,
}

/// Special move pattern preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpecialMovePatternPreferences {
    /// Projectile usage patterns
    pub projectile_patterns: Vec<ProjectilePattern>,
    /// Anti-projectile patterns
    pub anti_projectile_patterns: Vec<AntiProjectilePattern>,
    /// Combo special move patterns
    pub combo_patterns: Vec<ComboPattern>,
    /// Reversal special move patterns
    pub reversal_patterns: Vec<ReversalPattern>,
}

/// Projectile usage pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectilePattern {
    /// Special move ID
    pub special_move_id: SpecialMoveId,
    /// Optimal distance range
    pub optimal_distance: (u16, u16),
    /// Usage frequency
    pub frequency: f32,
    /// Minimum difficulty
    pub min_difficulty: u8,
}

/// Anti-projectile pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AntiProjectilePattern {
    /// Response action
    pub action: AIAction,
    /// Distance threshold
    pub distance_threshold: u16,
    /// Reaction weight
    pub weight: f32,
}

/// Combo pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComboPattern {
    /// Combo sequence
    pub sequence: Vec<AIAction>,
    /// Success rate requirement
    pub min_success_rate: f32,
    /// Difficulty requirement
    pub min_difficulty: u8,
}

/// Reversal pattern
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReversalPattern {
    /// Reversal action
    pub action: AIAction,
    /// Frame window
    pub frame_window: u8,
    /// Usage probability
    pub probability: f32,
}

/// Character-specific AI flags
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterAIFlags {
    /// Prefers close combat
    pub prefers_close_combat: bool,
    /// Prefers long range
    pub prefers_long_range: bool,
    /// Uses projectiles frequently
    pub projectile_heavy: bool,
    /// Aggressive rushdown style
    pub rushdown_style: bool,
    /// Defensive/zoning style
    pub zoning_style: bool,
    /// Grappler style (throw-heavy)
    pub grappler_style: bool,
    /// Has charge moves
    pub has_charge_moves: bool,
    /// Has command grabs
    pub has_command_grabs: bool,
}

/// Character AI Pattern Manager Component
#[derive(Component, Debug)]
pub struct CharacterAIPatternManager {
    /// Current character's AI modifiers
    pub character_modifiers: CharacterAIModifiers,
    /// Current opponent ID
    pub current_opponent: Option<FighterId>,
    /// Active pattern adjustments
    pub active_adjustments: PatternAdjustments,
    /// Performance tracking for pattern effectiveness
    pub pattern_performance: PatternPerformanceTracker,
}

/// Active pattern adjustments
#[derive(Debug, Clone, Default)]
pub struct PatternAdjustments {
    /// Current aggression adjustment
    pub aggression_adjustment: f32,
    /// Current defense adjustment
    pub defense_adjustment: f32,
    /// Current special frequency adjustment
    pub special_frequency_adjustment: f32,
    /// Current distance preference adjustment
    pub distance_adjustment: i16,
    /// Current reaction time adjustment
    pub reaction_time_adjustment: i16,
}

/// Pattern performance tracking
#[derive(Debug, Clone, Default)]
pub struct PatternPerformanceTracker {
    /// Success rates by pattern type
    pub pattern_success_rates: HashMap<String, f32>,
    /// Usage counts by pattern
    pub pattern_usage_counts: HashMap<String, u32>,
    /// Last update frame
    pub last_update_frame: u64,
}

/// Character AI Pattern Resource
#[derive(Resource, Debug)]
pub struct CharacterAIPatternDatabase {
    /// All character AI patterns
    pub character_patterns: HashMap<FighterId, CharacterAIModifiers>,
    /// Environment variable overrides
    pub env_overrides: PatternEnvironmentConfig,
}

/// Environment variable configuration for AI patterns
#[derive(Debug, Clone)]
pub struct PatternEnvironmentConfig {
    /// Global aggression scale
    pub global_aggression_scale: f32,
    /// Global defense scale
    pub global_defense_scale: f32,
    /// Global special frequency scale
    pub global_special_frequency_scale: f32,
    /// Enable character-specific patterns
    pub enable_character_patterns: bool,
    /// Enable opponent-specific adjustments
    pub enable_opponent_adjustments: bool,
    /// Pattern adaptation rate
    pub pattern_adaptation_rate: f32,
}

impl Default for PatternEnvironmentConfig {
    fn default() -> Self {
        Self {
            global_aggression_scale: std::env::var("AI_PATTERN_AGGRESSION_SCALE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(1.0),
            global_defense_scale: std::env::var("AI_PATTERN_DEFENSE_SCALE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(1.0),
            global_special_frequency_scale: std::env::var("AI_PATTERN_SPECIAL_SCALE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(1.0),
            enable_character_patterns: std::env::var("AI_ENABLE_CHARACTER_PATTERNS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(true),
            enable_opponent_adjustments: std::env::var("AI_ENABLE_OPPONENT_ADJUSTMENTS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(true),
            pattern_adaptation_rate: std::env::var("AI_PATTERN_ADAPTATION_RATE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(0.1),
        }
    }
}

impl CharacterAIPatternManager {
    /// Create Ken-specific AI modifiers (aggressive shoto)
    fn create_ken_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();

        // Ken vs Ryu - more aggressive, closer range
        opponent_modifiers.insert(FighterId::Ryu, OpponentSpecificModifier {
            aggression_modifier: 1.2,
            defense_modifier: 0.9,
            special_frequency_modifier: 1.1,
            distance_modifier: -15, // Get closer
            reaction_time_modifier: -1, // Slightly faster
        });

        // Ken vs Zangief - hit and run tactics
        opponent_modifiers.insert(FighterId::Zangief, OpponentSpecificModifier {
            aggression_modifier: 0.8,
            defense_modifier: 1.1,
            special_frequency_modifier: 1.3,
            distance_modifier: 40,
            reaction_time_modifier: -2,
        });

        // Ken vs Dhalsim - very aggressive rushdown
        opponent_modifiers.insert(FighterId::Dhalsim, OpponentSpecificModifier {
            aggression_modifier: 1.5,
            defense_modifier: 0.7,
            special_frequency_modifier: 0.9,
            distance_modifier: -40, // Get much closer
            reaction_time_modifier: -2,
        });

        CharacterAIModifiers {
            character_id: FighterId::Ken,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::LightPunch,
                        weight: 0.3,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick, // Ken's signature move
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::Throw,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: -2,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.6,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.4,
                        min_difficulty: 3,
                        frame_advantage_required: 2,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.7,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch, // Shoryuken
                        weight: 0.8,
                        min_difficulty: 3,
                        frame_advantage_required: -3,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.2,
                        min_difficulty: 2,
                        frame_advantage_required: 0,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.7); // Less defensive than Ryu
                    patterns.insert(AIAttackType::MediumPunch, 0.8);
                    patterns.insert(AIAttackType::HeavyPunch, 0.9);
                    patterns.insert(AIAttackType::LightKick, 0.6);
                    patterns.insert(AIAttackType::MediumKick, 0.75);
                    patterns.insert(AIAttackType::HeavyKick, 0.85);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::JumpForward, // More aggressive escape
                        weight: 0.5,
                        min_difficulty: 2,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::TatsumakiSenpukyaku),
                        weight: 0.3,
                        min_difficulty: 3,
                    },
                    EscapePattern {
                        action: AIAction::WalkBackward,
                        weight: 0.2,
                        min_difficulty: 1,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium)),
                        weight: 0.4,
                        frame_window: 3,
                    },
                    CounterPattern {
                        action: AIAction::Throw,
                        weight: 0.4,
                        frame_window: 2,
                    },
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::Shoryuken),
                        weight: 0.2,
                        frame_window: 4,
                    },
                ],
                throw_escape_weight: 0.6, // Less likely to escape throws
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![
                    ProjectilePattern {
                        special_move_id: SpecialMoveId::Hadoken,
                        optimal_distance: (100, 250), // Closer than Ryu
                        frequency: 0.5,
                        min_difficulty: 2,
                    },
                ],
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::JumpForward,
                        distance_threshold: 120,
                        weight: 0.6, // More aggressive
                    },
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::TatsumakiSenpukyaku),
                        distance_threshold: 100,
                        weight: 0.4,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium)),
                            AIAction::SpecialMove(SpecialMoveId::Hadoken),
                        ],
                        min_success_rate: 0.6,
                        min_difficulty: 4,
                    },
                    ComboPattern {
                        sequence: vec![
                            AIAction::JumpForward,
                            AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium)),
                            AIAction::SpecialMove(SpecialMoveId::Shoryuken),
                        ],
                        min_success_rate: 0.8,
                        min_difficulty: 6,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::Shoryuken),
                        frame_window: 3,
                        probability: 0.9, // More likely to reversal
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: true,
                prefers_long_range: false,
                projectile_heavy: false,
                rushdown_style: true,
                zoning_style: false,
                grappler_style: false,
                has_charge_moves: false,
                has_command_grabs: false,
            },
        }
    }

    /// Update active pattern adjustments based on opponent
    fn update_opponent_adjustments(&mut self, opponent_id: FighterId, env_config: &PatternEnvironmentConfig) {
        if !env_config.enable_opponent_adjustments {
            return;
        }

        self.current_opponent = Some(opponent_id);

        if let Some(modifier) = self.character_modifiers.opponent_modifiers.get(&opponent_id) {
            self.active_adjustments.aggression_adjustment =
                modifier.aggression_modifier * env_config.global_aggression_scale;
            self.active_adjustments.defense_adjustment =
                modifier.defense_modifier * env_config.global_defense_scale;
            self.active_adjustments.special_frequency_adjustment =
                modifier.special_frequency_modifier * env_config.global_special_frequency_scale;
            self.active_adjustments.distance_adjustment = modifier.distance_modifier;
            self.active_adjustments.reaction_time_adjustment = modifier.reaction_time_modifier;
        } else {
            // Reset to defaults if no specific modifier
            self.active_adjustments = PatternAdjustments::default();
        }
    }

    /// Get adjusted behavior pattern based on current adjustments
    fn get_adjusted_pattern(&self, base_pattern: &AIBehaviorPattern) -> AIBehaviorPattern {
        AIBehaviorPattern {
            aggression: (base_pattern.aggression * self.active_adjustments.aggression_adjustment).clamp(0.0, 2.0),
            defense: (base_pattern.defense * self.active_adjustments.defense_adjustment).clamp(0.0, 2.0),
            special_move_frequency: (base_pattern.special_move_frequency * self.active_adjustments.special_frequency_adjustment).clamp(0.0, 2.0),
            jump_frequency: base_pattern.jump_frequency,
            preferred_distance: Fixed8_8::from_raw(
                (base_pattern.preferred_distance.raw() as i32 + self.active_adjustments.distance_adjustment as i32)
                    .clamp(20, 400) as i16
            ),
            reaction_time: (base_pattern.reaction_time as i32 + self.active_adjustments.reaction_time_adjustment as i32)
                .clamp(1, 60) as u16,
        }
    }

    /// Record pattern performance for adaptation
    fn record_pattern_performance(&mut self, pattern_name: &str, success: bool, current_frame: u64) {
        let entry = self.pattern_performance.pattern_success_rates.entry(pattern_name.to_string()).or_insert(0.5);
        let usage_count = self.pattern_performance.pattern_usage_counts.entry(pattern_name.to_string()).or_insert(0);

        *usage_count += 1;

        // Exponential moving average for success rate
        let alpha = 0.1; // Learning rate
        *entry = *entry * (1.0 - alpha) + if success { 1.0 } else { 0.0 } * alpha;

        self.pattern_performance.last_update_frame = current_frame;
    }

    /// Get pattern success rate
    fn get_pattern_success_rate(&self, pattern_name: &str) -> f32 {
        self.pattern_performance.pattern_success_rates.get(pattern_name).copied().unwrap_or(0.5)
    }

    /// Select best attack pattern for current distance and situation
    fn select_attack_pattern(&self, distance: u16, difficulty: u8, frame_advantage: i8) -> Option<AIAttackType> {
        let patterns = if distance < 80 {
            &self.character_modifiers.attack_patterns.close_range
        } else if distance < 150 {
            &self.character_modifiers.attack_patterns.medium_range
        } else {
            &self.character_modifiers.attack_patterns.long_range
        };

        let valid_patterns: Vec<_> = patterns.iter()
            .filter(|p| p.min_difficulty <= difficulty && p.frame_advantage_required <= frame_advantage)
            .collect();

        if valid_patterns.is_empty() {
            return None;
        }

        // Weighted random selection
        let total_weight: f32 = valid_patterns.iter().map(|p| p.weight).sum();
        let mut random_value = fastrand::f32() * total_weight;

        for pattern in &valid_patterns {
            random_value -= pattern.weight;
            if random_value <= 0.0 {
                return Some(pattern.attack_type);
            }
        }

        valid_patterns.last().map(|p| p.attack_type)
    }

    /// Select defensive action based on incoming attack
    fn select_defensive_action(&self, incoming_attack: AIAttackType, difficulty: u8) -> Option<AIAction> {
        // Check block probability first
        if let Some(&block_prob) = self.character_modifiers.defensive_patterns.block_patterns.get(&incoming_attack) {
            if fastrand::f32() < block_prob {
                return Some(AIAction::Block);
            }
        }

        // Select counter pattern
        let valid_counters: Vec<_> = self.character_modifiers.defensive_patterns.counter_patterns.iter()
            .filter(|p| difficulty >= 3) // Only use counters at higher difficulty
            .collect();

        if !valid_counters.is_empty() {
            let total_weight: f32 = valid_counters.iter().map(|p| p.weight).sum();
            let mut random_value = fastrand::f32() * total_weight;

            for counter in valid_counters {
                random_value -= counter.weight;
                if random_value <= 0.0 {
                    return Some(counter.action.clone());
                }
            }
        }

        Some(AIAction::Block)
    }
}

impl Default for CharacterAIPatternDatabase {
    fn default() -> Self {
        let mut character_patterns = HashMap::new();

        // Initialize all character patterns
        character_patterns.insert(FighterId::Ryu, CharacterAIPatternManager::create_ryu_modifiers());
        character_patterns.insert(FighterId::Ken, CharacterAIPatternManager::create_ken_modifiers());
        character_patterns.insert(FighterId::ChunLi, CharacterAIPatternManager::create_chun_li_modifiers());
        character_patterns.insert(FighterId::Guile, CharacterAIPatternManager::create_guile_modifiers());
        character_patterns.insert(FighterId::Blanka, CharacterAIPatternManager::create_blanka_modifiers());
        character_patterns.insert(FighterId::EHonda, CharacterAIPatternManager::create_e_honda_modifiers());
        character_patterns.insert(FighterId::Zangief, CharacterAIPatternManager::create_zangief_modifiers());
        character_patterns.insert(FighterId::Dhalsim, CharacterAIPatternManager::create_dhalsim_modifiers());

        Self {
            character_patterns,
            env_overrides: PatternEnvironmentConfig::default(),
        }
    }
}

// Bevy Systems for Character AI Pattern Management

/// Initialize character AI pattern managers for fighters
pub fn initialize_character_ai_patterns_system(
    mut commands: Commands,
    fighters: Query<(Entity, &Fighter), (Added<Fighter>, Without<CharacterAIPatternManager>)>,
    pattern_db: Res<CharacterAIPatternDatabase>,
) {
    for (entity, fighter) in fighters.iter() {
        if pattern_db.env_overrides.enable_character_patterns {
            let pattern_manager = CharacterAIPatternManager::new(fighter.fighter_id);
            commands.entity(entity).insert(pattern_manager);
        }
    }
}

/// Update opponent-specific adjustments when matchup changes
pub fn update_opponent_adjustments_system(
    mut pattern_managers: Query<&mut CharacterAIPatternManager>,
    fighters: Query<&Fighter>,
    pattern_db: Res<CharacterAIPatternDatabase>,
) {
    let fighter_ids: Vec<FighterId> = fighters.iter().map(|f| f.fighter_id).collect();

    if fighter_ids.len() == 2 {
        let (fighter1_id, fighter2_id) = (fighter_ids[0], fighter_ids[1]);

        for mut pattern_manager in pattern_managers.iter_mut() {
            let opponent_id = if pattern_manager.character_modifiers.character_id == fighter1_id {
                fighter2_id
            } else {
                fighter1_id
            };

            pattern_manager.update_opponent_adjustments(opponent_id, &pattern_db.env_overrides);
        }
    }
}

/// Update environment variable overrides
pub fn update_pattern_environment_system(
    mut pattern_db: ResMut<CharacterAIPatternDatabase>,
) {
    // Check for environment variable changes
    let new_config = PatternEnvironmentConfig::default();

    // Only update if values have changed to avoid unnecessary work
    if pattern_db.env_overrides.global_aggression_scale != new_config.global_aggression_scale ||
       pattern_db.env_overrides.global_defense_scale != new_config.global_defense_scale ||
       pattern_db.env_overrides.global_special_frequency_scale != new_config.global_special_frequency_scale ||
       pattern_db.env_overrides.enable_character_patterns != new_config.enable_character_patterns ||
       pattern_db.env_overrides.enable_opponent_adjustments != new_config.enable_opponent_adjustments ||
       pattern_db.env_overrides.pattern_adaptation_rate != new_config.pattern_adaptation_rate {
        pattern_db.env_overrides = new_config;
    }
}

/// Adapt patterns based on performance
pub fn adapt_character_patterns_system(
    mut pattern_managers: Query<&mut CharacterAIPatternManager>,
    pattern_db: Res<CharacterAIPatternDatabase>,
    time: Res<Time>,
) {
    if !pattern_db.env_overrides.enable_character_patterns {
        return;
    }

    let current_frame = (time.elapsed_secs() * 60.0) as u64; // Assuming 60 FPS

    for mut pattern_manager in pattern_managers.iter_mut() {
        // Adapt patterns based on success rates
        let adaptation_rate = pattern_db.env_overrides.pattern_adaptation_rate;

        // Example: Adjust aggression based on success rate
        if let Some(&success_rate) = pattern_manager.pattern_performance.pattern_success_rates.get("aggression") {
            if success_rate < 0.3 {
                // Reduce aggression if performing poorly
                pattern_manager.active_adjustments.aggression_adjustment *= 1.0 - adaptation_rate;
            } else if success_rate > 0.7 {
                // Increase aggression if performing well
                pattern_manager.active_adjustments.aggression_adjustment *= 1.0 + adaptation_rate;
            }
        }

        // Clamp adjustments to reasonable bounds
        pattern_manager.active_adjustments.aggression_adjustment =
            pattern_manager.active_adjustments.aggression_adjustment.clamp(0.2, 2.0);
        pattern_manager.active_adjustments.defense_adjustment =
            pattern_manager.active_adjustments.defense_adjustment.clamp(0.2, 2.0);
        pattern_manager.active_adjustments.special_frequency_adjustment =
            pattern_manager.active_adjustments.special_frequency_adjustment.clamp(0.1, 3.0);
    }
}

/// Character AI Pattern Plugin
pub struct CharacterAIPatternPlugin;

impl Plugin for CharacterAIPatternPlugin {
    fn build(&self, app: &mut App) {
        app
            .init_resource::<CharacterAIPatternDatabase>()
            .add_systems(Update, (
                initialize_character_ai_patterns_system,
                update_opponent_adjustments_system,
                update_pattern_environment_system,
                adapt_character_patterns_system,
            ).chain());
    }
}

impl CharacterAIPatternManager {
    /// Create new pattern manager for character
    pub fn new(character_id: FighterId) -> Self {
        Self {
            character_modifiers: Self::create_default_modifiers(character_id),
            current_opponent: None,
            active_adjustments: PatternAdjustments::default(),
            pattern_performance: PatternPerformanceTracker::default(),
        }
    }
    
    /// Create default AI modifiers for character
    fn create_default_modifiers(character_id: FighterId) -> CharacterAIModifiers {
        match character_id {
            FighterId::Ryu => Self::create_ryu_modifiers(),
            FighterId::Ken => Self::create_ken_modifiers(),
            FighterId::ChunLi => Self::create_chun_li_modifiers(),
            FighterId::Guile => Self::create_guile_modifiers(),
            FighterId::Blanka => Self::create_blanka_modifiers(),
            FighterId::EHonda => Self::create_e_honda_modifiers(),
            FighterId::Zangief => Self::create_zangief_modifiers(),
            FighterId::Dhalsim => Self::create_dhalsim_modifiers(),
            // Boss characters - use default patterns for now
            FighterId::Balrog => Self::create_ryu_modifiers(), // Use Ryu as base
            FighterId::Vega => Self::create_ken_modifiers(),   // Use Ken as base
            FighterId::Sagat => Self::create_ryu_modifiers(),  // Use Ryu as base
            FighterId::MBison => Self::create_guile_modifiers(), // Use Guile as base
        }
    }

    /// Create Ryu-specific AI modifiers (balanced shoto)
    fn create_ryu_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();

        // Ryu vs Ken - slightly more defensive
        opponent_modifiers.insert(FighterId::Ken, OpponentSpecificModifier {
            aggression_modifier: 0.9,
            defense_modifier: 1.1,
            special_frequency_modifier: 1.0,
            distance_modifier: 10, // Slightly further back
            reaction_time_modifier: 0,
        });

        // Ryu vs Zangief - keep distance, use projectiles
        opponent_modifiers.insert(FighterId::Zangief, OpponentSpecificModifier {
            aggression_modifier: 0.7,
            defense_modifier: 1.2,
            special_frequency_modifier: 1.4, // More hadokens
            distance_modifier: 50, // Much further back
            reaction_time_modifier: -2, // Faster reactions
        });

        // Ryu vs Dhalsim - more aggressive to close distance
        opponent_modifiers.insert(FighterId::Dhalsim, OpponentSpecificModifier {
            aggression_modifier: 1.3,
            defense_modifier: 0.8,
            special_frequency_modifier: 0.8,
            distance_modifier: -30, // Get closer
            reaction_time_modifier: -1,
        });

        CharacterAIModifiers {
            character_id: FighterId::Ryu,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::LightPunch,
                        weight: 0.4,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumPunch,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::Throw,
                        weight: 0.3,
                        min_difficulty: 3,
                        frame_advantage_required: -2,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::MediumPunch,
                        weight: 0.5,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.3,
                        min_difficulty: 3,
                        frame_advantage_required: 2,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.2,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.6,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch, // Shoryuken
                        weight: 0.7,
                        min_difficulty: 4,
                        frame_advantage_required: -3,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumPunch,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: 0,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.8);
                    patterns.insert(AIAttackType::MediumPunch, 0.9);
                    patterns.insert(AIAttackType::HeavyPunch, 0.95);
                    patterns.insert(AIAttackType::LightKick, 0.7);
                    patterns.insert(AIAttackType::MediumKick, 0.85);
                    patterns.insert(AIAttackType::HeavyKick, 0.9);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::JumpBackward,
                        weight: 0.4,
                        min_difficulty: 2,
                    },
                    EscapePattern {
                        action: AIAction::WalkBackward,
                        weight: 0.3,
                        min_difficulty: 1,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::Hadoken),
                        weight: 0.3,
                        min_difficulty: 3,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Light)),
                        weight: 0.5,
                        frame_window: 3,
                    },
                    CounterPattern {
                        action: AIAction::Throw,
                        weight: 0.3,
                        frame_window: 2,
                    },
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::Shoryuken),
                        weight: 0.2,
                        frame_window: 4,
                    },
                ],
                throw_escape_weight: 0.7,
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![
                    ProjectilePattern {
                        special_move_id: SpecialMoveId::Hadoken,
                        optimal_distance: (120, 300),
                        frequency: 0.6,
                        min_difficulty: 2,
                    },
                ],
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::JumpForward,
                        distance_threshold: 150,
                        weight: 0.4,
                    },
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::Hadoken),
                        distance_threshold: 200,
                        weight: 0.6,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Light)),
                            AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Medium)),
                            AIAction::SpecialMove(SpecialMoveId::Hadoken),
                        ],
                        min_success_rate: 0.7,
                        min_difficulty: 5,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::Shoryuken),
                        frame_window: 3,
                        probability: 0.8,
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: false,
                prefers_long_range: false,
                projectile_heavy: true,
                rushdown_style: false,
                zoning_style: true,
                grappler_style: false,
                has_charge_moves: false,
                has_command_grabs: false,
            },
        }
    }

    // Chun-Li modifiers implemented in ai_character_patterns_extended.rs

    // Guile, Blanka, E.Honda modifiers implemented in ai_character_patterns_extended.rs
    // Zangief, Dhalsim modifiers implemented in ai_character_patterns_final.rs
}
