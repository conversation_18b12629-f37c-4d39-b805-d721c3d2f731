// Extended Character-Specific AI Patterns - remaining character implementations
// Implements character-specific patterns for <PERSON><PERSON><PERSON>, Guile, Blanka, E.Honda, Zangief, and Dhalsim

use super::ai_character_patterns::{*, AIAttackType};
use sf2_types::*;
use crate::ai::ai_behavior_tree::AIAction;
use std::collections::HashMap;

impl CharacterAIPatternManager {
    /// Create Chun-Li-specific AI modifiers (speed and mobility focused)
    pub fn create_chun_li_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();
        
        // Chun-Li vs Zangief - keep distance, use speed
        opponent_modifiers.insert(FighterId::Zangief, OpponentSpecificModifier {
            aggression_modifier: 0.6,
            defense_modifier: 1.3,
            special_frequency_modifier: 1.2,
            distance_modifier: 60,
            reaction_time_modifier: -3,
        });
        
        // Chun-Li vs Dhalsim - aggressive rushdown
        opponent_modifiers.insert(FighterId::Dhalsim, OpponentSpecificModifier {
            aggression_modifier: 1.4,
            defense_modifier: 0.8,
            special_frequency_modifier: 0.9,
            distance_modifier: -35,
            reaction_time_modifier: -2,
        });
        
        CharacterAIModifiers {
            character_id: FighterId::ChunLi,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::LightKick,
                        weight: 0.5,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::Throw,
                        weight: 0.2,
                        min_difficulty: 3,
                        frame_advantage_required: -1,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.6,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.8,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.7,
                        min_difficulty: 2,
                        frame_advantage_required: -2,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.3,
                        min_difficulty: 3,
                        frame_advantage_required: -1,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.85);
                    patterns.insert(AIAttackType::MediumPunch, 0.9);
                    patterns.insert(AIAttackType::HeavyPunch, 0.95);
                    patterns.insert(AIAttackType::LightKick, 0.8);
                    patterns.insert(AIAttackType::MediumKick, 0.85);
                    patterns.insert(AIAttackType::HeavyKick, 0.9);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::JumpBackward,
                        weight: 0.4,
                        min_difficulty: 1,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::LightningLegs),
                        weight: 0.4,
                        min_difficulty: 3,
                    },
                    EscapePattern {
                        action: AIAction::WalkBackward,
                        weight: 0.2,
                        min_difficulty: 1,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Light)),
                        weight: 0.6,
                        frame_window: 2,
                    },
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::LightningLegs),
                        weight: 0.4,
                        frame_window: 3,
                    },
                ],
                throw_escape_weight: 0.8,
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![
                    ProjectilePattern {
                        special_move_id: SpecialMoveId::Kikoken,
                        optimal_distance: (80, 200),
                        frequency: 0.7,
                        min_difficulty: 2,
                    },
                ],
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::JumpForward,
                        distance_threshold: 100,
                        weight: 0.7,
                    },
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::Kikoken),
                        distance_threshold: 150,
                        weight: 0.3,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Light)),
                            AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium)),
                            AIAction::SpecialMove(SpecialMoveId::LightningLegs),
                        ],
                        min_success_rate: 0.7,
                        min_difficulty: 5,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::LightningLegs),
                        frame_window: 4,
                        probability: 0.6,
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: true,
                prefers_long_range: false,
                projectile_heavy: false,
                rushdown_style: true,
                zoning_style: false,
                grappler_style: false,
                has_charge_moves: false,
                has_command_grabs: false,
            },
        }
    }
    
    /// Create Guile-specific AI modifiers (charge character, defensive)
    pub fn create_guile_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();
        
        // Guile vs rushdown characters - extra defensive
        opponent_modifiers.insert(FighterId::Ken, OpponentSpecificModifier {
            aggression_modifier: 0.5,
            defense_modifier: 1.4,
            special_frequency_modifier: 1.3,
            distance_modifier: 80,
            reaction_time_modifier: -2,
        });
        
        opponent_modifiers.insert(FighterId::ChunLi, OpponentSpecificModifier {
            aggression_modifier: 0.6,
            defense_modifier: 1.3,
            special_frequency_modifier: 1.2,
            distance_modifier: 70,
            reaction_time_modifier: -1,
        });
        
        CharacterAIModifiers {
            character_id: FighterId::Guile,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::LightPunch,
                        weight: 0.4,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::Throw,
                        weight: 0.2,
                        min_difficulty: 3,
                        frame_advantage_required: -2,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.6,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.8,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch, // Flash Kick
                        weight: 0.9,
                        min_difficulty: 3,
                        frame_advantage_required: -3,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.95);
                    patterns.insert(AIAttackType::MediumPunch, 0.98);
                    patterns.insert(AIAttackType::HeavyPunch, 0.99);
                    patterns.insert(AIAttackType::LightKick, 0.9);
                    patterns.insert(AIAttackType::MediumKick, 0.95);
                    patterns.insert(AIAttackType::HeavyKick, 0.98);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::SonicBoom),
                        weight: 0.5,
                        min_difficulty: 2,
                    },
                    EscapePattern {
                        action: AIAction::WalkBackward,
                        weight: 0.3,
                        min_difficulty: 1,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::FlashKick),
                        weight: 0.2,
                        min_difficulty: 4,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::FlashKick),
                        weight: 0.6,
                        frame_window: 3,
                    },
                    CounterPattern {
                        action: AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Light)),
                        weight: 0.4,
                        frame_window: 2,
                    },
                ],
                throw_escape_weight: 0.9,
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![
                    ProjectilePattern {
                        special_move_id: SpecialMoveId::SonicBoom,
                        optimal_distance: (150, 400),
                        frequency: 0.9,
                        min_difficulty: 1,
                    },
                ],
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::SonicBoom),
                        distance_threshold: 200,
                        weight: 0.8,
                    },
                    AntiProjectilePattern {
                        action: AIAction::WalkBackward,
                        distance_threshold: 100,
                        weight: 0.2,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium)),
                            AIAction::SpecialMove(SpecialMoveId::SonicBoom),
                        ],
                        min_success_rate: 0.8,
                        min_difficulty: 4,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::FlashKick),
                        frame_window: 3,
                        probability: 0.95,
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: false,
                prefers_long_range: true,
                projectile_heavy: true,
                rushdown_style: false,
                zoning_style: true,
                grappler_style: false,
                has_charge_moves: true,
                has_command_grabs: false,
            },
        }
    }

    /// Create Blanka-specific AI modifiers (wild, unpredictable)
    pub fn create_blanka_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();

        // Blanka vs projectile characters - aggressive approach
        opponent_modifiers.insert(FighterId::Ryu, OpponentSpecificModifier {
            aggression_modifier: 1.3,
            defense_modifier: 0.7,
            special_frequency_modifier: 1.1,
            distance_modifier: -25,
            reaction_time_modifier: -1,
        });

        opponent_modifiers.insert(FighterId::Guile, OpponentSpecificModifier {
            aggression_modifier: 1.4,
            defense_modifier: 0.6,
            special_frequency_modifier: 1.2,
            distance_modifier: -30,
            reaction_time_modifier: -2,
        });

        CharacterAIModifiers {
            character_id: FighterId::Blanka,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::LightPunch,
                        weight: 0.3,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::Throw,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: -2,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.5,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.5,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.7,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch, // Electricity
                        weight: 0.8,
                        min_difficulty: 3,
                        frame_advantage_required: -2,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.6); // Very aggressive, blocks less
                    patterns.insert(AIAttackType::MediumPunch, 0.7);
                    patterns.insert(AIAttackType::HeavyPunch, 0.8);
                    patterns.insert(AIAttackType::LightKick, 0.5);
                    patterns.insert(AIAttackType::MediumKick, 0.6);
                    patterns.insert(AIAttackType::HeavyKick, 0.7);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::RollingAttack),
                        weight: 0.5,
                        min_difficulty: 2,
                    },
                    EscapePattern {
                        action: AIAction::JumpBackward,
                        weight: 0.3,
                        min_difficulty: 1,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::ElectricThunder),
                        weight: 0.2,
                        min_difficulty: 3,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::ElectricThunder),
                        weight: 0.5,
                        frame_window: 2,
                    },
                    CounterPattern {
                        action: AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium)),
                        weight: 0.3,
                        frame_window: 3,
                    },
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::RollingAttack),
                        weight: 0.2,
                        frame_window: 4,
                    },
                ],
                throw_escape_weight: 0.5, // Often doesn't escape throws
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![], // No projectiles
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::RollingAttack),
                        distance_threshold: 120,
                        weight: 0.7,
                    },
                    AntiProjectilePattern {
                        action: AIAction::JumpForward,
                        distance_threshold: 100,
                        weight: 0.3,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Kick(sf2_types::fighter::AttackStrength::Medium)),
                            AIAction::SpecialMove(SpecialMoveId::ElectricThunder),
                        ],
                        min_success_rate: 0.6,
                        min_difficulty: 4,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::ElectricThunder),
                        frame_window: 2,
                        probability: 0.7,
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: true,
                prefers_long_range: false,
                projectile_heavy: false,
                rushdown_style: true,
                zoning_style: false,
                grappler_style: false,
                has_charge_moves: false,
                has_command_grabs: false,
            },
        }
    }

    /// Create E.Honda-specific AI modifiers (sumo wrestler, close range)
    pub fn create_e_honda_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();

        // E.Honda vs long range characters - very aggressive
        opponent_modifiers.insert(FighterId::Dhalsim, OpponentSpecificModifier {
            aggression_modifier: 1.5,
            defense_modifier: 0.6,
            special_frequency_modifier: 1.3,
            distance_modifier: -50,
            reaction_time_modifier: -2,
        });

        opponent_modifiers.insert(FighterId::Guile, OpponentSpecificModifier {
            aggression_modifier: 1.3,
            defense_modifier: 0.7,
            special_frequency_modifier: 1.2,
            distance_modifier: -40,
            reaction_time_modifier: -1,
        });

        CharacterAIModifiers {
            character_id: FighterId::EHonda,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.4,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::Throw,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: -2,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumPunch,
                        weight: 0.2,
                        min_difficulty: 1,
                        frame_advantage_required: 1,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.7,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 1.0,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.8,
                        min_difficulty: 2,
                        frame_advantage_required: -2,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.7);
                    patterns.insert(AIAttackType::MediumPunch, 0.8);
                    patterns.insert(AIAttackType::HeavyPunch, 0.85);
                    patterns.insert(AIAttackType::LightKick, 0.65);
                    patterns.insert(AIAttackType::MediumKick, 0.75);
                    patterns.insert(AIAttackType::HeavyKick, 0.8);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::HundredHandSlap),
                        weight: 0.4,
                        min_difficulty: 2,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::SumoHeadbutt),
                        weight: 0.4,
                        min_difficulty: 3,
                    },
                    EscapePattern {
                        action: AIAction::WalkBackward,
                        weight: 0.2,
                        min_difficulty: 1,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::HundredHandSlap),
                        weight: 0.5,
                        frame_window: 3,
                    },
                    CounterPattern {
                        action: AIAction::Throw,
                        weight: 0.5,
                        frame_window: 2,
                    },
                ],
                throw_escape_weight: 0.6,
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![], // No projectiles
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::SumoHeadbutt),
                        distance_threshold: 150,
                        weight: 0.6,
                    },
                    AntiProjectilePattern {
                        action: AIAction::JumpForward,
                        distance_threshold: 120,
                        weight: 0.4,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Medium)),
                            AIAction::SpecialMove(SpecialMoveId::HundredHandSlap),
                        ],
                        min_success_rate: 0.7,
                        min_difficulty: 4,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::SumoHeadbutt),
                        frame_window: 3,
                        probability: 0.8,
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: true,
                prefers_long_range: false,
                projectile_heavy: false,
                rushdown_style: true,
                zoning_style: false,
                grappler_style: true,
                has_charge_moves: false,
                has_command_grabs: false,
            },
        }
    }
}
