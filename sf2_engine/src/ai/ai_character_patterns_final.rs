// Final Character-Specific AI Patterns - Zangief and Dhalsim implementations
// Implements the most specialized character patterns for grappler and long-range specialists

use super::ai_character_patterns::{*, AIAttackType};
use sf2_types::*;
use crate::ai::ai_behavior_tree::AIAction;
use std::collections::HashMap;

impl CharacterAIPatternManager {
    /// Create Zangief-specific AI modifiers (grappler, command grabs)
    pub fn create_zangief_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();
        
        // Zangief vs projectile characters - very aggressive approach
        opponent_modifiers.insert(FighterId::Ryu, OpponentSpecificModifier {
            aggression_modifier: 1.5,
            defense_modifier: 0.5,
            special_frequency_modifier: 0.8,
            distance_modifier: -60,
            reaction_time_modifier: -1,
        });
        
        opponent_modifiers.insert(FighterId::Guile, OpponentSpecificModifier {
            aggression_modifier: 1.6,
            defense_modifier: 0.4,
            special_frequency_modifier: 0.7,
            distance_modifier: -70,
            reaction_time_modifier: -2,
        });
        
        opponent_modifiers.insert(FighterId::Dhalsim, OpponentSpecificModifier {
            aggression_modifier: 1.8,
            defense_modifier: 0.3,
            special_frequency_modifier: 0.6,
            distance_modifier: -80,
            reaction_time_modifier: -3,
        });
        
        CharacterAIModifiers {
            character_id: FighterId::Zangief,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::Throw, // Command grabs
                        weight: 0.6,
                        min_difficulty: 2,
                        frame_advantage_required: -3,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.3,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::LightPunch,
                        weight: 0.1,
                        min_difficulty: 1,
                        frame_advantage_required: 1,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch,
                        weight: 0.7,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.8,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch, // Lariat
                        weight: 0.9,
                        min_difficulty: 3,
                        frame_advantage_required: -2,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.5); // Very aggressive, blocks minimally
                    patterns.insert(AIAttackType::MediumPunch, 0.6);
                    patterns.insert(AIAttackType::HeavyPunch, 0.7);
                    patterns.insert(AIAttackType::LightKick, 0.4);
                    patterns.insert(AIAttackType::MediumKick, 0.5);
                    patterns.insert(AIAttackType::HeavyKick, 0.6);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::LariatClothesline),
                        weight: 0.5,
                        min_difficulty: 2,
                    },
                    EscapePattern {
                        action: AIAction::JumpForward, // Aggressive escape
                        weight: 0.3,
                        min_difficulty: 1,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::ScremPileDriver),
                        weight: 0.2,
                        min_difficulty: 4,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::ScremPileDriver),
                        weight: 0.6,
                        frame_window: 2,
                    },
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::LariatClothesline),
                        weight: 0.4,
                        frame_window: 3,
                    },
                ],
                throw_escape_weight: 0.3, // Rarely escapes throws, prefers to grab
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![], // No projectiles
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::JumpForward,
                        distance_threshold: 200,
                        weight: 0.6,
                    },
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::LariatClothesline),
                        distance_threshold: 100,
                        weight: 0.4,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Light)),
                            AIAction::SpecialMove(SpecialMoveId::ScremPileDriver),
                        ],
                        min_success_rate: 0.8,
                        min_difficulty: 6,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::LariatClothesline),
                        frame_window: 2,
                        probability: 0.9,
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: true,
                prefers_long_range: false,
                projectile_heavy: false,
                rushdown_style: true,
                zoning_style: false,
                grappler_style: true,
                has_charge_moves: false,
                has_command_grabs: true,
            },
        }
    }
    
    /// Create Dhalsim-specific AI modifiers (long range specialist)
    pub fn create_dhalsim_modifiers() -> CharacterAIModifiers {
        let mut opponent_modifiers = HashMap::new();
        
        // Dhalsim vs rushdown characters - extreme defensive
        opponent_modifiers.insert(FighterId::Ken, OpponentSpecificModifier {
            aggression_modifier: 0.3,
            defense_modifier: 1.6,
            special_frequency_modifier: 1.4,
            distance_modifier: 100,
            reaction_time_modifier: -2,
        });
        
        opponent_modifiers.insert(FighterId::ChunLi, OpponentSpecificModifier {
            aggression_modifier: 0.4,
            defense_modifier: 1.5,
            special_frequency_modifier: 1.3,
            distance_modifier: 90,
            reaction_time_modifier: -1,
        });
        
        opponent_modifiers.insert(FighterId::Zangief, OpponentSpecificModifier {
            aggression_modifier: 0.2,
            defense_modifier: 1.8,
            special_frequency_modifier: 1.6,
            distance_modifier: 120,
            reaction_time_modifier: -3,
        });
        
        CharacterAIModifiers {
            character_id: FighterId::Dhalsim,
            opponent_modifiers,
            attack_patterns: AttackPatternPreferences {
                close_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::LightPunch,
                        weight: 0.6,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::LightKick,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                medium_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::MediumPunch,
                        weight: 0.6,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::MediumKick,
                        weight: 0.4,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                long_range: vec![
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch, // Long range stretchy limbs
                        weight: 0.7,
                        min_difficulty: 1,
                        frame_advantage_required: 0,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyKick,
                        weight: 0.3,
                        min_difficulty: 2,
                        frame_advantage_required: 1,
                    },
                ],
                anti_air: vec![
                    AttackPreference {
                        attack_type: AIAttackType::MediumPunch,
                        weight: 0.8,
                        min_difficulty: 2,
                        frame_advantage_required: -1,
                    },
                    AttackPreference {
                        attack_type: AIAttackType::HeavyPunch, // Yoga Fire
                        weight: 0.2,
                        min_difficulty: 3,
                        frame_advantage_required: -2,
                    },
                ],
            },
            defensive_patterns: DefensivePatternPreferences {
                block_patterns: {
                    let mut patterns = HashMap::new();
                    patterns.insert(AIAttackType::LightPunch, 0.99); // Extremely defensive
                    patterns.insert(AIAttackType::MediumPunch, 0.99);
                    patterns.insert(AIAttackType::HeavyPunch, 1.0);
                    patterns.insert(AIAttackType::LightKick, 0.98);
                    patterns.insert(AIAttackType::MediumKick, 0.99);
                    patterns.insert(AIAttackType::HeavyKick, 0.99);
                    patterns
                },
                escape_patterns: vec![
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::YogaFlame),
                        weight: 0.5,
                        min_difficulty: 3,
                    },
                    EscapePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::YogaFire),
                        weight: 0.3,
                        min_difficulty: 2,
                    },
                    EscapePattern {
                        action: AIAction::WalkBackward,
                        weight: 0.2,
                        min_difficulty: 1,
                    },
                ],
                counter_patterns: vec![
                    CounterPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::YogaFire),
                        weight: 0.6,
                        frame_window: 4,
                    },
                    CounterPattern {
                        action: AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Heavy)),
                        weight: 0.4,
                        frame_window: 3,
                    },
                ],
                throw_escape_weight: 0.95, // Almost always escapes throws
            },
            special_move_patterns: SpecialMovePatternPreferences {
                projectile_patterns: vec![
                    ProjectilePattern {
                        special_move_id: SpecialMoveId::YogaFire,
                        optimal_distance: (200, 500),
                        frequency: 0.8,
                        min_difficulty: 1,
                    },
                ],
                anti_projectile_patterns: vec![
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::YogaFire),
                        distance_threshold: 300,
                        weight: 0.7,
                    },
                    AntiProjectilePattern {
                        action: AIAction::SpecialMove(SpecialMoveId::YogaFlame),
                        distance_threshold: 150,
                        weight: 0.3,
                    },
                ],
                combo_patterns: vec![
                    ComboPattern {
                        sequence: vec![
                            AIAction::Attack(sf2_types::fighter::AttackType::Punch(sf2_types::fighter::AttackStrength::Heavy)),
                            AIAction::SpecialMove(SpecialMoveId::YogaFire),
                        ],
                        min_success_rate: 0.8,
                        min_difficulty: 4,
                    },
                ],
                reversal_patterns: vec![
                    ReversalPattern {
                        action: AIAction::SpecialMove(SpecialMoveId::YogaFlame),
                        frame_window: 4,
                        probability: 0.9,
                    },
                ],
            },
            ai_flags: CharacterAIFlags {
                prefers_close_combat: false,
                prefers_long_range: true,
                projectile_heavy: true,
                rushdown_style: false,
                zoning_style: true,
                grappler_style: false,
                has_charge_moves: false,
                has_command_grabs: false,
            },
        }
    }
}
