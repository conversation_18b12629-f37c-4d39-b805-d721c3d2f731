// AI Core Architecture - foundational AI system matching C99 implementation
// This module provides the core AI state machines, decision trees, and Bevy ECS integration

use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use sf2_types::*;

/// AI Mode 1 - Primary AI state machine (matching C99 AIMode1)
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AIMode1 {
    Init = 0,
    Active = 2,
    Strategy = 4,
    Transition = 6,
}

/// AI Mode 2 - Secondary AI state machine (matching C99 AIMode2)
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AIMode2 {
    Init = 0,
    Timer = 2,
    NoFireball = 4,
    OppJump = 6,
    TooClose = 8,
    HeadHittable = 10,
}

/// AI Strategy Types (matching C99 strategy constants)
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, E<PERSON>, <PERSON>h, Serialize, Deserialize)]
pub enum AIStrategyType {
    StandStill = 0,
    Attack = 1,
    Jump = 2,
    LongWalk = 3,
    Kick = 4,
    Block = 5,
    Special = 6,
    Throw = 7,
}

/// AI Aggressive/Defensive State (matching C99 AIAgressive/AIForceDefensive)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AIBehaviorState {
    Aggressive0,    // AIAgressive = false, normal aggressive
    Aggressive1,    // AIAgressive = true, enhanced aggressive
    Defensive,      // AIForceDefensive = true
}

/// AI Parameters (matching C99 AIParam1-4)
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct AIParameters {
    pub param1: u8,
    pub param2: i8,
    pub param3: i8,
    pub param4: i8,
}

/// AI Strategy Execution State
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIStrategyState {
    pub current_strategy: AIStrategyType,
    pub parameters: AIParameters,
    pub strategy_index: u16,
    pub strategy_data: Vec<u8>,
    pub execution_timer: u16,
}

/// AI Timers (matching C99 AITimers array)
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct AITimers {
    pub timers: [u8; 8],
    pub main_timer: u8,
    pub threat_check_timer: u8,
    pub agg_timer0: u8,
    pub agg_timer1: u8,
}

/// AI Flags and Signals (matching C99 AI boolean flags)
#[derive(Debug, Clone, Copy, Default, Serialize, Deserialize)]
pub struct AIFlags {
    pub sig_attack: bool,           // AISigAttack
    pub sig_special: bool,          // AISigSpecial
    pub volley: bool,               // AIVolley
    pub force_defensive: bool,      // AIForceDefensive
    pub allow_aggressive: bool,     // AIAllowAggressive
    pub threat_found: bool,         // AIThreatFound
    pub can_jump_attack: bool,      // AICanJumpAttack
    pub is_within_bounds: bool,     // IsWithinBounds
}

/// AI Control Signals (matching C99 CompDo* variables)
#[derive(Debug, Clone, Copy, Default, Serialize, Deserialize)]
pub struct AIControlSignals {
    pub do_throw: bool,             // CompDoThrow
    pub do_block: bool,             // CompDoBlock
    pub do_jump: bool,              // CompDoJump
    pub do_block_stun: bool,        // CompDoBlockStun
    pub immune: u8,                 // CompImmune
}

/// AI Walk and Movement Control
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct AIMovementControl {
    pub walk_direction: u8,         // AIWalkDirection (STEP_* constants)
    pub walk_target: i16,           // AIWalkTarget
    pub jump_selection: i16,        // AIJumpSel
    pub too_close_distance: i16,    // AITooClose
    pub opp_jump_height: i16,       // AIOppJumpHeight
}

/// Core AI Component for Bevy ECS
#[derive(Component, Debug, Clone, Serialize, Deserialize)]
pub struct AICore {
    /// Primary AI state machine
    pub mode1: AIMode1,
    /// Secondary AI state machine  
    pub mode2: AIMode2,
    /// Current behavior state (aggressive/defensive)
    pub behavior_state: AIBehaviorState,
    /// Strategy execution state
    pub strategy_state: AIStrategyState,
    /// AI timers
    pub timers: AITimers,
    /// AI flags and signals
    pub flags: AIFlags,
    /// Control signals for actions
    pub control_signals: AIControlSignals,
    /// Movement control
    pub movement: AIMovementControl,
    /// Saved state for defensive mode restoration
    pub saved_state: Option<Box<AICore>>,
    /// Multi-hit counter for combo attacks
    pub multi_count: u8,
    /// Dice roll counter for volley attacks
    pub dice_roll_count: i8,
    /// Button strength for attacks
    pub button_strength: i8,
    /// Punch/kick selection
    pub punch_kick: i8,
    /// Start again flag for strategy restart
    pub start_again: u8,
    /// Threat check mode
    pub threat_check_mode: u8,
    /// Yoke attack tracking
    pub yoke_attacking_me: u8,
    pub yoke_saved: i8,
}

impl Default for AICore {
    fn default() -> Self {
        Self {
            mode1: AIMode1::Init,
            mode2: AIMode2::Init,
            behavior_state: AIBehaviorState::Aggressive0,
            strategy_state: AIStrategyState {
                current_strategy: AIStrategyType::StandStill,
                parameters: AIParameters {
                    param1: 0,
                    param2: 0,
                    param3: 0,
                    param4: 0,
                },
                strategy_index: 0,
                strategy_data: Vec::new(),
                execution_timer: 0,
            },
            timers: AITimers {
                timers: [0; 8],
                main_timer: 0,
                threat_check_timer: 0,
                agg_timer0: 0,
                agg_timer1: 0,
            },
            flags: AIFlags::default(),
            control_signals: AIControlSignals::default(),
            movement: AIMovementControl {
                walk_direction: 0,
                walk_target: 0,
                jump_selection: 0,
                too_close_distance: 0,
                opp_jump_height: 0,
            },
            saved_state: None,
            multi_count: 0,
            dice_roll_count: 0,
            button_strength: 0,
            punch_kick: 0,
            start_again: 0,
            threat_check_mode: 2, // Initially 2 as per C99
            yoke_attacking_me: 0,
            yoke_saved: -1, // Will not match initially
        }
    }
}

impl AICore {
    /// Initialize AI for a new player (matching C99 AIInitPlayer)
    pub fn init_player(&mut self, fighter_id: FighterId, difficulty: u8) {
        self.mode1 = AIMode1::Init;
        self.mode2 = AIMode2::Init;
        self.behavior_state = AIBehaviorState::Aggressive0;
        self.threat_check_mode = 2;
        self.flags = AIFlags::default();
        self.control_signals = AIControlSignals::default();
        
        // Reset all timers
        self.timers = AITimers {
            timers: [0; 8],
            main_timer: 0,
            threat_check_timer: 0,
            agg_timer0: 0,
            agg_timer1: 0,
        };
        
        // Initialize movement control
        self.movement.walk_direction = 0; // STEP_STILL
        
        // Clear saved state
        self.saved_state = None;
        self.yoke_saved = -1;
    }
    
    /// Reset AI state (matching C99 _AIResetState)
    pub fn reset_state(&mut self) {
        self.mode1 = AIMode1::Init;
        self.mode2 = AIMode2::Init;
    }
    
    /// Prepare new AI state (matching C99 AIPrepareNewState)
    pub fn prepare_new_state(&mut self, strategy_type: u8, time_remain: u8) {
        match strategy_type {
            0 => {
                if self.flags.force_defensive || self.start_again == time_remain {
                    self.mode2 = AIMode2::Init;
                    // Don't reset other state
                } else {
                    self.start_again = 0;
                    self.reset_state();
                }
            }
            2 => {
                self.reset_state();
            }
            4 => {
                // Difficulty-based decision would go here
                // For now, simplified logic
                if self.flags.force_defensive || self.start_again == time_remain {
                    self.mode2 = AIMode2::Init;
                } else {
                    self.start_again = 0;
                    self.reset_state();
                }
            }
            _ => {
                warn!("Unknown AI strategy type: {}", strategy_type);
                self.reset_state();
            }
        }
    }
    
    /// Save current state for defensive mode restoration
    pub fn save_state(&mut self) {
        self.saved_state = Some(Box::new(self.clone()));
    }
    
    /// Restore saved state (for defensive mode)
    pub fn restore_state(&mut self) -> bool {
        if let Some(saved) = self.saved_state.take() {
            *self = *saved;
            true
        } else {
            false
        }
    }
}

/// AI Resource for global AI configuration
#[derive(Resource, Debug, Clone, Serialize, Deserialize)]
pub struct AIResource {
    /// Global AI difficulty scaling
    pub global_difficulty: u8,
    /// AI debug mode enabled
    pub debug_mode: bool,
    /// AI update frequency (frames)
    pub update_frequency: u8,
    /// Environment variable overrides
    pub env_overrides: HashMap<String, String>,
}

impl Default for AIResource {
    fn default() -> Self {
        Self {
            global_difficulty: 16, // Default medium difficulty
            debug_mode: false,
            update_frequency: 1, // Update every frame
            env_overrides: HashMap::new(),
        }
    }
}
