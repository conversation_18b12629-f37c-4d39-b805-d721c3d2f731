// AI Debug System - comprehensive AI debugging and tuning tools
// Provides real-time AI state visualization, performance metrics, and tuning capabilities

use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use sf2_types::*;
use crate::ai::ai_core::*;
use crate::ai::ai_strategy::*;
use crate::ai::ai_difficulty::*;
use crate::ai::ai_threat::*;
use std::collections::{HashMap, VecDeque};

/// AI Debug Component for Bevy ECS
#[derive(Component, Debug, Clone, Serialize, Deserialize)]
pub struct AIDebugInfo {
    /// Debug mode enabled
    pub enabled: bool,
    /// Current debug level (0 = off, 1 = basic, 2 = detailed, 3 = verbose)
    pub debug_level: u8,
    /// Performance metrics
    pub performance_metrics: AIPerformanceMetrics,
    /// Decision history for analysis
    pub decision_history: VecDeque<AIDecision>,
    /// Strategy execution log
    pub strategy_log: VecDeque<StrategyLogEntry>,
    /// Threat assessment log
    pub threat_log: VecDeque<ThreatLogEntry>,
    /// Frame-by-frame state snapshots
    pub state_snapshots: VecDeque<AIStateSnapshot>,
    /// Maximum history length
    pub max_history_length: usize,
    /// Debug visualization settings
    pub visualization: AIDebugVisualization,
}

/// AI Performance Metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIPerformanceMetrics {
    /// Total decisions made
    pub total_decisions: u64,
    /// Successful decisions (led to desired outcome)
    pub successful_decisions: u64,
    /// Average decision time (microseconds)
    pub avg_decision_time_us: f64,
    /// Strategy execution success rate
    pub strategy_success_rate: f32,
    /// Threat detection accuracy
    pub threat_detection_accuracy: f32,
    /// Reaction time statistics
    pub reaction_time_stats: ReactionTimeStats,
    /// Win/loss statistics
    pub win_loss_stats: WinLossStats,
}

/// AI Decision record for analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDecision {
    /// Frame when decision was made
    pub frame: u64,
    /// Decision type
    pub decision_type: AIDecisionType,
    /// Input factors that influenced the decision
    pub input_factors: DecisionInputs,
    /// Confidence level (0.0 to 1.0)
    pub confidence: f32,
    /// Outcome of the decision (filled in later)
    pub outcome: Option<DecisionOutcome>,
    /// Time taken to make decision (microseconds)
    pub decision_time_us: u64,
}

/// Types of AI decisions
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AIDecisionType {
    Attack,
    Defend,
    Move,
    Jump,
    Special,
    Throw,
    Block,
    Wait,
    StrategyChange,
}

/// Input factors for decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecisionInputs {
    /// Distance to opponent
    pub opponent_distance: i16,
    /// Opponent state
    pub opponent_state: String,
    /// Current threat level
    pub threat_level: f32,
    /// AI health percentage
    pub ai_health_percent: f32,
    /// Opponent health percentage
    pub opponent_health_percent: f32,
    /// Time remaining in round
    pub time_remaining: u16,
    /// Current AI strategy
    pub current_strategy: String,
}

/// Decision outcome
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum DecisionOutcome {
    Success,
    Failure,
    Neutral,
    Interrupted,
}

/// Strategy execution log entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyLogEntry {
    /// Frame when strategy was executed
    pub frame: u64,
    /// Strategy name
    pub strategy_name: String,
    /// Bytecode instruction executed
    pub instruction: String,
    /// Parameters used
    pub parameters: Vec<u8>,
    /// Execution result
    pub result: StrategyExecutionResult,
}

/// Strategy execution result
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum StrategyExecutionResult {
    Success,
    Failed,
    Interrupted,
    Completed,
}

/// Threat assessment log entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatLogEntry {
    /// Frame when threat was assessed
    pub frame: u64,
    /// Threat level calculated
    pub threat_level: f32,
    /// Threat type detected
    pub threat_type: String,
    /// Distance to threat
    pub threat_distance: i16,
    /// Response chosen
    pub response: String,
}

/// AI state snapshot for frame-by-frame analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIStateSnapshot {
    /// Frame number
    pub frame: u64,
    /// AI mode states
    pub mode1: String,
    pub mode2: String,
    /// Current strategy
    pub current_strategy: String,
    /// AI flags
    pub flags: HashMap<String, bool>,
    /// Timer values
    pub timers: HashMap<String, u8>,
    /// Position and movement
    pub position: Vec2,
    pub velocity: Vec2,
}

/// Reaction time statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReactionTimeStats {
    /// Average reaction time (frames)
    pub avg_reaction_time: f32,
    /// Minimum reaction time
    pub min_reaction_time: u16,
    /// Maximum reaction time
    pub max_reaction_time: u16,
    /// Reaction time samples
    pub samples: VecDeque<u16>,
}

/// Win/loss statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WinLossStats {
    /// Total rounds played
    pub total_rounds: u32,
    /// Rounds won
    pub rounds_won: u32,
    /// Rounds lost
    pub rounds_lost: u32,
    /// Win rate
    pub win_rate: f32,
    /// Average round duration
    pub avg_round_duration: f32,
}

/// Debug visualization settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDebugVisualization {
    /// Show AI state overlay
    pub show_state_overlay: bool,
    /// Show threat zones
    pub show_threat_zones: bool,
    /// Show decision tree
    pub show_decision_tree: bool,
    /// Show strategy execution
    pub show_strategy_execution: bool,
    /// Show performance metrics
    pub show_performance_metrics: bool,
    /// Overlay position
    pub overlay_position: Vec2,
    /// Text color
    pub text_color: Color,
    /// Background color
    pub background_color: Color,
}

impl Default for AIDebugInfo {
    fn default() -> Self {
        Self {
            enabled: false,
            debug_level: 0,
            performance_metrics: AIPerformanceMetrics::default(),
            decision_history: VecDeque::new(),
            strategy_log: VecDeque::new(),
            threat_log: VecDeque::new(),
            state_snapshots: VecDeque::new(),
            max_history_length: 300, // 5 seconds at 60fps
            visualization: AIDebugVisualization::default(),
        }
    }
}

impl Default for AIPerformanceMetrics {
    fn default() -> Self {
        Self {
            total_decisions: 0,
            successful_decisions: 0,
            avg_decision_time_us: 0.0,
            strategy_success_rate: 0.0,
            threat_detection_accuracy: 0.0,
            reaction_time_stats: ReactionTimeStats::default(),
            win_loss_stats: WinLossStats::default(),
        }
    }
}

impl Default for ReactionTimeStats {
    fn default() -> Self {
        Self {
            avg_reaction_time: 15.0,
            min_reaction_time: u16::MAX,
            max_reaction_time: 0,
            samples: VecDeque::new(),
        }
    }
}

impl Default for WinLossStats {
    fn default() -> Self {
        Self {
            total_rounds: 0,
            rounds_won: 0,
            rounds_lost: 0,
            win_rate: 0.0,
            avg_round_duration: 0.0,
        }
    }
}

impl Default for AIDebugVisualization {
    fn default() -> Self {
        Self {
            show_state_overlay: false,
            show_threat_zones: false,
            show_decision_tree: false,
            show_strategy_execution: false,
            show_performance_metrics: false,
            overlay_position: Vec2::new(10.0, 10.0),
            text_color: Color::WHITE,
            background_color: Color::srgba(0.0, 0.0, 0.0, 0.7),
        }
    }
}

impl AIDebugInfo {
    /// Initialize debug system from environment variables
    pub fn init_from_env() -> Self {
        let mut debug_info = Self::default();
        
        // Check for debug environment variables
        if let Ok(debug_level) = std::env::var("AI_DEBUG_LEVEL") {
            if let Ok(level) = debug_level.parse::<u8>() {
                debug_info.debug_level = level.min(3);
                debug_info.enabled = level > 0;
            }
        }
        
        if let Ok(_) = std::env::var("AI_DEBUG_ENABLED") {
            debug_info.enabled = true;
            if debug_info.debug_level == 0 {
                debug_info.debug_level = 1;
            }
        }
        
        // Configure visualization from environment
        if let Ok(_) = std::env::var("AI_DEBUG_SHOW_STATE") {
            debug_info.visualization.show_state_overlay = true;
        }
        
        if let Ok(_) = std::env::var("AI_DEBUG_SHOW_THREATS") {
            debug_info.visualization.show_threat_zones = true;
        }
        
        if let Ok(_) = std::env::var("AI_DEBUG_SHOW_STRATEGY") {
            debug_info.visualization.show_strategy_execution = true;
        }
        
        debug_info
    }
    
    /// Record an AI decision
    pub fn record_decision(&mut self, decision: AIDecision, frame: u64) {
        if !self.enabled || self.debug_level < 1 {
            return;
        }
        
        self.decision_history.push_back(decision);
        self.performance_metrics.total_decisions += 1;
        
        // Maintain history size
        if self.decision_history.len() > self.max_history_length {
            self.decision_history.pop_front();
        }
    }
    
    /// Record strategy execution
    pub fn record_strategy_execution(&mut self, entry: StrategyLogEntry) {
        if !self.enabled || self.debug_level < 2 {
            return;
        }
        
        self.strategy_log.push_back(entry);
        
        if self.strategy_log.len() > self.max_history_length {
            self.strategy_log.pop_front();
        }
    }
    
    /// Record threat assessment
    pub fn record_threat_assessment(&mut self, entry: ThreatLogEntry) {
        if !self.enabled || self.debug_level < 2 {
            return;
        }
        
        self.threat_log.push_back(entry);
        
        if self.threat_log.len() > self.max_history_length {
            self.threat_log.pop_front();
        }
    }
    
    /// Take state snapshot
    pub fn take_state_snapshot(&mut self, snapshot: AIStateSnapshot) {
        if !self.enabled || self.debug_level < 3 {
            return;
        }
        
        self.state_snapshots.push_back(snapshot);
        
        if self.state_snapshots.len() > self.max_history_length {
            self.state_snapshots.pop_front();
        }
    }
    
    /// Update performance metrics
    pub fn update_performance_metrics(&mut self, decision_outcome: DecisionOutcome, decision_time_us: u64) {
        if !self.enabled {
            return;
        }
        
        // Update decision success rate
        if decision_outcome == DecisionOutcome::Success {
            self.performance_metrics.successful_decisions += 1;
        }
        
        // Update average decision time
        let total_time = self.performance_metrics.avg_decision_time_us * (self.performance_metrics.total_decisions - 1) as f64;
        self.performance_metrics.avg_decision_time_us = (total_time + decision_time_us as f64) / self.performance_metrics.total_decisions as f64;
    }
    
    /// Update reaction time statistics
    pub fn update_reaction_time(&mut self, reaction_time_frames: u16) {
        if !self.enabled {
            return;
        }
        
        let stats = &mut self.performance_metrics.reaction_time_stats;
        
        stats.samples.push_back(reaction_time_frames);
        if stats.samples.len() > 100 {
            stats.samples.pop_front();
        }
        
        stats.min_reaction_time = stats.min_reaction_time.min(reaction_time_frames);
        stats.max_reaction_time = stats.max_reaction_time.max(reaction_time_frames);
        
        // Calculate average
        let sum: u32 = stats.samples.iter().map(|&x| x as u32).sum();
        stats.avg_reaction_time = sum as f32 / stats.samples.len() as f32;
    }
    
    /// Update win/loss statistics
    pub fn update_win_loss_stats(&mut self, won: bool, round_duration_frames: u32) {
        if !self.enabled {
            return;
        }
        
        let stats = &mut self.performance_metrics.win_loss_stats;
        
        stats.total_rounds += 1;
        if won {
            stats.rounds_won += 1;
        } else {
            stats.rounds_lost += 1;
        }
        
        stats.win_rate = stats.rounds_won as f32 / stats.total_rounds as f32;
        
        // Update average round duration
        let total_duration = stats.avg_round_duration * (stats.total_rounds - 1) as f32;
        stats.avg_round_duration = (total_duration + round_duration_frames as f32) / stats.total_rounds as f32;
    }
    
    /// Generate debug report
    pub fn generate_debug_report(&self) -> String {
        if !self.enabled {
            return "AI Debug disabled".to_string();
        }
        
        let mut report = String::new();
        
        report.push_str("=== AI Debug Report ===\n");
        report.push_str(&format!("Debug Level: {}\n", self.debug_level));
        report.push_str(&format!("Total Decisions: {}\n", self.performance_metrics.total_decisions));
        report.push_str(&format!("Success Rate: {:.2}%\n", 
            (self.performance_metrics.successful_decisions as f32 / self.performance_metrics.total_decisions as f32) * 100.0));
        report.push_str(&format!("Avg Decision Time: {:.2}μs\n", self.performance_metrics.avg_decision_time_us));
        report.push_str(&format!("Avg Reaction Time: {:.1} frames\n", self.performance_metrics.reaction_time_stats.avg_reaction_time));
        report.push_str(&format!("Win Rate: {:.2}%\n", self.performance_metrics.win_loss_stats.win_rate * 100.0));
        
        if self.debug_level >= 2 {
            report.push_str("\n=== Recent Decisions ===\n");
            for decision in self.decision_history.iter().rev().take(5) {
                report.push_str(&format!("Frame {}: {:?} (confidence: {:.2})\n", 
                    decision.frame, decision.decision_type, decision.confidence));
            }
        }
        
        if self.debug_level >= 3 {
            report.push_str("\n=== Recent Strategy Executions ===\n");
            for entry in self.strategy_log.iter().rev().take(5) {
                report.push_str(&format!("Frame {}: {} - {} ({:?})\n", 
                    entry.frame, entry.strategy_name, entry.instruction, entry.result));
            }
        }
        
        report
    }
    
    /// Export debug data to JSON
    pub fn export_to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string_pretty(self)
    }
    
    /// Clear all debug history
    pub fn clear_history(&mut self) {
        self.decision_history.clear();
        self.strategy_log.clear();
        self.threat_log.clear();
        self.state_snapshots.clear();
        self.performance_metrics = AIPerformanceMetrics::default();
    }
}
