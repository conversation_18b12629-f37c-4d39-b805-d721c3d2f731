// AI Strategy System - behavior trees and strategy execution
// Implements the sophisticated AI bytecode interpretation and strategy selection from C99

use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use sf2_types::*;
use crate::ai::ai_core::*;

/// AI Strategy Bytecode Instructions (matching C99 AIB_* constants)
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AIBytecode {
    // Basic strategy types
    Restart = 0x00,
    StandStill = 0x02,
    Attack = 0x04,
    Jump = 0x06,
    LongWalk = 0x08,
    Kick = 0x0A,
    Block = 0x0C,
    
    // Control flow
    Type2 = 0x0E,
    SetBlock = 0x10,
    Label = 0x12,
    
    // Conditional instructions
    NotWithin = 0x14,
    IfOppJump = 0x16,
    IfNoFireball = 0x18,
    IfHeadHittable = 0x1A,
    
    // Advanced instructions
    PowerMove = 0x1C,
    Throw = 0x1E,
    Volley = 0x20,
    Immune = 0x22,
    
    // High-bit instructions (0x80+)
    EndScript = 0x80,
    DefensiveHigh = 0x82,
}

/// AI Strategy Flags (matching C99 AIB_* bit flags)
pub mod ai_flags {
    pub const AIB_THROW: u8 = 0x80;
    pub const AIB_VOLLEY: u8 = 0x40;
    pub const AIB_WALKAWAY: u8 = 0x80;
    pub const AIB_SHIFT: u8 = 0x10;
    pub const AIB_POWERMOVE: u8 = 0x80;
    pub const AIB_WITHIN: u8 = 0x01;
}

/// AI Strategy Data Structure (matching C99 aggressive/defensive strategy tables)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIStrategyData {
    /// Strategy bytecode sequence
    pub bytecode: Vec<u8>,
    /// Strategy name for debugging
    pub name: String,
    /// Character-specific flag
    pub character_id: FighterId,
    /// Difficulty range this strategy applies to
    pub min_difficulty: u8,
    pub max_difficulty: u8,
}

/// AI Strategy Table (matching C99 AIAggTable structure)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIStrategyTable {
    /// Time-based strategy selection (32 entries matching C99)
    pub time_selectors: [u8; 32],
    /// Strategy data pointers (16 entries matching C99)
    pub strategies: Vec<AIStrategyData>,
}

/// AI Strategy Executor Component
#[derive(Component, Debug, Clone, Serialize, Deserialize)]
pub struct AIStrategyExecutor {
    /// Current aggressive strategy table (Agg0)
    pub agg0_table: Option<AIStrategyTable>,
    /// Current aggressive strategy table (Agg1)
    pub agg1_table: Option<AIStrategyTable>,
    /// Current defensive strategy table
    pub defensive_table: Option<AIStrategyTable>,
    /// Current strategy execution state
    pub current_strategy: Option<AIStrategyData>,
    /// Bytecode instruction pointer
    pub instruction_pointer: usize,
    /// Strategy execution stack for nested calls
    pub execution_stack: Vec<usize>,
    /// Strategy parameters cache
    pub parameter_cache: [u8; 16],
}

impl Default for AIStrategyExecutor {
    fn default() -> Self {
        Self {
            agg0_table: None,
            agg1_table: None,
            defensive_table: None,
            current_strategy: None,
            instruction_pointer: 0,
            execution_stack: Vec::new(),
            parameter_cache: [0; 16],
        }
    }
}

impl AIStrategyExecutor {
    /// Load strategy tables for a character (matching C99 _AILookupStrategy)
    pub fn load_character_strategies(&mut self, character_id: FighterId, opponent_id: FighterId) {
        // This would load from character-specific strategy data
        // For now, create placeholder tables
        self.agg0_table = Some(self.create_default_aggressive_table(character_id));
        self.agg1_table = Some(self.create_default_aggressive_table(character_id));
        self.defensive_table = Some(self.create_default_defensive_table(character_id));
    }
    
    /// Begin aggressive strategy execution (matching C99 AIBeginAgg0/1)
    pub fn begin_aggressive(&mut self, ai_core: &mut AICore, enhanced: bool) {
        let table = if enhanced {
            &self.agg1_table
        } else {
            &self.agg0_table
        };
        
        if let Some(strategy_table) = table {
            // Select strategy based on time remaining (matching C99 logic)
            let time_index = (ai_core.start_again as usize).min(31);
            let strategy_index = strategy_table.time_selectors[time_index] as usize;
            
            if strategy_index < strategy_table.strategies.len() {
                self.current_strategy = Some(strategy_table.strategies[strategy_index].clone());
                self.instruction_pointer = 0;
                ai_core.mode1 = AIMode1::Strategy;
            }
        }
    }
    
    /// Begin defensive strategy execution (matching C99 AIBeginDef)
    pub fn begin_defensive(&mut self, ai_core: &mut AICore) {
        if let Some(defensive_table) = &self.defensive_table {
            // Defensive strategy selection logic
            let strategy_index = 0; // Simplified for now
            if strategy_index < defensive_table.strategies.len() {
                self.current_strategy = Some(defensive_table.strategies[strategy_index].clone());
                self.instruction_pointer = 0;
                ai_core.mode1 = AIMode1::Strategy;
            }
        }
    }
    
    /// Execute current strategy (matching C99 _AIBeginStrategy)
    pub fn execute_strategy(&mut self, ai_core: &mut AICore) -> bool {
        if let Some(strategy) = &self.current_strategy {
            if self.instruction_pointer >= strategy.bytecode.len() {
                return false; // Strategy complete
            }
            
            let instruction = strategy.bytecode[self.instruction_pointer];
            self.instruction_pointer += 1;
            
            match AIBytecode::from_u8(instruction) {
                Some(AIBytecode::StandStill) => {
                    self.execute_standstill(ai_core);
                }
                Some(AIBytecode::Attack) => {
                    self.execute_attack(ai_core);
                }
                Some(AIBytecode::Jump) => {
                    self.execute_jump(ai_core);
                }
                Some(AIBytecode::LongWalk) => {
                    self.execute_longwalk(ai_core);
                }
                Some(AIBytecode::Kick) => {
                    self.execute_kick(ai_core);
                }
                Some(AIBytecode::Block) => {
                    self.execute_block(ai_core);
                }
                Some(AIBytecode::Restart) => {
                    self.instruction_pointer = 0; // Restart strategy
                }
                Some(AIBytecode::EndScript) => {
                    return false; // End strategy execution
                }
                _ => {
                    warn!("Unknown AI bytecode instruction: 0x{:02X}", instruction);
                }
            }
            
            true
        } else {
            false
        }
    }
    
    /// Read next byte from strategy (matching C99 _AIReadByte)
    fn read_byte(&mut self) -> Option<u8> {
        if let Some(strategy) = &self.current_strategy {
            if self.instruction_pointer < strategy.bytecode.len() {
                let byte = strategy.bytecode[self.instruction_pointer];
                self.instruction_pointer += 1;
                Some(byte)
            } else {
                None
            }
        } else {
            None
        }
    }
    
    /// Execute STANDSTILL strategy (matching C99 _AIStratStandStill)
    fn execute_standstill(&mut self, ai_core: &mut AICore) {
        if let Some(param1) = self.read_byte() {
            ai_core.strategy_state.parameters.param1 = param1;
            
            // Load additional parameters based on param1 value
            if param1 == 0x81 || param1 == 0xc0 {
                if let Some(param2) = self.read_byte() {
                    ai_core.strategy_state.parameters.param2 = param2 as i8;
                }
            }
            
            // Set up timer-based execution
            if param1 & 0x80 != 0 {
                // Complex parameter handling
                self.handle_complex_standstill(ai_core, param1);
            } else {
                // Simple timer setup
                ai_core.timers.main_timer = ai_core.timers.timers[param1 as usize];
                ai_core.mode2 = AIMode2::Timer;
            }
        }
    }
    
    /// Execute ATTACK strategy (matching C99 _AIStratAttack)
    fn execute_attack(&mut self, ai_core: &mut AICore) {
        ai_core.flags.sig_attack = true;
        ai_core.punch_kick = 0; // PLY_PUNCHING
        ai_core.flags.volley = false;
        ai_core.flags.sig_special = false;
        
        if let Some(param1) = self.read_byte() {
            ai_core.strategy_state.parameters.param1 = param1;
            
            // Handle throw flag
            if param1 & ai_flags::AIB_THROW != 0 {
                ai_core.control_signals.do_throw = true;
            } else {
                ai_core.control_signals.do_throw = false;
            }
            
            // Handle volley flag
            if param1 & ai_flags::AIB_VOLLEY != 0 {
                if let Some(param2) = self.read_byte() {
                    ai_core.dice_roll_count = param2 as i8;
                    ai_core.flags.volley = true;
                }
            }
        }
    }
    
    /// Execute JUMP strategy (matching C99 _AIStratJump)
    fn execute_jump(&mut self, ai_core: &mut AICore) {
        ai_core.control_signals.do_jump = true;
        
        // Read jump parameters
        if let Some(param1) = self.read_byte() {
            ai_core.strategy_state.parameters.param1 = param1;
            
            if let Some(param2) = self.read_byte() {
                ai_core.strategy_state.parameters.param2 = param2 as i8;
                
                if let Some(param3) = self.read_byte() {
                    ai_core.strategy_state.parameters.param3 = param3 as i8;
                    
                    if let Some(param4) = self.read_byte() {
                        ai_core.strategy_state.parameters.param4 = param4 as i8;
                    }
                }
            }
        }
    }
    
    /// Execute LONGWALK strategy (matching C99 _AIStratLongWalk)
    fn execute_longwalk(&mut self, ai_core: &mut AICore) {
        ai_core.control_signals.do_jump = false;
        ai_core.control_signals.do_block = false;
        
        if let Some(param1) = self.read_byte() {
            ai_core.strategy_state.parameters.param1 = param1;
            
            if let Some(param2) = self.read_byte() {
                ai_core.strategy_state.parameters.param2 = param2 as i8;
                
                // Calculate walk direction and distance
                let mut walk_distance = param2 as i16;
                if param2 != 0 {
                    if param1 & ai_flags::AIB_WALKAWAY != 0 {
                        walk_distance = -walk_distance;
                    }
                } else {
                    // Complex distance calculation would go here
                    walk_distance = param1 as i16;
                }
                
                ai_core.movement.walk_target = walk_distance;
            }
        }
    }
    
    /// Execute KICK strategy (matching C99 _AIStratKick)
    fn execute_kick(&mut self, ai_core: &mut AICore) {
        ai_core.flags.sig_attack = true;
        ai_core.punch_kick = 2; // PLY_KICKING
        ai_core.flags.volley = false;
        ai_core.flags.sig_special = false;
        
        if let Some(param1) = self.read_byte() {
            ai_core.strategy_state.parameters.param1 = param1;
            
            if param1 & ai_flags::AIB_THROW != 0 {
                ai_core.control_signals.do_throw = true;
            } else {
                ai_core.control_signals.do_throw = false;
            }
            
            if param1 & ai_flags::AIB_VOLLEY != 0 {
                if let Some(param2) = self.read_byte() {
                    ai_core.dice_roll_count = param2 as i8;
                    ai_core.flags.volley = true;
                }
            }
        }
    }
    
    /// Execute BLOCK strategy
    fn execute_block(&mut self, ai_core: &mut AICore) {
        ai_core.control_signals.do_block = true;
        
        if let Some(param1) = self.read_byte() {
            ai_core.strategy_state.parameters.param1 = param1;
        }
    }
    
    /// Handle complex standstill parameters
    fn handle_complex_standstill(&mut self, ai_core: &mut AICore, param1: u8) {
        if param1 & 0x01 != 0 {
            // Timer too close logic
            ai_core.mode2 = AIMode2::TooClose;
            if let Some(param2) = self.read_byte() {
                ai_core.movement.too_close_distance = param2 as i16;
            }
        } else if param1 & 0x40 != 0 {
            // Opponent jump logic
            ai_core.mode2 = AIMode2::OppJump;
            if let Some(param2) = self.read_byte() {
                ai_core.movement.opp_jump_height = param2 as i16 + 0x28;
            }
        } else if param1 & 0x02 != 0 {
            // Head hittable logic
            ai_core.mode2 = AIMode2::HeadHittable;
        } else {
            // No fireball logic
            ai_core.mode2 = AIMode2::NoFireball;
        }
    }
    
    /// Create default aggressive strategy table
    fn create_default_aggressive_table(&self, character_id: FighterId) -> AIStrategyTable {
        // This would be loaded from character-specific data files
        // For now, create a simple default table
        AIStrategyTable {
            time_selectors: [0; 32], // All point to first strategy
            strategies: vec![
                AIStrategyData {
                    bytecode: vec![
                        AIBytecode::StandStill as u8, 0x10,
                        AIBytecode::Attack as u8, 0x04,
                        AIBytecode::Restart as u8,
                    ],
                    name: format!("{:?}_default_aggressive", character_id),
                    character_id,
                    min_difficulty: 0,
                    max_difficulty: 31,
                }
            ],
        }
    }
    
    /// Create default defensive strategy table
    fn create_default_defensive_table(&self, character_id: FighterId) -> AIStrategyTable {
        AIStrategyTable {
            time_selectors: [0; 32],
            strategies: vec![
                AIStrategyData {
                    bytecode: vec![
                        AIBytecode::Block as u8, 0x01,
                        AIBytecode::StandStill as u8, 0x20,
                        AIBytecode::Restart as u8,
                    ],
                    name: format!("{:?}_default_defensive", character_id),
                    character_id,
                    min_difficulty: 0,
                    max_difficulty: 31,
                }
            ],
        }
    }
}

impl AIBytecode {
    fn from_u8(value: u8) -> Option<Self> {
        match value {
            0x00 => Some(AIBytecode::Restart),
            0x02 => Some(AIBytecode::StandStill),
            0x04 => Some(AIBytecode::Attack),
            0x06 => Some(AIBytecode::Jump),
            0x08 => Some(AIBytecode::LongWalk),
            0x0A => Some(AIBytecode::Kick),
            0x0C => Some(AIBytecode::Block),
            0x80 => Some(AIBytecode::EndScript),
            _ => None,
        }
    }
}
