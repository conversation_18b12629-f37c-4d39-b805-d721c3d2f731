// AI Threat Assessment System - real-time opponent analysis and threat detection
// Implements the sophisticated threat detection and distance-based decision making from C99

use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use sf2_types::*;
use crate::ai::ai_core::*;
use crate::ai::ai_difficulty::*;

/// Threat Assessment Component for Bevy ECS
#[derive(Component, Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AIThreatAssessment {
    /// Current threat level (0.0 = no threat, 1.0 = maximum threat)
    pub threat_level: f32,
    /// Closest threat distance
    pub closest_threat_distance: i16,
    /// Threat direction (-1 = left, 0 = neutral, 1 = right)
    pub threat_direction: i8,
    /// Opponent state analysis
    pub opponent_analysis: OpponentAnalysis,
    /// Distance-based threat zones
    pub threat_zones: ThreatZones,
    /// Threat history for pattern recognition
    pub threat_history: Vec<ThreatEvent>,
    /// Maximum history length
    pub max_history_length: usize,
}

/// Opponent state analysis (matching C99 opponent checks)
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct OpponentAnalysis {
    /// Opponent is jumping
    pub is_jumping: bool,
    /// Opponent is crouching
    pub is_crouching: bool,
    /// Opponent is attacking
    pub is_attacking: bool,
    /// Opponent is blocking
    pub is_blocking: bool,
    /// Opponent is throwing projectile
    pub is_throwing_projectile: bool,
    /// Opponent is in recovery frames
    pub is_in_recovery: bool,
    /// Opponent is dizzy/stunned
    pub is_dizzy: bool,
    /// Opponent's current animation frame
    pub animation_frame: u16,
    /// Opponent's remaining recovery frames
    pub recovery_frames: u16,
    /// Opponent's current move priority
    pub move_priority: u8,
    /// Opponent's head hittable state
    pub head_hittable: bool,
}

/// Distance-based threat zones (matching C99 distance checks)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatZones {
    /// Very close range (throw distance)
    pub very_close_range: i16,
    /// Close range (normal attack distance)
    pub close_range: i16,
    /// Medium range (jump attack distance)
    pub medium_range: i16,
    /// Long range (projectile distance)
    pub long_range: i16,
    /// Current zone the opponent is in
    pub current_zone: ThreatZone,
}

/// Threat zone enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ThreatZone {
    VeryClose,
    Close,
    Medium,
    Long,
    OutOfRange,
}

/// Threat event for pattern recognition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatEvent {
    /// Frame when threat was detected
    pub frame: u64,
    /// Type of threat
    pub threat_type: ThreatType,
    /// Distance when threat occurred
    pub distance: i16,
    /// Opponent action that caused threat
    pub opponent_action: OpponentAction,
}

/// Types of threats (matching C99 threat detection)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ThreatType {
    /// Opponent is too close for comfort
    TooClose,
    /// Opponent is jumping toward AI
    JumpAttack,
    /// Opponent is throwing projectile
    Projectile,
    /// Opponent is in attack range
    AttackRange,
    /// Opponent is attempting throw
    ThrowAttempt,
    /// Opponent is in recovery (opportunity)
    Recovery,
}

/// Opponent actions for threat analysis
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OpponentAction {
    Standing,
    Walking,
    Jumping,
    Crouching,
    Attacking,
    Blocking,
    ThrowingProjectile,
    SpecialMove,
    Recovering,
    Dizzy,
}

impl Default for AIThreatAssessment {
    fn default() -> Self {
        Self {
            threat_level: 0.0,
            closest_threat_distance: i16::MAX,
            threat_direction: 0,
            opponent_analysis: OpponentAnalysis::default(),
            threat_zones: ThreatZones::default(),
            threat_history: Vec::new(),
            max_history_length: 60, // Keep 1 second of history at 60fps
        }
    }
}

impl Default for OpponentAnalysis {
    fn default() -> Self {
        Self {
            is_jumping: false,
            is_crouching: false,
            is_attacking: false,
            is_blocking: false,
            is_throwing_projectile: false,
            is_in_recovery: false,
            is_dizzy: false,
            animation_frame: 0,
            recovery_frames: 0,
            move_priority: 0,
            head_hittable: false,
        }
    }
}

impl Default for ThreatZones {
    fn default() -> Self {
        Self {
            very_close_range: 48,   // Throw range
            close_range: 96,        // Normal attack range
            medium_range: 192,      // Jump attack range
            long_range: 384,        // Projectile range
            current_zone: ThreatZone::OutOfRange,
        }
    }
}

impl AIThreatAssessment {
    /// Update threat assessment (matching C99 AICheckThreats)
    pub fn update_threat_assessment(
        &mut self,
        ai_core: &mut AICore,
        ai_difficulty: &AIDifficulty,
        ai_position: Vec2,
        opponent_position: Vec2,
        opponent_state: &FighterStateData,
        frame_count: u64,
    ) {
        // Calculate distance and direction
        let distance_vector = opponent_position - ai_position;
        let distance = distance_vector.x.abs() as i16;
        let direction = if distance_vector.x > 0.0 { 1 } else { -1 };
        
        self.closest_threat_distance = distance;
        self.threat_direction = direction;
        
        // Update opponent analysis
        self.update_opponent_analysis(opponent_state);
        
        // Update threat zones
        self.update_threat_zones(distance);
        
        // Calculate threat level
        self.calculate_threat_level(ai_difficulty);
        
        // Update AI core threat flags (matching C99 logic)
        if ai_core.threat_check_mode != 0 && self.should_check_threats() {
            if ai_core.timers.threat_check_timer > 0 {
                ai_core.timers.threat_check_timer -= 1;
                ai_core.flags.threat_found = false;
            } else if ai_core.flags.threat_found || 
                     ai_core.threat_check_mode != 2 || 
                     self.difficulty_based_threat_check(ai_difficulty) {
                ai_core.flags.threat_found = true;
            } else {
                // Delay next threat check for 30 frames
                ai_core.timers.threat_check_timer = 30;
            }
        } else {
            ai_core.timers.threat_check_timer = 0;
            ai_core.flags.threat_found = false;
        }
        
        // Record threat event if significant
        if self.threat_level > 0.5 {
            self.record_threat_event(frame_count);
        }
        
        // Maintain history size
        if self.threat_history.len() > self.max_history_length {
            self.threat_history.remove(0);
        }
    }
    
    /// Update opponent analysis from fighter state
    fn update_opponent_analysis(&mut self, opponent_state: &FighterStateData) {
        self.opponent_analysis.is_jumping = matches!(opponent_state.state, FighterState::Jumping);
        self.opponent_analysis.is_crouching = matches!(opponent_state.state, FighterState::Crouch);
        self.opponent_analysis.is_attacking = matches!(opponent_state.state, FighterState::Attacking);
        self.opponent_analysis.is_blocking = matches!(opponent_state.state, FighterState::StandBlock);
        self.opponent_analysis.is_in_recovery = opponent_state.hit_stun > 0 || opponent_state.block_stun > 0;
        self.opponent_analysis.is_dizzy = opponent_state.flags.dizzy;
        self.opponent_analysis.recovery_frames = opponent_state.hit_stun.max(opponent_state.block_stun);
        
        // Determine if opponent is throwing projectile (simplified check)
        self.opponent_analysis.is_throwing_projectile = matches!(
            opponent_state.current_attack,
            Some(sf2_types::fighter_state::AttackType::Special) if opponent_state.animation_frame < 10
        );
        
        // Determine head hittable state (simplified)
        self.opponent_analysis.head_hittable = !self.opponent_analysis.is_crouching && 
                                               !self.opponent_analysis.is_blocking;
    }
    
    /// Update threat zones based on distance
    fn update_threat_zones(&mut self, distance: i16) {
        self.threat_zones.current_zone = if distance <= self.threat_zones.very_close_range {
            ThreatZone::VeryClose
        } else if distance <= self.threat_zones.close_range {
            ThreatZone::Close
        } else if distance <= self.threat_zones.medium_range {
            ThreatZone::Medium
        } else if distance <= self.threat_zones.long_range {
            ThreatZone::Long
        } else {
            ThreatZone::OutOfRange
        };
    }
    
    /// Calculate overall threat level
    fn calculate_threat_level(&mut self, ai_difficulty: &AIDifficulty) {
        let mut threat = 0.0f32;
        
        // Distance-based threat
        threat += match self.threat_zones.current_zone {
            ThreatZone::VeryClose => 0.9,
            ThreatZone::Close => 0.7,
            ThreatZone::Medium => 0.4,
            ThreatZone::Long => 0.2,
            ThreatZone::OutOfRange => 0.0,
        };
        
        // Opponent action-based threat
        if self.opponent_analysis.is_attacking {
            threat += 0.8;
        }
        if self.opponent_analysis.is_jumping {
            threat += 0.6;
        }
        if self.opponent_analysis.is_throwing_projectile {
            threat += 0.5;
        }
        
        // Opportunity detection (negative threat = opportunity)
        if self.opponent_analysis.is_in_recovery {
            threat -= 0.4;
        }
        if self.opponent_analysis.is_dizzy {
            threat -= 0.6;
        }
        
        // Scale by AI difficulty (higher difficulty = more sensitive to threats)
        threat *= ai_difficulty.defense_scale;
        
        self.threat_level = threat.max(0.0).min(1.0);
    }
    
    /// Check if threats should be assessed (matching C99 sub_2c0ce)
    fn should_check_threats(&self) -> bool {
        // Simplified threat check conditions
        !self.opponent_analysis.is_dizzy && 
        self.threat_zones.current_zone != ThreatZone::OutOfRange
    }
    
    /// Difficulty-based threat check (matching C99 comp_ply_difficulty_lookup)
    fn difficulty_based_threat_check(&self, ai_difficulty: &AIDifficulty) -> bool {
        // Simplified difficulty check - higher difficulty = more likely to detect threats
        let random_value = fastrand::u32(..);
        ai_difficulty.dice_roll(random_value)
    }
    
    /// Record a threat event for pattern recognition
    fn record_threat_event(&mut self, frame_count: u64) {
        let threat_type = self.determine_primary_threat_type();
        let opponent_action = self.determine_opponent_action();
        
        let event = ThreatEvent {
            frame: frame_count,
            threat_type,
            distance: self.closest_threat_distance,
            opponent_action,
        };
        
        self.threat_history.push(event);
    }
    
    /// Determine the primary threat type
    fn determine_primary_threat_type(&self) -> ThreatType {
        if self.threat_zones.current_zone == ThreatZone::VeryClose {
            if self.opponent_analysis.is_attacking {
                ThreatType::ThrowAttempt
            } else {
                ThreatType::TooClose
            }
        } else if self.opponent_analysis.is_jumping {
            ThreatType::JumpAttack
        } else if self.opponent_analysis.is_throwing_projectile {
            ThreatType::Projectile
        } else if self.opponent_analysis.is_attacking {
            ThreatType::AttackRange
        } else if self.opponent_analysis.is_in_recovery {
            ThreatType::Recovery
        } else {
            ThreatType::TooClose
        }
    }
    
    /// Determine opponent's current action
    fn determine_opponent_action(&self) -> OpponentAction {
        if self.opponent_analysis.is_dizzy {
            OpponentAction::Dizzy
        } else if self.opponent_analysis.is_in_recovery {
            OpponentAction::Recovering
        } else if self.opponent_analysis.is_throwing_projectile {
            OpponentAction::ThrowingProjectile
        } else if self.opponent_analysis.is_attacking {
            OpponentAction::Attacking
        } else if self.opponent_analysis.is_jumping {
            OpponentAction::Jumping
        } else if self.opponent_analysis.is_crouching {
            OpponentAction::Crouching
        } else if self.opponent_analysis.is_blocking {
            OpponentAction::Blocking
        } else {
            OpponentAction::Standing
        }
    }
    
    /// Get recommended AI response to current threat
    pub fn get_threat_response(&self) -> AIThreatResponse {
        match self.threat_zones.current_zone {
            ThreatZone::VeryClose => {
                if self.opponent_analysis.is_attacking {
                    AIThreatResponse::Block
                } else if self.opponent_analysis.is_in_recovery {
                    AIThreatResponse::Attack
                } else {
                    AIThreatResponse::BackAway
                }
            }
            ThreatZone::Close => {
                if self.opponent_analysis.is_jumping {
                    AIThreatResponse::AntiAir
                } else if self.opponent_analysis.is_in_recovery {
                    AIThreatResponse::Attack
                } else {
                    AIThreatResponse::Block
                }
            }
            ThreatZone::Medium => {
                if self.opponent_analysis.is_jumping {
                    AIThreatResponse::AntiAir
                } else if self.opponent_analysis.is_throwing_projectile {
                    AIThreatResponse::Jump
                } else {
                    AIThreatResponse::Advance
                }
            }
            ThreatZone::Long => {
                if self.opponent_analysis.is_throwing_projectile {
                    AIThreatResponse::Jump
                } else {
                    AIThreatResponse::Advance
                }
            }
            ThreatZone::OutOfRange => AIThreatResponse::Advance,
        }
    }
    
    /// Analyze threat patterns for adaptive behavior
    pub fn analyze_threat_patterns(&self) -> ThreatPatternAnalysis {
        let mut analysis = ThreatPatternAnalysis::default();
        
        if self.threat_history.len() < 10 {
            return analysis; // Not enough data
        }
        
        // Analyze recent threat types
        let recent_threats: Vec<_> = self.threat_history.iter()
            .rev()
            .take(20)
            .collect();
        
        // Count threat type frequencies
        for event in &recent_threats {
            match event.threat_type {
                ThreatType::JumpAttack => analysis.jump_attack_frequency += 1.0,
                ThreatType::Projectile => analysis.projectile_frequency += 1.0,
                ThreatType::ThrowAttempt => analysis.throw_frequency += 1.0,
                ThreatType::AttackRange => analysis.attack_frequency += 1.0,
                _ => {}
            }
        }
        
        // Normalize frequencies
        let total = recent_threats.len() as f32;
        analysis.jump_attack_frequency /= total;
        analysis.projectile_frequency /= total;
        analysis.throw_frequency /= total;
        analysis.attack_frequency /= total;
        
        analysis
    }
}

/// AI response to threats
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AIThreatResponse {
    Attack,
    Block,
    Jump,
    AntiAir,
    Advance,
    BackAway,
    Special,
    Throw,
}

/// Threat pattern analysis for adaptive AI
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ThreatPatternAnalysis {
    pub jump_attack_frequency: f32,
    pub projectile_frequency: f32,
    pub throw_frequency: f32,
    pub attack_frequency: f32,
}
