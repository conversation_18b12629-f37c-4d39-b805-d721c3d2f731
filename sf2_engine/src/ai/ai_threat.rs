// AI Threat Assessment System - real-time opponent analysis and threat detection
// Implements the sophisticated threat detection and distance-based decision making from C99

use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use sf2_types::*;
use crate::ai::ai_core::*;
use crate::ai::ai_difficulty::*;
use character_traits::AIBehaviorPattern;
use crate::Fighter;
use crate::events::HitEvent;

/// Threat Assessment Component for Bevy ECS
#[derive(Component, Debug, Clone, Serialize, Deserialize)]
pub struct AIThreatAssessment {
    /// Current threat level (0.0 = no threat, 1.0 = maximum threat)
    pub threat_level: f32,
    /// Closest threat distance
    pub closest_threat_distance: i16,
    /// Threat direction (-1 = left, 0 = neutral, 1 = right)
    pub threat_direction: i8,
    /// Opponent state analysis
    pub opponent_analysis: OpponentAnalysis,
    /// Distance-based threat zones
    pub threat_zones: ThreatZones,
    /// Threat history for pattern recognition
    pub threat_history: Vec<ThreatEvent>,
    /// Maximum history length
    pub max_history_length: usize,
}

/// Opponent state analysis (matching C99 opponent checks)
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct OpponentAnalysis {
    /// Opponent is jumping
    pub is_jumping: bool,
    /// Opponent is crouching
    pub is_crouching: bool,
    /// Opponent is attacking
    pub is_attacking: bool,
    /// Opponent is blocking
    pub is_blocking: bool,
    /// Opponent is throwing projectile
    pub is_throwing_projectile: bool,
    /// Opponent is in recovery frames
    pub is_in_recovery: bool,
    /// Opponent is dizzy/stunned
    pub is_dizzy: bool,
    /// Opponent's current animation frame
    pub animation_frame: u16,
    /// Opponent's remaining recovery frames
    pub recovery_frames: u16,
    /// Opponent's current move priority
    pub move_priority: u8,
    /// Opponent's head hittable state
    pub head_hittable: bool,
}

/// Distance-based threat zones (matching C99 distance checks)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatZones {
    /// Very close range (throw distance)
    pub very_close_range: i16,
    /// Close range (normal attack distance)
    pub close_range: i16,
    /// Medium range (jump attack distance)
    pub medium_range: i16,
    /// Long range (projectile distance)
    pub long_range: i16,
    /// Current zone the opponent is in
    pub current_zone: ThreatZone,
}

/// Threat zone enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ThreatZone {
    VeryClose,
    Close,
    Medium,
    Long,
    OutOfRange,
}

/// Threat event for pattern recognition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatEvent {
    /// Frame when threat was detected
    pub frame: u64,
    /// Type of threat
    pub threat_type: ThreatType,
    /// Distance when threat occurred
    pub distance: i16,
    /// Opponent action that caused threat
    pub opponent_action: OpponentAction,
}

/// Types of threats (matching C99 threat detection)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ThreatType {
    /// Opponent is too close for comfort
    TooClose,
    /// Opponent is jumping toward AI
    JumpAttack,
    /// Opponent is throwing projectile
    Projectile,
    /// Opponent is in attack range
    AttackRange,
    /// Opponent is attempting throw
    ThrowAttempt,
    /// Opponent is in recovery (opportunity)
    Recovery,
}

/// Opponent actions for threat analysis
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OpponentAction {
    Standing,
    Walking,
    Jumping,
    Crouching,
    Attacking,
    Blocking,
    ThrowingProjectile,
    SpecialMove,
    Recovering,
    Dizzy,
}

impl Default for AIThreatAssessment {
    fn default() -> Self {
        Self {
            threat_level: 0.0,
            closest_threat_distance: i16::MAX,
            threat_direction: 0,
            opponent_analysis: OpponentAnalysis::default(),
            threat_zones: ThreatZones::default(),
            threat_history: Vec::new(),
            max_history_length: 60, // Keep 1 second of history at 60fps
        }
    }
}

impl Default for OpponentAnalysis {
    fn default() -> Self {
        Self {
            is_jumping: false,
            is_crouching: false,
            is_attacking: false,
            is_blocking: false,
            is_throwing_projectile: false,
            is_in_recovery: false,
            is_dizzy: false,
            animation_frame: 0,
            recovery_frames: 0,
            move_priority: 0,
            head_hittable: false,
        }
    }
}

impl Default for ThreatZones {
    fn default() -> Self {
        Self {
            very_close_range: 48,   // Throw range
            close_range: 96,        // Normal attack range
            medium_range: 192,      // Jump attack range
            long_range: 384,        // Projectile range
            current_zone: ThreatZone::OutOfRange,
        }
    }
}

impl AIThreatAssessment {
    /// Update threat assessment (matching C99 AICheckThreats)
    pub fn update_threat_assessment(
        &mut self,
        ai_core: &mut AICore,
        ai_difficulty: &AIDifficulty,
        ai_position: Vec2,
        opponent_position: Vec2,
        opponent_state: &FighterStateData,
        frame_count: u64,
    ) {
        // Calculate distance and direction
        let distance_vector = opponent_position - ai_position;
        let distance = distance_vector.x.abs() as i16;
        let direction = if distance_vector.x > 0.0 { 1 } else { -1 };
        
        self.closest_threat_distance = distance;
        self.threat_direction = direction;
        
        // Update opponent analysis
        self.update_opponent_analysis(opponent_state);
        
        // Update threat zones
        self.update_threat_zones(distance);
        
        // Calculate threat level
        self.calculate_threat_level(ai_difficulty);
        
        // Update AI core threat flags (matching C99 logic)
        if ai_core.threat_check_mode != 0 && self.should_check_threats() {
            if ai_core.timers.threat_check_timer > 0 {
                ai_core.timers.threat_check_timer -= 1;
                ai_core.flags.threat_found = false;
            } else if ai_core.flags.threat_found || 
                     ai_core.threat_check_mode != 2 || 
                     self.difficulty_based_threat_check(ai_difficulty) {
                ai_core.flags.threat_found = true;
            } else {
                // Delay next threat check for 30 frames
                ai_core.timers.threat_check_timer = 30;
            }
        } else {
            ai_core.timers.threat_check_timer = 0;
            ai_core.flags.threat_found = false;
        }
        
        // Record threat event if significant
        if self.threat_level > 0.5 {
            self.record_threat_event(frame_count);
        }
        
        // Maintain history size
        if self.threat_history.len() > self.max_history_length {
            self.threat_history.remove(0);
        }
    }
    
    /// Update opponent analysis from fighter state
    fn update_opponent_analysis(&mut self, opponent_state: &FighterStateData) {
        self.opponent_analysis.is_jumping = matches!(opponent_state.state, FighterState::Jumping);
        self.opponent_analysis.is_crouching = matches!(opponent_state.state, FighterState::Crouch);
        self.opponent_analysis.is_attacking = matches!(opponent_state.state, FighterState::Attacking);
        self.opponent_analysis.is_blocking = matches!(opponent_state.state, FighterState::StandBlock);
        self.opponent_analysis.is_in_recovery = opponent_state.hit_stun > 0 || opponent_state.block_stun > 0;
        self.opponent_analysis.is_dizzy = opponent_state.flags.dizzy;
        self.opponent_analysis.recovery_frames = opponent_state.hit_stun.max(opponent_state.block_stun);
        
        // Determine if opponent is throwing projectile (simplified check)
        self.opponent_analysis.is_throwing_projectile = matches!(
            opponent_state.current_attack,
            Some(sf2_types::fighter_state::AttackType::Special) if opponent_state.animation_frame < 10
        );
        
        // Determine head hittable state (simplified)
        self.opponent_analysis.head_hittable = !self.opponent_analysis.is_crouching && 
                                               !self.opponent_analysis.is_blocking;
    }
    
    /// Update threat zones based on distance
    fn update_threat_zones(&mut self, distance: i16) {
        self.threat_zones.current_zone = if distance <= self.threat_zones.very_close_range {
            ThreatZone::VeryClose
        } else if distance <= self.threat_zones.close_range {
            ThreatZone::Close
        } else if distance <= self.threat_zones.medium_range {
            ThreatZone::Medium
        } else if distance <= self.threat_zones.long_range {
            ThreatZone::Long
        } else {
            ThreatZone::OutOfRange
        };
    }
    
    /// Calculate overall threat level
    fn calculate_threat_level(&mut self, ai_difficulty: &AIDifficulty) {
        let mut threat = 0.0f32;
        
        // Distance-based threat
        threat += match self.threat_zones.current_zone {
            ThreatZone::VeryClose => 0.9,
            ThreatZone::Close => 0.7,
            ThreatZone::Medium => 0.4,
            ThreatZone::Long => 0.2,
            ThreatZone::OutOfRange => 0.0,
        };
        
        // Opponent action-based threat
        if self.opponent_analysis.is_attacking {
            threat += 0.8;
        }
        if self.opponent_analysis.is_jumping {
            threat += 0.6;
        }
        if self.opponent_analysis.is_throwing_projectile {
            threat += 0.5;
        }
        
        // Opportunity detection (negative threat = opportunity)
        if self.opponent_analysis.is_in_recovery {
            threat -= 0.4;
        }
        if self.opponent_analysis.is_dizzy {
            threat -= 0.6;
        }
        
        // Scale by AI difficulty (higher difficulty = more sensitive to threats)
        threat *= ai_difficulty.defense_scale;
        
        self.threat_level = threat.max(0.0).min(1.0);
    }
    
    /// Check if threats should be assessed (matching C99 sub_2c0ce)
    fn should_check_threats(&self) -> bool {
        // Simplified threat check conditions
        !self.opponent_analysis.is_dizzy && 
        self.threat_zones.current_zone != ThreatZone::OutOfRange
    }
    
    /// Difficulty-based threat check (matching C99 comp_ply_difficulty_lookup)
    fn difficulty_based_threat_check(&self, ai_difficulty: &AIDifficulty) -> bool {
        // Simplified difficulty check - higher difficulty = more likely to detect threats
        let random_value = fastrand::u32(..);
        ai_difficulty.dice_roll(random_value)
    }
    
    /// Record a threat event for pattern recognition
    fn record_threat_event(&mut self, frame_count: u64) {
        let threat_type = self.determine_primary_threat_type();
        let opponent_action = self.determine_opponent_action();
        
        let event = ThreatEvent {
            frame: frame_count,
            threat_type,
            distance: self.closest_threat_distance,
            opponent_action,
        };
        
        self.threat_history.push(event);
    }
    
    /// Determine the primary threat type
    fn determine_primary_threat_type(&self) -> ThreatType {
        if self.threat_zones.current_zone == ThreatZone::VeryClose {
            if self.opponent_analysis.is_attacking {
                ThreatType::ThrowAttempt
            } else {
                ThreatType::TooClose
            }
        } else if self.opponent_analysis.is_jumping {
            ThreatType::JumpAttack
        } else if self.opponent_analysis.is_throwing_projectile {
            ThreatType::Projectile
        } else if self.opponent_analysis.is_attacking {
            ThreatType::AttackRange
        } else if self.opponent_analysis.is_in_recovery {
            ThreatType::Recovery
        } else {
            ThreatType::TooClose
        }
    }
    
    /// Determine opponent's current action
    fn determine_opponent_action(&self) -> OpponentAction {
        if self.opponent_analysis.is_dizzy {
            OpponentAction::Dizzy
        } else if self.opponent_analysis.is_in_recovery {
            OpponentAction::Recovering
        } else if self.opponent_analysis.is_throwing_projectile {
            OpponentAction::ThrowingProjectile
        } else if self.opponent_analysis.is_attacking {
            OpponentAction::Attacking
        } else if self.opponent_analysis.is_jumping {
            OpponentAction::Jumping
        } else if self.opponent_analysis.is_crouching {
            OpponentAction::Crouching
        } else if self.opponent_analysis.is_blocking {
            OpponentAction::Blocking
        } else {
            OpponentAction::Standing
        }
    }
    
    /// Get recommended AI response to current threat
    pub fn get_threat_response(&self) -> AIThreatResponse {
        match self.threat_zones.current_zone {
            ThreatZone::VeryClose => {
                if self.opponent_analysis.is_attacking {
                    AIThreatResponse::Block
                } else if self.opponent_analysis.is_in_recovery {
                    AIThreatResponse::Attack
                } else {
                    AIThreatResponse::BackAway
                }
            }
            ThreatZone::Close => {
                if self.opponent_analysis.is_jumping {
                    AIThreatResponse::AntiAir
                } else if self.opponent_analysis.is_in_recovery {
                    AIThreatResponse::Attack
                } else {
                    AIThreatResponse::Block
                }
            }
            ThreatZone::Medium => {
                if self.opponent_analysis.is_jumping {
                    AIThreatResponse::AntiAir
                } else if self.opponent_analysis.is_throwing_projectile {
                    AIThreatResponse::Jump
                } else {
                    AIThreatResponse::Advance
                }
            }
            ThreatZone::Long => {
                if self.opponent_analysis.is_throwing_projectile {
                    AIThreatResponse::Jump
                } else {
                    AIThreatResponse::Advance
                }
            }
            ThreatZone::OutOfRange => AIThreatResponse::Advance,
        }
    }
    
    /// Analyze threat patterns for adaptive behavior
    pub fn analyze_threat_patterns(&self) -> ThreatPatternAnalysis {
        let mut analysis = ThreatPatternAnalysis::default();
        
        if self.threat_history.len() < 10 {
            return analysis; // Not enough data
        }
        
        // Analyze recent threat types
        let recent_threats: Vec<_> = self.threat_history.iter()
            .rev()
            .take(20)
            .collect();
        
        // Count threat type frequencies
        for event in &recent_threats {
            match event.threat_type {
                ThreatType::JumpAttack => analysis.jump_attack_frequency += 1.0,
                ThreatType::Projectile => analysis.projectile_frequency += 1.0,
                ThreatType::ThrowAttempt => analysis.throw_frequency += 1.0,
                ThreatType::AttackRange => analysis.attack_frequency += 1.0,
                _ => {}
            }
        }
        
        // Normalize frequencies
        let total = recent_threats.len() as f32;
        analysis.jump_attack_frequency /= total;
        analysis.projectile_frequency /= total;
        analysis.throw_frequency /= total;
        analysis.attack_frequency /= total;
        
        analysis
    }
}

/// AI response to threats
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AIThreatResponse {
    Attack,
    Block,
    Jump,
    AntiAir,
    Advance,
    BackAway,
    Special,
    Throw,
}

/// Threat pattern analysis for adaptive AI
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ThreatPatternAnalysis {
    pub jump_attack_frequency: f32,
    pub projectile_frequency: f32,
    pub throw_frequency: f32,
    pub attack_frequency: f32,
}

/// AI Decision Making System - integrates threat assessment with tactical decisions
#[derive(Component, Debug, Clone, Serialize, Deserialize)]
pub struct AIDecisionMaker {
    /// Current tactical decision
    pub current_decision: Option<AITacticalDecision>,
    /// Decision confidence level (0.0 to 1.0)
    pub decision_confidence: f32,
    /// Frame when decision was made
    pub decision_frame: u64,
    /// Decision history for pattern analysis
    pub decision_history: Vec<AIDecisionEvent>,
    /// Maximum decision history length
    pub max_decision_history: usize,
    /// Opponent state tracking
    pub opponent_tracker: OpponentStateTracker,
    /// Projectile awareness system
    pub projectile_awareness: ProjectileAwarenessSystem,
    /// Positioning system
    pub positioning_system: PositioningSystem,
}

/// Tactical decision types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AITacticalDecision {
    /// Aggressive decisions
    PressureAttack,
    ComboAttack,
    ThrowAttempt,
    SpecialAttack(SpecialMoveId),
    JumpAttack,

    /// Defensive decisions
    Block,
    Retreat,
    AntiAir,
    Escape,
    Counter,

    /// Positioning decisions
    MaintainDistance,
    CloseDistance,
    CreateSpace,
    CornerOpponent,
    EscapeCorner,

    /// Neutral decisions
    Wait,
    Bait,
    Feint,
    Observe,
}

/// Decision event for history tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDecisionEvent {
    pub decision: AITacticalDecision,
    pub frame: u64,
    pub threat_level: f32,
    pub distance: i16,
    pub success: Option<bool>, // None = pending, Some(true/false) = evaluated
}

/// Opponent state tracking system
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct OpponentStateTracker {
    /// Opponent's last known position
    pub last_position: Vec2,
    /// Opponent's velocity tracking
    pub velocity_history: Vec<Vec2>,
    /// Opponent's action patterns
    pub action_patterns: OpponentActionPatterns,
    /// Frame advantage tracking
    pub frame_advantage: i8,
    /// Opponent's health percentage
    pub health_percentage: f32,
    /// Opponent's meter/super availability
    pub meter_level: u8,
}

/// Opponent action pattern tracking
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct OpponentActionPatterns {
    /// Frequency of different attack types
    pub attack_frequencies: std::collections::HashMap<String, f32>,
    /// Defensive patterns
    pub defensive_patterns: Vec<DefensivePattern>,
    /// Movement patterns
    pub movement_patterns: Vec<MovementPattern>,
    /// Special move usage patterns
    pub special_move_patterns: Vec<SpecialMoveUsagePattern>,
}

/// Projectile awareness and tracking
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ProjectileAwarenessSystem {
    /// Active projectiles being tracked
    pub tracked_projectiles: Vec<TrackedProjectile>,
    /// Projectile prediction data
    pub projectile_predictions: Vec<ProjectilePrediction>,
    /// Anti-projectile strategies
    pub anti_projectile_strategies: Vec<AntiProjectileStrategy>,
}

/// Tracked projectile data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrackedProjectile {
    pub position: Vec2,
    pub velocity: Vec2,
    pub projectile_type: ProjectileType,
    pub threat_level: f32,
    pub time_to_impact: f32,
}

/// Projectile prediction for decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectilePrediction {
    pub predicted_path: Vec<Vec2>,
    pub impact_point: Vec2,
    pub impact_time: f32,
    pub avoidance_options: Vec<AvoidanceOption>,
}

/// Positioning system for tactical movement
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PositioningSystem {
    /// Optimal positioning zones
    pub optimal_zones: Vec<PositionZone>,
    /// Current position evaluation
    pub position_score: f32,
    /// Target position for movement
    pub target_position: Option<Vec2>,
    /// Movement strategy
    pub movement_strategy: MovementStrategy,
}

/// Position zone definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionZone {
    pub zone_type: PositionZoneType,
    pub bounds: Rect,
    pub advantage_score: f32,
    pub threat_level: f32,
}

/// Position zone types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PositionZoneType {
    Optimal,
    Advantageous,
    Neutral,
    Disadvantageous,
    Dangerous,
}

/// Movement strategy types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MovementStrategy {
    Aggressive,
    Defensive,
    Neutral,
    Evasive,
    Positioning,
}

impl Default for MovementStrategy {
    fn default() -> Self {
        MovementStrategy::Neutral
    }
}

/// Anti-projectile strategy
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AntiProjectileStrategy {
    Block,
    Jump,
    Duck,
    Advance,
    Retreat,
    Counter,
}

// Additional supporting types for decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DefensivePattern {
    pub pattern_type: String,
    pub frequency: f32,
    pub effectiveness: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MovementPattern {
    pub pattern_type: String,
    pub frequency: f32,
    pub predictability: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpecialMoveUsagePattern {
    pub move_id: SpecialMoveId,
    pub usage_frequency: f32,
    pub success_rate: f32,
    pub preferred_distances: Vec<i16>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProjectileType {
    Hadoken,
    SonicBoom,
    YogaFire,
    ElectricBlast,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AvoidanceOption {
    pub action: AITacticalDecision,
    pub success_probability: f32,
    pub risk_level: f32,
}

impl Default for AIDecisionMaker {
    fn default() -> Self {
        Self {
            current_decision: None,
            decision_confidence: 0.0,
            decision_frame: 0,
            decision_history: Vec::new(),
            max_decision_history: 120, // 2 seconds at 60fps
            opponent_tracker: OpponentStateTracker::default(),
            projectile_awareness: ProjectileAwarenessSystem::default(),
            positioning_system: PositioningSystem::default(),
        }
    }
}

impl AIDecisionMaker {
    /// Make tactical decision based on threat assessment and game state
    pub fn make_decision(
        &mut self,
        threat_assessment: &AIThreatAssessment,
        ai_core: &mut AICore,
        ai_difficulty: &AIDifficulty,
        character_patterns: &AIBehaviorPattern,
        frame_count: u64,
    ) -> Option<AITacticalDecision> {
        // Update opponent tracking
        self.update_opponent_tracking(threat_assessment, frame_count);

        // Update projectile awareness
        self.update_projectile_awareness(threat_assessment);

        // Update positioning analysis
        self.update_positioning_analysis(threat_assessment);

        // Analyze current situation
        let situation_analysis = self.analyze_situation(threat_assessment, ai_difficulty);

        // Make decision based on analysis
        let decision = self.select_tactical_decision(
            &situation_analysis,
            character_patterns,
            ai_difficulty,
            frame_count,
        );

        // Record decision
        if let Some(decision) = decision {
            self.record_decision(decision, threat_assessment, frame_count);
            self.current_decision = Some(decision);
            self.decision_frame = frame_count;

            // Update AI core control signals based on decision
            self.apply_decision_to_ai_core(decision, ai_core);
        }

        decision
    }

    /// Update opponent state tracking
    fn update_opponent_tracking(&mut self, threat_assessment: &AIThreatAssessment, frame_count: u64) {
        // Update opponent position tracking
        let opponent_pos = Vec2::new(
            threat_assessment.closest_threat_distance as f32 * threat_assessment.threat_direction as f32,
            0.0, // Simplified - would need actual Y position
        );

        // Calculate velocity from position history
        let velocity = if self.opponent_tracker.velocity_history.len() > 0 {
            let last_pos = self.opponent_tracker.last_position;
            opponent_pos - last_pos
        } else {
            Vec2::ZERO
        };

        // Update velocity history (keep last 10 frames)
        self.opponent_tracker.velocity_history.push(velocity);
        if self.opponent_tracker.velocity_history.len() > 10 {
            self.opponent_tracker.velocity_history.remove(0);
        }

        self.opponent_tracker.last_position = opponent_pos;

        // Update action patterns based on opponent analysis
        self.update_action_patterns(&threat_assessment.opponent_analysis);
    }

    /// Update action pattern tracking
    fn update_action_patterns(&mut self, opponent_analysis: &OpponentAnalysis) {
        // Track opponent actions for pattern recognition
        if opponent_analysis.is_attacking {
            *self.opponent_tracker.action_patterns.attack_frequencies
                .entry("attack".to_string())
                .or_insert(0.0) += 1.0;
        }

        if opponent_analysis.is_jumping {
            *self.opponent_tracker.action_patterns.attack_frequencies
                .entry("jump".to_string())
                .or_insert(0.0) += 1.0;
        }

        if opponent_analysis.is_throwing_projectile {
            *self.opponent_tracker.action_patterns.attack_frequencies
                .entry("projectile".to_string())
                .or_insert(0.0) += 1.0;
        }

        if opponent_analysis.is_blocking {
            *self.opponent_tracker.action_patterns.attack_frequencies
                .entry("block".to_string())
                .or_insert(0.0) += 1.0;
        }
    }

    /// Update projectile awareness system
    fn update_projectile_awareness(&mut self, threat_assessment: &AIThreatAssessment) {
        // Clear old projectile data
        self.projectile_awareness.tracked_projectiles.clear();
        self.projectile_awareness.projectile_predictions.clear();

        // If opponent is throwing projectile, track it
        if threat_assessment.opponent_analysis.is_throwing_projectile {
            let projectile_position = Vec2::new(
                threat_assessment.closest_threat_distance as f32 * threat_assessment.threat_direction as f32,
                0.0,
            );
            let projectile_velocity = Vec2::new(-threat_assessment.threat_direction as f32 * 200.0, 0.0);
            let projectile_time_to_impact = threat_assessment.closest_threat_distance as f32 / 200.0;

            let projectile = TrackedProjectile {
                position: projectile_position,
                velocity: projectile_velocity,
                projectile_type: ProjectileType::Hadoken, // Simplified - would determine actual type
                threat_level: 0.8,
                time_to_impact: projectile_time_to_impact,
            };

            self.projectile_awareness.tracked_projectiles.push(projectile);

            // Generate prediction
            let prediction = ProjectilePrediction {
                predicted_path: vec![projectile_position, projectile_position + projectile_velocity],
                impact_point: Vec2::ZERO, // Simplified - would calculate actual impact
                impact_time: projectile_time_to_impact,
                avoidance_options: vec![
                    AvoidanceOption {
                        action: AITacticalDecision::Block,
                        success_probability: 0.9,
                        risk_level: 0.1,
                    },
                    AvoidanceOption {
                        action: AITacticalDecision::JumpAttack,
                        success_probability: 0.7,
                        risk_level: 0.3,
                    },
                ],
            };

            self.projectile_awareness.projectile_predictions.push(prediction);
        }
    }

    /// Update positioning analysis
    fn update_positioning_analysis(&mut self, threat_assessment: &AIThreatAssessment) {
        // Evaluate current position
        self.positioning_system.position_score = self.evaluate_position_score(threat_assessment);

        // Determine optimal zones based on threat assessment
        self.positioning_system.optimal_zones.clear();

        // Add optimal zones based on distance and threat level
        match threat_assessment.threat_zones.current_zone {
            ThreatZone::VeryClose => {
                // Too close - need to create space
                self.positioning_system.optimal_zones.push(PositionZone {
                    zone_type: PositionZoneType::Optimal,
                    bounds: Rect::new(100.0, -50.0, 200.0, 100.0), // Simplified bounds
                    advantage_score: 0.8,
                    threat_level: 0.2,
                });
                self.positioning_system.movement_strategy = MovementStrategy::Defensive;
            }
            ThreatZone::Close => {
                // Good for pressure
                self.positioning_system.optimal_zones.push(PositionZone {
                    zone_type: PositionZoneType::Advantageous,
                    bounds: Rect::new(50.0, -50.0, 100.0, 100.0),
                    advantage_score: 0.9,
                    threat_level: 0.4,
                });
                self.positioning_system.movement_strategy = MovementStrategy::Aggressive;
            }
            ThreatZone::Medium => {
                // Neutral game
                self.positioning_system.optimal_zones.push(PositionZone {
                    zone_type: PositionZoneType::Neutral,
                    bounds: Rect::new(80.0, -50.0, 150.0, 100.0),
                    advantage_score: 0.5,
                    threat_level: 0.3,
                });
                self.positioning_system.movement_strategy = MovementStrategy::Neutral;
            }
            ThreatZone::Long => {
                // Good for projectiles
                self.positioning_system.optimal_zones.push(PositionZone {
                    zone_type: PositionZoneType::Advantageous,
                    bounds: Rect::new(150.0, -50.0, 250.0, 100.0),
                    advantage_score: 0.7,
                    threat_level: 0.2,
                });
                self.positioning_system.movement_strategy = MovementStrategy::Positioning;
            }
            ThreatZone::OutOfRange => {
                // Need to close distance
                self.positioning_system.movement_strategy = MovementStrategy::Aggressive;
            }
        }
    }

    /// Evaluate current position score
    fn evaluate_position_score(&self, threat_assessment: &AIThreatAssessment) -> f32 {
        let mut score = 0.5; // Base neutral score

        // Adjust based on threat level
        score -= threat_assessment.threat_level * 0.3;

        // Adjust based on distance
        match threat_assessment.threat_zones.current_zone {
            ThreatZone::VeryClose => score -= 0.2,
            ThreatZone::Close => score += 0.1,
            ThreatZone::Medium => score += 0.0,
            ThreatZone::Long => score += 0.1,
            ThreatZone::OutOfRange => score -= 0.3,
        }

        // Adjust based on opponent state
        if threat_assessment.opponent_analysis.is_in_recovery {
            score += 0.3; // Good position for punishment
        }

        if threat_assessment.opponent_analysis.is_attacking {
            score -= 0.2; // Dangerous position
        }

        score.max(0.0).min(1.0)
    }

    /// Analyze current situation for decision making
    fn analyze_situation(&self, threat_assessment: &AIThreatAssessment, ai_difficulty: &AIDifficulty) -> SituationAnalysis {
        let mut analysis = SituationAnalysis::default();

        // Analyze threat level
        analysis.threat_level = threat_assessment.threat_level;
        analysis.immediate_danger = threat_assessment.threat_level > 0.7;

        // Analyze distance
        analysis.distance = threat_assessment.closest_threat_distance;
        analysis.optimal_distance = matches!(
            threat_assessment.threat_zones.current_zone,
            ThreatZone::Close | ThreatZone::Medium
        );

        // Analyze opponent state
        analysis.opponent_vulnerable = threat_assessment.opponent_analysis.is_in_recovery ||
                                      threat_assessment.opponent_analysis.is_dizzy;
        analysis.opponent_aggressive = threat_assessment.opponent_analysis.is_attacking ||
                                      threat_assessment.opponent_analysis.is_jumping;

        // Analyze projectile situation
        analysis.projectile_threat = !self.projectile_awareness.tracked_projectiles.is_empty();

        // Analyze positioning
        analysis.position_advantage = self.positioning_system.position_score > 0.6;
        analysis.cornered = self.positioning_system.position_score < 0.3;

        // Analyze frame advantage
        analysis.frame_advantage = self.opponent_tracker.frame_advantage;

        // Difficulty-based analysis adjustments
        analysis.confidence_modifier = ai_difficulty.aggression_scale;

        analysis
    }

    /// Select tactical decision based on situation analysis
    fn select_tactical_decision(
        &self,
        analysis: &SituationAnalysis,
        character_patterns: &AIBehaviorPattern,
        ai_difficulty: &AIDifficulty,
        frame_count: u64,
    ) -> Option<AITacticalDecision> {
        // Priority-based decision making

        // 1. Handle immediate danger
        if analysis.immediate_danger {
            return self.select_defensive_decision(analysis, ai_difficulty);
        }

        // 2. Exploit opponent vulnerability
        if analysis.opponent_vulnerable {
            return self.select_aggressive_decision(analysis, character_patterns, ai_difficulty);
        }

        // 3. Handle projectile threats
        if analysis.projectile_threat {
            return self.select_anti_projectile_decision(analysis, ai_difficulty);
        }

        // 4. Positioning decisions
        if !analysis.optimal_distance {
            return self.select_positioning_decision(analysis, ai_difficulty);
        }

        // 5. Pattern-based decisions
        self.select_pattern_based_decision(analysis, character_patterns, ai_difficulty, frame_count)
    }

    /// Select defensive decision
    fn select_defensive_decision(&self, analysis: &SituationAnalysis, ai_difficulty: &AIDifficulty) -> Option<AITacticalDecision> {
        if analysis.cornered {
            // Try to escape corner
            if fastrand::f32() < ai_difficulty.aggression_scale {
                Some(AITacticalDecision::EscapeCorner)
            } else {
                Some(AITacticalDecision::Block)
            }
        } else if analysis.distance < 50 {
            // Very close - block or escape
            if fastrand::f32() < 0.7 {
                Some(AITacticalDecision::Block)
            } else {
                Some(AITacticalDecision::Retreat)
            }
        } else {
            // Medium distance - anti-air or retreat
            if analysis.opponent_aggressive && fastrand::f32() < ai_difficulty.aggression_scale {
                Some(AITacticalDecision::AntiAir)
            } else {
                Some(AITacticalDecision::Retreat)
            }
        }
    }

    /// Select aggressive decision
    fn select_aggressive_decision(
        &self,
        analysis: &SituationAnalysis,
        character_patterns: &AIBehaviorPattern,
        ai_difficulty: &AIDifficulty,
    ) -> Option<AITacticalDecision> {
        let aggression_chance = character_patterns.aggression * ai_difficulty.aggression_scale;

        if analysis.distance < 30 {
            // Very close - throw or combo
            if fastrand::f32() < aggression_chance * 0.3 {
                Some(AITacticalDecision::ThrowAttempt)
            } else {
                Some(AITacticalDecision::ComboAttack)
            }
        } else if analysis.distance < 80 {
            // Close range - pressure or special
            if fastrand::f32() < aggression_chance {
                Some(AITacticalDecision::PressureAttack)
            } else if character_patterns.special_move_frequency > 0.5 {
                // Use special move based on frequency
                Some(AITacticalDecision::SpecialAttack(SpecialMoveId::Hadoken)) // Default special move
            } else {
                Some(AITacticalDecision::PressureAttack)
            }
        } else {
            // Medium range - jump attack or special
            if fastrand::f32() < aggression_chance * 0.6 {
                Some(AITacticalDecision::JumpAttack)
            } else {
                Some(AITacticalDecision::PressureAttack)
            }
        }
    }

    /// Select anti-projectile decision
    fn select_anti_projectile_decision(&self, analysis: &SituationAnalysis, ai_difficulty: &AIDifficulty) -> Option<AITacticalDecision> {
        if !self.projectile_awareness.projectile_predictions.is_empty() {
            let prediction = &self.projectile_awareness.projectile_predictions[0];

            // Choose best avoidance option based on AI difficulty
            let mut best_option = None;
            let mut best_score = 0.0;

            for option in &prediction.avoidance_options {
                let score = option.success_probability * ai_difficulty.aggression_scale - option.risk_level;
                if score > best_score {
                    best_score = score;
                    best_option = Some(option.action);
                }
            }

            best_option
        } else {
            // Default anti-projectile behavior
            if analysis.distance > 100 {
                Some(AITacticalDecision::JumpAttack)
            } else {
                Some(AITacticalDecision::Block)
            }
        }
    }

    /// Select positioning decision
    fn select_positioning_decision(&self, analysis: &SituationAnalysis, ai_difficulty: &AIDifficulty) -> Option<AITacticalDecision> {
        if analysis.distance < 50 {
            // Too close
            Some(AITacticalDecision::CreateSpace)
        } else if analysis.distance > 200 {
            // Too far
            Some(AITacticalDecision::CloseDistance)
        } else if analysis.cornered {
            // Escape corner
            Some(AITacticalDecision::EscapeCorner)
        } else if analysis.position_advantage {
            // Maintain good position
            Some(AITacticalDecision::MaintainDistance)
        } else {
            // Improve position
            if fastrand::f32() < ai_difficulty.aggression_scale {
                Some(AITacticalDecision::CornerOpponent)
            } else {
                Some(AITacticalDecision::MaintainDistance)
            }
        }
    }

    /// Select pattern-based decision
    fn select_pattern_based_decision(
        &self,
        analysis: &SituationAnalysis,
        character_patterns: &AIBehaviorPattern,
        ai_difficulty: &AIDifficulty,
        frame_count: u64,
    ) -> Option<AITacticalDecision> {
        // Use character-specific patterns for decision making
        let pattern_weight = character_patterns.aggression;

        // Time-based decision variation
        let time_factor = (frame_count % 180) as f32 / 180.0; // 3-second cycle

        // Combine pattern weight with time factor and difficulty
        let decision_threshold = pattern_weight * ai_difficulty.aggression_scale * (0.5 + time_factor * 0.5);

        if decision_threshold > 0.7 {
            // Aggressive pattern
            if analysis.distance < 100 {
                Some(AITacticalDecision::PressureAttack)
            } else {
                Some(AITacticalDecision::CloseDistance)
            }
        } else if decision_threshold > 0.4 {
            // Neutral pattern
            Some(AITacticalDecision::Wait)
        } else {
            // Defensive pattern
            Some(AITacticalDecision::MaintainDistance)
        }
    }

    /// Record decision for history tracking
    fn record_decision(&mut self, decision: AITacticalDecision, threat_assessment: &AIThreatAssessment, frame_count: u64) {
        let event = AIDecisionEvent {
            decision,
            frame: frame_count,
            threat_level: threat_assessment.threat_level,
            distance: threat_assessment.closest_threat_distance,
            success: None, // Will be evaluated later
        };

        self.decision_history.push(event);

        // Limit history size
        if self.decision_history.len() > self.max_decision_history {
            self.decision_history.remove(0);
        }
    }

    /// Apply decision to AI core control signals
    fn apply_decision_to_ai_core(&self, decision: AITacticalDecision, ai_core: &mut AICore) {
        // Reset all control signals
        ai_core.control_signals = AIControlSignals::default();
        ai_core.flags.sig_attack = false;
        ai_core.flags.sig_special = false;

        // Apply decision-specific control signals
        match decision {
            AITacticalDecision::PressureAttack | AITacticalDecision::ComboAttack => {
                ai_core.flags.sig_attack = true;
                ai_core.punch_kick = 0; // Punching
            }
            AITacticalDecision::ThrowAttempt => {
                ai_core.control_signals.do_throw = true;
            }
            AITacticalDecision::Block => {
                ai_core.control_signals.do_block = true;
            }
            AITacticalDecision::JumpAttack | AITacticalDecision::AntiAir => {
                ai_core.control_signals.do_jump = true;
                ai_core.flags.sig_attack = true;
            }
            AITacticalDecision::SpecialAttack(_) => {
                ai_core.flags.sig_special = true;
            }
            AITacticalDecision::Retreat | AITacticalDecision::CreateSpace => {
                ai_core.movement.walk_direction = 2; // Walk backward (STEP_BACK)
            }
            AITacticalDecision::CloseDistance => {
                ai_core.movement.walk_direction = 1; // Walk forward (STEP_FORWARD)
            }
            _ => {
                // Other decisions don't directly map to control signals
                // They would be handled by higher-level AI systems
            }
        }
    }

    /// Evaluate decision success (called after action completes)
    pub fn evaluate_decision_success(&mut self, success: bool, frame_count: u64) {
        // Find the most recent decision within a reasonable time window
        for event in self.decision_history.iter_mut().rev() {
            if event.success.is_none() && frame_count - event.frame < 60 {
                event.success = Some(success);
                break;
            }
        }
    }

    /// Get decision statistics for debugging
    pub fn get_decision_statistics(&self) -> DecisionStatistics {
        let mut stats = DecisionStatistics::default();

        for event in &self.decision_history {
            stats.total_decisions += 1;

            if let Some(success) = event.success {
                if success {
                    stats.successful_decisions += 1;
                }
            }

            // Count decision types
            match event.decision {
                AITacticalDecision::PressureAttack | AITacticalDecision::ComboAttack => {
                    stats.aggressive_decisions += 1;
                }
                AITacticalDecision::Block | AITacticalDecision::Retreat => {
                    stats.defensive_decisions += 1;
                }
                _ => {
                    stats.neutral_decisions += 1;
                }
            }
        }

        stats.success_rate = if stats.total_decisions > 0 {
            stats.successful_decisions as f32 / stats.total_decisions as f32
        } else {
            0.0
        };

        stats
    }
}

/// Situation analysis for decision making
#[derive(Debug, Clone, Default)]
pub struct SituationAnalysis {
    pub threat_level: f32,
    pub immediate_danger: bool,
    pub distance: i16,
    pub optimal_distance: bool,
    pub opponent_vulnerable: bool,
    pub opponent_aggressive: bool,
    pub projectile_threat: bool,
    pub position_advantage: bool,
    pub cornered: bool,
    pub frame_advantage: i8,
    pub confidence_modifier: f32,
}

/// Decision statistics for debugging and tuning
#[derive(Debug, Clone, Default)]
pub struct DecisionStatistics {
    pub total_decisions: u32,
    pub successful_decisions: u32,
    pub success_rate: f32,
    pub aggressive_decisions: u32,
    pub defensive_decisions: u32,
    pub neutral_decisions: u32,
}

// Bevy Systems for AI Decision Making and Threat Assessment

/// System to update AI threat assessment each frame
pub fn ai_threat_assessment_system(
    mut ai_query: Query<(
        &mut AIThreatAssessment,
        &mut AICore,
        &AIDifficulty,
        &Transform,
    ), With<Fighter>>,
    opponent_query: Query<(&Transform, &FighterStateData), (With<Fighter>, Without<AIThreatAssessment>)>,
    time: Res<Time>,
) {
    let frame_count = (time.elapsed_secs() * 60.0) as u64;

    for (mut threat_assessment, mut ai_core, ai_difficulty, transform) in ai_query.iter_mut() {
        if let Ok((opponent_transform, opponent_state)) = opponent_query.get_single() {
            let ai_position = transform.translation.truncate();
            let opponent_position = opponent_transform.translation.truncate();

            threat_assessment.update_threat_assessment(
                &mut ai_core,
                ai_difficulty,
                ai_position,
                opponent_position,
                opponent_state,
                frame_count,
            );
        }
    }
}

/// System to execute AI decision making each frame
pub fn ai_decision_making_system(
    mut ai_query: Query<(
        &mut AIDecisionMaker,
        &AIThreatAssessment,
        &mut AICore,
        &AIDifficulty,
        &AIBehaviorPattern,
    ), With<Fighter>>,
    time: Res<Time>,
) {
    let frame_count = (time.elapsed_secs() * 60.0) as u64;

    for (mut decision_maker, threat_assessment, mut ai_core, ai_difficulty, character_patterns) in ai_query.iter_mut() {
        // Make tactical decision based on current situation
        if let Some(decision) = decision_maker.make_decision(
            threat_assessment,
            &mut ai_core,
            ai_difficulty,
            character_patterns,
            frame_count,
        ) {
            // Decision has been made and applied to AI core
            // The decision will be executed by other AI systems
        }
    }
}

/// System to evaluate AI decision success based on game outcomes
pub fn ai_decision_evaluation_system(
    mut ai_query: Query<&mut AIDecisionMaker, With<Fighter>>,
    hit_events: EventReader<HitEvent>,
    time: Res<Time>,
) {
    let frame_count = (time.elapsed_secs() * 60.0) as u64;

    // This is a simplified evaluation system
    // In a full implementation, you would track various success metrics
    for mut decision_maker in ai_query.iter_mut() {
        // For now, just mark recent decisions as successful if no hits were taken
        // This would be expanded to include more sophisticated success criteria
        if hit_events.is_empty() {
            decision_maker.evaluate_decision_success(true, frame_count);
        }
    }
}
