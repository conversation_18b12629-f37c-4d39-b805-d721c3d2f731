// AI system module - comprehensive AI decision making and behavior trees
// Porting the sophisticated C99 AI system to Rust with Bevy ECS integration

pub mod ai_core;
pub mod ai_strategy;
pub mod ai_difficulty;
pub mod ai_threat;
pub mod ai_debug;
pub mod ai_character_patterns;
pub mod ai_character_patterns_extended;
pub mod ai_character_patterns_final;
pub mod ai_behavior_tree;

pub use ai_core::*;
pub use ai_strategy::*;
pub use ai_difficulty::*;
pub use ai_threat::*;
pub use ai_debug::*;
pub use ai_behavior_tree::*;
