//! # Character System
//!
//! Character selection, loading, and management system for Street Fighter II.
//! Handles character instantiation, move set loading, and character-specific behavior.

use bevy::prelude::*;
use sf2_types::{
    FighterId, FighterStateData, Fixed16_16, Point16, Vect16,
    InputState, ButtonFlags, InputDirection,
    character_data::CharacterDatabase,
    move_sets::{MoveSetFactory, MoveSet},
    character_traits::{FighterCharacter, FighterStats, FighterAction, GlitchId},
};
use crate::components::*;
use crate::characters::*;
use std::collections::HashMap;

/// Resource containing all character data
#[derive(Resource)]
pub struct CharacterRegistry {
    pub database: CharacterDatabase,
    pub move_sets: HashMap<FighterId, MoveSet>,
    pub character_implementations: HashMap<FighterId, Box<dyn FighterCharacter>>,
}

/// Component for character selection state
#[derive(Component, Debug)]
pub struct CharacterSelector {
    pub player_number: u8,
    pub selected_character: Option<FighterId>,
    pub cursor_position: usize,
    pub confirmed: bool,
}

/// Event for character selection
#[derive(Event, Debug)]
pub struct CharacterSelectedEvent {
    pub player_number: u8,
    pub character_id: FighterId,
}

/// Event for character loading completion
#[derive(Event, Debug)]
pub struct CharacterLoadedEvent {
    pub entity: Entity,
    pub character_id: FighterId,
    pub player_number: u8,
}

/// System for handling character selection input
pub fn character_selection_system(
    keyboard: Res<ButtonInput<KeyCode>>,
    mut selector_query: Query<&mut CharacterSelector>,
    mut selection_events: EventWriter<CharacterSelectedEvent>,
    character_registry: Res<CharacterRegistry>,
) {
    for mut selector in selector_query.iter_mut() {
        if selector.confirmed {
            continue;
        }
        
        let character_ids = character_registry.database.get_all_character_ids();
        
        // Handle cursor movement
        if keyboard.just_pressed(KeyCode::ArrowLeft) {
            if selector.cursor_position > 0 {
                selector.cursor_position -= 1;
            } else {
                selector.cursor_position = character_ids.len() - 1;
            }
        }
        
        if keyboard.just_pressed(KeyCode::ArrowRight) {
            selector.cursor_position = (selector.cursor_position + 1) % character_ids.len();
        }
        
        // Update selected character
        if let Some(&character_id) = character_ids.get(selector.cursor_position) {
            selector.selected_character = Some(character_id);
        }
        
        // Handle selection confirmation
        let confirm_key = match selector.player_number {
            1 => KeyCode::Enter,
            2 => KeyCode::Space,
            _ => KeyCode::Enter,
        };
        
        if keyboard.just_pressed(confirm_key) {
            if let Some(character_id) = selector.selected_character {
                selector.confirmed = true;
                selection_events.send(CharacterSelectedEvent {
                    player_number: selector.player_number,
                    character_id,
                });
            }
        }
    }
}

/// System for loading selected characters
pub fn character_loading_system(
    mut commands: Commands,
    mut selection_events: EventReader<CharacterSelectedEvent>,
    mut loaded_events: EventWriter<CharacterLoadedEvent>,
    character_registry: Res<CharacterRegistry>,
) {
    for event in selection_events.read() {
        let character_data = character_registry.database
            .get_character(event.character_id)
            .expect("Character data not found");
        
        let move_set = character_registry.move_sets
            .get(&event.character_id)
            .expect("Move set not found");
        
        // Create fighter entity
        let entity = commands.spawn((
            Fighter {
                fighter_id: event.character_id,
                player_number: event.player_number,
            },
            Position {
                x: Fixed16_16::from_i16(if event.player_number == 1 { -100 } else { 100 }),
                y: Fixed16_16::ZERO,
            },
            Velocity {
                x: Fixed16_16::ZERO,
                y: Fixed16_16::ZERO,
            },
            Health {
                current: character_data.stats.health as i32,
                maximum: character_data.stats.health as i32,
            },
            FighterStateData::new(),
            CharacterMoveSet {
                move_set: move_set.clone(),
            },
            CharacterStats {
                stats: character_data.stats,
            },
            Name::new(character_data.name.clone()),
        )).id();
        
        loaded_events.send(CharacterLoadedEvent {
            entity,
            character_id: event.character_id,
            player_number: event.player_number,
        });
    }
}

/// Component containing character move set
#[derive(Component, Debug, Clone)]
pub struct CharacterMoveSet {
    pub move_set: MoveSet,
}

/// Component containing character stats
#[derive(Component, Debug, Clone)]
pub struct CharacterStats {
    pub stats: FighterStats,
}

/// System for character-specific behavior updates
pub fn character_behavior_system(
    mut fighter_query: Query<(
        Entity,
        &Fighter,
        &mut FighterStateData,
        &mut Position,
        &mut Velocity,
        &PlayerInput,
    )>,
    character_registry: Res<CharacterRegistry>,
) {
    for (entity, fighter, mut state, mut position, mut velocity, input) in fighter_query.iter_mut() {
        if let Some(character_impl) = character_registry.character_implementations.get(&fighter.fighter_id) {
            // Process character-specific input
            let actions = character_impl.process_input(&input.current_input, &mut state);
            
            // Execute actions
            for action in actions {
                match action {
                    FighterAction::StateTransition(new_state) => {
                        state.transition_state(new_state);
                    }
                    FighterAction::SetVelocity(vel_x, vel_y) => {
                        // Convert Fixed8_8 to Fixed16_16 by shifting left 8 bits
                        velocity.x = Fixed16_16::from_raw(vel_x.raw() as i32 * 256);
                        velocity.y = Fixed16_16::from_raw(vel_y.raw() as i32 * 256);
                    }
                    FighterAction::SetAnimation(anim_id) => {
                        // Handle animation changes
                    }
                    _ => {
                        // Handle other actions
                    }
                }
            }
            
            // Update character physics
            character_impl.update_physics(&mut state, &mut position, &mut velocity);
        }
    }
}

/// System for character glitch handling
pub fn character_glitch_system(
    mut fighter_query: Query<(&Fighter, &mut FighterStateData)>,
    character_registry: Res<CharacterRegistry>,
    keyboard: Res<ButtonInput<KeyCode>>,
) {
    // Check for glitch activation inputs
    for (fighter, mut state) in fighter_query.iter_mut() {
        let character_data = character_registry.database
            .get_character(fighter.fighter_id)
            .unwrap();
        
        // Guile's invisible throw glitch
        if character_data.glitches.contains(&GlitchId::GuileInvisibleThrow) {
            // Check for specific input sequence: charge back, forward + fierce + roundhouse
            // This is a simplified check - full implementation would use input buffer
            if keyboard.pressed(KeyCode::ArrowLeft) && 
               keyboard.just_pressed(KeyCode::ArrowRight) &&
               keyboard.pressed(KeyCode::KeyZ) && // fierce
               keyboard.pressed(KeyCode::KeyX) { // roundhouse
                
                // Execute invisible throw glitch
                info!("Guile invisible throw glitch activated!");
                // Implementation would handle the actual glitch behavior
            }
        }
        
        // Guile's handcuff glitch
        if character_data.glitches.contains(&GlitchId::GuileHandcuff) {
            // Check for handcuff glitch conditions
            // Implementation would check for specific timing and input conditions
        }
        
        // Blanka's rolling attack damage glitch
        if character_data.glitches.contains(&GlitchId::BlankaRollingDamage) {
            // Check if Blanka is interrupted during rolling attack
            // Implementation would apply 60%+ damage if hit during roll
        }
    }
}

/// Plugin for character system
pub struct CharacterSystemPlugin;

impl Plugin for CharacterSystemPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_event::<CharacterSelectedEvent>()
            .add_event::<CharacterLoadedEvent>()
            .add_systems(Update, (
                character_selection_system,
                character_loading_system,
                character_behavior_system,
                character_glitch_system,
            ));
    }
}

impl Default for CharacterRegistry {
    fn default() -> Self {
        Self::new()
    }
}

impl CharacterRegistry {
    /// Create new character registry with all character data
    pub fn new() -> Self {
        let database = CharacterDatabase::new();
        let mut move_sets = HashMap::new();
        let mut character_implementations = HashMap::new();
        
        // Load move sets for all characters
        for &character_id in database.get_all_character_ids().iter() {
            let move_set = MoveSetFactory::create_move_set(character_id);
            move_sets.insert(character_id, move_set);
        }
        
        // Register character implementations
        character_implementations.insert(FighterId::Ryu, Box::new(RyuCharacter) as Box<dyn FighterCharacter>);
        character_implementations.insert(FighterId::Ken, Box::new(KenCharacter) as Box<dyn FighterCharacter>);
        
        Self {
            database,
            move_sets,
            character_implementations,
        }
    }
}
