//! # Blanka Character Implementation
//!
//! <PERSON><PERSON><PERSON> is the wild character with unique rolling attacks and electric moves.
//! Known for Rolling Attack, Electric Thunder, and unique movement patterns.

use bevy::prelude::*;
use sf2_types::{
    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,
    FighterStateData, FighterState, Point16, Vect16,
    character_traits::{
        FighterCharacter, FighterStats, FighterAction, SpecialMoveResult, 
        FrameData, HitboxData, HurtboxData, AttackData, HitResponse, BlockResponse,
        CharacterGlitches, GlitchId
    },
    move_sets::{SpecialMove, MoveInput, MoveInputType, MoveCondition},
};

/// Blanka character implementation
#[derive(Debug, <PERSON><PERSON>)]
pub struct Blanka {
    pub stats: FighterStats,
    pub rolling_state: RollingState,
    pub electric_charge: u8, // Electric charge buildup
    pub rolling_momentum: Fixed8_8, // Current rolling momentum
    pub rolling_damage_immunity: bool, // Rolling damage glitch
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Partial<PERSON>q)]
pub enum RollingState {
    Normal,
    Rolling,
    RollingRecovery,
}

impl Default for Blanka {
    fn default() -> Self {
        Self {
            stats: FighterStats {
                health: 16200,
                stun_threshold: 7000,
                walk_speed: Fixed8_8::from_i16(115),
                jump_speed: Fixed8_8::from_i16(420), // High jump
                gravity: Fixed8_8::from_i16(30),
                throw_range: 19,
                max_projectiles: 0, // No projectiles
            },
            rolling_state: RollingState::Normal,
            electric_charge: 0,
            rolling_momentum: Fixed8_8::ZERO,
            rolling_damage_immunity: false,
        }
    }
}

impl FighterCharacter for Blanka {
    fn get_fighter_id(&self) -> sf2_types::FighterId {
        sf2_types::FighterId::Blanka
    }

    fn get_stats(&self) -> &FighterStats {
        &self.stats
    }

    fn update_ai(&self, _state: &FighterStateData, _opponent_state: &FighterStateData, distance: Fixed8_8) -> Option<FighterAction> {
        // Blanka AI: Aggressive, uses rolling attacks and electric moves
        if distance > Fixed8_8::from_i16(120) {
            Some(FighterAction::SpecialMove("Rolling Attack".to_string()))
        } else if distance < Fixed8_8::from_i16(60) {
            Some(FighterAction::SpecialMove("Electric Thunder".to_string()))
        } else {
            Some(FighterAction::Move(Vect16 { 
                x: Fixed8_8::from_i16(100), // Move forward aggressively
                y: Fixed8_8::ZERO 
            }))
        }
    }

    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Handle rolling physics
        match self.rolling_state {
            RollingState::Rolling => {
                // Override normal physics during rolling
                velocity.x = self.rolling_momentum;
                velocity.y = Fixed8_8::ZERO; // Stay on ground while rolling
                
                // Rolling collision detection would be different
                position.x = position.x.saturating_add((velocity.x.raw() >> 8) as i16);
                
                // Gradually reduce rolling momentum
                let mut new_momentum = self.rolling_momentum;
                if new_momentum > Fixed8_8::ZERO {
                    new_momentum = new_momentum - Fixed8_8::from_i16(12);
                    if new_momentum < Fixed8_8::from_i16(50) {
                        // End rolling when momentum is too low
                        state.current_state = FighterState::Standing;
                    }
                } else {
                    new_momentum = new_momentum + Fixed8_8::from_i16(12);
                    if new_momentum > Fixed8_8::from_i16(-50) {
                        state.current_state = FighterState::Standing;
                    }
                }
                return;
            },
            RollingState::RollingRecovery => {
                // Slower movement during recovery
                velocity.x = velocity.x / Fixed8_8::from_i16(2);
            },
            RollingState::Normal => {
                // Normal physics
            }
        }

        // Standard physics
        position.x = position.x.saturating_add((velocity.x.raw() >> 8) as i16);
        position.y = position.y.saturating_add((velocity.y.raw() >> 8) as i16);
        
        // Apply gravity
        velocity.y = velocity.y + self.stats.gravity;
        
        // Ground collision
        if position.y >= 0 {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;
            if matches!(state.current_state, FighterState::Airborne) {
                state.current_state = FighterState::Standing;
            }
        }
    }

    fn get_frame_data(&self, action: FighterAction) -> FrameData {
        match action {
            FighterAction::LightPunch => FrameData {
                startup: 4,
                active: 3,
                recovery: 6,
                frame_advantage_hit: 4,
                frame_advantage_block: 2,
                damage: 220,
                stun: 110,
            },
            FighterAction::MediumPunch => FrameData {
                startup: 6,
                active: 4,
                recovery: 8,
                frame_advantage_hit: 5,
                frame_advantage_block: 2,
                damage: 420,
                stun: 210,
            },
            FighterAction::HeavyPunch => FrameData {
                startup: 8,
                active: 5,
                recovery: 12,
                frame_advantage_hit: 6,
                frame_advantage_block: -1,
                damage: 650,
                stun: 320,
            },
            FighterAction::LightKick => FrameData {
                startup: 5,
                active: 3,
                recovery: 7,
                frame_advantage_hit: 4,
                frame_advantage_block: 2,
                damage: 270,
                stun: 130,
            },
            FighterAction::MediumKick => FrameData {
                startup: 7,
                active: 4,
                recovery: 9,
                frame_advantage_hit: 5,
                frame_advantage_block: 1,
                damage: 470,
                stun: 230,
            },
            FighterAction::HeavyKick => FrameData {
                startup: 10,
                active: 6,
                recovery: 14,
                frame_advantage_hit: 7,
                frame_advantage_block: -2,
                damage: 720,
                stun: 360,
            },
            FighterAction::SpecialMove(ref name) => {
                match name.as_str() {
                    "Rolling Attack" => FrameData {
                        startup: 12,
                        active: 40, // Long active frames while rolling
                        recovery: 20,
                        frame_advantage_hit: 3,
                        frame_advantage_block: -8,
                        damage: 700,
                        stun: 500,
                    },
                    "Electric Thunder" => FrameData {
                        startup: 6,
                        active: 15, // Multi-hit electric
                        recovery: 18,
                        frame_advantage_hit: 4,
                        frame_advantage_block: -3,
                        damage: 120, // Per hit
                        stun: 80, // Per hit
                    },
                    "Vertical Rolling" => FrameData {
                        startup: 15,
                        active: 25,
                        recovery: 30,
                        frame_advantage_hit: 2,
                        frame_advantage_block: -10,
                        damage: 800,
                        stun: 600,
                    },
                    _ => FrameData::default(),
                }
            },
            _ => FrameData::default(),
        }
    }

    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {
        match self.rolling_state {
            RollingState::Rolling => {
                // Rolling attack has different hitbox
                vec![
                    HitboxData {
                        x: 0, // Centered on Blanka
                        y: -30,
                        width: 50, // Wide rolling hitbox
                        height: 40,
                        damage: 700,
                        stun: 500,
                        knockback: Vect16 { x: Fixed8_8::from_i16(120), y: Fixed8_8::from_i16(-60) },
                    }
                ]
            },
            _ => {
                // Normal hitboxes
                vec![
                    HitboxData {
                        x: 22,
                        y: -40,
                        width: 32,
                        height: 22,
                        damage: 420,
                        stun: 210,
                        knockback: Vect16 { x: Fixed8_8::from_i16(110), y: Fixed8_8::from_i16(-55) },
                    }
                ]
            }
        }
    }

    fn get_hurtboxes(&self, _state: &FighterStateData) -> Vec<HurtboxData> {
        match self.rolling_state {
            RollingState::Rolling => {
                // Smaller hurtbox while rolling (harder to hit)
                vec![
                    HurtboxData { x: -20, y: -35, width: 40, height: 35 }, // Compact rolling form
                ]
            },
            _ => {
                // Normal hurtboxes - Blanka is a bit wider than average
                vec![
                    HurtboxData { x: -20, y: -65, width: 40, height: 65 }, // Body
                    HurtboxData { x: -15, y: -85, width: 30, height: 20 }, // Head
                ]
            }
        }
    }

    fn on_hit(&self, attack: &AttackData, _state: &mut FighterStateData) -> HitResponse {
        // Rolling damage immunity glitch
        if self.rolling_damage_immunity && self.rolling_state == RollingState::Rolling {
            HitResponse {
                damage_taken: 0, // No damage while rolling (glitch)
                stun_taken: 0,
                knockback: Vect16 { x: Fixed8_8::ZERO, y: Fixed8_8::ZERO },
                state_change: None, // Don't change state
            }
        } else {
            HitResponse {
                damage_taken: 100,
                stun_taken: 50,
                knockback: Vect16 { x: Fixed8_8::from_i16(90), y: Fixed8_8::from_i16(-40) },
                state_change: Some(FighterState::Reel),
            }
        }
    }

    fn on_block(&self, _attack: &AttackData, _state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: 12,
            pushback: Fixed8_8::from_i16(25),
            frame_advantage: -1,
        }
    }

    fn get_special_moves(&self) -> Vec<SpecialMove> {
        vec![
            // Rolling Attack (Charge Back, Forward + Punch)
            SpecialMove {
                name: "Rolling Attack".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 45,
                        conditions: vec![MoveCondition::Hold],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 3,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Electric Thunder (Punch rapidly)
            SpecialMove {
                name: "Electric Thunder".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Vertical Rolling (Charge Down, Up + Kick)
            SpecialMove {
                name: "Vertical Rolling".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 45,
                        conditions: vec![MoveCondition::Hold],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Up),
                        duration: 3,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Kick),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
        ]
    }

    fn check_special_moves(&self, input: &InputState, _state: &FighterStateData) -> Option<FighterAction> {
        // Check for Rolling Attack
        if input.buttons.any_punch() && input.current_direction == InputDirection::Right {
            return Some(FighterAction::SpecialMove("Rolling Attack".to_string()));
        }

        // Check for Electric Thunder (rapid punch)
        if input.buttons.any_punch() {
            return Some(FighterAction::SpecialMove("Electric Thunder".to_string()));
        }

        // Check for Vertical Rolling
        if input.buttons.any_kick() && input.current_direction == InputDirection::Up {
            return Some(FighterAction::SpecialMove("Vertical Rolling".to_string()));
        }

        None
    }

    fn execute_special_move(&self, move_name: &str, _state: &mut FighterStateData) -> SpecialMoveResult {
        match move_name {
            "Rolling Attack" => self.execute_rolling_attack(_state),
            "Electric Thunder" => self.execute_electric_thunder(_state),
            "Vertical Rolling" => self.execute_vertical_rolling(_state),
            _ => SpecialMoveResult::Failed,
        }
    }
}

impl Blanka {
    fn execute_rolling_attack(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 700,
            stun: 500,
            meter_gain: 200,
            projectile: None,
        }
    }

    fn execute_electric_thunder(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 120 * 6, // 6 hits
            stun: 80 * 6,
            meter_gain: 180,
            projectile: None,
        }
    }

    fn execute_vertical_rolling(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 800,
            stun: 600,
            meter_gain: 250,
            projectile: None,
        }
    }
}

impl CharacterGlitches for Blanka {
    fn has_glitch(&self, glitch_id: GlitchId) -> bool {
        matches!(glitch_id, GlitchId::BlankaRollingDamage)
    }

    fn execute_glitch(&self, glitch_id: GlitchId, _state: &mut FighterStateData) -> bool {
        match glitch_id {
            GlitchId::BlankaRollingDamage => {
                // Activate rolling damage immunity
                // In a real implementation, this would set rolling_damage_immunity = true
                true
            },
            _ => false,
        }
    }
}
