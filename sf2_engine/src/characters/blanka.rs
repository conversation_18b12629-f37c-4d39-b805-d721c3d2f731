// Blanka character implementation for SF2 Rust port
// Maintains high fidelity to original C99 implementation
// Preserves character-specific mechanics and authentic glitches

use bevy::prelude::*;
use sf2_types::{
    Fixed8_8, InputState, InputDirection, Point16,
    FighterStateData, FighterState, Vect16, Rect8, FighterId,
    character_traits::{
        FighterCharacter, FighterStats, FighterAction, SpecialMoveResult,
        FrameData, HitboxData, HurtboxData, AttackData, HitResponse, BlockResponse,
        MoveId, AIBehaviorPattern, CharacterSounds, CharacterEffects, BodyPart,
        CharacterGlitches, GlitchId, GlitchResult, EffectId
    },
    fighter::SpecialMoveId,
};

#[derive(Component, Debug, Clone)]
pub struct Blanka {
    pub stats: FighterStats,
    pub rolling_state: bool,
    pub electric_charge: u8,
    pub glitches_enabled: bool,
}

impl Default for Blanka {
    fn default() -> Self {
        Self {
            stats: FighterStats {
                health: 1000,
                stun_threshold: 1000,
                walk_speed: Fixed8_8::from_i16(45),
                jump_speed: Fixed8_8::from_i16(120),
                gravity: Fixed8_8::from_i16(30),
                throw_range: 19,
                max_projectiles: 0,
            },
            rolling_state: false,
            electric_charge: 0,
            glitches_enabled: true,
        }
    }
}

impl FighterCharacter for Blanka {
    fn fighter_id(&self) -> FighterId {
        FighterId::Blanka
    }

    fn name(&self) -> &'static str {
        "Blanka"
    }

    fn base_stats(&self) -> FighterStats {
        self.stats.clone()
    }

    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {
        let mut actions = Vec::new();

        // Check for special moves first
        if input.buttons.any_punch() && input.direction == InputDirection::Down {
            actions.push(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::ElectricThunder)));
        } else if input.buttons.any_kick() && input.direction == InputDirection::Left {
            actions.push(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::RollingAttack)));
        } else if input.buttons.any_punch() && input.direction == InputDirection::Up {
            actions.push(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::ElectricThunder)));
        }

        // Basic movement
        match input.direction {
            InputDirection::Left => {
                actions.push(FighterAction::SetVelocity(-self.stats.walk_speed, Fixed8_8::ZERO));
            },
            InputDirection::Right => {
                actions.push(FighterAction::SetVelocity(self.stats.walk_speed, Fixed8_8::ZERO));
            },
            _ => {},
        }

        actions
    }

    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Apply gravity
        velocity.y += self.stats.gravity;

        // Update position
        position.x += velocity.x.integer_part() as i16;
        position.y += velocity.y.integer_part() as i16;

        // Ground collision
        if position.y >= 0 {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;

            if state.state == FighterState::Jumping {
                state.state = FighterState::Normal;
            }
        }

        // Apply friction when on ground
        if position.y == 0 {
            velocity.x *= Fixed8_8::from_f32(0.85);
        }
    }

    fn get_frame_data(&self, move_id: MoveId) -> FrameData {
        match move_id {
            MoveId::StandingLightPunch => FrameData {
                startup_frames: 4,
                active_frames: 3,
                recovery_frames: 6,
                block_stun: 2,
                hit_stun: 4,
                damage: 220,
                stun_damage: 110,
                knockback_x: 10,
                knockback_y: 0,
            },
            MoveId::StandingMediumPunch => FrameData {
                startup_frames: 6,
                active_frames: 4,
                recovery_frames: 8,
                block_stun: 2,
                hit_stun: 5,
                damage: 420,
                stun_damage: 210,
                knockback_x: 15,
                knockback_y: 0,
            },
            MoveId::StandingHeavyPunch => FrameData {
                startup_frames: 8,
                active_frames: 5,
                recovery_frames: 12,
                block_stun: 1,
                hit_stun: 6,
                damage: 650,
                stun_damage: 320,
                knockback_x: 25,
                knockback_y: 0,
            },
            _ => FrameData {
                startup_frames: 0,
                active_frames: 0,
                recovery_frames: 0,
                block_stun: 0,
                hit_stun: 0,
                damage: 0,
                stun_damage: 0,
                knockback_x: 0,
                knockback_y: 0,
            },
        }
    }

    fn get_hitboxes(&self, _state: &FighterStateData) -> Vec<HitboxData> {
        vec![]
    }

    fn get_hurtboxes(&self, _state: &FighterStateData) -> Vec<HurtboxData> {
        vec![
            HurtboxData {
                rect: Rect8::new(-20, -65, 40, 65),
                vulnerability: 1.0,
                body_part: BodyPart::Body,
            }
        ]
    }

    fn on_hit(&self, attack: &AttackData, _state: &mut FighterStateData) -> HitResponse {
        HitResponse {
            damage_taken: attack.damage,
            stun_taken: attack.stun,
            knockback: attack.knockback,
            new_state: FighterState::Reel,
            hit_stun_frames: 15,
            effects: vec![]
        }
    }

    fn on_block(&self, attack: &AttackData, _state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: attack.damage / 8,
            block_stun_frames: 8,
            pushback: Fixed8_8::from_i16(10),
            effects: vec![]
        }
    }

    fn execute_special_move(&self, move_id: SpecialMoveId, _state: &mut FighterStateData) -> SpecialMoveResult {
        match move_id {
            SpecialMoveId::RollingAttack => {
                SpecialMoveResult {
                    success: true,
                    actions: vec![FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::RollingAttack))],
                    meter_cost: 0,
                    recovery_frames: 20,
                }
            },
            SpecialMoveId::ElectricThunder => {
                SpecialMoveResult {
                    success: true,
                    actions: vec![FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::ElectricThunder))],
                    meter_cost: 0,
                    recovery_frames: 18,
                }
            },
            _ => SpecialMoveResult {
                success: false,
                actions: vec![],
                meter_cost: 0,
                recovery_frames: 0,
            },
        }
    }

    fn get_ai_behavior(&self, _difficulty: u8) -> AIBehaviorPattern {
        AIBehaviorPattern {
            aggression: 0.8,
            defense: 0.6,
            special_move_frequency: 0.7,
            jump_frequency: 0.4,
            preferred_distance: Fixed8_8::from_i16(120),
            reaction_time: 12,
        }
    }

    fn get_sound_effects(&self) -> CharacterSounds {
        CharacterSounds {
            voice_clips: vec![0xB0, 0xB1, 0xB2],
            attack_sounds: vec![0x90, 0x91, 0x92],
            special_move_sounds: vec![0xB0, 0xB1, 0xB2],
            hit_sounds: vec![0xA0, 0xA1],
            victory_sound: 0xB5,
        }
    }

    fn get_visual_effects(&self) -> CharacterEffects {
        CharacterEffects {
            hit_sparks: vec![EffectId::HitSpark],
            special_effects: vec![EffectId::Lightning, EffectId::Custom(300)],
            victory_effects: vec![EffectId::Custom(105)],
        }
    }

}

impl CharacterGlitches for Blanka {
    fn has_glitch(&self, glitch_id: GlitchId) -> bool {
        match glitch_id {
            GlitchId::BlankaRollingDamage => true,
            _ => false,
        }
    }

    fn execute_glitch(&self, glitch_id: GlitchId, _state: &mut FighterStateData) -> GlitchResult {
        match glitch_id {
            GlitchId::BlankaRollingDamage => {
                GlitchResult {
                    executed: true,
                    actions: vec![FighterAction::StateTransition(FighterState::Normal)],
                    description: "Rolling attack damage immunity activated".to_string(),
                }
            },
            _ => GlitchResult {
                executed: false,
                actions: vec![],
                description: "Unknown glitch".to_string(),
            },
        }
    }
}
