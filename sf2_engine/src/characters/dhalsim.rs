//! # Dhalsim Character Implementation
//!
//! <PERSON><PERSON><PERSON><PERSON> is the long-range character with unique stretchy limbs and teleportation.
//! Known for Yoga Fire, Yoga Flame, stretchy attacks, and teleportation.

use bevy::prelude::*;
use sf2_types::{
    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,
    FighterStateData, FighterState, Point16, Vect16,
    character_traits::{
        FighterCharacter, FighterStats, FighterAction, SpecialMoveResult, 
        FrameData, HitboxData, HurtboxData, AttackData, HitResponse, BlockResponse,
        CharacterGlitches, GlitchId
    },
    move_sets::{SpecialMove, MoveInput, MoveInputType, MoveCondition},
};

/// Dhalsim character implementation
#[derive(Debug, Clone)]
pub struct Dhalsim {
    pub stats: FighterStats,
    pub limb_extension: u8, // Current limb extension level
    pub teleport_cooldown: u8, // Teleport cooldown timer
    pub yoga_fire_count: u8, // Number of yoga fires on screen
    pub meditation_state: bool, // Enhanced recovery state
}

impl Default for Dhalsim {
    fn default() -> Self {
        Self {
            stats: FighterStats {
                health: 14500, // Lowest health
                stun_threshold: 6500, // Low stun threshold
                walk_speed: Fixed8_8::from_i16(105),
                jump_speed: Fixed8_8::from_i16(370),
                gravity: Fixed8_8::from_i16(26), // Floatiest
                throw_range: 35, // Longest range due to stretchy limbs
                max_projectiles: 2, // Yoga Fire/Flame
            },
            limb_extension: 0,
            teleport_cooldown: 0,
            yoga_fire_count: 0,
            meditation_state: false,
        }
    }
}

impl FighterCharacter for Dhalsim {
    fn get_fighter_id(&self) -> sf2_types::FighterId {
        sf2_types::FighterId::Dhalsim
    }

    fn get_stats(&self) -> &FighterStats {
        &self.stats
    }

    fn update_ai(&self, _state: &FighterStateData, _opponent_state: &FighterStateData, distance: Fixed8_8) -> Option<FighterAction> {
        // Dhalsim AI: Very defensive, prefers long range
        if distance > Fixed8_8::from_i16(200) {
            Some(FighterAction::SpecialMove("Yoga Fire".to_string()))
        } else if distance < Fixed8_8::from_i16(80) && self.teleport_cooldown == 0 {
            Some(FighterAction::SpecialMove("Yoga Teleport".to_string()))
        } else if distance < Fixed8_8::from_i16(120) {
            Some(FighterAction::SpecialMove("Yoga Flame".to_string()))
        } else {
            Some(FighterAction::Move(Vect16 { 
                x: Fixed8_8::from_i16(-60), // Back away
                y: Fixed8_8::ZERO 
            }))
        }
    }

    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Handle teleport cooldown
        if self.teleport_cooldown > 0 {
            // Reduce cooldown each frame
        }

        // Meditation state affects recovery
        if self.meditation_state {
            // Faster recovery from attacks
            if matches!(state.current_state, FighterState::Reel) {
                // Reduce recovery time
            }
        }

        // Floaty physics - Dhalsim has unique air movement
        if matches!(state.current_state, FighterState::Airborne) {
            // Slower fall speed, more air control
            velocity.y = velocity.y + (self.stats.gravity / Fixed8_8::from_i16(2));
        } else {
            velocity.y = velocity.y + self.stats.gravity;
        }

        // Standard physics
        position.x = position.x.saturating_add((velocity.x.raw() >> 8) as i16);
        position.y = position.y.saturating_add((velocity.y.raw() >> 8) as i16);
        
        // Ground collision
        if position.y >= 0 {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;
            if matches!(state.current_state, FighterState::Airborne) {
                state.current_state = FighterState::Standing;
            }
        }
    }

    fn get_frame_data(&self, action: FighterAction) -> FrameData {
        match action {
            FighterAction::LightPunch => FrameData {
                startup: 8, // Slower startup due to stretching
                active: 4,
                recovery: 12,
                frame_advantage_hit: 2,
                frame_advantage_block: -1,
                damage: 180, // Lower damage
                stun: 90,
            },
            FighterAction::MediumPunch => FrameData {
                startup: 12,
                active: 6,
                recovery: 16,
                frame_advantage_hit: 3,
                frame_advantage_block: -2,
                damage: 350,
                stun: 175,
            },
            FighterAction::HeavyPunch => FrameData {
                startup: 16,
                active: 8,
                recovery: 22,
                frame_advantage_hit: 4,
                frame_advantage_block: -4,
                damage: 520,
                stun: 260,
            },
            FighterAction::LightKick => FrameData {
                startup: 10,
                active: 5,
                recovery: 14,
                frame_advantage_hit: 2,
                frame_advantage_block: -1,
                damage: 200,
                stun: 100,
            },
            FighterAction::MediumKick => FrameData {
                startup: 14,
                active: 7,
                recovery: 18,
                frame_advantage_hit: 3,
                frame_advantage_block: -2,
                damage: 380,
                stun: 190,
            },
            FighterAction::HeavyKick => FrameData {
                startup: 18,
                active: 9,
                recovery: 25,
                frame_advantage_hit: 5,
                frame_advantage_block: -5,
                damage: 560,
                stun: 280,
            },
            FighterAction::SpecialMove(ref name) => {
                match name.as_str() {
                    "Yoga Fire" => FrameData {
                        startup: 16,
                        active: 90, // Long-lasting projectile
                        recovery: 24,
                        frame_advantage_hit: 0,
                        frame_advantage_block: -8,
                        damage: 500,
                        stun: 300,
                    },
                    "Yoga Flame" => FrameData {
                        startup: 12,
                        active: 18,
                        recovery: 20,
                        frame_advantage_hit: 4,
                        frame_advantage_block: -6,
                        damage: 650,
                        stun: 450,
                    },
                    "Yoga Teleport" => FrameData {
                        startup: 8,
                        active: 12, // Invincible frames
                        recovery: 16,
                        frame_advantage_hit: 0,
                        frame_advantage_block: 0,
                        damage: 0,
                        stun: 0,
                    },
                    _ => FrameData::default(),
                }
            },
            _ => FrameData::default(),
        }
    }

    fn get_hitboxes(&self, _state: &FighterStateData) -> Vec<HitboxData> {
        // Dhalsim's hitboxes extend based on limb extension
        let extension_multiplier = 1.0 + (self.limb_extension as f32 * 0.3);
        let base_range = 25;
        let extended_range = (base_range as f32 * extension_multiplier) as i16;
        
        vec![
            HitboxData {
                x: extended_range,
                y: -40,
                width: (20.0 * extension_multiplier) as u16,
                height: 15,
                damage: 350,
                stun: 175,
                knockback: Vect16 { x: Fixed8_8::from_i16(80), y: Fixed8_8::from_i16(-40) },
            }
        ]
    }

    fn get_hurtboxes(&self, _state: &FighterStateData) -> Vec<HurtboxData> {
        // Dhalsim's hurtboxes - tall and thin
        vec![
            HurtboxData { x: -12, y: -75, width: 24, height: 75 }, // Thin body
            HurtboxData { x: -10, y: -95, width: 20, height: 20 }, // Head
        ]
    }

    fn on_hit(&self, _attack: &AttackData, _state: &mut FighterStateData) -> HitResponse {
        // Dhalsim takes more damage due to low defense
        HitResponse {
            damage_taken: 120, // Takes more damage
            stun_taken: 60,
            knockback: Vect16 { x: Fixed8_8::from_i16(100), y: Fixed8_8::from_i16(-50) },
            state_change: Some(FighterState::Reel),
        }
    }

    fn on_block(&self, _attack: &AttackData, _state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: 15, // Takes more chip damage
            pushback: Fixed8_8::from_i16(30), // More pushback
            frame_advantage: -3,
        }
    }

    fn get_special_moves(&self) -> Vec<SpecialMove> {
        vec![
            // Yoga Fire (Quarter Circle Forward + Punch)
            SpecialMove {
                name: "Yoga Fire".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Yoga Flame (Half Circle Back + Punch)
            SpecialMove {
                name: "Yoga Flame".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Yoga Teleport (Quarter Circle Back + Punch or Kick)
            SpecialMove {
                name: "Yoga Teleport".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
        ]
    }

    fn check_special_moves(&self, input: &InputState, _state: &FighterStateData) -> Option<FighterAction> {
        // Check for Yoga Fire (quarter circle forward + punch)
        if input.buttons.any_punch() && input.current_direction == InputDirection::Right {
            if self.yoga_fire_count < 2 {
                return Some(FighterAction::SpecialMove("Yoga Fire".to_string()));
            }
        }

        // Check for Yoga Flame (half circle back + punch)
        if input.buttons.any_punch() && input.current_direction == InputDirection::Left {
            return Some(FighterAction::SpecialMove("Yoga Flame".to_string()));
        }

        // Check for Yoga Teleport (quarter circle back + punch/kick)
        if (input.buttons.any_punch() || input.buttons.any_kick()) && 
           input.current_direction == InputDirection::Left && 
           self.teleport_cooldown == 0 {
            return Some(FighterAction::SpecialMove("Yoga Teleport".to_string()));
        }

        None
    }

    fn execute_special_move(&self, move_name: &str, _state: &mut FighterStateData) -> SpecialMoveResult {
        match move_name {
            "Yoga Fire" => self.execute_yoga_fire(_state),
            "Yoga Flame" => self.execute_yoga_flame(_state),
            "Yoga Teleport" => self.execute_yoga_teleport(_state),
            _ => SpecialMoveResult::Failed,
        }
    }
}

impl Dhalsim {
    fn execute_yoga_fire(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 500,
            stun: 300,
            meter_gain: 150,
            projectile: Some("Yoga Fire".to_string()),
        }
    }

    fn execute_yoga_flame(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 650,
            stun: 450,
            meter_gain: 180,
            projectile: None,
        }
    }

    fn execute_yoga_teleport(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 0,
            stun: 0,
            meter_gain: 50,
            projectile: None,
        }
    }
}

impl CharacterGlitches for Dhalsim {
    fn has_glitch(&self, _glitch_id: GlitchId) -> bool {
        false // Dhalsim has no major glitches
    }

    fn execute_glitch(&self, _glitch_id: GlitchId, _state: &mut FighterStateData) -> bool {
        false
    }
}
