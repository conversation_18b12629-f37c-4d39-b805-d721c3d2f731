//! # <PERSON><PERSON> Character Implementation
//!
//! <PERSON><PERSON> is the sumo wrestler with unique grappling mechanics.
//! Known for Hundred Hand Slap, Sumo Headbutt, and traditional sumo movement.

use bevy::prelude::*;
use sf2_types::{
    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,
    FighterStateData, FighterState, Point16, Vect16,
    character_traits::{
        FighterCharacter, FighterStats, FighterAction, SpecialMoveResult, 
        FrameData, HitboxData, HurtboxData, AttackData, HitResponse, BlockResponse,
        CharacterGlitches, GlitchId
    },
    move_sets::{SpecialMove, MoveInput, MoveInputType, MoveCondition},
};

/// E.Honda character implementation
#[derive(Debug, Clone)]
pub struct EHonda {
    pub stats: FighterStats,
    pub hand_slap_hits: u8, // Current hits in Hundred Hand Slap
    pub headbutt_charge: u8, // Charge for Sumo Headbutt
    pub sumo_stance: bool, // Special sumo stance
    pub throw_priority: bool, // Enhanced throw priority
}

impl Default for E<PERSON>onda {
    fn default() -> Self {
        Self {
            stats: FighterStats {
                health: 17000, // High health
                stun_threshold: 8000, // High stun threshold
                walk_speed: Fixed8_8::from_i16(95), // Slow walk
                jump_speed: Fixed8_8::from_i16(350), // Low jump
                gravity: Fixed8_8::from_i16(36), // Heavy
                throw_range: 25, // Long throw range
                max_projectiles: 0, // No projectiles
            },
            hand_slap_hits: 0,
            headbutt_charge: 0,
            sumo_stance: false,
            throw_priority: false,
        }
    }
}

impl FighterCharacter for EHonda {
    fn get_fighter_id(&self) -> sf2_types::FighterId {
        sf2_types::FighterId::EHonda
    }

    fn get_stats(&self) -> &FighterStats {
        &self.stats
    }

    fn update_ai(&self, _state: &FighterStateData, _opponent_state: &FighterStateData, distance: Fixed8_8) -> Option<FighterAction> {
        // E.Honda AI: Aggressive close-range fighter, prefers grappling
        if distance > Fixed8_8::from_i16(100) {
            Some(FighterAction::SpecialMove("Sumo Headbutt".to_string()))
        } else if distance < Fixed8_8::from_i16(40) {
            Some(FighterAction::SpecialMove("Hundred Hand Slap".to_string()))
        } else {
            Some(FighterAction::Move(Vect16 { 
                x: Fixed8_8::from_i16(80), // Move forward to close distance
                y: Fixed8_8::ZERO 
            }))
        }
    }

    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Sumo stance affects movement
        if self.sumo_stance {
            // Slower movement but more stable
            velocity.x = velocity.x / Fixed8_8::from_i16(2);
        }

        // Enhanced throw priority affects collision detection
        if self.throw_priority && matches!(state.current_state, FighterState::Throwing) {
            // Extend throw range temporarily
            // This would be handled in collision detection
        }

        // Standard physics with heavy character modifications
        position.x = position.x.saturating_add((velocity.x.raw() >> 8) as i16);
        position.y = position.y.saturating_add((velocity.y.raw() >> 8) as i16);
        
        // Apply gravity (heavier than normal)
        velocity.y = velocity.y + self.stats.gravity;
        
        // Ground collision
        if position.y >= 0 {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;
            if matches!(state.current_state, FighterState::Airborne) {
                state.current_state = FighterState::Standing;
            }
        }
    }

    fn get_frame_data(&self, action: FighterAction) -> FrameData {
        match action {
            FighterAction::LightPunch => FrameData {
                startup: 6,
                active: 3,
                recovery: 8,
                frame_advantage_hit: 3,
                frame_advantage_block: 1,
                damage: 240,
                stun: 120,
            },
            FighterAction::MediumPunch => FrameData {
                startup: 8,
                active: 4,
                recovery: 10,
                frame_advantage_hit: 4,
                frame_advantage_block: 1,
                damage: 460,
                stun: 230,
            },
            FighterAction::HeavyPunch => FrameData {
                startup: 10,
                active: 5,
                recovery: 15,
                frame_advantage_hit: 6,
                frame_advantage_block: -2,
                damage: 680,
                stun: 340,
            },
            FighterAction::LightKick => FrameData {
                startup: 7,
                active: 3,
                recovery: 9,
                frame_advantage_hit: 3,
                frame_advantage_block: 0,
                damage: 280,
                stun: 140,
            },
            FighterAction::MediumKick => FrameData {
                startup: 9,
                active: 4,
                recovery: 11,
                frame_advantage_hit: 4,
                frame_advantage_block: 0,
                damage: 500,
                stun: 250,
            },
            FighterAction::HeavyKick => FrameData {
                startup: 12,
                active: 6,
                recovery: 18,
                frame_advantage_hit: 7,
                frame_advantage_block: -3,
                damage: 750,
                stun: 375,
            },
            FighterAction::SpecialMove(ref name) => {
                match name.as_str() {
                    "Hundred Hand Slap" => FrameData {
                        startup: 5,
                        active: 45, // Long multi-hit sequence
                        recovery: 15,
                        frame_advantage_hit: 3,
                        frame_advantage_block: -5,
                        damage: 60, // Per hit
                        stun: 30, // Per hit
                    },
                    "Sumo Headbutt" => FrameData {
                        startup: 18,
                        active: 12,
                        recovery: 25,
                        frame_advantage_hit: 5,
                        frame_advantage_block: -8,
                        damage: 900,
                        stun: 600,
                    },
                    "Sumo Splash" => FrameData {
                        startup: 25,
                        active: 8,
                        recovery: 35,
                        frame_advantage_hit: 8,
                        frame_advantage_block: -12,
                        damage: 1200,
                        stun: 800,
                    },
                    _ => FrameData::default(),
                }
            },
            _ => FrameData::default(),
        }
    }

    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {
        if self.throw_priority && matches!(state.current_state, FighterState::Throwing) {
            // Enhanced throw hitbox
            vec![
                HitboxData {
                    x: 30, // Extended range
                    y: -50,
                    width: 45, // Wider
                    height: 40,
                    damage: 1000,
                    stun: 500,
                    knockback: Vect16 { x: Fixed8_8::from_i16(200), y: Fixed8_8::from_i16(-100) },
                }
            ]
        } else {
            // Normal hitboxes - powerful but slow
            vec![
                HitboxData {
                    x: 25,
                    y: -45,
                    width: 35,
                    height: 25,
                    damage: 460,
                    stun: 230,
                    knockback: Vect16 { x: Fixed8_8::from_i16(130), y: Fixed8_8::from_i16(-65) },
                }
            ]
        }
    }

    fn get_hurtboxes(&self, _state: &FighterStateData) -> Vec<HurtboxData> {
        // E.Honda's hurtboxes - large target but high health
        vec![
            HurtboxData { x: -25, y: -70, width: 50, height: 70 }, // Large body
            HurtboxData { x: -18, y: -90, width: 36, height: 20 }, // Head
        ]
    }

    fn on_hit(&self, _attack: &AttackData, _state: &mut FighterStateData) -> HitResponse {
        // E.Honda takes less damage due to his bulk
        HitResponse {
            damage_taken: 80, // Reduced damage
            stun_taken: 40,
            knockback: Vect16 { x: Fixed8_8::from_i16(60), y: Fixed8_8::from_i16(-20) }, // Less knockback
            state_change: Some(FighterState::Reel),
        }
    }

    fn on_block(&self, _attack: &AttackData, _state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: 5, // Takes less chip damage
            pushback: Fixed8_8::from_i16(10), // Less pushback
            frame_advantage: 0,
        }
    }

    fn get_special_moves(&self) -> Vec<SpecialMove> {
        vec![
            // Hundred Hand Slap (Punch rapidly)
            SpecialMove {
                name: "Hundred Hand Slap".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Sumo Headbutt (Charge Back, Forward + Punch)
            SpecialMove {
                name: "Sumo Headbutt".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 50,
                        conditions: vec![MoveCondition::Hold],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 3,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Sumo Splash (360 + Punch)
            SpecialMove {
                name: "Sumo Splash".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Up),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
        ]
    }

    fn check_special_moves(&self, input: &InputState, _state: &FighterStateData) -> Option<FighterAction> {
        // Check for Hundred Hand Slap (rapid punch)
        if input.buttons.any_punch() {
            return Some(FighterAction::SpecialMove("Hundred Hand Slap".to_string()));
        }

        // Check for Sumo Headbutt (charge back, forward + punch)
        if self.headbutt_charge >= 50 && input.buttons.any_punch() {
            if input.current_direction == InputDirection::Right {
                return Some(FighterAction::SpecialMove("Sumo Headbutt".to_string()));
            }
        }

        // Check for Sumo Splash (360 + punch) - simplified check
        if input.buttons.any_punch() && input.current_direction == InputDirection::Up {
            return Some(FighterAction::SpecialMove("Sumo Splash".to_string()));
        }

        None
    }

    fn execute_special_move(&self, move_name: &str, _state: &mut FighterStateData) -> SpecialMoveResult {
        match move_name {
            "Hundred Hand Slap" => self.execute_hundred_hand_slap(_state),
            "Sumo Headbutt" => self.execute_sumo_headbutt(_state),
            "Sumo Splash" => self.execute_sumo_splash(_state),
            _ => SpecialMoveResult::Failed,
        }
    }
}

impl EHonda {
    fn execute_hundred_hand_slap(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 60 * 12, // 12 hits
            stun: 30 * 12,
            meter_gain: 200,
            projectile: None,
        }
    }

    fn execute_sumo_headbutt(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 900,
            stun: 600,
            meter_gain: 250,
            projectile: None,
        }
    }

    fn execute_sumo_splash(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 1200,
            stun: 800,
            meter_gain: 300,
            projectile: None,
        }
    }
}

impl CharacterGlitches for EHonda {
    fn has_glitch(&self, _glitch_id: GlitchId) -> bool {
        false // E.Honda has no major glitches
    }

    fn execute_glitch(&self, _glitch_id: GlitchId, _state: &mut FighterStateData) -> bool {
        false
    }
}
