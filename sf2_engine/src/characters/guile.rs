//! # Guile Character Implementation
//!
//! Guile is the defensive charge character with authentic glitches.
//! Known for Sonic Boom, Flash Kick, and the infamous invisible throw glitch.

use bevy::prelude::*;
use sf2_types::{
    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,
    FighterStateData, FighterState, Point16, Vect16, Rect8, FighterId, Point8, <PERSON>ze8,
    character_traits::{
        FighterCharacter, FighterStats, FighterAction, SpecialMoveResult,
        FrameData, HitboxData, HurtboxData, AttackData, HitResponse, BlockResponse,
        MoveId, AIBehaviorPattern, CharacterSounds, CharacterEffects, HitType, BodyPart,
        CharacterGlitches, GlitchId, GlitchResult, EffectId
    },
    SpecialMoveId,
};

/// Guile character implementation with authentic glitches
#[derive(Debug, Clone)]
pub struct Guile {
    pub stats: FighterStats,
    pub charge_time: u8, // Frames of charge stored
    pub sonic_boom_count: u8, // Number of sonic booms on screen
    pub invisible_throw_active: bool, // Invisible throw glitch state
    pub handcuff_glitch_active: bool, // Handcuff glitch state
    pub freeze_glitch_timer: u8, // Freeze glitch timer
}

impl Default for Guile {
    fn default() -> Self {
        Self {
            stats: FighterStats {
                health: 16500,
                stun_threshold: 7400,
                walk_speed: Fixed8_8::from_i16(110), // Slower walk
                jump_speed: Fixed8_8::from_i16(390),
                gravity: Fixed8_8::from_i16(32),
                throw_range: 22, // Longer throw range for invisible throw
                max_projectiles: 2, // Can have 2 sonic booms
            },
            charge_time: 0,
            sonic_boom_count: 0,
            invisible_throw_active: false,
            handcuff_glitch_active: false,
            freeze_glitch_timer: 0,
        }
    }
}

impl FighterCharacter for Guile {
    fn fighter_id(&self) -> sf2_types::FighterId {
        sf2_types::FighterId::Guile
    }

    fn name(&self) -> &'static str {
        "Guile"
    }

    fn base_stats(&self) -> FighterStats {
        self.stats
    }

    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction> {
        let mut actions = Vec::new();

        // Check for special moves first (charge moves)
        if let Some(special_action) = self.check_special_moves(input, state) {
            actions.push(special_action);
        }

        // Basic movement
        if input.direction != InputDirection::Neutral {
            let move_vector = match input.direction {
                InputDirection::Left => Vect16::from_ints(-1, 0),
                InputDirection::Right => Vect16::from_ints(1, 0),
                InputDirection::Up => Vect16::from_ints(0, -1),
                InputDirection::Down => Vect16::from_ints(0, 1),
                _ => Vect16::ZERO,
            };
            actions.push(FighterAction::SetVelocity(move_vector.x, move_vector.y));
        }

        actions
    }

    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Handle freeze glitch
        if self.freeze_glitch_timer > 0 {
            // Character is frozen, don't update physics
            return;
        }

        // Handle handcuff glitch - opponent can't move
        if self.handcuff_glitch_active {
            velocity.x = Fixed8_8::ZERO;
        }

        // Standard physics
        position.x = position.x.saturating_add((velocity.x.raw() >> 8) as i16);
        position.y = position.y.saturating_add((velocity.y.raw() >> 8) as i16);
        
        // Apply gravity
        velocity.y = velocity.y + self.stats.gravity;
        
        // Ground collision
        if position.y >= 0 {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;
            if matches!(state.state, FighterState::Jumping) {
                state.state = FighterState::Normal;
            }
        }
    }

    fn execute_special_move(&self, move_id: SpecialMoveId, state: &mut FighterStateData) -> SpecialMoveResult {
        match move_id {
            SpecialMoveId::SonicBoom => self.execute_sonic_boom(state),
            SpecialMoveId::FlashKick => self.execute_flash_kick(state),
            _ => SpecialMoveResult {
                success: false,
                actions: vec![],
                meter_cost: 0,
                recovery_frames: 0,
            },
        }
    }

    fn get_frame_data(&self, move_id: MoveId) -> FrameData {
        match move_id {
            MoveId::StandingLightPunch => FrameData {
                startup_frames: 5,
                active_frames: 2,
                recovery_frames: 7,
                block_stun: 8,
                hit_stun: 12,
                damage: 200,
                stun_damage: 100,
                knockback_x: 50,
                knockback_y: 0,
            },
            MoveId::StandingMediumPunch => FrameData {
                startup_frames: 7,
                active_frames: 3,
                recovery_frames: 9,
                block_stun: 10,
                hit_stun: 15,
                damage: 400,
                stun_damage: 200,
                knockback_x: 80,
                knockback_y: 0,
            },
            MoveId::StandingHeavyPunch => FrameData {
                startup_frames: 9,
                active_frames: 4,
                recovery_frames: 14,
                block_stun: 12,
                hit_stun: 18,
                damage: 600,
                stun_damage: 300,
                knockback_x: 120,
                knockback_y: -20,
            },
            MoveId::StandingLightKick => FrameData {
                startup_frames: 6,
                active_frames: 3,
                recovery_frames: 8,
                block_stun: 8,
                hit_stun: 12,
                damage: 250,
                stun_damage: 120,
                knockback_x: 60,
                knockback_y: 0,
            },
            MoveId::StandingMediumKick => FrameData {
                startup_frames: 8,
                active_frames: 4,
                recovery_frames: 10,
                block_stun: 10,
                hit_stun: 15,
                damage: 450,
                stun_damage: 220,
                knockback_x: 90,
                knockback_y: 0,
            },
            MoveId::StandingHeavyKick => FrameData {
                startup_frames: 11,
                active_frames: 5,
                recovery_frames: 16,
                block_stun: 14,
                hit_stun: 20,
                damage: 700,
                stun_damage: 350,
                knockback_x: 140,
                knockback_y: -30,
            },
            MoveId::Special(SpecialMoveId::SonicBoom) => FrameData {
                startup_frames: 14,
                active_frames: 80, // Projectile active frames
                recovery_frames: 22,
                block_stun: 8,
                hit_stun: 15,
                damage: 600,
                stun_damage: 400,
                knockback_x: 100,
                knockback_y: 0,
            },
            MoveId::Special(SpecialMoveId::FlashKick) => FrameData {
                startup_frames: 4, // Very fast anti-air
                active_frames: 12,
                recovery_frames: 28,
                block_stun: 20,
                hit_stun: 25,
                damage: 800,
                stun_damage: 600,
                knockback_x: 80,
                knockback_y: -150,
            },
            _ => FrameData {
                startup_frames: 5,
                active_frames: 2,
                recovery_frames: 8,
                block_stun: 8,
                hit_stun: 12,
                damage: 100,
                stun_damage: 50,
                knockback_x: 30,
                knockback_y: 0,
            },
        }
    }

    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {
        // Handle invisible throw glitch
        if self.invisible_throw_active && matches!(state.state, FighterState::Attacking) {
            // Invisible throw has extended range
            vec![
                HitboxData {
                    rect: Rect8 {
                        origin: Point8::new(25, -40),
                        size: Size8::new(40, 30)
                    },
                    damage: 800,
                    stun: 400,
                    knockback: Vect16 { x: Fixed8_8::from_i16(150), y: Fixed8_8::from_i16(-80) },
                    hit_type: HitType::Throw,
                    priority: 5
                }
            ]
        } else {
            // Normal hitboxes
            vec![
                HitboxData {
                    rect: Rect8 {
                        origin: Point8::new(20, -40),
                        size: Size8::new(30, 20)
                    },
                    damage: 400,
                    stun: 200,
                    knockback: Vect16 { x: Fixed8_8::from_i16(100), y: Fixed8_8::from_i16(-50) },
                    hit_type: HitType::Normal,
                    priority: 3
                }
            ]
        }
    }

    fn get_hurtboxes(&self, _state: &FighterStateData) -> Vec<HurtboxData> {
        // Guile's hurtboxes - standard military build
        vec![
            HurtboxData {
                rect: Rect8 {
                    origin: Point8::new(-18, -65),
                    size: Size8::new(36, 65)
                },
                vulnerability: 1.0,
                body_part: BodyPart::Body
            },
            HurtboxData {
                rect: Rect8 {
                    origin: Point8::new(-12, -85),
                    size: Size8::new(24, 20)
                },
                vulnerability: 1.2,
                body_part: BodyPart::Head
            },
        ]
    }

    fn on_hit(&self, _attack: &AttackData, _state: &mut FighterStateData) -> HitResponse {
        HitResponse {
            damage_taken: 100,
            stun_taken: 50,
            knockback: Vect16 { x: Fixed8_8::from_i16(70), y: Fixed8_8::from_i16(-25) },
            new_state: FighterState::Reel,
            hit_stun_frames: 15,
            effects: vec![],
        }
    }

    fn on_block(&self, _attack: &AttackData, _state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: 8,
            block_stun_frames: 12,
            pushback: Fixed8_8::from_i16(15),
            effects: vec![],
        }
    }

    fn get_ai_behavior(&self, _difficulty: u8) -> AIBehaviorPattern {
        AIBehaviorPattern {
            aggression: 0.4,
            defense: 0.9,
            special_move_frequency: 0.7,
            jump_frequency: 0.2,
            preferred_distance: Fixed8_8::from_i16(200),
            reaction_time: 15,
        }
    }

    fn get_sound_effects(&self) -> CharacterSounds {
        CharacterSounds {
            voice_clips: vec![0x90, 0x91, 0x92],
            attack_sounds: vec![0x50, 0x51, 0x52],
            special_move_sounds: vec![0x90, 0x91, 0x92],
            hit_sounds: vec![0x60, 0x61],
            victory_sound: 0x95,
        }
    }

    fn get_visual_effects(&self) -> CharacterEffects {
        CharacterEffects {
            hit_sparks: vec![EffectId::HitSpark],
            special_effects: vec![EffectId::Custom(200)],
            victory_effects: vec![EffectId::Custom(103)],
        }
    }
}

impl Guile {
    fn check_special_moves(&self, input: &InputState, _state: &FighterStateData) -> Option<FighterAction> {
        // Check for Sonic Boom (charge back, forward + punch)
        if self.charge_time >= 60 && input.buttons.any_punch() {
            if input.direction == InputDirection::Right && self.sonic_boom_count < 2 {
                return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::SonicBoom)));
            }
        }

        // Check for Flash Kick (charge down, up + kick)
        if self.charge_time >= 60 && input.buttons.any_kick() {
            if input.direction == InputDirection::Up {
                return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::FlashKick)));
            }
        }

        None
    }

    fn execute_sonic_boom(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult {
            success: true,
            actions: vec![FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::SonicBoom))],
            meter_cost: 0,
            recovery_frames: 20,
        }
    }

    fn execute_flash_kick(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult {
            success: true,
            actions: vec![FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::FlashKick))],
            meter_cost: 0,
            recovery_frames: 30,
        }
    }
}

impl CharacterGlitches for Guile {
    fn has_glitch(&self, glitch_id: GlitchId) -> bool {
        matches!(glitch_id, 
            GlitchId::GuileInvisibleThrow | 
            GlitchId::GuileHandcuff | 
            GlitchId::GuileFreeze
        )
    }

    fn execute_glitch(&self, glitch_id: GlitchId, state: &mut FighterStateData) -> GlitchResult {
        match glitch_id {
            GlitchId::GuileInvisibleThrow => {
                // Activate invisible throw - extends throw range and makes it harder to see
                state.state = FighterState::Attacking;
                GlitchResult {
                    executed: true,
                    actions: vec![FighterAction::StateTransition(FighterState::Attacking)],
                    description: "Invisible throw activated - extended range".to_string(),
                }
            },
            GlitchId::GuileHandcuff => {
                // Handcuff glitch - opponent becomes unable to move properly
                GlitchResult {
                    executed: true,
                    actions: vec![],
                    description: "Handcuff glitch activated - opponent movement restricted".to_string(),
                }
            },
            GlitchId::GuileFreeze => {
                // Freeze glitch - character becomes temporarily frozen
                GlitchResult {
                    executed: true,
                    actions: vec![],
                    description: "Freeze glitch activated - character temporarily frozen".to_string(),
                }
            },
            _ => GlitchResult {
                executed: false,
                actions: vec![],
                description: "Unknown glitch".to_string(),
            },
        }
    }
}
