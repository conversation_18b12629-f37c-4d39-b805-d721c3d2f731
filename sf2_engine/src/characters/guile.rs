//! # Guile Character Implementation
//!
//! Guile is the defensive charge character with authentic glitches.
//! Known for Sonic Boom, Flash Kick, and the infamous invisible throw glitch.

use bevy::prelude::*;
use sf2_types::{
    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,
    FighterStateData, FighterState, Point16, Vect16, Rect8, FighterId,
    character_traits::{
        FighterCharacter, FighterStats, FighterAction, SpecialMoveResult,
        FrameData, HitboxData, HurtboxData, AttackData, HitResponse, BlockResponse,
        MoveId, AIBehaviorPattern, CharacterSounds, CharacterEffects, HitType, BodyPart
    },
    SpecialMoveId,
};

/// Guile character implementation with authentic glitches
#[derive(Debug, Clone)]
pub struct Guile {
    pub stats: FighterStats,
    pub charge_time: u8, // Frames of charge stored
    pub sonic_boom_count: u8, // Number of sonic booms on screen
    pub invisible_throw_active: bool, // Invisible throw glitch state
    pub handcuff_glitch_active: bool, // Handcuff glitch state
    pub freeze_glitch_timer: u8, // Freeze glitch timer
}

impl Default for Guile {
    fn default() -> Self {
        Self {
            stats: FighterStats {
                health: 16500,
                stun_threshold: 7400,
                walk_speed: Fixed8_8::from_i16(110), // Slower walk
                jump_speed: Fixed8_8::from_i16(390),
                gravity: Fixed8_8::from_i16(32),
                throw_range: 22, // Longer throw range for invisible throw
                max_projectiles: 2, // Can have 2 sonic booms
            },
            charge_time: 0,
            sonic_boom_count: 0,
            invisible_throw_active: false,
            handcuff_glitch_active: false,
            freeze_glitch_timer: 0,
        }
    }
}

impl FighterCharacter for Guile {
    fn get_fighter_id(&self) -> sf2_types::FighterId {
        sf2_types::FighterId::Guile
    }

    fn get_stats(&self) -> &FighterStats {
        &self.stats
    }

    fn update_ai(&self, _state: &FighterStateData, _opponent_state: &FighterStateData, distance: Fixed8_8) -> Option<FighterAction> {
        // Guile AI: Very defensive, prefers long range
        if distance > Fixed8_8::from_i16(150) {
            Some(FighterAction::SpecialMove("Sonic Boom".to_string()))
        } else {
            Some(FighterAction::Move(Vect16 { 
                x: Fixed8_8::from_i16(-80), // Back up
                y: Fixed8_8::ZERO 
            }))
        }
    }

    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Handle freeze glitch
        if self.freeze_glitch_timer > 0 {
            // Character is frozen, don't update physics
            return;
        }

        // Handle handcuff glitch - opponent can't move
        if self.handcuff_glitch_active {
            velocity.x = Fixed8_8::ZERO;
        }

        // Standard physics
        position.x = position.x.saturating_add((velocity.x.raw() >> 8) as i16);
        position.y = position.y.saturating_add((velocity.y.raw() >> 8) as i16);
        
        // Apply gravity
        velocity.y = velocity.y + self.stats.gravity;
        
        // Ground collision
        if position.y >= 0 {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;
            if matches!(state.current_state, FighterState::Airborne) {
                state.current_state = FighterState::Standing;
            }
        }
    }

    fn get_frame_data(&self, action: FighterAction) -> FrameData {
        match action {
            FighterAction::LightPunch => FrameData {
                startup: 5,
                active: 2,
                recovery: 7,
                frame_advantage_hit: 2,
                frame_advantage_block: 0,
                damage: 200,
                stun: 100,
            },
            FighterAction::MediumPunch => FrameData {
                startup: 7,
                active: 3,
                recovery: 9,
                frame_advantage_hit: 3,
                frame_advantage_block: 0,
                damage: 400,
                stun: 200,
            },
            FighterAction::HeavyPunch => FrameData {
                startup: 9,
                active: 4,
                recovery: 14,
                frame_advantage_hit: 4,
                frame_advantage_block: -3,
                damage: 600,
                stun: 300,
            },
            FighterAction::LightKick => FrameData {
                startup: 6,
                active: 3,
                recovery: 8,
                frame_advantage_hit: 2,
                frame_advantage_block: 0,
                damage: 250,
                stun: 120,
            },
            FighterAction::MediumKick => FrameData {
                startup: 8,
                active: 4,
                recovery: 10,
                frame_advantage_hit: 3,
                frame_advantage_block: -1,
                damage: 450,
                stun: 220,
            },
            FighterAction::HeavyKick => FrameData {
                startup: 11,
                active: 5,
                recovery: 16,
                frame_advantage_hit: 5,
                frame_advantage_block: -4,
                damage: 700,
                stun: 350,
            },
            FighterAction::SpecialMove(ref name) => {
                match name.as_str() {
                    "Sonic Boom" => FrameData {
                        startup: 14,
                        active: 80, // Projectile active frames
                        recovery: 22,
                        frame_advantage_hit: 0,
                        frame_advantage_block: -6,
                        damage: 600,
                        stun: 400,
                    },
                    "Flash Kick" => FrameData {
                        startup: 4, // Very fast anti-air
                        active: 12,
                        recovery: 28,
                        frame_advantage_hit: 5,
                        frame_advantage_block: -15,
                        damage: 800,
                        stun: 600,
                    },
                    _ => FrameData::default(),
                }
            },
            _ => FrameData::default(),
        }
    }

    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {
        // Handle invisible throw glitch
        if self.invisible_throw_active && matches!(state.current_state, FighterState::Throwing) {
            // Invisible throw has extended range
            vec![
                HitboxData {
                    x: 25, // Extended range
                    y: -40,
                    width: 40, // Wider hitbox
                    height: 30,
                    damage: 800,
                    stun: 400,
                    knockback: Vect16 { x: Fixed8_8::from_i16(150), y: Fixed8_8::from_i16(-80) },
                }
            ]
        } else {
            // Normal hitboxes
            vec![
                HitboxData {
                    x: 20,
                    y: -40,
                    width: 30,
                    height: 20,
                    damage: 400,
                    stun: 200,
                    knockback: Vect16 { x: Fixed8_8::from_i16(100), y: Fixed8_8::from_i16(-50) },
                }
            ]
        }
    }

    fn get_hurtboxes(&self, _state: &FighterStateData) -> Vec<HurtboxData> {
        // Guile's hurtboxes - standard military build
        vec![
            HurtboxData { x: -18, y: -65, width: 36, height: 65 }, // Body
            HurtboxData { x: -12, y: -85, width: 24, height: 20 }, // Head
        ]
    }

    fn on_hit(&self, _attack: &AttackData, _state: &mut FighterStateData) -> HitResponse {
        HitResponse {
            damage_taken: 100,
            stun_taken: 50,
            knockback: Vect16 { x: Fixed8_8::from_i16(70), y: Fixed8_8::from_i16(-25) },
            state_change: Some(FighterState::Reel),
        }
    }

    fn on_block(&self, _attack: &AttackData, _state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: 8,
            pushback: Fixed8_8::from_i16(15),
            frame_advantage: -1,
        }
    }

    fn get_special_moves(&self) -> Vec<SpecialMove> {
        vec![
            // Sonic Boom (Charge Back, Forward + Punch)
            SpecialMove {
                name: "Sonic Boom".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 60, // Longer charge time than Chun-Li
                        conditions: vec![MoveCondition::Hold],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 3,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Flash Kick (Charge Down, Up + Kick)
            SpecialMove {
                name: "Flash Kick".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 60, // Charge time
                        conditions: vec![MoveCondition::Hold],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Up),
                        duration: 3,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Kick),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
        ]
    }

    fn check_special_moves(&self, input: &InputState, _state: &FighterStateData) -> Option<FighterAction> {
        // Check for Sonic Boom (charge back, forward + punch)
        if self.charge_time >= 60 && input.buttons.any_punch() {
            if input.current_direction == InputDirection::Right && self.sonic_boom_count < 2 {
                return Some(FighterAction::SpecialMove("Sonic Boom".to_string()));
            }
        }

        // Check for Flash Kick (charge down, up + kick)
        if self.charge_time >= 60 && input.buttons.any_kick() {
            if input.current_direction == InputDirection::Up {
                return Some(FighterAction::SpecialMove("Flash Kick".to_string()));
            }
        }

        None
    }

    fn execute_special_move(&self, move_name: &str, _state: &mut FighterStateData) -> SpecialMoveResult {
        match move_name {
            "Sonic Boom" => self.execute_sonic_boom(_state),
            "Flash Kick" => self.execute_flash_kick(_state),
            _ => SpecialMoveResult::Failed,
        }
    }
}

impl Guile {
    fn execute_sonic_boom(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 600,
            stun: 400,
            meter_gain: 180,
            projectile: Some("Sonic Boom".to_string()),
        }
    }

    fn execute_flash_kick(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 800,
            stun: 600,
            meter_gain: 220,
            projectile: None,
        }
    }
}

impl CharacterGlitches for Guile {
    fn has_glitch(&self, glitch_id: GlitchId) -> bool {
        matches!(glitch_id, 
            GlitchId::GuileInvisibleThrow | 
            GlitchId::GuileHandcuff | 
            GlitchId::GuileFreeze
        )
    }

    fn execute_glitch(&self, glitch_id: GlitchId, state: &mut FighterStateData) -> bool {
        match glitch_id {
            GlitchId::GuileInvisibleThrow => {
                // Activate invisible throw - extends throw range and makes it harder to see
                state.current_state = FighterState::Throwing;
                true
            },
            GlitchId::GuileHandcuff => {
                // Handcuff glitch - opponent becomes unable to move properly
                // This would affect the opponent's state in a real implementation
                true
            },
            GlitchId::GuileFreeze => {
                // Freeze glitch - character becomes temporarily frozen
                // This would set the freeze timer in a real implementation
                true
            },
            _ => false,
        }
    }
}
