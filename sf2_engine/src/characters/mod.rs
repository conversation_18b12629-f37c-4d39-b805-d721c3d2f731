//! # Character Implementations
//!
//! Individual character implementations for Street Fighter II.
//! Each character has unique frame data, special moves, and behavior patterns.

pub mod ryu;
pub mod ken;
pub mod chun_li;
// Temporarily commented out until fixed
// pub mod guile;
// pub mod blanka;
// pub mod e_honda;
// pub mod zangief;
// pub mod dhalsim;

use sf2_types::{FighterId, character_traits::FighterCharacter};
use std::collections::HashMap;

pub use ryu::RyuCharacter;
pub use ken::KenCharacter;
pub use chun_li::ChunLi;
// Temporarily commented out until fixed
// pub use guile::Guile;
// pub use blanka::Blanka;
// pub use e_honda::EHonda;
// pub use zangief::Zangief;
// pub use dhalsim::Dhalsim;

/// Factory for creating character implementations
pub struct CharacterFactory;

impl CharacterFactory {
    /// Create a character implementation by ID
    pub fn create_character(fighter_id: FighterId) -> Option<Box<dyn <PERSON><PERSON>haracter>> {
        match fighter_id {
            FighterId::Ryu => Some(Box::new(Ryu<PERSON>haracter)),
            FighterId::Ken => Some(Box::new(KenCharacter)),
            FighterId::ChunLi => Some(Box::new(ChunLi::default())),
            // Temporarily commented out until fixed
            // FighterId::Guile => Some(Box::new(Guile::default())),
            // FighterId::Blanka => Some(Box::new(Blanka::default())),
            // FighterId::EHonda => Some(Box::new(EHonda::default())),
            // FighterId::Zangief => Some(Box::new(Zangief::default())),
            // FighterId::Dhalsim => Some(Box::new(Dhalsim::default())),
            _ => None,
        }
    }
    
    /// Get all available character implementations
    pub fn get_available_characters() -> Vec<FighterId> {
        vec![
            FighterId::Ryu,
            FighterId::Ken,
            FighterId::ChunLi,
            // Temporarily commented out until fixed
            // FighterId::Guile,
            // FighterId::Blanka,
            // FighterId::EHonda,
            // FighterId::Zangief,
            // FighterId::Dhalsim,
        ]
    }
    
    /// Create all character implementations
    pub fn create_all_characters() -> HashMap<FighterId, Box<dyn FighterCharacter>> {
        let mut characters = HashMap::new();
        
        for fighter_id in Self::get_available_characters() {
            if let Some(character) = Self::create_character(fighter_id) {
                characters.insert(fighter_id, character);
            }
        }
        
        characters
    }
}

/// Character registration system for the engine
#[derive(bevy::prelude::Resource)]
pub struct CharacterImplementationRegistry {
    characters: HashMap<FighterId, Box<dyn FighterCharacter>>,
}

impl CharacterImplementationRegistry {
    /// Create new character registry with all available characters
    pub fn new() -> Self {
        Self {
            characters: CharacterFactory::create_all_characters(),
        }
    }
    
    /// Get character implementation by ID
    pub fn get_character(&self, fighter_id: FighterId) -> Option<&dyn FighterCharacter> {
        self.characters.get(&fighter_id).map(|c| c.as_ref())
    }
    
    /// Get all registered character IDs
    pub fn get_character_ids(&self) -> Vec<FighterId> {
        self.characters.keys().copied().collect()
    }
    
    /// Check if character is available
    pub fn has_character(&self, fighter_id: FighterId) -> bool {
        self.characters.contains_key(&fighter_id)
    }
}

impl Default for CharacterImplementationRegistry {
    fn default() -> Self {
        Self::new()
    }
}
