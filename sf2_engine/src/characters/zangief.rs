//! # Zangief Character Implementation
//!
//! <PERSON><PERSON><PERSON> is the ultimate grappler with 360-degree command grabs.
//! Known for Screw Piledriver, Spinning Piledriver, and devastating throws.

use bevy::prelude::*;
use sf2_types::{
    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,
    FighterStateData, FighterState, Point16, Vect16,
    character_traits::{
        FighterCharacter, FighterStats, FighterAction, SpecialMoveResult, 
        FrameData, HitboxData, HurtboxData, AttackData, HitResponse, BlockResponse,
        CharacterGlitches, GlitchId
    },
    move_sets::{SpecialMove, MoveInput, MoveInputType, MoveCondition},
};

/// Zangief character implementation
#[derive(Debug, Clone)]
pub struct Zangief {
    pub stats: FighterStats,
    pub grapple_range: u8, // Enhanced grappling range
    pub command_grab_buffer: u8, // Buffer for 360-degree inputs
    pub muscle_power: bool, // Enhanced damage state
    pub iron_body: bool, // Damage reduction state
}

impl Default for Zangief {
    fn default() -> Self {
        Self {
            stats: FighterStats {
                health: 18000, // Highest health
                stun_threshold: 8500, // Highest stun threshold
                walk_speed: Fixed8_8::from_i16(85), // Slowest walk
                jump_speed: Fixed8_8::from_i16(340),
                gravity: Fixed8_8::from_i16(38), // Heaviest
                throw_range: 30, // Longest throw range
                max_projectiles: 0, // No projectiles
            },
            grapple_range: 30,
            command_grab_buffer: 0,
            muscle_power: false,
            iron_body: false,
        }
    }
}

impl FighterCharacter for Zangief {
    fn get_fighter_id(&self) -> sf2_types::FighterId {
        sf2_types::FighterId::Zangief
    }

    fn get_stats(&self) -> &FighterStats {
        &self.stats
    }

    fn update_ai(&self, _state: &FighterStateData, _opponent_state: &FighterStateData, distance: Fixed8_8) -> Option<FighterAction> {
        // Zangief AI: Extremely aggressive, always trying to get close for grabs
        if distance > Fixed8_8::from_i16(80) {
            Some(FighterAction::Move(Vect16 { 
                x: Fixed8_8::from_i16(120), // Move forward aggressively
                y: Fixed8_8::ZERO 
            }))
        } else if distance < Fixed8_8::from_i16(40) {
            Some(FighterAction::SpecialMove("Screw Piledriver".to_string()))
        } else {
            Some(FighterAction::Move(Vect16 { 
                x: Fixed8_8::from_i16(100),
                y: Fixed8_8::ZERO 
            }))
        }
    }

    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16) {
        // Grappler physics - very heavy and stable
        if matches!(state.current_state, FighterState::Throwing) {
            // During throws, Zangief has enhanced control
            velocity.x = velocity.x / Fixed8_8::from_i16(4); // Very slow during throws
        }

        // Iron body reduces knockback
        if self.iron_body {
            velocity.x = velocity.x / Fixed8_8::from_i16(2);
            velocity.y = velocity.y / Fixed8_8::from_i16(2);
        }

        // Standard physics with heavy modifications
        position.x = position.x.saturating_add((velocity.x.raw() >> 8) as i16);
        position.y = position.y.saturating_add((velocity.y.raw() >> 8) as i16);
        
        // Apply gravity (heaviest character)
        velocity.y = velocity.y + self.stats.gravity;
        
        // Ground collision
        if position.y >= 0 {
            position.y = 0;
            velocity.y = Fixed8_8::ZERO;
            if matches!(state.current_state, FighterState::Airborne) {
                state.current_state = FighterState::Standing;
            }
        }
    }

    fn get_frame_data(&self, action: FighterAction) -> FrameData {
        match action {
            FighterAction::LightPunch => FrameData {
                startup: 7,
                active: 3,
                recovery: 9,
                frame_advantage_hit: 4,
                frame_advantage_block: 1,
                damage: 280,
                stun: 140,
            },
            FighterAction::MediumPunch => FrameData {
                startup: 9,
                active: 4,
                recovery: 12,
                frame_advantage_hit: 5,
                frame_advantage_block: 1,
                damage: 520,
                stun: 260,
            },
            FighterAction::HeavyPunch => FrameData {
                startup: 12,
                active: 5,
                recovery: 18,
                frame_advantage_hit: 7,
                frame_advantage_block: -2,
                damage: 780,
                stun: 390,
            },
            FighterAction::LightKick => FrameData {
                startup: 8,
                active: 3,
                recovery: 10,
                frame_advantage_hit: 4,
                frame_advantage_block: 0,
                damage: 320,
                stun: 160,
            },
            FighterAction::MediumKick => FrameData {
                startup: 10,
                active: 4,
                recovery: 13,
                frame_advantage_hit: 5,
                frame_advantage_block: 0,
                damage: 560,
                stun: 280,
            },
            FighterAction::HeavyKick => FrameData {
                startup: 14,
                active: 6,
                recovery: 20,
                frame_advantage_hit: 8,
                frame_advantage_block: -3,
                damage: 820,
                stun: 410,
            },
            FighterAction::SpecialMove(ref name) => {
                match name.as_str() {
                    "Screw Piledriver" => FrameData {
                        startup: 3, // Very fast grab
                        active: 5,
                        recovery: 45, // Long recovery
                        frame_advantage_hit: 20, // Huge advantage
                        frame_advantage_block: -20, // Punishable if whiffed
                        damage: 1400,
                        stun: 1000,
                    },
                    "Spinning Piledriver" => FrameData {
                        startup: 2, // Even faster
                        active: 3,
                        recovery: 60, // Longer recovery
                        frame_advantage_hit: 25,
                        frame_advantage_block: -25,
                        damage: 1800,
                        stun: 1200,
                    },
                    "Lariat" => FrameData {
                        startup: 8,
                        active: 20, // Long active frames
                        recovery: 25,
                        frame_advantage_hit: 6,
                        frame_advantage_block: -5,
                        damage: 700,
                        stun: 500,
                    },
                    _ => FrameData::default(),
                }
            },
            _ => FrameData::default(),
        }
    }

    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {
        if matches!(state.current_state, FighterState::Throwing) {
            // Command grab hitboxes - very large range
            vec![
                HitboxData {
                    x: 35, // Extended range
                    y: -55,
                    width: 50, // Very wide
                    height: 45,
                    damage: if self.muscle_power { 1800 } else { 1400 },
                    stun: if self.muscle_power { 1200 } else { 1000 },
                    knockback: Vect16 { x: Fixed8_8::ZERO, y: Fixed8_8::ZERO }, // Grabs don't knockback
                }
            ]
        } else {
            // Normal hitboxes - powerful but slow
            vec![
                HitboxData {
                    x: 28,
                    y: -50,
                    width: 40,
                    height: 30,
                    damage: if self.muscle_power { 650 } else { 520 },
                    stun: if self.muscle_power { 325 } else { 260 },
                    knockback: Vect16 { x: Fixed8_8::from_i16(150), y: Fixed8_8::from_i16(-75) },
                }
            ]
        }
    }

    fn get_hurtboxes(&self, _state: &FighterStateData) -> Vec<HurtboxData> {
        // Zangief's hurtboxes - largest character
        vec![
            HurtboxData { x: -30, y: -80, width: 60, height: 80 }, // Massive body
            HurtboxData { x: -20, y: -100, width: 40, height: 20 }, // Head
        ]
    }

    fn on_hit(&self, _attack: &AttackData, _state: &mut FighterStateData) -> HitResponse {
        // Zangief takes the least damage due to his bulk and iron body
        let damage_reduction = if self.iron_body { 0.5 } else { 0.8 };
        
        HitResponse {
            damage_taken: (80.0 * damage_reduction) as u16,
            stun_taken: (40.0 * damage_reduction) as u16,
            knockback: Vect16 { 
                x: Fixed8_8::from_i16(if self.iron_body { 30 } else { 50 }), 
                y: Fixed8_8::from_i16(if self.iron_body { 10 } else { 15 })
            },
            state_change: Some(FighterState::Reel),
        }
    }

    fn on_block(&self, _attack: &AttackData, _state: &mut FighterStateData) -> BlockResponse {
        BlockResponse {
            chip_damage: 3, // Takes very little chip damage
            pushback: Fixed8_8::from_i16(5), // Minimal pushback
            frame_advantage: 1, // Actually gains advantage on block
        }
    }

    fn get_special_moves(&self) -> Vec<SpecialMove> {
        vec![
            // Screw Piledriver (360 + Punch)
            SpecialMove {
                name: "Screw Piledriver".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Up),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Spinning Piledriver (720 + Punch)
            SpecialMove {
                name: "Spinning Piledriver".to_string(),
                inputs: vec![
                    // Double 360 motion - simplified representation
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Up),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Down),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Left),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Up),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Direction(InputDirection::Right),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
            // Lariat (Punch + Kick simultaneously)
            SpecialMove {
                name: "Lariat".to_string(),
                inputs: vec![
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Punch),
                        duration: 1,
                        conditions: vec![],
                    },
                    MoveInput {
                        input_type: MoveInputType::Button(ButtonInput::Kick),
                        duration: 1,
                        conditions: vec![],
                    },
                ],
                properties: std::collections::HashMap::new(),
            },
        ]
    }

    fn check_special_moves(&self, input: &InputState, _state: &FighterStateData) -> Option<FighterAction> {
        // Check for Screw Piledriver (360 + punch) - simplified
        if input.buttons.any_punch() && self.command_grab_buffer >= 4 {
            return Some(FighterAction::SpecialMove("Screw Piledriver".to_string()));
        }

        // Check for Spinning Piledriver (720 + punch) - simplified
        if input.buttons.any_punch() && self.command_grab_buffer >= 8 {
            return Some(FighterAction::SpecialMove("Spinning Piledriver".to_string()));
        }

        // Check for Lariat (punch + kick)
        if input.buttons.any_punch() && input.buttons.any_kick() {
            return Some(FighterAction::SpecialMove("Lariat".to_string()));
        }

        None
    }

    fn execute_special_move(&self, move_name: &str, _state: &mut FighterStateData) -> SpecialMoveResult {
        match move_name {
            "Screw Piledriver" => self.execute_screw_piledriver(_state),
            "Spinning Piledriver" => self.execute_spinning_piledriver(_state),
            "Lariat" => self.execute_lariat(_state),
            _ => SpecialMoveResult::Failed,
        }
    }
}

impl Zangief {
    fn execute_screw_piledriver(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: if self.muscle_power { 1800 } else { 1400 },
            stun: if self.muscle_power { 1200 } else { 1000 },
            meter_gain: 400,
            projectile: None,
        }
    }

    fn execute_spinning_piledriver(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: if self.muscle_power { 2200 } else { 1800 },
            stun: if self.muscle_power { 1500 } else { 1200 },
            meter_gain: 500,
            projectile: None,
        }
    }

    fn execute_lariat(&self, _state: &mut FighterStateData) -> SpecialMoveResult {
        SpecialMoveResult::Success {
            damage: 700,
            stun: 500,
            meter_gain: 200,
            projectile: None,
        }
    }
}

impl CharacterGlitches for Zangief {
    fn has_glitch(&self, _glitch_id: GlitchId) -> bool {
        false // Zangief has no major glitches
    }

    fn execute_glitch(&self, _glitch_id: GlitchId, _state: &mut FighterStateData) -> bool {
        false
    }
}
