//! # Components
//! 
//! ECS components for the Street Fighter II game engine.

use bevy::prelude::*;
use sf2_types::*;

/// Fighter component - marks an entity as a playable character
#[derive(Component, Debug)]
pub struct Fighter {
    pub fighter_id: FighterId,
    pub player_number: u8, // 1 or 2
}

/// Health component for fighters
#[derive(Component, Debug)]
pub struct Health {
    pub current: i32,
    pub maximum: i32,
}

/// Position component using fixed-point arithmetic for consistency
#[derive(Component, Debug)]
pub struct Position {
    pub x: Fixed16_16,
    pub y: Fixed16_16,
}

/// Velocity component for movement
#[derive(Component, Debug)]
pub struct Velocity {
    pub x: Fixed16_16,
    pub y: Fixed16_16,
}

/// Animation state component
#[derive(Component, Debug)]
pub struct AnimationState {
    pub current_animation: String,
    pub frame: u32,
    pub timer: f32,
}

/// Enhanced input buffer component for special move detection
#[derive(Component, Debug)]
pub struct InputBufferComponent {
    pub buffer: FrameInputBuffer,
    pub special_move_detector: SpecialMoveDetector,
    pub c99_state: C99InputState,
    pub config: InputConfig,
}

/// Player input component for tracking current input state
#[derive(Component, Debug)]
pub struct PlayerInput {
    pub player_number: u8,
    pub current_input: InputState,
    pub previous_input: InputState,
    pub facing_direction: FbDirection,
}

/// Collision box component
#[derive(Component, Debug)]
pub struct CollisionBox {
    pub width: u16,
    pub height: u16,
    pub offset_x: i16,
    pub offset_y: i16,
}

/// Hitbox component for attacks
#[derive(Component, Debug)]
pub struct Hitbox {
    pub damage: u16,
    pub stun: u16,
    pub knockback_x: i16,
    pub knockback_y: i16,
    pub active_frames: u16,
    pub current_frame: u16,
}

/// Hurtbox component for taking damage
#[derive(Component, Debug)]
pub struct Hurtbox {
    pub vulnerability: f32, // Damage multiplier
}

/// Fighter state component (legacy - replaced by FighterStateData)
#[derive(Component, Debug)]
pub struct FighterState {
    pub stance: Stance,
    pub facing: Direction,
    pub airborne: AirborneState,
}

/// Placeholder types - these will be properly defined in sf2_types
#[derive(Debug, Clone, Copy)]
pub enum FighterId {
    Ryu, Ken, ChunLi, Guile, Blanka, EHonda,
    Zangief, Dhalsim, MBison, Sagat, Balrog, Vega,
}

#[derive(Debug, Clone, Copy)]
pub enum Stance {
    Standing,
    Crouching,
    Jumping,
    Attacking,
    Blocking,
    Stunned,
}

#[derive(Debug, Clone, Copy)]
pub enum Direction {
    Left,
    Right,
}

#[derive(Debug, Clone, Copy)]
pub enum AirborneState {
    OnGround,
    Jumping,
    Falling,
    Reeling,
}

/// Input event for the input buffer
#[derive(Debug, Clone)]
pub struct InputEvent {
    pub input_type: InputType,
    pub timestamp: f64,
}

#[derive(Debug, Clone, Copy)]
pub enum InputType {
    Up, Down, Left, Right,
    LightPunch, MediumPunch, HeavyPunch,
    LightKick, MediumKick, HeavyKick,
}
