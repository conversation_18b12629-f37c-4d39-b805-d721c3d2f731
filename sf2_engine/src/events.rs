//! # Events
//! 
//! Event types for the Street Fighter II game engine.

use bevy::prelude::*;
use crate::components::*;
use sf2_types::{Point16, Vect16};

/// Event fired when a hit occurs
#[derive(Event, Debug)]
pub struct HitEvent {
    pub attacker: Entity,
    pub defender: Entity,
    pub damage: u16,
    pub knockback: Vect16,
    pub hit_position: Point16,
}

/// Event fired when an attack is blocked
#[derive(Event, Debug)]
pub struct BlockEvent {
    pub attacker: Entity,
    pub defender: Entity,
    pub chip_damage: u16,
    pub block_position: Point16,
}

/// Event fired when entities are pushed apart
#[derive(Event, Debug)]
pub struct PushEvent {
    pub entity1: Entity,
    pub entity2: Entity,
    pub separation_force: Vect16,
}

/// Event fired when a special move is executed
#[derive(Event, Debug)]
pub struct SpecialMoveEvent {
    pub entity: Entity,
    pub pattern: sf2_types::SpecialMovePattern,
    pub frame: u64,
}

/// Event fired when a round ends
#[derive(Event, Debug)]
pub struct RoundEndEvent {
    pub winner: Option<Entity>,
    pub reason: RoundEndReason,
}

#[derive(Debug, Clone, Copy)]
pub enum RoundEndReason {
    KnockOut,
    TimeOut,
    PerfectVictory,
}

/// Event fired when a fighter is knocked out
#[derive(Event, Debug)]
pub struct KnockOutEvent {
    pub fighter: Entity,
    pub attacker: Option<Entity>,
}

/// Event fired when the game state should change
#[derive(Event, Debug)]
pub struct GameStateChangeEvent {
    pub new_state: crate::states::GameState,
}

/// Event fired when audio should be played
#[derive(Event, Debug)]
pub struct AudioEvent {
    pub sound_id: String,
    pub volume: f32,
    pub position: Option<Vec2>, // For positional audio
}

/// Event fired when a combo is performed
#[derive(Event, Debug)]
pub struct ComboEvent {
    pub fighter: Entity,
    pub hit_count: u8,
    pub damage_total: u16,
}

/// Event fired when input is processed
#[derive(Event, Debug)]
pub struct InputProcessedEvent {
    pub player: u8,
    pub input_type: InputType,
    pub timestamp: f64,
}
