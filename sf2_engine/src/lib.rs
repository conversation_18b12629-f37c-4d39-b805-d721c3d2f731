//! # SF2 Engine
//! 
//! Core Street Fighter II game engine built with Bevy ECS.
//! 
//! This crate provides the main game systems, components, and resources
//! needed to run a Street Fighter II game.

use bevy::prelude::*;
use sf2_types::{
    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,
    <PERSON>ile,
    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,
    GameMode, FightMode, RoundMode, GameState as SF2GameState,
    FrameTimer, FrameBudgetManager, CountdownTimer,
    StateValidator, StateTransition,
    FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,
    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,
    FrameInputBuffer, SpecialMoveDetector, InputConfig,
    CollisionConfig, SpatialGrid, CollisionResponseProcessor, HitboxManager,
};

pub mod components;
pub mod systems;
pub mod resources;
pub mod events;
pub mod states;
pub mod game_logic;
pub mod performance;
pub mod collision_system;
pub mod character_system;
pub mod characters;

pub use components::*;
pub use systems::*;
pub use resources::{GameConfig, InputBuffer as GlobalInputBuffer};
pub use states::{GameState, FightState};
pub use events::*;
pub use states::*;
pub use character_system::*;
pub use characters::*;

/// Main plugin that sets up the SF2 game engine
pub struct SF2EnginePlugin;

impl Plugin for SF2EnginePlugin {
    fn build(&self, app: &mut App) {
        app
            // Add game states
            .init_state::<GameState>()
            .init_state::<FightState>()
            
            // Add resources
            .init_resource::<GameConfig>()
            .init_resource::<GlobalInputBuffer>()
            .init_resource::<game_logic::GameLogic>()
            .init_resource::<performance::PerformanceConfig>()
            .init_resource::<performance::PerformanceMetrics>()
            .init_resource::<collision_system::CollisionSystemConfig>()
            .init_resource::<collision_system::CollisionMetrics>()
            .init_resource::<SpatialGrid>()
            .init_resource::<CollisionResponseProcessor>()
            .init_resource::<CharacterRegistry>()
            
            // Add events
            .add_event::<HitEvent>()
            .add_event::<SpecialMoveEvent>()
            .add_event::<RoundEndEvent>()
            .add_event::<BlockEvent>()
            .add_event::<PushEvent>()
            .add_event::<CharacterSelectedEvent>()
            .add_event::<CharacterLoadedEvent>()
            
            // Add core systems
            .add_systems(Startup, setup_game)
            .add_systems(
                Update,
                (
                    input_system,
                    special_move_detection_system,
                    movement_system,
                    animation_system,
                    collision_system::collision_detection_system,
                    character_selection_system,
                    character_loading_system,
                    character_behavior_system,
                    character_glitch_system,
                ).run_if(in_state(GameState::InGame))
            )
            .add_systems(
                FixedUpdate,
                (
                    performance::optimized_game_loop_system,
                    physics_system,
                    combat_system,
                ).run_if(in_state(GameState::InGame))
            );
    }
}

/// Initialize the game engine with default settings
pub fn setup_game(mut commands: Commands) {
    info!("SF2 Engine initializing...");

    // Spawn the main camera
    commands.spawn(Camera2d);

    // Create test fighters with new input system
    setup_test_fighters(&mut commands);

    info!("SF2 Engine initialized successfully");
}

/// Set up test fighters for development
fn setup_test_fighters(commands: &mut Commands) {
    use sf2_types::{InputConfig, FbDirection, InputDirection, ButtonFlags, InputState};

    // Load input configuration from environment variables
    let input_config = InputConfig::from_env();

    if input_config.debug_input_display {
        input_config.print_config();
    }

    // Player 1 (Left side)
    commands.spawn((
        Fighter {
            fighter_id: FighterId::Ryu,
            player_number: 1,
        },
        Position {
            x: sf2_types::Fixed16_16::from_f32(100.0),
            y: sf2_types::Fixed16_16::from_f32(200.0),
        },
        Velocity {
            x: sf2_types::Fixed16_16::ZERO,
            y: sf2_types::Fixed16_16::ZERO,
        },
        Health {
            current: 100,
            maximum: 100,
        },
        PlayerInput {
            player_number: 1,
            current_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            previous_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            facing_direction: FbDirection::FacingRight,
        },
        InputBufferComponent {
            buffer: sf2_types::FrameInputBuffer::new(),
            special_move_detector: sf2_types::SpecialMoveDetector::new(),
            c99_state: sf2_types::C99InputState::new(),
            config: input_config.clone(),
        },
        components::FighterState {
            stance: Stance::Standing,
            facing: components::Direction::Right,
            airborne: components::AirborneState::OnGround,
        },
        FighterStateData::new(),
        CollisionBox {
            width: 32,
            height: 64,
            offset_x: -16,
            offset_y: -64,
        },
        AnimationState {
            current_animation: "idle".to_string(),
            frame: 0,
            timer: 0.0,
        },
        HitboxManager::new(sf2_types::FighterId::Ryu),
    ));

    // Player 2 (Right side) - for future multiplayer support
    commands.spawn((
        Fighter {
            fighter_id: FighterId::Ken,
            player_number: 2,
        },
        Position {
            x: sf2_types::Fixed16_16::from_f32(300.0),
            y: sf2_types::Fixed16_16::from_f32(200.0),
        },
        Velocity {
            x: sf2_types::Fixed16_16::ZERO,
            y: sf2_types::Fixed16_16::ZERO,
        },
        Health {
            current: 100,
            maximum: 100,
        },
        PlayerInput {
            player_number: 2,
            current_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            previous_input: InputState {
                direction: InputDirection::Neutral,
                buttons: ButtonFlags::NONE,
                frame: 0,
            },
            facing_direction: FbDirection::FacingLeft,
        },
        InputBufferComponent {
            buffer: sf2_types::FrameInputBuffer::new(),
            special_move_detector: sf2_types::SpecialMoveDetector::new(),
            c99_state: sf2_types::C99InputState::new(),
            config: input_config,
        },
        components::FighterState {
            stance: Stance::Standing,
            facing: components::Direction::Left,
            airborne: components::AirborneState::OnGround,
        },
        FighterStateData::new(),
        CollisionBox {
            width: 32,
            height: 64,
            offset_x: -16,
            offset_y: -64,
        },
        AnimationState {
            current_animation: "idle".to_string(),
            frame: 0,
            timer: 0.0,
        },
        HitboxManager::new(sf2_types::FighterId::Ken),
    ));

    info!("Test fighters spawned with enhanced input system");
}
