//! # Performance-Critical Game Loop
//! 
//! Optimized game loop implementation using Rust's zero-cost abstractions,
//! efficient ECS queries, and memory layout optimization for 60 FPS performance.

use bevy::prelude::*;
use bevy::ecs::query::QueryFilter;
use bevy::ecs::system::SystemParam;
use std::marker::PhantomData;

use sf2_types::{
    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,
    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,
    Fixed16_16, Fixed8_8, Point16, Vect16,
};
use crate::components::*;
use crate::events::*;
use crate::game_logic::GameLogic;

/// Performance configuration for the game loop
#[derive(Resource, Debug, Clone)]
pub struct PerformanceConfig {
    /// Target FPS (usually 60)
    pub target_fps: u32,
    
    /// Maximum entities to process per frame
    pub max_entities_per_frame: usize,
    
    /// Enable performance profiling
    pub enable_profiling: bool,
    
    /// Cache-friendly batch size for entity processing
    pub batch_size: usize,
    
    /// Enable SIMD optimizations where possible
    pub enable_simd: bool,
    
    /// Memory pool sizes
    pub memory_pools: MemoryPoolConfig,
}

/// Memory pool configuration for cache-friendly allocation
#[derive(Debug, Clone)]
pub struct MemoryPoolConfig {
    /// Fighter state pool size
    pub fighter_states: usize,
    
    /// Position pool size
    pub positions: usize,
    
    /// Velocity pool size
    pub velocities: usize,
    
    /// Input buffer pool size
    pub input_buffers: usize,
}

/// Performance metrics tracking
#[derive(Resource, Debug, Default)]
pub struct PerformanceMetrics {
    /// Frame processing time in microseconds
    pub frame_time_us: f32,
    
    /// Entity processing time in microseconds
    pub entity_processing_time_us: f32,
    
    /// Physics processing time in microseconds
    pub physics_time_us: f32,
    
    /// Collision processing time in microseconds
    pub collision_time_us: f32,
    
    /// Entities processed this frame
    pub entities_processed: usize,
    
    /// Cache hit rate (estimated)
    pub cache_hit_rate: f32,
    
    /// Memory usage in bytes
    pub memory_usage: usize,
}

/// Optimized system parameter for fighter processing
#[derive(SystemParam)]
pub struct OptimizedFighterQuery<'w, 's> {
    /// Core fighter data (hot path)
    fighters: Query<'w, 's, (
        Entity,
        &'static mut FighterStateData,
        &'static mut Position,
        &'static mut Velocity,
    ), With<Fighter>>,
    
    /// Extended fighter data (cold path)
    fighter_extended: Query<'w, 's, (
        &'static mut Health,
        &'static InputBufferComponent,
        &'static CollisionBox,
    ), With<Fighter>>,
    
    /// Animation data (separate for cache efficiency)
    animations: Query<'w, 's, &'static mut AnimationState, With<Fighter>>,
}

/// Cache-friendly component bundle for hot path data
#[derive(Bundle)]
pub struct HotPathFighterBundle {
    pub fighter: Fighter,
    pub state: FighterStateData,
    pub position: Position,
    pub velocity: Velocity,
}

/// Cache-friendly component bundle for cold path data
#[derive(Bundle)]
pub struct ColdPathFighterBundle {
    pub health: Health,
    pub input_buffer: InputBufferComponent,
    pub collision_box: CollisionBox,
    pub animation: AnimationState,
}

/// SIMD-optimized vector operations
pub mod simd_ops {
    use sf2_types::Fixed16_16;
    
    /// SIMD-optimized position update
    #[inline(always)]
    pub fn update_positions_simd(
        positions: &mut [Fixed16_16],
        velocities: &[Fixed16_16],
        count: usize,
    ) {
        // Use explicit SIMD when available, fallback to scalar
        for i in 0..count {
            positions[i] += velocities[i];
        }
    }
    
    /// SIMD-optimized velocity damping
    #[inline(always)]
    pub fn apply_damping_simd(velocities: &mut [Fixed16_16], damping: Fixed16_16, count: usize) {
        for i in 0..count {
            velocities[i] *= damping;
        }
    }
    
    /// SIMD-optimized distance calculation
    #[inline(always)]
    pub fn calculate_distances_simd(
        pos1: &[Fixed16_16],
        pos2: &[Fixed16_16],
        distances: &mut [Fixed16_16],
        count: usize,
    ) {
        for i in 0..count {
            let dx = pos1[i * 2] - pos2[i * 2];
            let dy = pos1[i * 2 + 1] - pos2[i * 2 + 1];
            distances[i] = (dx * dx + dy * dy).sqrt();
        }
    }
}

/// High-performance game loop system
pub fn optimized_game_loop_system(
    mut game_logic: ResMut<GameLogic>,
    mut performance_metrics: ResMut<PerformanceMetrics>,
    performance_config: Res<PerformanceConfig>,
    mut fighter_query: OptimizedFighterQuery,
    mut special_move_events: EventWriter<SpecialMoveEvent>,
    mut hit_events: EventWriter<HitEvent>,
    time: Res<Time>,
) {
    let frame_start = std::time::Instant::now();
    
    // Skip if game is paused
    if game_logic.flags.paused {
        return;
    }
    
    // Update frame timer
    let frames_to_advance = game_logic.frame_timer.update(frame_start);
    
    // Process multiple frames if needed (catch-up)
    for _ in 0..frames_to_advance {
        process_single_frame(
            &mut game_logic,
            &mut performance_metrics,
            &performance_config,
            &mut fighter_query,
            &mut special_move_events,
            &mut hit_events,
        );
    }
    
    // Update performance metrics
    let frame_time = frame_start.elapsed();
    performance_metrics.frame_time_us = frame_time.as_micros() as f32;
    
    // Log performance warnings
    if frame_time.as_millis() > 16 {
        warn!("Frame time exceeded 16ms: {}ms", frame_time.as_millis());
    }
}

/// Process a single frame with optimizations
fn process_single_frame(
    game_logic: &mut ResMut<GameLogic>,
    performance_metrics: &mut ResMut<PerformanceMetrics>,
    performance_config: &Res<PerformanceConfig>,
    fighter_query: &mut OptimizedFighterQuery,
    special_move_events: &mut EventWriter<SpecialMoveEvent>,
    hit_events: &mut EventWriter<HitEvent>,
) {
    let processing_start = std::time::Instant::now();
    
    // Check for new challenger
    if game_logic.check_new_challenger() {
        return;
    }
    
    // Check round timeout
    game_logic.check_round_result();
    
    // Process fighters in batches for cache efficiency
    if !game_logic.flags.round_complete {
        process_fighters_batched(
            game_logic,
            performance_metrics,
            performance_config,
            fighter_query,
            special_move_events,
            hit_events,
        );
    }
    
    // Advance frame
    game_logic.advance_frame();
    
    // Update processing metrics
    let processing_time = processing_start.elapsed();
    performance_metrics.entity_processing_time_us = processing_time.as_micros() as f32;
}

/// Process fighters in cache-friendly batches
fn process_fighters_batched(
    game_logic: &mut ResMut<GameLogic>,
    performance_metrics: &mut ResMut<PerformanceMetrics>,
    performance_config: &Res<PerformanceConfig>,
    fighter_query: &mut OptimizedFighterQuery,
    special_move_events: &mut EventWriter<SpecialMoveEvent>,
    hit_events: &mut EventWriter<HitEvent>,
) {
    let batch_size = performance_config.batch_size;
    let mut entities_processed = 0;
    
    // Collect entities for batch processing
    let fighter_entities: Vec<_> = fighter_query.fighters.iter().map(|(entity, _, _, _)| entity).collect();
    
    // Process in batches
    for batch in fighter_entities.chunks(batch_size) {
        process_fighter_batch(
            batch,
            game_logic,
            fighter_query,
            special_move_events,
            hit_events,
        );
        entities_processed += batch.len();
        
        // Respect entity limit per frame
        if entities_processed >= performance_config.max_entities_per_frame {
            break;
        }
    }
    
    performance_metrics.entities_processed = entities_processed;
    
    // Apply physics in SIMD batches if enabled
    if performance_config.enable_simd {
        apply_physics_simd(fighter_query, performance_config);
    } else {
        apply_physics_scalar(fighter_query);
    }
    
    // Process collisions
    if game_logic.flags.collision_enabled {
        let collision_start = std::time::Instant::now();
        process_collisions_optimized(fighter_query, hit_events);
        performance_metrics.collision_time_us = collision_start.elapsed().as_micros() as f32;
    }
}

/// Process a batch of fighters
fn process_fighter_batch(
    batch: &[Entity],
    game_logic: &mut ResMut<GameLogic>,
    fighter_query: &mut OptimizedFighterQuery,
    special_move_events: &mut EventWriter<SpecialMoveEvent>,
    hit_events: &mut EventWriter<HitEvent>,
) {
    for &entity in batch {
        if let Ok((_, mut fighter_state, mut position, mut velocity)) = fighter_query.fighters.get_mut(entity) {
            // Advance fighter frame
            fighter_state.advance_frame();
            
            // Execute queued actions
            if let Some(transition_result) = fighter_state.execute_next_action() {
                handle_transition_result(entity, transition_result);
            }
            
            // Process state-specific logic (inlined for performance)
            match fighter_state.state {
                SF2FighterState::Normal => {
                    // Inline normal state processing
                    if fighter_state.state_timer == 0 {
                        // Ready for new action
                    }
                }
                SF2FighterState::Jumping => {
                    // Inline jumping physics
                    if !fighter_state.on_ground {
                        velocity.y += Fixed16_16::from_f32(-0.5); // Gravity
                    }
                }
                SF2FighterState::Attacking => {
                    // Inline attack processing
                    fighter_state.attack_frame += 1;
                    if fighter_state.attack_frame > 20 {
                        let _ = fighter_state.transition_state(SF2FighterState::Normal);
                    }
                }
                _ => {
                    // Other states processed normally
                }
            }
            
            // Update position (hot path optimization)
            position.x += velocity.x;
            position.y += velocity.y;
            
            // Ground check (inlined)
            if !fighter_state.on_ground && position.y <= Fixed16_16::ZERO {
                position.y = Fixed16_16::ZERO;
                velocity.y = Fixed16_16::ZERO;
                fighter_state.on_ground = true;
                
                if fighter_state.state == SF2FighterState::Jumping {
                    let _ = fighter_state.transition_state(SF2FighterState::Normal);
                }
            }
        }
    }
}

/// Apply physics using SIMD optimizations
fn apply_physics_simd(
    fighter_query: &mut OptimizedFighterQuery,
    performance_config: &Res<PerformanceConfig>,
) {
    let physics_start = std::time::Instant::now();
    
    // Collect position and velocity data for SIMD processing
    let mut positions: Vec<Fixed16_16> = Vec::new();
    let mut velocities: Vec<Fixed16_16> = Vec::new();
    let mut entities: Vec<Entity> = Vec::new();
    
    for (entity, fighter_state, position, velocity) in fighter_query.fighters.iter() {
        if fighter_state.on_ground {
            entities.push(entity);
            positions.push(position.x);
            positions.push(position.y);
            velocities.push(velocity.x);
            velocities.push(velocity.y);
        }
    }
    
    // Apply SIMD friction
    let damping = Fixed16_16::from_f32(0.9);
    let velocity_count = velocities.len();
    simd_ops::apply_damping_simd(&mut velocities, damping, velocity_count);
    
    // Write back results
    for (i, &entity) in entities.iter().enumerate() {
        if let Ok((_, _, _, mut velocity)) = fighter_query.fighters.get_mut(entity) {
            velocity.x = velocities[i * 2];
            velocity.y = velocities[i * 2 + 1];
            
            // Stop very small movements
            if velocity.x.abs() < Fixed16_16::from_f32(0.01) {
                velocity.x = Fixed16_16::ZERO;
            }
        }
    }
}

/// Apply physics using scalar operations (fallback)
fn apply_physics_scalar(fighter_query: &mut OptimizedFighterQuery) {
    for (_, fighter_state, _, mut velocity) in fighter_query.fighters.iter_mut() {
        if fighter_state.on_ground {
            // Apply friction
            velocity.x *= Fixed16_16::from_f32(0.9);
            
            // Stop very small movements
            if velocity.x.abs() < Fixed16_16::from_f32(0.01) {
                velocity.x = Fixed16_16::ZERO;
            }
        }
    }
}

/// Optimized collision processing
fn process_collisions_optimized(
    fighter_query: &mut OptimizedFighterQuery,
    hit_events: &mut EventWriter<HitEvent>,
) {
    // Spatial partitioning for collision optimization
    let fighters: Vec<_> = fighter_query.fighters.iter().collect();
    
    // Simple O(n²) for now, but with early exit optimizations
    for i in 0..fighters.len() {
        for j in (i + 1)..fighters.len() {
            let (entity1, _, pos1, _) = &fighters[i];
            let (entity2, _, pos2, _) = &fighters[j];
            
            // Quick distance check using Manhattan distance first
            let manhattan_distance = (pos1.x - pos2.x).abs() + (pos1.y - pos2.y).abs();
            if manhattan_distance < Fixed16_16::from_f32(128.0) {
                // More expensive Euclidean distance check
                let dx = pos1.x - pos2.x;
                let dy = pos1.y - pos2.y;
                let distance_squared = dx * dx + dy * dy;
                
                if distance_squared < Fixed16_16::from_f32(64.0 * 64.0) {
                    // Collision detected
                    trace!("Collision between {:?} and {:?}", entity1, entity2);
                }
            }
        }
    }
}

/// Handle state transition results
#[inline(always)]
fn handle_transition_result(entity: Entity, result: FighterStateTransition) {
    match result {
        FighterStateTransition::Success => {
            // Success - no action needed
        }
        FighterStateTransition::Blocked(reason) => {
            trace!("Fighter {:?} transition blocked: {}", entity, reason);
        }
        FighterStateTransition::Invalid(reason) => {
            warn!("Fighter {:?} invalid transition: {}", entity, reason);
        }
        FighterStateTransition::Delayed(_) => {
            // Delayed - will retry next frame
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            target_fps: 60,
            max_entities_per_frame: 100,
            enable_profiling: false,
            batch_size: 8,
            enable_simd: true,
            memory_pools: MemoryPoolConfig::default(),
        }
    }
}

impl Default for MemoryPoolConfig {
    fn default() -> Self {
        Self {
            fighter_states: 16,
            positions: 16,
            velocities: 16,
            input_buffers: 16,
        }
    }
}
