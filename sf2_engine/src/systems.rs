//! # Systems
//! 
//! ECS systems for the Street Fighter II game engine.

use bevy::prelude::*;
use crate::components::*;
use crate::events::*;
use sf2_types::{
    Fixed16_16, InputDirection, ButtonFlags, InputState, C99InputState,
    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,
    FrameInputBuffer, SpecialMoveDetector, InputConfig,
};

/// Enhanced input processing system with special move detection
pub fn input_system(
    keyboard: Res<ButtonInput<KeyCode>>,
    mut player_query: Query<&mut PlayerInput, With<Fighter>>,
    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,
    time: Res<Time>,
) {
    let current_frame = (time.elapsed_secs() * 60.0) as u64; // 60 FPS frame counter

    for mut player_input in player_query.iter_mut() {
        // Store previous input
        player_input.previous_input = player_input.current_input;

        // Build current input state
        let mut direction = InputDirection::Neutral;
        let mut buttons = ButtonFlags::new();

        // Process directional input
        let up = keyboard.pressed(KeyCode::ArrowUp);
        let down = keyboard.pressed(KeyCode::ArrowDown);
        let left = keyboard.pressed(KeyCode::ArrowLeft);
        let right = keyboard.pressed(KeyCode::ArrowRight);

        direction = match (up, down, left, right) {
            (true, false, false, false) => InputDirection::Up,
            (false, true, false, false) => InputDirection::Down,
            (false, false, true, false) => InputDirection::Left,
            (false, false, false, true) => InputDirection::Right,
            (true, false, true, false) => InputDirection::UpLeft,
            (true, false, false, true) => InputDirection::UpRight,
            (false, true, true, false) => InputDirection::DownLeft,
            (false, true, false, true) => InputDirection::DownRight,
            _ => InputDirection::Neutral,
        };

        // Process button input
        if keyboard.pressed(KeyCode::KeyZ) {
            buttons.set_button(SF2ButtonInput::LightPunch);
        }
        if keyboard.pressed(KeyCode::KeyX) {
            buttons.set_button(SF2ButtonInput::MediumPunch);
        }
        if keyboard.pressed(KeyCode::KeyC) {
            buttons.set_button(SF2ButtonInput::HeavyPunch);
        }
        if keyboard.pressed(KeyCode::KeyA) {
            buttons.set_button(SF2ButtonInput::LightKick);
        }
        if keyboard.pressed(KeyCode::KeyS) {
            buttons.set_button(SF2ButtonInput::MediumKick);
        }
        if keyboard.pressed(KeyCode::KeyD) {
            buttons.set_button(SF2ButtonInput::HeavyKick);
        }

        // Update current input state
        player_input.current_input = InputState {
            direction,
            buttons,
            frame: current_frame,
        };
    }
}

/// Special move detection system
pub fn special_move_detection_system(
    player_query: Query<(&PlayerInput, Entity), With<Fighter>>,
    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,
    mut special_move_events: EventWriter<SpecialMoveEvent>,
) {
    for (player_input, entity) in player_query.iter() {
        if let Ok(mut input_buffer) = buffer_query.get_mut(entity) {
            // Update input buffer and special move detector
            input_buffer.buffer.push(player_input.current_input);
            input_buffer.special_move_detector.update(
                player_input.current_input,
                player_input.facing_direction,
            );

            // Update C99 compatibility state
            input_buffer.c99_state.update(
                C99InputState::from_modern_input(
                    player_input.current_input,
                    player_input.facing_direction,
                ).joy_decode,
                player_input.facing_direction,
            );

            // Check for special moves
            if let Some(special_move) = input_buffer.special_move_detector.detect_special_move(
                player_input.facing_direction,
            ) {
                special_move_events.send(SpecialMoveEvent {
                    entity,
                    pattern: special_move,
                    frame: player_input.current_input.frame,
                });

                if input_buffer.config.debug_special_moves {
                    info!("Special move detected: {:?} for entity {:?}", special_move, entity);
                }
            }

            // Clean up old inputs
            input_buffer.buffer.cleanup_old_inputs(60); // Keep 1 second of inputs at 60 FPS
        }
    }
}

/// Movement system
pub fn movement_system(
    mut query: Query<(&mut Position, &Velocity), With<Fighter>>,
    time: Res<Time>,
) {
    for (mut position, velocity) in query.iter_mut() {
        // Update position based on velocity
        let delta_time = Fixed16_16::from_f32(time.delta_secs());
        position.x = position.x + velocity.x * delta_time;
        position.y = position.y + velocity.y * delta_time;
    }
}

/// Animation system
pub fn animation_system(
    mut query: Query<&mut AnimationState, With<Fighter>>,
    time: Res<Time>,
) {
    for mut anim_state in query.iter_mut() {
        anim_state.timer += time.delta_secs();
        
        // Simple frame advancement (will be replaced with proper animation data)
        if anim_state.timer >= 1.0 / 12.0 { // 12 FPS animation
            anim_state.frame += 1;
            anim_state.timer = 0.0;
        }
    }
}

/// Collision detection system
pub fn collision_system(
    query: Query<(Entity, &Position, &CollisionBox), With<Fighter>>,
    mut hit_events: EventWriter<HitEvent>,
) {
    // Simple collision detection between fighters
    let fighters: Vec<_> = query.iter().collect();
    
    for (i, (entity_a, pos_a, box_a)) in fighters.iter().enumerate() {
        for (entity_b, pos_b, box_b) in fighters.iter().skip(i + 1) {
            // Check if collision boxes overlap
            let a_left = pos_a.x.to_f32() + box_a.offset_x as f32;
            let a_right = a_left + box_a.width as f32;
            let a_top = pos_a.y.to_f32() + box_a.offset_y as f32;
            let a_bottom = a_top + box_a.height as f32;
            
            let b_left = pos_b.x.to_f32() + box_b.offset_x as f32;
            let b_right = b_left + box_b.width as f32;
            let b_top = pos_b.y.to_f32() + box_b.offset_y as f32;
            let b_bottom = b_top + box_b.height as f32;
            
            if a_left < b_right && a_right > b_left && a_top < b_bottom && a_bottom > b_top {
                // Collision detected
                hit_events.send(HitEvent {
                    attacker: *entity_a,
                    defender: *entity_b,
                    damage: 10, // Placeholder
                    knockback: sf2_types::Vect16::from_ints(5, 0),
                    hit_position: sf2_types::Point16::new(
                        pos_a.x.integer_part(),
                        pos_a.y.integer_part(),
                    ),
                });
            }
        }
    }
}

/// Physics system (runs on fixed timestep)
pub fn physics_system(
    mut query: Query<(&mut Velocity, &FighterState), With<Fighter>>,
) {
    for (mut velocity, state) in query.iter_mut() {
        // Apply gravity if airborne
        match state.airborne {
            AirborneState::Jumping | AirborneState::Falling => {
                velocity.y = velocity.y - sf2_types::Fixed16_16::from_f32(9.8 * 1.0/60.0); // Gravity
            }
            AirborneState::OnGround => {
                // Ground friction
                velocity.x = velocity.x * sf2_types::Fixed16_16::from_f32(0.9);
                velocity.y = sf2_types::Fixed16_16::ZERO;
            }
            _ => {}
        }
    }
}

/// Combat system (runs on fixed timestep)
pub fn combat_system(
    mut hit_events: EventReader<HitEvent>,
    mut health_query: Query<&mut Health>,
) {
    for hit_event in hit_events.read() {
        if let Ok(mut health) = health_query.get_mut(hit_event.defender) {
            health.current = (health.current - hit_event.damage as i32).max(0);
            info!("Fighter took {} damage, health now: {}", hit_event.damage, health.current);
        }
    }
}
