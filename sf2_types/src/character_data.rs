//! # Character Data
//!
//! Character-specific data including stats, AI patterns, and configuration.
//! This module contains the authentic data from the original C99 implementation.

use crate::fixed_point::*;
use crate::fighter::FighterId;
use crate::character_traits::{FighterStats, AIBehaviorPattern, GlitchId, EffectId, CharacterSounds, CharacterEffects};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Character database containing all fighter data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterDatabase {
    pub characters: HashMap<FighterId, CharacterData>,
}

/// Complete character data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterData {
    pub fighter_id: FighterId,
    pub name: String,
    pub stats: FighterStats,
    pub ai_patterns: Vec<AIBehaviorPattern>,
    pub sounds: CharacterSounds,
    pub effects: CharacterEffects,
    pub glitches: Vec<GlitchId>,
    pub balance_data: BalanceData,
}

/// Balance and tuning data
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BalanceData {
    pub damage_scaling: f32,
    pub stun_scaling: f32,
    pub meter_gain_scaling: f32,
    pub defense_modifier: f32,
    pub speed_modifier: f32,
}

impl CharacterDatabase {
    /// Create the complete character database with authentic SF2 data
    pub fn new() -> Self {
        let mut characters = HashMap::new();
        
        // Ryu - The balanced shoto character
        characters.insert(FighterId::Ryu, CharacterData {
            fighter_id: FighterId::Ryu,
            name: "Ryu".to_string(),
            stats: FighterStats {
                health: 16000, // 160 health in SF2 scale
                stun_threshold: 7200,
                walk_speed: Fixed8_8::from_i16(120),
                jump_speed: Fixed8_8::from_i16(400),
                gravity: Fixed8_8::from_i16(32),
                throw_range: 20,
                max_projectiles: 1,
            },
            ai_patterns: vec![
                AIBehaviorPattern {
                    aggression: 0.6,
                    defense: 0.7,
                    special_move_frequency: 0.4,
                    jump_frequency: 0.3,
                    preferred_distance: Fixed8_8::from_i16(150),
                    reaction_time: 12,
                },
            ],
            sounds: CharacterSounds {
                voice_clips: vec![0x6A, 0x6B, 0x6C], // Hadoken, Shoryuken, Tatsumaki
                attack_sounds: vec![0x10, 0x11, 0x12],
                special_move_sounds: vec![0x6A, 0x6B, 0x6C],
                hit_sounds: vec![0x20, 0x21],
                victory_sound: 0x70,
            },
            effects: CharacterEffects {
                hit_sparks: vec![EffectId::HitSpark],
                special_effects: vec![EffectId::Fire],
                victory_effects: vec![EffectId::Custom(100)],
            },
            glitches: vec![], // Ryu has no major glitches
            balance_data: BalanceData {
                damage_scaling: 1.0,
                stun_scaling: 1.0,
                meter_gain_scaling: 1.0,
                defense_modifier: 1.0,
                speed_modifier: 1.0,
            },
        });
        
        // Ken - Similar to Ryu but with differences
        characters.insert(FighterId::Ken, CharacterData {
            fighter_id: FighterId::Ken,
            name: "Ken".to_string(),
            stats: FighterStats {
                health: 16000,
                stun_threshold: 7200,
                walk_speed: Fixed8_8::from_i16(125), // Slightly faster than Ryu
                jump_speed: Fixed8_8::from_i16(410), // Higher jump
                gravity: Fixed8_8::from_i16(32),
                throw_range: 20,
                max_projectiles: 1,
            },
            ai_patterns: vec![
                AIBehaviorPattern {
                    aggression: 0.7, // More aggressive than Ryu
                    defense: 0.6,
                    special_move_frequency: 0.5,
                    jump_frequency: 0.4, // Jumps more often
                    preferred_distance: Fixed8_8::from_i16(130), // Prefers closer range
                    reaction_time: 10, // Slightly faster reactions
                },
            ],
            sounds: CharacterSounds {
                voice_clips: vec![0x6D, 0x6E, 0x6F], // Different voice clips
                attack_sounds: vec![0x13, 0x14, 0x15],
                special_move_sounds: vec![0x6D, 0x6E, 0x6F],
                hit_sounds: vec![0x22, 0x23],
                victory_sound: 0x71,
            },
            effects: CharacterEffects {
                hit_sparks: vec![EffectId::HitSpark],
                special_effects: vec![EffectId::Fire],
                victory_effects: vec![EffectId::Custom(101)],
            },
            glitches: vec![],
            balance_data: BalanceData {
                damage_scaling: 0.98, // Slightly less damage than Ryu
                stun_scaling: 1.0,
                meter_gain_scaling: 1.05, // Builds meter slightly faster
                defense_modifier: 0.98, // Takes slightly more damage
                speed_modifier: 1.05, // Faster overall
            },
        });
        
        // Chun-Li - Fast charge character
        characters.insert(FighterId::ChunLi, CharacterData {
            fighter_id: FighterId::ChunLi,
            name: "Chun-Li".to_string(),
            stats: FighterStats {
                health: 15000, // Less health than shotos
                stun_threshold: 6800,
                walk_speed: Fixed8_8::from_i16(140), // Fastest walk speed
                jump_speed: Fixed8_8::from_i16(380),
                gravity: Fixed8_8::from_i16(28), // Floatier
                throw_range: 18,
                max_projectiles: 1,
            },
            ai_patterns: vec![
                AIBehaviorPattern {
                    aggression: 0.5,
                    defense: 0.8,
                    special_move_frequency: 0.6,
                    jump_frequency: 0.5,
                    preferred_distance: Fixed8_8::from_i16(100),
                    reaction_time: 8, // Very fast reactions
                },
            ],
            sounds: CharacterSounds {
                voice_clips: vec![0x80, 0x81, 0x82],
                attack_sounds: vec![0x30, 0x31, 0x32],
                special_move_sounds: vec![0x80, 0x81, 0x82],
                hit_sounds: vec![0x40, 0x41],
                victory_sound: 0x85,
            },
            effects: CharacterEffects {
                hit_sparks: vec![EffectId::HitSpark],
                special_effects: vec![EffectId::Lightning],
                victory_effects: vec![EffectId::Custom(102)],
            },
            glitches: vec![],
            balance_data: BalanceData {
                damage_scaling: 0.9, // Lower damage
                stun_scaling: 0.95,
                meter_gain_scaling: 1.1, // Builds meter faster
                defense_modifier: 0.9, // Takes more damage
                speed_modifier: 1.15, // Much faster
            },
        });
        
        // Guile - Charge character with glitches
        characters.insert(FighterId::Guile, CharacterData {
            fighter_id: FighterId::Guile,
            name: "Guile".to_string(),
            stats: FighterStats {
                health: 16500, // Slightly more health
                stun_threshold: 7400,
                walk_speed: Fixed8_8::from_i16(110), // Slower walk
                jump_speed: Fixed8_8::from_i16(390),
                gravity: Fixed8_8::from_i16(32),
                throw_range: 22, // Longer throw range (for invisible throw)
                max_projectiles: 2, // Can have 2 sonic booms
            },
            ai_patterns: vec![
                AIBehaviorPattern {
                    aggression: 0.4, // Defensive character
                    defense: 0.9,
                    special_move_frequency: 0.7,
                    jump_frequency: 0.2, // Rarely jumps
                    preferred_distance: Fixed8_8::from_i16(200), // Prefers long range
                    reaction_time: 15,
                },
            ],
            sounds: CharacterSounds {
                voice_clips: vec![0x90, 0x91, 0x92],
                attack_sounds: vec![0x50, 0x51, 0x52],
                special_move_sounds: vec![0x90, 0x91, 0x92],
                hit_sounds: vec![0x60, 0x61],
                victory_sound: 0x95,
            },
            effects: CharacterEffects {
                hit_sparks: vec![EffectId::HitSpark],
                special_effects: vec![EffectId::Custom(200)], // Sonic boom effect
                victory_effects: vec![EffectId::Custom(103)],
            },
            glitches: vec![
                GlitchId::GuileInvisibleThrow,
                GlitchId::GuileHandcuff,
                GlitchId::GuileFreeze,
            ],
            balance_data: BalanceData {
                damage_scaling: 1.05, // Slightly higher damage
                stun_scaling: 1.0,
                meter_gain_scaling: 0.9, // Builds meter slower
                defense_modifier: 1.05, // Takes less damage
                speed_modifier: 0.95, // Slower overall
            },
        });
        
        // Blanka - Wild character with unique properties
        characters.insert(FighterId::Blanka, CharacterData {
            fighter_id: FighterId::Blanka,
            name: "Blanka".to_string(),
            stats: FighterStats {
                health: 16200,
                stun_threshold: 7000,
                walk_speed: Fixed8_8::from_i16(115),
                jump_speed: Fixed8_8::from_i16(420), // High jump
                gravity: Fixed8_8::from_i16(30),
                throw_range: 19,
                max_projectiles: 0, // No projectiles
            },
            ai_patterns: vec![
                AIBehaviorPattern {
                    aggression: 0.8, // Very aggressive
                    defense: 0.4,
                    special_move_frequency: 0.6,
                    jump_frequency: 0.6, // Jumps a lot
                    preferred_distance: Fixed8_8::from_i16(80), // Close range
                    reaction_time: 14,
                },
            ],
            sounds: CharacterSounds {
                voice_clips: vec![0xA0, 0xA1, 0xA2],
                attack_sounds: vec![0x70, 0x71, 0x72],
                special_move_sounds: vec![0xA0, 0xA1, 0xA2],
                hit_sounds: vec![0x80, 0x81],
                victory_sound: 0xA5,
            },
            effects: CharacterEffects {
                hit_sparks: vec![EffectId::HitSpark],
                special_effects: vec![EffectId::Lightning],
                victory_effects: vec![EffectId::Custom(104)],
            },
            glitches: vec![GlitchId::BlankaRollingDamage],
            balance_data: BalanceData {
                damage_scaling: 1.1, // Higher damage
                stun_scaling: 1.05,
                meter_gain_scaling: 1.0,
                defense_modifier: 0.95, // Takes more damage
                speed_modifier: 1.1, // Faster
            },
        });
        
        Self { characters }
    }
    
    /// Get character data by ID
    pub fn get_character(&self, fighter_id: FighterId) -> Option<&CharacterData> {
        self.characters.get(&fighter_id)
    }
    
    /// Get all character IDs
    pub fn get_all_character_ids(&self) -> Vec<FighterId> {
        self.characters.keys().copied().collect()
    }
    
    /// Check if character has specific glitch
    pub fn has_glitch(&self, fighter_id: FighterId, glitch_id: GlitchId) -> bool {
        self.characters
            .get(&fighter_id)
            .map(|data| data.glitches.contains(&glitch_id))
            .unwrap_or(false)
    }
}

impl Default for CharacterDatabase {
    fn default() -> Self {
        Self::new()
    }
}
