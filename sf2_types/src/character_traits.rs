//! # Character Traits
//!
//! Trait system for character-specific behavior patterns and implementations.
//! This provides a framework for implementing the unique characteristics of each fighter.

use crate::fixed_point::*;
use crate::geometry::*;
use crate::fighter::*;
use crate::input::*;
use crate::fighter_state::*;
use bevy::prelude::Component;

use serde::{Deserialize, Serialize};

/// Core trait that all fighters must implement
pub trait FighterCharacter: Send + Sync {
    /// Get the fighter's unique identifier
    fn fighter_id(&self) -> FighterId;
    
    /// Get the fighter's display name
    fn name(&self) -> &'static str;
    
    /// Get the fighter's health and stun values
    fn base_stats(&self) -> FighterStats;
    
    /// Process input and update fighter state
    fn process_input(&self, input: &InputState, state: &mut FighterStateData) -> Vec<FighterAction>;
    
    /// Update fighter physics and movement
    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16);
    
    /// Handle special move execution
    fn execute_special_move(&self, move_id: SpecialMoveId, state: &mut FighterStateData) -> SpecialMoveResult;
    
    /// Get frame data for specific moves
    fn get_frame_data(&self, move_id: MoveId) -> FrameData;
    
    /// Get hitbox data for current animation frame
    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData>;
    
    /// Get hurtbox data for current animation frame
    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData>;
    
    /// Handle being hit by an attack
    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse;
    
    /// Handle blocking an attack
    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse;
    
    /// Get AI behavior patterns for computer players
    fn get_ai_behavior(&self, difficulty: u8) -> AIBehaviorPattern;
    
    /// Get character-specific sound effects
    fn get_sound_effects(&self) -> CharacterSounds;
    
    /// Get character-specific visual effects
    fn get_visual_effects(&self) -> CharacterEffects;
}

/// Base fighter statistics
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct FighterStats {
    pub health: u16,
    pub stun_threshold: u16,
    pub walk_speed: Fixed8_8,
    pub jump_speed: Fixed8_8,
    pub gravity: Fixed8_8,
    pub throw_range: u8,
    pub max_projectiles: u8,
}

/// Fighter action that can be performed
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FighterAction {
    /// Change fighter state
    StateTransition(FighterState),
    /// Execute a move
    ExecuteMove(MoveId),
    /// Play sound effect
    PlaySound(u16),
    /// Spawn visual effect
    SpawnEffect(EffectId),
    /// Update velocity
    SetVelocity(Fixed8_8, Fixed8_8),
    /// Set animation
    SetAnimation(u16),
}

/// Move identifier for all possible moves
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MoveId {
    // Normal moves
    StandingLightPunch,
    StandingMediumPunch,
    StandingHeavyPunch,
    StandingLightKick,
    StandingMediumKick,
    StandingHeavyKick,
    CrouchingLightPunch,
    CrouchingMediumPunch,
    CrouchingHeavyPunch,
    CrouchingLightKick,
    CrouchingMediumKick,
    CrouchingHeavyKick,
    JumpingLightPunch,
    JumpingMediumPunch,
    JumpingHeavyPunch,
    JumpingLightKick,
    JumpingMediumKick,
    JumpingHeavyKick,
    
    // Universal moves
    ForwardThrow,
    BackwardThrow,
    
    // Special moves (character-specific)
    Special(SpecialMoveId),
}

/// Special move execution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpecialMoveResult {
    pub success: bool,
    pub actions: Vec<FighterAction>,
    pub meter_cost: u16,
    pub recovery_frames: u16,
}

/// Frame data for moves
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct FrameData {
    pub startup_frames: u16,
    pub active_frames: u16,
    pub recovery_frames: u16,
    pub block_stun: u16,
    pub hit_stun: u16,
    pub damage: u16,
    pub stun_damage: u16,
    pub knockback_x: i16,
    pub knockback_y: i16,
}

/// Hitbox data for collision detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HitboxData {
    pub rect: Rect8,
    pub damage: u16,
    pub stun: u16,
    pub knockback: Vect16,
    pub hit_type: HitType,
    pub priority: u8,
}

/// Hurtbox data for taking damage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HurtboxData {
    pub rect: Rect8,
    pub vulnerability: f32,
    pub body_part: BodyPart,
}

/// Type of hit for different effects
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum HitType {
    Normal,
    Knockdown,
    Launcher,
    Overhead,
    Low,
    Throw,
    Projectile,
}

/// Body part for hit reactions
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BodyPart {
    Head,
    Body,
    Legs,
    Weak, // Special vulnerable area
}

/// Attack data for hit calculations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttackData {
    pub damage: u16,
    pub stun: u16,
    pub knockback: Vect16,
    pub hit_type: HitType,
    pub attacker_id: FighterId,
    pub move_id: MoveId,
}

/// Response to being hit
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HitResponse {
    pub damage_taken: u16,
    pub stun_taken: u16,
    pub knockback: Vect16,
    pub new_state: FighterState,
    pub hit_stun_frames: u16,
    pub effects: Vec<EffectId>,
}

/// Response to blocking an attack
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockResponse {
    pub chip_damage: u16,
    pub block_stun_frames: u16,
    pub pushback: Fixed8_8,
    pub effects: Vec<EffectId>,
}

/// AI behavior pattern for computer players
#[derive(Debug, Clone, Serialize, Deserialize, Component)]
pub struct AIBehaviorPattern {
    pub aggression: f32,
    pub defense: f32,
    pub special_move_frequency: f32,
    pub jump_frequency: f32,
    pub preferred_distance: Fixed8_8,
    pub reaction_time: u16, // frames
}

/// Character-specific sound effects
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterSounds {
    pub voice_clips: Vec<u16>,
    pub attack_sounds: Vec<u16>,
    pub special_move_sounds: Vec<u16>,
    pub hit_sounds: Vec<u16>,
    pub victory_sound: u16,
}

/// Character-specific visual effects
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterEffects {
    pub hit_sparks: Vec<EffectId>,
    pub special_effects: Vec<EffectId>,
    pub victory_effects: Vec<EffectId>,
}

/// Effect identifier
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EffectId {
    HitSpark,
    BlockSpark,
    Dust,
    Fire,
    Lightning,
    Ice,
    Custom(u16),
}

/// Trait for character-specific glitches and quirks
pub trait CharacterGlitches {
    /// Check if character has specific glitches enabled
    fn has_glitch(&self, glitch_id: GlitchId) -> bool;
    
    /// Execute character-specific glitch behavior
    fn execute_glitch(&self, glitch_id: GlitchId, state: &mut FighterStateData) -> GlitchResult;
}

/// Known character glitches
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum GlitchId {
    /// Guile's invisible throw
    GuileInvisibleThrow,
    /// Guile's handcuff glitch
    GuileHandcuff,
    /// Guile's freeze glitch
    GuileFreeze,
    /// Blanka's rolling attack interrupt damage
    BlankaRollingDamage,
    /// Dhalsim's teleport glitches
    DhalsimTeleport,
    /// Zangief's spinning piledriver range
    ZangiefSPDRange,
}

/// Result of glitch execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlitchResult {
    pub executed: bool,
    pub actions: Vec<FighterAction>,
    pub description: String,
}
