//! # Collision Detection Algorithms
//!
//! Core collision detection algorithms for the Street Fighter II engine.
//! Implements AABB, circle-circle, and circle-rectangle collision detection
//! with frame-accurate timing matching the original C99 implementation.

use crate::geometry::*;
use crate::collision_shapes::*;
use crate::fixed_point::*;
use crate::collision_config::CollisionConfig;
use serde::{Deserialize, Serialize};

/// Collision detection result
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct CollisionResult {
    /// Whether collision occurred
    pub hit: bool,
    /// Collision point (world coordinates)
    pub collision_point: Point8,
    /// Penetration depth
    pub penetration: u8,
    /// Collision normal (direction to separate)
    pub normal: Point8,
    /// Distance between centers
    pub distance: u16,
}

impl CollisionResult {
    pub const NO_COLLISION: Self = Self {
        hit: false,
        collision_point: Point8::ZERO,
        penetration: 0,
        normal: Point8::ZERO,
        distance: 0,
    };
    
    pub fn new_hit(collision_point: Point8, penetration: u8, normal: Point8, distance: u16) -> Self {
        Self {
            hit: true,
            collision_point,
            penetration,
            normal,
            distance,
        }
    }
}

/// Collision detection precision levels
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CollisionPrecision {
    /// Fast collision detection (AABB only)
    Fast,
    /// Normal collision detection (AABB + basic overlap)
    Normal,
    /// Precise collision detection (full geometric calculations)
    Precise,
}

impl From<u8> for CollisionPrecision {
    fn from(value: u8) -> Self {
        match value {
            0 => CollisionPrecision::Fast,
            1 => CollisionPrecision::Normal,
            2 => CollisionPrecision::Precise,
            _ => CollisionPrecision::Normal,
        }
    }
}

/// Core collision detection functions
pub struct CollisionDetector;

impl CollisionDetector {
    /// Check collision between two rectangles (AABB)
    pub fn rect_rect_collision(
        rect1: &CollisionRect,
        pos1: Point16,
        rect2: &CollisionRect,
        pos2: Point16,
        precision: CollisionPrecision,
    ) -> CollisionResult {
        // Convert to absolute coordinates
        let abs_rect1 = rect1.to_absolute(pos1.x, pos1.y);
        let abs_rect2 = rect2.to_absolute(pos2.x, pos2.y);
        
        // Fast AABB check
        if !Self::aabb_intersects(&abs_rect1, &abs_rect2) {
            return CollisionResult::NO_COLLISION;
        }
        
        match precision {
            CollisionPrecision::Fast => {
                // Just return basic collision info
                let center1 = Point8::new(
                    abs_rect1.origin.x + abs_rect1.size.width / 2,
                    abs_rect1.origin.y + abs_rect1.size.height / 2,
                );
                let center2 = Point8::new(
                    abs_rect2.origin.x + abs_rect2.size.width / 2,
                    abs_rect2.origin.y + abs_rect2.size.height / 2,
                );
                
                let collision_point = Point8::new(
                    (center1.x + center2.x) / 2,
                    (center1.y + center2.y) / 2,
                );
                
                CollisionResult::new_hit(collision_point, 1, Point8::new(1, 0), 0)
            }
            CollisionPrecision::Normal | CollisionPrecision::Precise => {
                Self::detailed_rect_collision(&abs_rect1, &abs_rect2)
            }
        }
    }
    
    /// Check collision between two circles
    pub fn circle_circle_collision(
        circle1: &CollisionCircle,
        pos1: Point16,
        circle2: &CollisionCircle,
        pos2: Point16,
        _precision: CollisionPrecision,
    ) -> CollisionResult {
        let (center1, radius1) = circle1.to_absolute(pos1.x, pos1.y);
        let (center2, radius2) = circle2.to_absolute(pos2.x, pos2.y);
        
        let dx = center2.x - center1.x;
        let dy = center2.y - center1.y;
        let distance_sq = (dx as i32 * dx as i32 + dy as i32 * dy as i32) as u32;
        let distance = (distance_sq as f32).sqrt() as u16;
        
        let combined_radius = radius1 as u16 + radius2 as u16;
        
        if distance <= combined_radius {
            let penetration = (combined_radius - distance) as u8;
            let collision_point = Point8::new(
                center1.x + (dx * radius1 as i8) / distance as i8,
                center1.y + (dy * radius1 as i8) / distance as i8,
            );
            
            // Normal points from circle1 to circle2
            let normal = if distance > 0 {
                Point8::new(
                    (dx * 127) / distance as i8,
                    (dy * 127) / distance as i8,
                )
            } else {
                Point8::new(127, 0) // Default normal if circles are at same position
            };
            
            CollisionResult::new_hit(collision_point, penetration, normal, distance)
        } else {
            CollisionResult::NO_COLLISION
        }
    }
    
    /// Check collision between circle and rectangle
    pub fn circle_rect_collision(
        circle: &CollisionCircle,
        circle_pos: Point16,
        rect: &CollisionRect,
        rect_pos: Point16,
        precision: CollisionPrecision,
    ) -> CollisionResult {
        let (circle_center, radius) = circle.to_absolute(circle_pos.x, circle_pos.y);
        let abs_rect = rect.to_absolute(rect_pos.x, rect_pos.y);
        
        // Find closest point on rectangle to circle center
        let closest_x = circle_center.x.clamp(abs_rect.left(), abs_rect.right());
        let closest_y = circle_center.y.clamp(abs_rect.top(), abs_rect.bottom());
        
        let dx = circle_center.x - closest_x;
        let dy = circle_center.y - closest_y;
        let distance_sq = (dx as i32 * dx as i32 + dy as i32 * dy as i32) as u32;
        let distance = (distance_sq as f32).sqrt() as u16;
        
        if distance <= radius as u16 {
            let penetration = (radius as u16 - distance) as u8;
            let collision_point = Point8::new(closest_x, closest_y);
            
            // Normal points from closest point to circle center
            let normal = if distance > 0 {
                Point8::new(
                    (dx * 127) / distance as i8,
                    (dy * 127) / distance as i8,
                )
            } else {
                Point8::new(0, -127) // Default normal if circle center is on rectangle
            };
            
            CollisionResult::new_hit(collision_point, penetration, normal, distance)
        } else {
            CollisionResult::NO_COLLISION
        }
    }
    
    /// Check if two AABB rectangles intersect
    fn aabb_intersects(rect1: &Rect8, rect2: &Rect8) -> bool {
        rect1.left() < rect2.right() &&
        rect1.right() > rect2.left() &&
        rect1.top() < rect2.bottom() &&
        rect1.bottom() > rect2.top()
    }
    
    /// Detailed rectangle collision with penetration and normal calculation
    fn detailed_rect_collision(rect1: &Rect8, rect2: &Rect8) -> CollisionResult {
        // Calculate overlap amounts
        let overlap_x = (rect1.right().min(rect2.right()) - rect1.left().max(rect2.left())).max(0);
        let overlap_y = (rect1.bottom().min(rect2.bottom()) - rect1.top().max(rect2.top())).max(0);
        
        if overlap_x <= 0 || overlap_y <= 0 {
            return CollisionResult::NO_COLLISION;
        }
        
        // Determine collision normal based on smallest overlap
        let (normal, penetration) = if overlap_x < overlap_y {
            // Horizontal separation
            let normal_x = if rect1.left() + rect1.size.width / 2 < rect2.left() + rect2.size.width / 2 {
                -127 // rect1 is to the left
            } else {
                127  // rect1 is to the right
            };
            (Point8::new(normal_x, 0), overlap_x as u8)
        } else {
            // Vertical separation
            let normal_y = if rect1.top() + rect1.size.height / 2 < rect2.top() + rect2.size.height / 2 {
                -127 // rect1 is above
            } else {
                127  // rect1 is below
            };
            (Point8::new(0, normal_y), overlap_y as u8)
        };
        
        // Collision point is center of overlap area
        let collision_point = Point8::new(
            rect1.left().max(rect2.left()) + overlap_x / 2,
            rect1.top().max(rect2.top()) + overlap_y / 2,
        );
        
        // Calculate distance between centers
        let center1 = Point8::new(
            rect1.origin.x + rect1.size.width / 2,
            rect1.origin.y + rect1.size.height / 2,
        );
        let center2 = Point8::new(
            rect2.origin.x + rect2.size.width / 2,
            rect2.origin.y + rect2.size.height / 2,
        );
        
        let dx = center2.x - center1.x;
        let dy = center2.y - center1.y;
        let distance = ((dx as i32 * dx as i32 + dy as i32 * dy as i32) as f32).sqrt() as u16;
        
        CollisionResult::new_hit(collision_point, penetration, normal, distance)
    }
}

/// High-level collision checking functions matching C99 interface
pub struct SF2CollisionChecker;

impl SF2CollisionChecker {
    /// Check hitbox overlap (equivalent to C99 check_hitbox_overlap)
    pub fn check_hitbox_overlap(
        attacker_hitbox: &ActiveHitbox,
        attacker_pos: Point16,
        defender_hurtbox: &Hurtbox,
        defender_pos: Point16,
        config: &CollisionConfig,
    ) -> CollisionResult {
        if !config.collision_enabled {
            return CollisionResult::NO_COLLISION;
        }
        
        let precision = CollisionPrecision::from(config.collision_precision);
        
        // Handle special shove values (negative means special collision behavior)
        if attacker_hitbox.shove < 0 {
            return Self::special_collision_check(
                attacker_hitbox,
                attacker_pos,
                defender_hurtbox,
                defender_pos,
                -attacker_hitbox.shove as u8,
                precision,
            );
        }
        
        // Standard rectangle collision
        CollisionDetector::rect_rect_collision(
            &attacker_hitbox.shape,
            attacker_pos,
            &defender_hurtbox.shape,
            defender_pos,
            precision,
        )
    }
    
    /// Check pushbox overlap for character collision
    pub fn check_pushbox_overlap(
        pushbox1: &Pushbox,
        pos1: Point16,
        pushbox2: &Pushbox,
        pos2: Point16,
        config: &CollisionConfig,
    ) -> CollisionResult {
        if !config.collision_enabled {
            return CollisionResult::NO_COLLISION;
        }
        
        let precision = CollisionPrecision::from(config.collision_precision);
        
        CollisionDetector::rect_rect_collision(
            &pushbox1.shape,
            pos1,
            &pushbox2.shape,
            pos2,
            precision,
        )
    }
    
    /// Special collision check for negative shove values
    fn special_collision_check(
        attacker_hitbox: &ActiveHitbox,
        attacker_pos: Point16,
        defender_hurtbox: &Hurtbox,
        defender_pos: Point16,
        special_value: u8,
        precision: CollisionPrecision,
    ) -> CollisionResult {
        // Special collision behavior based on the original C99 sub_7e094 function
        // This handles special cases like throws, grabs, and environmental interactions
        
        match special_value {
            1..=5 => {
                // Extended collision check with modified hitbox
                let mut modified_hitbox = attacker_hitbox.shape;
                modified_hitbox.x += special_value as i8;
                
                CollisionDetector::rect_rect_collision(
                    &modified_hitbox,
                    attacker_pos,
                    &defender_hurtbox.shape,
                    defender_pos,
                    precision,
                )
            }
            _ => {
                // Default to standard collision
                CollisionDetector::rect_rect_collision(
                    &attacker_hitbox.shape,
                    attacker_pos,
                    &defender_hurtbox.shape,
                    defender_pos,
                    precision,
                )
            }
        }
    }
}
