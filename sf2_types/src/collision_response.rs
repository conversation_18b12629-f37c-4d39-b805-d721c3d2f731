//! # Collision Response System
//!
//! Handles collision response for attacks, blocks, environmental collisions,
//! and state transitions in the Street Fighter II engine.

use crate::collision_detection::CollisionResult;
use crate::collision_shapes::*;
use crate::hitbox_manager::*;
use crate::fighter_state::{FighterState as SF2FighterState, FighterSubState};
use crate::geometry::*;
use crate::fixed_point::*;
use bevy::prelude::*;
use serde::{Deserialize, Serialize};

/// Types of collision responses
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum CollisionResponseType {
    /// Attack hit successfully
    Hit,
    /// Attack was blocked
    Block,
    /// Characters pushed apart (pushbox collision)
    Push,
    /// Environmental collision (walls, floor)
    Environment,
    /// Projectile collision
    Projectile,
    /// Throw/grab collision
    Throw,
}

/// Collision response data
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct CollisionResponse {
    /// Type of collision response
    pub response_type: CollisionResponseType,
    /// Entities involved in collision
    pub entities: (Entity, Entity),
    /// Collision result data
    pub collision_data: CollisionResult,
    /// Damage dealt (if any)
    pub damage: u16,
    /// Stun/hitstun frames
    pub stun_frames: u16,
    /// Knockback force
    pub knockback: Vect16,
    /// Sound effect to play
    pub sound_effect: Option<u8>,
    /// Visual effect to spawn
    pub visual_effect: Option<VisualEffect>,
    /// State transitions to apply
    pub state_transitions: Vec<StateTransition>,
}

/// Visual effect data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VisualEffect {
    /// Effect type ID
    pub effect_id: u16,
    /// Position to spawn effect
    pub position: Point16,
    /// Duration in frames
    pub duration: u16,
    /// Scale multiplier
    pub scale: f32,
}

/// State transition to apply after collision
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateTransition {
    /// Entity to apply transition to
    pub entity: Entity,
    /// New fighter state
    pub new_state: SF2FighterState,
    /// New sub-state
    pub new_sub_state: Option<FighterSubState>,
    /// Frame data to set
    pub frame_data: Option<u16>,
}

/// Collision response processor
#[derive(Debug, Resource)]
pub struct CollisionResponseProcessor {
    /// Pending collision responses
    pub pending_responses: Vec<CollisionResponse>,
    /// Response configuration
    pub config: ResponseConfig,
}

/// Configuration for collision responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseConfig {
    /// Damage scaling factor
    pub damage_scaling: f32,
    /// Stun scaling factor
    pub stun_scaling: f32,
    /// Knockback scaling factor
    pub knockback_scaling: f32,
    /// Enable hit sparks
    pub enable_hit_sparks: bool,
    /// Enable screen shake
    pub enable_screen_shake: bool,
    /// Enable hit freeze frames
    pub enable_hit_freeze: bool,
}

impl Default for ResponseConfig {
    fn default() -> Self {
        Self {
            damage_scaling: 1.0,
            stun_scaling: 1.0,
            knockback_scaling: 1.0,
            enable_hit_sparks: true,
            enable_screen_shake: true,
            enable_hit_freeze: true,
        }
    }
}

impl CollisionResponseProcessor {
    /// Create a new collision response processor
    pub fn new() -> Self {
        Self {
            pending_responses: Vec::new(),
            config: ResponseConfig::default(),
        }
    }
    
    /// Process a hit collision
    pub fn process_hit_collision(
        &mut self,
        attacker: Entity,
        defender: Entity,
        attacker_hitbox: &ActiveHitbox,
        defender_hurtbox: &Hurtbox,
        collision_result: CollisionResult,
    ) {
        let base_damage = attacker_hitbox.damage as u16;
        let vulnerability = defender_hurtbox.vulnerability.to_f32();
        let final_damage = (base_damage as f32 * vulnerability * self.config.damage_scaling) as u16;
        
        // Calculate stun frames based on attack strength
        let base_stun = match attacker_hitbox.strength {
            0 => 8,  // Light attack
            1 => 12, // Medium attack
            2 => 16, // Heavy attack
            _ => 10, // Default
        };
        let stun_frames = (base_stun as f32 * self.config.stun_scaling) as u16;
        
        // Calculate knockback
        let knockback_x = attacker_hitbox.shove as i16 * 4; // Scale shove value
        let knockback_y = match attacker_hitbox.attack_type {
            1 => -8, // Sweep attacks knock down
            2 => -4, // Jumping attacks have slight upward knockback
            _ => 0,  // Normal attacks
        };
        let knockback = Vect16::new(
            Fixed8_8::from_i16(knockback_x),
            Fixed8_8::from_i16(knockback_y),
        );
        
        // Determine visual effect
        let visual_effect = if self.config.enable_hit_sparks {
            Some(VisualEffect {
                effect_id: match attacker_hitbox.strength {
                    0 => 1, // Light hit spark
                    1 => 2, // Medium hit spark
                    2 => 3, // Heavy hit spark
                    _ => 1,
                },
                position: Point16::new(
                    collision_result.collision_point.x as i16,
                    collision_result.collision_point.y as i16,
                ),
                duration: 6,
                scale: 1.0,
            })
        } else {
            None
        };
        
        // Determine state transitions
        let mut state_transitions = Vec::new();
        
        // Defender enters hitstun
        state_transitions.push(StateTransition {
            entity: defender,
            new_state: SF2FighterState::Normal,
            new_sub_state: Some(FighterSubState::Stun1),
            frame_data: Some(stun_frames),
        });

        // Attacker may enter hit recovery
        if attacker_hitbox.attack_type != 4 { // Priority attacks don't have recovery
            state_transitions.push(StateTransition {
                entity: attacker,
                new_state: SF2FighterState::Normal,
                new_sub_state: Some(FighterSubState::Normal),
                frame_data: Some(4), // Brief recovery frames
            });
        }
        
        let response = CollisionResponse {
            response_type: CollisionResponseType::Hit,
            entities: (attacker, defender),
            collision_data: collision_result,
            damage: final_damage,
            stun_frames,
            knockback: Vect16::new(
                knockback.x * Fixed8_8::from_f32(self.config.knockback_scaling),
                knockback.y * Fixed8_8::from_f32(self.config.knockback_scaling),
            ),
            sound_effect: Some(attacker_hitbox.sound),
            visual_effect,
            state_transitions,
        };
        
        self.pending_responses.push(response);
    }
    
    /// Process a block collision
    pub fn process_block_collision(
        &mut self,
        attacker: Entity,
        defender: Entity,
        attacker_hitbox: &ActiveHitbox,
        collision_result: CollisionResult,
    ) {
        // Blocked attacks deal chip damage
        let chip_damage = (attacker_hitbox.damage as f32 * 0.1) as u16;
        
        // Block stun is less than hit stun
        let block_stun = match attacker_hitbox.strength {
            0 => 4,  // Light attack
            1 => 6,  // Medium attack
            2 => 8,  // Heavy attack
            _ => 5,  // Default
        };
        
        // Minimal knockback on block
        let knockback = Vect16::new(
            Fixed8_8::from_i16(attacker_hitbox.shove as i16),
            Fixed8_8::ZERO,
        );
        
        // Block spark effect
        let visual_effect = if self.config.enable_hit_sparks {
            Some(VisualEffect {
                effect_id: 10, // Block spark
                position: Point16::new(
                    collision_result.collision_point.x as i16,
                    collision_result.collision_point.y as i16,
                ),
                duration: 4,
                scale: 0.8,
            })
        } else {
            None
        };
        
        // State transitions
        let mut state_transitions = Vec::new();
        
        // Defender enters block stun
        state_transitions.push(StateTransition {
            entity: defender,
            new_state: SF2FighterState::Normal,
            new_sub_state: Some(FighterSubState::StandBlockFreeze),
            frame_data: Some(block_stun),
        });

        // Attacker enters recovery
        state_transitions.push(StateTransition {
            entity: attacker,
            new_state: SF2FighterState::Normal,
            new_sub_state: Some(FighterSubState::Normal),
            frame_data: Some(6), // Longer recovery on block
        });
        
        let response = CollisionResponse {
            response_type: CollisionResponseType::Block,
            entities: (attacker, defender),
            collision_data: collision_result,
            damage: chip_damage,
            stun_frames: block_stun,
            knockback: Vect16::new(
                knockback.x * Fixed8_8::from_f32(self.config.knockback_scaling),
                knockback.y * Fixed8_8::from_f32(self.config.knockback_scaling),
            ),
            sound_effect: Some(20), // Block sound
            visual_effect,
            state_transitions,
        };
        
        self.pending_responses.push(response);
    }
    
    /// Process a pushbox collision
    pub fn process_pushbox_collision(
        &mut self,
        entity1: Entity,
        entity2: Entity,
        collision_result: CollisionResult,
    ) {
        // Calculate separation force
        let separation_force = collision_result.penetration as i16 / 2;
        let normal_x = if collision_result.normal.x > 0 { 1 } else { -1 };
        
        let knockback1 = Vect16::new(
            Fixed8_8::from_i16(-separation_force * normal_x),
            Fixed8_8::ZERO,
        );
        let knockback2 = Vect16::new(
            Fixed8_8::from_i16(separation_force * normal_x),
            Fixed8_8::ZERO,
        );
        
        // Create responses for both entities
        let response1 = CollisionResponse {
            response_type: CollisionResponseType::Push,
            entities: (entity1, entity2),
            collision_data: collision_result,
            damage: 0,
            stun_frames: 0,
            knockback: knockback1,
            sound_effect: None,
            visual_effect: None,
            state_transitions: Vec::new(),
        };
        
        let response2 = CollisionResponse {
            response_type: CollisionResponseType::Push,
            entities: (entity2, entity1),
            collision_data: collision_result,
            damage: 0,
            stun_frames: 0,
            knockback: knockback2,
            sound_effect: None,
            visual_effect: None,
            state_transitions: Vec::new(),
        };
        
        self.pending_responses.push(response1);
        self.pending_responses.push(response2);
    }
    
    /// Process an environmental collision
    pub fn process_environment_collision(
        &mut self,
        entity: Entity,
        collision_result: CollisionResult,
        surface_type: EnvironmentSurface,
    ) {
        let knockback = match surface_type {
            EnvironmentSurface::Wall => {
                // Stop horizontal movement
                Vect16::new(Fixed8_8::ZERO, Fixed8_8::ZERO)
            }
            EnvironmentSurface::Floor => {
                // Stop vertical movement, land on ground
                Vect16::new(Fixed8_8::ZERO, Fixed8_8::ZERO)
            }
            EnvironmentSurface::Ceiling => {
                // Bounce off ceiling
                Vect16::new(Fixed8_8::ZERO, Fixed8_8::from_i16(4))
            }
        };
        
        let state_transition = match surface_type {
            EnvironmentSurface::Floor => Some(StateTransition {
                entity,
                new_state: SF2FighterState::Normal,
                new_sub_state: Some(FighterSubState::Stand),
                frame_data: None,
            }),
            _ => None,
        };
        
        let response = CollisionResponse {
            response_type: CollisionResponseType::Environment,
            entities: (entity, Entity::PLACEHOLDER), // No second entity for environment
            collision_data: collision_result,
            damage: 0,
            stun_frames: 0,
            knockback,
            sound_effect: None,
            visual_effect: None,
            state_transitions: state_transition.into_iter().collect(),
        };
        
        self.pending_responses.push(response);
    }
    
    /// Get and clear pending responses
    pub fn take_pending_responses(&mut self) -> Vec<CollisionResponse> {
        std::mem::take(&mut self.pending_responses)
    }
    
    /// Update configuration
    pub fn set_config(&mut self, config: ResponseConfig) {
        self.config = config;
    }
}

/// Environment surface types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EnvironmentSurface {
    Wall,
    Floor,
    Ceiling,
}

impl Default for CollisionResponseProcessor {
    fn default() -> Self {
        Self::new()
    }
}
