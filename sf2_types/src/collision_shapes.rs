//! # Collision Shapes
//!
//! Collision shape definitions and operations for the Street Fighter II engine.
//! Provides rectangle and circle collision shapes matching the original C99 implementation.

use crate::geometry::*;
use crate::fixed_point::*;
use crate::collision_config::CollisionConfig;
use serde::{Deserialize, Serialize};
use bytemuck::{Pod, Zeroable};

/// Collision shape types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CollisionShapeType {
    Rectangle,
    Circle,
}

/// Rectangle collision shape (equivalent to C99 HitBox)
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct CollisionRect {
    /// X position offset from entity center
    pub x: i8,
    /// Y position offset from entity center  
    pub y: i8,
    /// Width of the collision box
    pub width: i8,
    /// Height of the collision box
    pub height: i8,
}

unsafe impl Pod for CollisionRect {}
unsafe impl Zeroable for CollisionRect {}

impl CollisionRect {
    pub const EMPTY: Self = Self { x: 0, y: 0, width: 0, height: 0 };
    
    pub fn new(x: i8, y: i8, width: i8, height: i8) -> Self {
        Self { x, y, width, height }
    }
    
    /// Check if this collision rect is empty (zero area)
    pub fn is_empty(&self) -> bool {
        self.width <= 0 || self.height <= 0
    }
    
    /// Get the left edge relative to entity position
    pub fn left(&self) -> i8 {
        self.x
    }
    
    /// Get the right edge relative to entity position
    pub fn right(&self) -> i8 {
        self.x + self.width
    }
    
    /// Get the top edge relative to entity position
    pub fn top(&self) -> i8 {
        self.y
    }
    
    /// Get the bottom edge relative to entity position
    pub fn bottom(&self) -> i8 {
        self.y + self.height
    }
    
    /// Convert to absolute coordinates given entity position
    pub fn to_absolute(&self, entity_x: i16, entity_y: i16) -> Rect8 {
        Rect8::new(
            entity_x as i8 + self.x,
            entity_y as i8 + self.y,
            self.width,
            self.height,
        )
    }
    
    /// Apply collision configuration adjustments
    pub fn apply_config(&mut self, config: &CollisionConfig, fighter_id: u8, is_hitbox: bool) {
        if is_hitbox {
            config.apply_hitbox_config(fighter_id, &mut self.width, &mut self.height, &mut self.x, &mut self.y);
        } else {
            config.apply_hurtbox_config(fighter_id, &mut self.width, &mut self.height, &mut self.x, &mut self.y);
        }
    }
}

/// Circle collision shape for special attacks and projectiles
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct CollisionCircle {
    /// X position offset from entity center
    pub x: i8,
    /// Y position offset from entity center
    pub y: i8,
    /// Radius of the collision circle
    pub radius: u8,
    /// Padding for alignment
    pub _padding: u8,
}

unsafe impl Pod for CollisionCircle {}
unsafe impl Zeroable for CollisionCircle {}

impl CollisionCircle {
    pub const EMPTY: Self = Self { x: 0, y: 0, radius: 0, _padding: 0 };
    
    pub fn new(x: i8, y: i8, radius: u8) -> Self {
        Self { x, y, radius, _padding: 0 }
    }
    
    /// Check if this collision circle is empty (zero radius)
    pub fn is_empty(&self) -> bool {
        self.radius == 0
    }
    
    /// Get center point relative to entity position
    pub fn center(&self) -> Point8 {
        Point8::new(self.x, self.y)
    }
    
    /// Convert to absolute coordinates given entity position
    pub fn to_absolute(&self, entity_x: i16, entity_y: i16) -> (Point8, u8) {
        (
            Point8::new(entity_x as i8 + self.x, entity_y as i8 + self.y),
            self.radius,
        )
    }
    
    /// Apply collision configuration adjustments
    pub fn apply_config(&mut self, config: &CollisionConfig, fighter_id: u8, is_hitbox: bool) {
        let multiplier = if is_hitbox {
            config.hitbox_size_multiplier * config.get_fighter_hitbox_multiplier(fighter_id)
        } else {
            config.hurtbox_size_multiplier * config.get_fighter_hurtbox_multiplier(fighter_id)
        };
        
        // Apply size multiplier to radius
        self.radius = (self.radius as f32 * multiplier) as u8;
        
        // Apply position offsets
        if is_hitbox {
            self.x = self.x.saturating_add(config.hitbox_offset_x as i8);
            self.y = self.y.saturating_add(config.hitbox_offset_y as i8);
        } else {
            self.x = self.x.saturating_add(config.hurtbox_offset_x as i8);
            self.y = self.y.saturating_add(config.hurtbox_offset_y as i8);
        }
    }
}

/// Generic collision shape that can be either rectangle or circle
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum CollisionShape {
    Rectangle(CollisionRect),
    Circle(CollisionCircle),
}

impl CollisionShape {
    /// Check if this collision shape is empty
    pub fn is_empty(&self) -> bool {
        match self {
            CollisionShape::Rectangle(rect) => rect.is_empty(),
            CollisionShape::Circle(circle) => circle.is_empty(),
        }
    }
    
    /// Get the shape type
    pub fn shape_type(&self) -> CollisionShapeType {
        match self {
            CollisionShape::Rectangle(_) => CollisionShapeType::Rectangle,
            CollisionShape::Circle(_) => CollisionShapeType::Circle,
        }
    }
    
    /// Apply collision configuration adjustments
    pub fn apply_config(&mut self, config: &CollisionConfig, fighter_id: u8, is_hitbox: bool) {
        match self {
            CollisionShape::Rectangle(rect) => rect.apply_config(config, fighter_id, is_hitbox),
            CollisionShape::Circle(circle) => circle.apply_config(config, fighter_id, is_hitbox),
        }
    }
}

/// Active hitbox data (equivalent to C99 HitBoxAct)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct ActiveHitbox {
    /// Collision shape
    pub shape: CollisionRect, // Most hitboxes are rectangular
    /// Damage value
    pub damage: u8,
    /// Shove/knockback value (negative for special behavior)
    pub shove: i8,
    /// Sound effect ID
    pub sound: u8,
    /// Attack type (0=normal, 1=sweep, 2=jumping, 3=special, 4=priority)
    pub attack_type: u8,
    /// Attack strength (0=light, 1=medium, 2=hard)
    pub strength: u8,
    /// Random value 1 (for damage variation)
    pub random1: u8,
    /// Random value 2 (for damage variation)
    pub random2: u8,
    /// React mode for hit reactions
    pub react_mode: u8,
}

unsafe impl Pod for ActiveHitbox {}
unsafe impl Zeroable for ActiveHitbox {}

impl ActiveHitbox {
    pub const EMPTY: Self = Self {
        shape: CollisionRect::EMPTY,
        damage: 0,
        shove: 0,
        sound: 0,
        attack_type: 0,
        strength: 0,
        random1: 0,
        random2: 0,
        react_mode: 0,
    };
    
    pub fn new(
        shape: CollisionRect,
        damage: u8,
        shove: i8,
        sound: u8,
        attack_type: u8,
        strength: u8,
        react_mode: u8,
    ) -> Self {
        Self {
            shape,
            damage,
            shove,
            sound,
            attack_type,
            strength,
            random1: 0,
            random2: 0,
            react_mode,
        }
    }
    
    /// Check if this hitbox is active (has damage)
    pub fn is_active(&self) -> bool {
        self.damage > 0 && !self.shape.is_empty()
    }
    
    /// Apply collision configuration to this hitbox
    pub fn apply_config(&mut self, config: &CollisionConfig, fighter_id: u8) {
        // Apply shape configuration
        self.shape.apply_config(config, fighter_id, true);
        
        // Apply damage multiplier
        let damage_multiplier = config.hitbox_damage_multiplier;
        self.damage = (self.damage as f32 * damage_multiplier) as u8;
    }
}

/// Hurtbox data for taking damage
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
#[repr(C)]
pub struct Hurtbox {
    /// Collision shape
    pub shape: CollisionRect,
    /// Vulnerability multiplier (1.0 = normal, >1.0 = more vulnerable)
    pub vulnerability: Fixed8_8,
}

unsafe impl Pod for Hurtbox {}
unsafe impl Zeroable for Hurtbox {}

impl Hurtbox {
    pub const EMPTY: Self = Self {
        shape: CollisionRect::EMPTY,
        vulnerability: Fixed8_8::ONE,
    };
    
    pub fn new(shape: CollisionRect, vulnerability: Fixed8_8) -> Self {
        Self { shape, vulnerability }
    }
    
    /// Check if this hurtbox is active
    pub fn is_active(&self) -> bool {
        !self.shape.is_empty()
    }
    
    /// Apply collision configuration to this hurtbox
    pub fn apply_config(&mut self, config: &CollisionConfig, fighter_id: u8) {
        // Apply shape configuration
        self.shape.apply_config(config, fighter_id, false);
        
        // Apply vulnerability multiplier
        let vulnerability_multiplier = Fixed8_8::from_f32(config.hurtbox_vulnerability_multiplier);
        self.vulnerability = self.vulnerability * vulnerability_multiplier;
    }
}

/// Pushbox data for character collision
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct Pushbox {
    /// Collision shape
    pub shape: CollisionRect,
}

unsafe impl Pod for Pushbox {}
unsafe impl Zeroable for Pushbox {}

impl Pushbox {
    pub const EMPTY: Self = Self {
        shape: CollisionRect::EMPTY,
    };
    
    pub fn new(shape: CollisionRect) -> Self {
        Self { shape }
    }
    
    /// Check if this pushbox is active
    pub fn is_active(&self) -> bool {
        !self.shape.is_empty()
    }
    
    /// Apply collision configuration to this pushbox
    pub fn apply_config(&mut self, config: &CollisionConfig) {
        config.apply_pushbox_config(
            &mut self.shape.width,
            &mut self.shape.height,
            &mut self.shape.x,
            &mut self.shape.y,
        );
    }
}
