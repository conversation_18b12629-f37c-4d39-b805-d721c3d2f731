//! # SF2 Constants
//! 
//! Core constants and magic numbers from the original Street Fighter II engine.
//! These values match the C99 sf2const.h definitions.

/// Player user data size (equivalent to PLAYER_USER_DATA_SIZE)
pub const PLAYER_USER_DATA_SIZE: usize = 0x80;

/// Image attribute flag (equivalent to IMAGE_ATTR)
pub const IMAGE_ATTR: u16 = 0x8000;

/// Game timing constants
pub const TICKS_PER_SECOND: u32 = 60;
pub const SF2_GAME_TICKS: u32 = 40;

/// Screen dimensions
pub const SCREEN_WIDTH: u32 = 384;
pub const SCREEN_HEIGHT: u32 = 256;
pub const VISIBLE_SCREEN_HEIGHT: u32 = 224; // According to MAME

/// Stage and boundary constants
pub const VISIBLE_MARGIN: u32 = 64;
pub const STAGE_LENGTH: u32 = 640; // 0x280

/// Boolean constants (for C99 compatibility)
pub const TRUE: i32 = 1;
pub const FALSE: i32 = 0;

/// Game balance constants
pub const ENERGY_START: u16 = 0x90;
pub const VS_SCREEN_DELAY: u32 = 180; // ticks

/// Direction constants (equivalent to FBDirection enum)
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
#[repr(u8)]
pub enum FbDirection {
    FacingLeft = 0,  // FALSE: not flipped
    FacingRight = 1, // TRUE: flipped
}

impl FbDirection {
    pub fn opposite(self) -> Self {
        match self {
            FbDirection::FacingLeft => FbDirection::FacingRight,
            FbDirection::FacingRight => FbDirection::FacingLeft,
        }
    }
    
    pub fn to_bool(self) -> bool {
        match self {
            FbDirection::FacingLeft => false,
            FbDirection::FacingRight => true,
        }
    }
    
    pub fn from_bool(facing_right: bool) -> Self {
        if facing_right {
            FbDirection::FacingRight
        } else {
            FbDirection::FacingLeft
        }
    }
    
    /// Returns -1 if facing left, 1 if facing right (equivalent to FLIP macro)
    pub fn flip_multiplier(self) -> i32 {
        match self {
            FbDirection::FacingLeft => -1,
            FbDirection::FacingRight => 1,
        }
    }
}

/// Physics and movement constants
pub mod physics {
    use crate::fixed_point::*;
    
    /// Gravity acceleration (typical value)
    pub const GRAVITY: Fixed8_8 = Fixed8_8::from_raw(0x0080); // 0.5 in 8.8 fixed point
    
    /// Ground level Y coordinate
    pub const GROUND_Y: Fixed16_16 = Fixed16_16::from_raw(0x00E00000); // 224 in 16.16 fixed point
    
    /// Maximum fall velocity
    pub const MAX_FALL_VELOCITY: Fixed8_8 = Fixed8_8::from_raw(0x0800); // 8.0 in 8.8 fixed point
    
    /// Jump initial velocity (typical)
    pub const JUMP_VELOCITY: Fixed8_8 = Fixed8_8::from_raw(-0x0600); // -6.0 in 8.8 fixed point
    
    /// Walk speed (typical)
    pub const WALK_SPEED: Fixed8_8 = Fixed8_8::from_raw(0x0200); // 2.0 in 8.8 fixed point
}

/// Input and control constants
pub mod input {
    /// Joystick direction bits
    pub const JOY_UP: u8 = 0x01;
    pub const JOY_DOWN: u8 = 0x02;
    pub const JOY_LEFT: u8 = 0x04;
    pub const JOY_RIGHT: u8 = 0x08;
    
    /// Button bits
    pub const BTN_LIGHT_PUNCH: u8 = 0x10;
    pub const BTN_MEDIUM_PUNCH: u8 = 0x20;
    pub const BTN_HEAVY_PUNCH: u8 = 0x40;
    pub const BTN_LIGHT_KICK: u8 = 0x01;
    pub const BTN_MEDIUM_KICK: u8 = 0x02;
    pub const BTN_HEAVY_KICK: u8 = 0x04;
    
    /// Special move input timing
    pub const SPECIAL_MOVE_WINDOW: u32 = 20; // frames
    pub const CHARGE_TIME_REQUIRED: u32 = 45; // frames

    /// Input buffer configuration
    pub const INPUT_BUFFER_SIZE: usize = 16;

    /// Input lag and timing
    pub const MAX_INPUT_LAG_COMPENSATION: u32 = 10;
    pub const DEFAULT_INPUT_LAG: u32 = 0;

    /// Button and joystick sensitivity
    pub const MIN_BUTTON_SENSITIVITY: f32 = 0.1;
    pub const MAX_BUTTON_SENSITIVITY: f32 = 2.0;
    pub const DEFAULT_BUTTON_SENSITIVITY: f32 = 1.0;

    pub const MIN_JOYSTICK_DEADZONE: f32 = 0.0;
    pub const MAX_JOYSTICK_DEADZONE: f32 = 0.5;
    pub const DEFAULT_JOYSTICK_DEADZONE: f32 = 0.1;

    /// Input validation ranges
    pub const MIN_SPECIAL_MOVE_WINDOW: u32 = 5;
    pub const MAX_SPECIAL_MOVE_WINDOW: u32 = 60;

    pub const MIN_CHARGE_TIME: u32 = 15;
    pub const MAX_CHARGE_TIME: u32 = 120;

    pub const MIN_INPUT_BUFFER_SIZE: usize = 8;
    pub const MAX_INPUT_BUFFER_SIZE: usize = 64;
}

/// Audio constants
pub mod audio {
    /// Sample rates and formats
    pub const SAMPLE_RATE: u32 = 22050;
    pub const CHANNELS: u8 = 2; // Stereo
    pub const BITS_PER_SAMPLE: u8 = 16;
    
    /// Volume levels (0-255)
    pub const MAX_VOLUME: u8 = 255;
    pub const DEFAULT_VOLUME: u8 = 192;
    pub const MUTE_VOLUME: u8 = 0;
}

/// Graphics and rendering constants
pub mod graphics {
    /// Palette constants
    pub const PALETTE_SIZE: usize = 16; // Colors per palette
    pub const MAX_PALETTES: usize = 32;
    pub const PALETTE_ENTRY_SIZE: usize = 2; // bytes per color
    
    /// Sprite constants
    pub const MAX_SPRITES_PER_FRAME: usize = 64;
    pub const SPRITE_TILE_SIZE: usize = 16; // 16x16 pixels
    
    /// Layer priorities
    pub const BACKGROUND_PRIORITY: u8 = 0;
    pub const FOREGROUND_PRIORITY: u8 = 1;
    pub const SPRITE_PRIORITY: u8 = 2;
    pub const UI_PRIORITY: u8 = 3;
}

/// Memory layout constants
pub mod memory {
    /// ROM layout
    pub const ROM_SIZE: usize = 0x400000; // 4MB
    pub const SPRITE_DATA_OFFSET: usize = 0x200000;
    pub const AUDIO_DATA_OFFSET: usize = 0x300000;
    
    /// RAM layout
    pub const WORK_RAM_SIZE: usize = 0x10000; // 64KB
    pub const VIDEO_RAM_SIZE: usize = 0x8000;  // 32KB
    pub const PALETTE_RAM_SIZE: usize = 0x1000; // 4KB
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_direction() {
        let left = FbDirection::FacingLeft;
        let right = FbDirection::FacingRight;
        
        assert_eq!(left.opposite(), right);
        assert_eq!(right.opposite(), left);
        
        assert_eq!(left.flip_multiplier(), -1);
        assert_eq!(right.flip_multiplier(), 1);
        
        assert!(!left.to_bool());
        assert!(right.to_bool());
    }
    
    #[test]
    fn test_constants() {
        assert_eq!(SCREEN_WIDTH, 384);
        assert_eq!(SCREEN_HEIGHT, 256);
        assert_eq!(TICKS_PER_SECOND, 60);
        assert_eq!(TRUE, 1);
        assert_eq!(FALSE, 0);
    }
    
    #[test]
    fn test_physics_constants() {
        assert!(physics::GRAVITY.to_f32() > 0.0);
        assert!(physics::JUMP_VELOCITY.to_f32() < 0.0);
        assert!(physics::WALK_SPEED.to_f32() > 0.0);
    }
}
