//! # Endianness Handling
//! 
//! Utilities for handling endianness conversion between M68k (big-endian)
//! ROM data and modern little-endian systems.

use bytemuck::{Pod, Zeroable};

/// Trait for types that can be converted between endianness
pub trait EndianConvert: Sized {
    /// Convert from big-endian (M68k) to native endianness
    fn from_be(self) -> Self;
    /// Convert from native endianness to big-endian (M68k)
    fn to_be(self) -> Self;
}

impl EndianConvert for u16 {
    fn from_be(self) -> Self {
        u16::from_be(self)
    }
    
    fn to_be(self) -> Self {
        u16::to_be(self)
    }
}

impl EndianConvert for u32 {
    fn from_be(self) -> Self {
        u32::from_be(self)
    }
    
    fn to_be(self) -> Self {
        u32::to_be(self)
    }
}

impl EndianConvert for i16 {
    fn from_be(self) -> Self {
        i16::from_be(self)
    }
    
    fn to_be(self) -> Self {
        i16::to_be(self)
    }
}

impl EndianConvert for i32 {
    fn from_be(self) -> Self {
        i32::from_be(self)
    }
    
    fn to_be(self) -> Self {
        i32::to_be(self)
    }
}

/// Big-endian 16-bit word (as stored in ROM)
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[repr(transparent)]
pub struct BeU16(u16);

unsafe impl Pod for BeU16 {}
unsafe impl Zeroable for BeU16 {}

impl BeU16 {
    pub fn new(value: u16) -> Self {
        Self(value.to_be())
    }
    
    pub fn get(self) -> u16 {
        u16::from_be(self.0)
    }
    
    pub fn from_bytes(bytes: [u8; 2]) -> Self {
        Self(u16::from_be_bytes(bytes))
    }
    
    pub fn to_bytes(self) -> [u8; 2] {
        self.0.to_be_bytes()
    }
}

/// Big-endian 32-bit word (as stored in ROM)
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[repr(transparent)]
pub struct BeU32(u32);

unsafe impl Pod for BeU32 {}
unsafe impl Zeroable for BeU32 {}

impl BeU32 {
    pub fn new(value: u32) -> Self {
        Self(value.to_be())
    }
    
    pub fn get(self) -> u32 {
        u32::from_be(self.0)
    }
    
    pub fn from_bytes(bytes: [u8; 4]) -> Self {
        Self(u32::from_be_bytes(bytes))
    }
    
    pub fn to_bytes(self) -> [u8; 4] {
        self.0.to_be_bytes()
    }
}

/// Utility functions for reading ROM data
pub mod rom_reader {
    
    /// Read a big-endian u16 from a byte slice
    pub fn read_be_u16(data: &[u8], offset: usize) -> Option<u16> {
        if offset + 2 <= data.len() {
            let bytes = [data[offset], data[offset + 1]];
            Some(u16::from_be_bytes(bytes))
        } else {
            None
        }
    }
    
    /// Read a big-endian u32 from a byte slice
    pub fn read_be_u32(data: &[u8], offset: usize) -> Option<u32> {
        if offset + 4 <= data.len() {
            let bytes = [data[offset], data[offset + 1], data[offset + 2], data[offset + 3]];
            Some(u32::from_be_bytes(bytes))
        } else {
            None
        }
    }
    
    /// Read a slice of big-endian u16 values
    pub fn read_be_u16_slice(data: &[u8], offset: usize, count: usize) -> Option<Vec<u16>> {
        if offset + (count * 2) <= data.len() {
            let mut result = Vec::with_capacity(count);
            for i in 0..count {
                let word_offset = offset + (i * 2);
                let bytes = [data[word_offset], data[word_offset + 1]];
                result.push(u16::from_be_bytes(bytes));
            }
            Some(result)
        } else {
            None
        }
    }
    
    /// Read a null-terminated string from ROM data
    pub fn read_cstring(data: &[u8], offset: usize, max_len: usize) -> Option<String> {
        if offset >= data.len() {
            return None;
        }
        
        let end_offset = (offset + max_len).min(data.len());
        let slice = &data[offset..end_offset];
        
        // Find null terminator
        let null_pos = slice.iter().position(|&b| b == 0).unwrap_or(slice.len());
        
        // Convert to string, handling potential encoding issues
        String::from_utf8(slice[..null_pos].to_vec()).ok()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_be_u16_conversion() {
        let value = 0x1234u16;
        let be_value = BeU16::new(value);
        assert_eq!(be_value.get(), value);
    }

    #[test]
    fn test_rom_reader() {
        let data = [0x12, 0x34, 0x56, 0x78];

        assert_eq!(rom_reader::read_be_u16(&data, 0), Some(0x1234));
        assert_eq!(rom_reader::read_be_u16(&data, 2), Some(0x5678));
        assert_eq!(rom_reader::read_be_u32(&data, 0), Some(0x12345678));
    }
}
