//! # Fighter Types
//!
//! Data types specific to fighter characters and combat mechanics.
//! These types match the original C99 player.h and related structures.

use crate::fixed_point::*;
use crate::geometry::*;
use crate::constants::*;
use bytemuck::{Pod, Zeroable};
#[allow(unused_imports)]
use serde::{Deserialize, Serialize};

/// Fighter character identifiers
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum FighterId {
    Ryu = 0,
    EHonda = 1,
    Blanka = 2,
    Guile = 3,
    <PERSON> = 4,
    <PERSON><PERSON><PERSON> = 5,
    <PERSON><PERSON><PERSON> = 6,
    <PERSON><PERSON>sim = 7,
    <PERSON><PERSON><PERSON> = 8,
    <PERSON> = 9,
    <PERSON>t = 10,
    <PERSON><PERSON> = 11,
}

impl FighterId {
    pub fn name(self) -> &'static str {
        match self {
            FighterId::Ryu => "Ryu",
            FighterId::EHonda => "E.Honda",
            FighterId::Blanka => "Blanka",
            FighterId::Guile => "Guile",
            FighterId::<PERSON> => "<PERSON>",
            FighterId::ChunLi => "<PERSON>-<PERSON>",
            FighterId::Zangief => "Zangief",
            FighterId::Dhalsim => "Dhalsim",
            FighterId::Balrog => "Balrog",
            FighterId::Vega => "Vega",
            FighterId::Sagat => "Sagat",
            FighterId::MBison => "M.Bison",
        }
    }
    
    pub fn country(self) -> &'static str {
        match self {
            FighterId::Ryu => "Japan",
            FighterId::EHonda => "Japan",
            FighterId::Blanka => "Brazil",
            FighterId::Guile => "USA",
            FighterId::Ken => "USA",
            FighterId::ChunLi => "China",
            FighterId::Zangief => "USSR",
            FighterId::Dhalsim => "India",
            FighterId::Balrog => "USA",
            FighterId::Vega => "Spain",
            FighterId::Sagat => "Thailand",
            FighterId::MBison => "Unknown",
        }
    }
    
    pub fn all_fighters() -> [FighterId; 12] {
        [
            FighterId::Ryu,
            FighterId::EHonda,
            FighterId::Blanka,
            FighterId::Guile,
            FighterId::Ken,
            FighterId::ChunLi,
            FighterId::Zangief,
            FighterId::Dhalsim,
            FighterId::Balrog,
            FighterId::Vega,
            FighterId::Sagat,
            FighterId::MBison,
        ]
    }
}

/// Fighter stance/state
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FighterStance {
    Standing,
    Crouching,
    Jumping,
    WalkingForward,
    WalkingBackward,
    Attacking,
    Blocking,
    Stunned,
    KnockedDown,
}

/// Direction the fighter is facing
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum Direction {
    Left,
    Right,
}

impl Direction {
    pub fn opposite(self) -> Self {
        match self {
            Direction::Left => Direction::Right,
            Direction::Right => Direction::Left,
        }
    }
    
    pub fn sign(self) -> i32 {
        match self {
            Direction::Left => -1,
            Direction::Right => 1,
        }
    }
}

/// Airborne state
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AirborneState {
    OnGround,
    Jumping,
    Falling,
    Reeling, // Hit while airborne
}

/// Attack strength levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AttackStrength {
    Light,
    Medium,
    Heavy,
}

/// Attack types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AttackType {
    Punch(AttackStrength),
    Kick(AttackStrength),
    Special(SpecialMoveId),
    Super(SuperMoveId),
}

/// Special move identifiers
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SpecialMoveId {
    // Ryu/Ken
    Hadoken,
    Shoryuken,
    TatsumakiSenpukyaku,
    
    // Chun-Li
    Kikoken,
    SpinningBirdKick,
    LightningLegs,
    
    // Guile
    SonicBoom,
    FlashKick,
    
    // Blanka
    ElectricThunder,
    RollingAttack,
    
    // E.Honda
    HundredHandSlap,
    SumoHeadbutt,
    
    // Zangief
    ScremPileDriver,
    LariatClothesline,
    
    // Dhalsim
    YogaFire,
    YogaFlame,
    
    // Balrog
    DashStraight,
    DashUpper,
    
    // Vega
    RollingCrystalFlash,
    SkyHighClaw,
    
    // Sagat
    TigerShot,
    TigerUppercut,
    
    // M.Bison
    ScissorKick,
    PsychoCrusher,
}

/// Super move identifiers (for future expansion)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SuperMoveId {
    // Placeholder for potential super moves
    Placeholder,
}





// ============================================================================
// Core Player Data Structure (equivalent to C99 player_t)
// ============================================================================

/// Player object state (equivalent to C99 player_t structure)
/// This is a massive structure containing all fighter state information
#[derive(Debug, Clone)]
#[repr(C)]
pub struct Player {
    // Physics and positioning (matching C99 layout)
    pub x: Fixed16_16,           // X position
    pub y: Fixed16_16,           // Y position
    pub vel_x: Fixed8_8,         // X velocity
    pub vel_y: Fixed8_8,         // Y velocity
    pub acl_x: Fixed8_8,         // X acceleration
    pub acl_y: Fixed8_8,         // Y acceleration

    // Fighter state
    pub fighter_id: FighterId,
    pub direction: FbDirection,
    pub stance: FighterStance,

    // Health and energy
    pub health: u16,
    pub stun: u16,
    pub super_meter: u16,

    // Animation and graphics
    pub current_frame: u16,
    pub frame_timer: u16,
    pub animation_id: u16,
    pub sprite_flags: u16,

    // Combat state
    pub hit_stun: u16,
    pub block_stun: u16,
    pub recovery_frames: u16,
    pub invincibility_frames: u16,

    // Input handling
    pub input_buffer: [u16; 16],  // Circular buffer for input history
    pub input_buffer_head: u8,
    pub charge_time_back: u8,
    pub charge_time_down: u8,

    // Collision and hitboxes
    pub collision_box: Rect8,
    pub hurt_boxes: [Rect8; 4],   // Multiple hurt boxes
    pub hit_boxes: [Rect8; 4],    // Attack hit boxes
    pub num_hurt_boxes: u8,
    pub num_hit_boxes: u8,

    // Special move state
    pub special_move_state: u16,
    pub combo_counter: u8,
    pub combo_damage: u16,

    // User data array (equivalent to C99 UserData)
    pub user_data: [u8; PLAYER_USER_DATA_SIZE],

    // Throw mechanics
    pub throw_range: u8,
    pub throw_damage: u16,
    pub being_thrown: bool,
    pub throw_tech_window: u8,

    // Projectile tracking
    pub projectile_count: u8,
    pub max_projectiles: u8,

    // AI and behavior (for CPU players) - comprehensive C99 AI system
    // Core AI state machines
    pub ai_mode1: u8,                    // AIMode1 - primary AI state
    pub ai_mode2: u8,                    // AIMode2 - secondary AI state
    pub ai_strategy: u8,                 // AIStrategy - current strategy type

    // AI parameters and configuration
    pub ai_param1: u8,                   // AIParam1 - strategy parameter 1
    pub ai_param2: i8,                   // AIParam2 - strategy parameter 2
    pub ai_param3: i8,                   // AIParam3 - strategy parameter 3
    pub ai_param4: i8,                   // AIParam4 - strategy parameter 4
    pub difficulty_level: u8,            // Difficulty - AI difficulty level (0-31)

    // AI strategy pointers and indices (using indices instead of raw pointers)
    pub ai_strat_agg0_index: u16,        // AIStratAgg0 - aggressive strategy 0 index
    pub ai_strat_agg1_index: u16,        // AIStratAgg1 - aggressive strategy 1 index
    pub ai_strat_def_index: u16,         // AIStratDef - defensive strategy index
    pub ai_strat_agg0_ptr: u16,          // Current position in Agg0 strategy
    pub ai_strat_agg1_ptr: u16,          // Current position in Agg1 strategy
    pub ai_strat_def_ptr: u16,           // Current position in Def strategy

    // AI timers and timing control
    pub ai_timer: u8,                    // AITimer - main AI timer
    pub ai_timers: [u8; 8],              // AITimers[8] - array of AI timers
    pub ai_agg_timer0: u8,               // AIAggTimer0 - aggressive timer 0
    pub ai_agg_timer1: u8,               // AIAggTimer1 - aggressive timer 1
    pub ai_timer_threat_check: u8,       // AITimerThreatCheck - threat check timer

    // AI behavioral flags and signals
    pub ai_sig_attack: bool,             // AISigAttack - signal to attack
    pub ai_sig_special: bool,            // AISigSpecial - signal for special move
    pub ai_volley: bool,                 // AIVolley - volley attack mode
    pub ai_force_defensive: bool,        // AIForceDefensive - force defensive mode
    pub ai_allow_aggressive: bool,       // AIAllowAggressive - allow aggressive mode
    pub ai_aggressive: bool,             // AIAgressive - aggressive mode flag
    pub ai_threat_found: bool,           // AIThreatFound - threat detected flag
    pub ai_can_jump_attack: bool,        // AICanJumpAttack - can perform jump attack
    pub ai_start_again: u8,              // AIStartAgain - restart strategy flag

    // AI control signals for actions
    pub comp_do_throw: bool,             // CompDoThrow - perform throw
    pub comp_do_block: bool,             // CompDoBlock - perform block
    pub comp_do_jump: bool,              // CompDoJump - perform jump
    pub comp_do_block_stun: bool,        // CompDoBlockStun - block stun state
    pub comp_immune: u8,                 // CompImmune - immunity frames

    // AI movement and positioning
    pub ai_walk_direction: u8,           // AIWalkDirection - walk direction flags
    pub ai_walk_target: i16,             // AIWalkTarget - target walk distance
    pub ai_jump_sel: i16,                // AIJumpSel - jump selection
    pub ai_too_close: i16,               // AITooClose - too close distance
    pub ai_opp_jump_height: i16,         // AIOppJumpHeight - opponent jump height

    // AI threat assessment and analysis
    pub ai_threat_check_mode: u8,        // AIThreatCheckMode - threat check mode
    pub ai_multi_count: u8,              // AIMultiCount - multi-hit counter
    pub ai_saved_state: Option<Box<[u8; 64]>>, // AISaveState - saved AI state

    // AI dice roll and randomness
    pub dice_roll_count: i8,             // DiceRollCount - dice roll counter
    pub button_strength: i8,             // ButtonStrength - attack strength
    pub punch_kick: i8,                  // PunchKick - punch/kick selection

    // AI yoke and special tracking
    pub yoke_attacking_me: u8,           // YokeAttackingMe - yoke attack tracking
    pub yoke_saved: i8,                  // YokeSaved - saved yoke state

    // AI bounds and positioning checks
    pub is_within_bounds: bool,          // IsWithinBounds - within stage bounds
    pub in_ground_attack: bool,          // InGroundAttack - in ground attack state

    // Sound and effects
    pub sound_id: u16,
    pub effect_id: u16,

    // Miscellaneous flags and counters
    pub flags: u32,              // Various boolean flags packed into bits
    pub timer: u16,              // General purpose timer
    pub counter: u16,            // General purpose counter
}

impl Default for Player {
    fn default() -> Self {
        Self {
            x: Fixed16_16::ZERO,
            y: Fixed16_16::ZERO,
            vel_x: Fixed8_8::ZERO,
            vel_y: Fixed8_8::ZERO,
            acl_x: Fixed8_8::ZERO,
            acl_y: Fixed8_8::ZERO,

            fighter_id: FighterId::Ryu,
            direction: FbDirection::FacingRight,
            stance: FighterStance::Standing,

            health: ENERGY_START,
            stun: 0,
            super_meter: 0,

            current_frame: 0,
            frame_timer: 0,
            animation_id: 0,
            sprite_flags: 0,

            hit_stun: 0,
            block_stun: 0,
            recovery_frames: 0,
            invincibility_frames: 0,

            input_buffer: [0; 16],
            input_buffer_head: 0,
            charge_time_back: 0,
            charge_time_down: 0,

            collision_box: Rect8::new(0, 0, 0, 0),
            hurt_boxes: [Rect8::new(0, 0, 0, 0); 4],
            hit_boxes: [Rect8::new(0, 0, 0, 0); 4],
            num_hurt_boxes: 0,
            num_hit_boxes: 0,

            special_move_state: 0,
            combo_counter: 0,
            combo_damage: 0,

            user_data: [0; PLAYER_USER_DATA_SIZE],

            throw_range: 0,
            throw_damage: 0,
            being_thrown: false,
            throw_tech_window: 0,

            projectile_count: 0,
            max_projectiles: 1,

            // AI system initialization (matching C99 defaults)
            ai_mode1: 0,
            ai_mode2: 0,
            ai_strategy: 0,

            ai_param1: 0,
            ai_param2: 0,
            ai_param3: 0,
            ai_param4: 0,
            difficulty_level: 16, // Medium difficulty (C99 default)

            ai_strat_agg0_index: 0,
            ai_strat_agg1_index: 0,
            ai_strat_def_index: 0,
            ai_strat_agg0_ptr: 0,
            ai_strat_agg1_ptr: 0,
            ai_strat_def_ptr: 0,

            ai_timer: 0,
            ai_timers: [0; 8],
            ai_agg_timer0: 0,
            ai_agg_timer1: 0,
            ai_timer_threat_check: 0,

            ai_sig_attack: false,
            ai_sig_special: false,
            ai_volley: false,
            ai_force_defensive: false,
            ai_allow_aggressive: false,
            ai_aggressive: false,
            ai_threat_found: false,
            ai_can_jump_attack: false,
            ai_start_again: 0,

            comp_do_throw: false,
            comp_do_block: false,
            comp_do_jump: false,
            comp_do_block_stun: false,
            comp_immune: 0,

            ai_walk_direction: 0, // STEP_STILL
            ai_walk_target: 0,
            ai_jump_sel: 0,
            ai_too_close: 0,
            ai_opp_jump_height: 0,

            ai_threat_check_mode: 2, // Default threat check mode
            ai_multi_count: 0,
            ai_saved_state: None,

            dice_roll_count: 0,
            button_strength: 0,
            punch_kick: 0,

            yoke_attacking_me: 0,
            yoke_saved: -1, // Will not match initially

            is_within_bounds: true,
            in_ground_attack: false,

            sound_id: 0,
            effect_id: 0,

            flags: 0,
            timer: 0,
            counter: 0,
        }
    }
}

impl Player {
    /// Create a new player with the specified fighter
    pub fn new(fighter_id: FighterId, x: Fixed16_16, y: Fixed16_16) -> Self {
        let mut player = Self::default();
        player.fighter_id = fighter_id;
        player.x = x;
        player.y = y;
        player
    }

    /// Check if player is on the ground
    pub fn is_grounded(&self) -> bool {
        self.y >= crate::constants::physics::GROUND_Y
    }

    /// Check if player is facing left
    pub fn is_facing_left(&self) -> bool {
        self.direction == FbDirection::FacingLeft
    }

    /// Check if player is facing right
    pub fn is_facing_right(&self) -> bool {
        self.direction == FbDirection::FacingRight
    }

    /// Flip the player's direction
    pub fn flip_direction(&mut self) {
        self.direction = self.direction.opposite();
    }

    /// Get the player's screen position as integers
    pub fn screen_position(&self) -> Point16 {
        Point16::new(
            self.x.integer_part(),
            self.y.integer_part(),
        )
    }

    /// Set position from integer coordinates
    pub fn set_position(&mut self, x: i16, y: i16) {
        self.x = Fixed16_16::from_i16(x);
        self.y = Fixed16_16::from_i16(y);
    }

    /// Apply velocity to position (basic physics step)
    pub fn update_position(&mut self) {
        // Convert 8.8 velocity to 16.16 for position update
        let vel_x_16 = Fixed16_16::from_raw((self.vel_x.raw() as i32) << 8);
        let vel_y_16 = Fixed16_16::from_raw((self.vel_y.raw() as i32) << 8);

        self.x += vel_x_16;
        self.y += vel_y_16;
    }

    /// Apply acceleration to velocity
    pub fn update_velocity(&mut self) {
        self.vel_x += self.acl_x;
        self.vel_y += self.acl_y;
    }

    /// Check if player is in hit stun
    pub fn is_in_hitstun(&self) -> bool {
        self.hit_stun > 0
    }

    /// Check if player is in block stun
    pub fn is_in_blockstun(&self) -> bool {
        self.block_stun > 0
    }

    /// Check if player is invincible
    pub fn is_invincible(&self) -> bool {
        self.invincibility_frames > 0
    }

    /// Reduce stun and invincibility timers
    pub fn update_timers(&mut self) {
        if self.hit_stun > 0 {
            self.hit_stun -= 1;
        }
        if self.block_stun > 0 {
            self.block_stun -= 1;
        }
        if self.invincibility_frames > 0 {
            self.invincibility_frames -= 1;
        }
        if self.recovery_frames > 0 {
            self.recovery_frames -= 1;
        }
        if self.frame_timer > 0 {
            self.frame_timer -= 1;
        }
    }
}

/// Projectile data structure (equivalent to C99 projectile structures)
#[derive(Debug, Clone)]
#[repr(C)]
pub struct Projectile {
    pub x: Fixed16_16,
    pub y: Fixed16_16,
    pub vel_x: Fixed8_8,
    pub vel_y: Fixed8_8,
    pub acl_x: Fixed8_8,
    pub acl_y: Fixed8_8,

    pub owner_id: u8,           // Which player owns this projectile
    pub projectile_type: u8,    // Type of projectile (fireball, sonic boom, etc.)
    pub damage: u16,
    pub hit_stun: u16,
    pub block_stun: u16,

    pub current_frame: u16,
    pub frame_timer: u16,
    pub animation_id: u16,

    pub hit_box: Rect8,
    pub active: bool,
    pub hit_player: bool,

    pub lifetime: u16,          // Frames until projectile disappears
    pub flags: u16,
}

impl Default for Projectile {
    fn default() -> Self {
        Self {
            x: Fixed16_16::ZERO,
            y: Fixed16_16::ZERO,
            vel_x: Fixed8_8::ZERO,
            vel_y: Fixed8_8::ZERO,
            acl_x: Fixed8_8::ZERO,
            acl_y: Fixed8_8::ZERO,

            owner_id: 0,
            projectile_type: 0,
            damage: 0,
            hit_stun: 0,
            block_stun: 0,

            current_frame: 0,
            frame_timer: 0,
            animation_id: 0,

            hit_box: Rect8::new(0, 0, 0, 0),
            active: false,
            hit_player: false,

            lifetime: 0,
            flags: 0,
        }
    }
}

impl Projectile {
    /// Create a new projectile
    pub fn new(x: Fixed16_16, y: Fixed16_16, vel_x: Fixed8_8, vel_y: Fixed8_8, owner_id: u8) -> Self {
        let mut projectile = Self::default();
        projectile.x = x;
        projectile.y = y;
        projectile.vel_x = vel_x;
        projectile.vel_y = vel_y;
        projectile.owner_id = owner_id;
        projectile.active = true;
        projectile
    }

    /// Update projectile position and state
    pub fn update(&mut self) {
        if !self.active {
            return;
        }

        // Update position
        let vel_x_16 = Fixed16_16::from_raw((self.vel_x.raw() as i32) << 8);
        let vel_y_16 = Fixed16_16::from_raw((self.vel_y.raw() as i32) << 8);

        self.x += vel_x_16;
        self.y += vel_y_16;

        // Update velocity with acceleration
        self.vel_x += self.acl_x;
        self.vel_y += self.acl_y;

        // Update timers
        if self.frame_timer > 0 {
            self.frame_timer -= 1;
        }

        if self.lifetime > 0 {
            self.lifetime -= 1;
            if self.lifetime == 0 {
                self.active = false;
            }
        }
    }

    /// Check if projectile is still active
    pub fn is_active(&self) -> bool {
        self.active && self.lifetime > 0
    }
}
