//! # Fighter State Management
//! 
//! Comprehensive fighter state machine system matching the original C99 PLSTAT_*
//! system with frame-accurate transitions and memory-safe state handling.

use serde::{Deserialize, Serialize};
use bevy::prelude::Component;
use std::fmt;

/// Frame counter for precise timing
pub type FrameCount = u64;

/// Fighter state machine matching C99 PLSTAT system
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum FighterState {
    /// Normal standing state (PLSTAT_NORMAL)
    Normal = 0x0,
    /// Crouching state (PLSTAT_CROUCH)
    Crouch = 0x2,
    /// Jumping state (PLSTAT_JUMPING)
    Jumping = 0x4,
    /// Turn around state (PLSTAT_TURNAROUND)
    TurnAround = 0x6,
    /// Standing block state (PLSTAT_STANDBLOCK)
    StandBlock = 0x8,
    /// Attacking state (PLSTAT_ATTACKING)
    Attacking = 0xa,
    /// Power move state (PLSTAT_IN_POWERMOVE)
    InPowerMove = 0xc,
    /// Reeling from hit (PLSTAT_REEL)
    Reel = 0xe,
    /// Victory state (PLSTAT_VICTORY)
    Victory = 0x10,
    /// Loss state (PLSTAT_LOSS)
    Loss = 0x12,
    /// Tumble/thrown recovery (PLSTAT_TUMBLE)
    Tumble = 0x14,
}

/// Fighter sub-state matching C99 STATUS system
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum FighterSubState {
    /// Standing (STATUS_STAND)
    Stand = 0x0,
    /// Walking forward/backward (STATUS_WALKING)
    Walking = 0x2,
    /// Normal state (STATUS_NORMAL)
    Normal = 0x6,
    /// Crouching (STATUS_CROUCH)
    Crouch = 0x4,
    /// Standing up (STATUS_STAND_UP)
    StandUp = 0x24,
    /// Jump start (STATUS_JUMP_START)
    JumpStart = 0x8,
    /// Landing (STATUS_LANDING)
    Landing = 0xa,
    /// Turn around (STATUS_TURN_AROUND)
    TurnAround = 0xc,
    /// Crouch turn (STATUS_CROUCH_TURN)
    CrouchTurn = 0xe,
    /// Falling (STATUS_FALLING)
    Falling = 0x10,
    /// Stand block (STATUS_STAND_BLOCK)
    StandBlock = 0x12,
    /// Stand block 2 (STATUS_STAND_BLOCK2)
    StandBlock2 = 0x14,
    /// Crouch block (STATUS_CROUCH_BLOCK)
    CrouchBlock = 0x16,
    /// Crouch block 2 (STATUS_CROUCH_BLOCK2)
    CrouchBlock2 = 0x18,
    /// Stun 1 (STATUS_STUN1)
    Stun1 = 0x1a,
    /// Stun 2 (STATUS_STUN2)
    Stun2 = 0x1c,
    /// Stand block freeze (STATUS_STAND_BLOCK_FREEZE)
    StandBlockFreeze = 0x1e,
    /// Crouch block freeze (STATUS_CROUCH_BLOCK_FREEZE)
    CrouchBlockFreeze = 0x20,
    /// Crouch stun (STATUS_CROUCH_STUN)
    CrouchStun = 0x22,
}

/// Fighter mode matching C99 PLMODE system
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum FighterMode {
    /// Normal active state
    Active = 0x0,
    /// Inactive/disabled
    Inactive = 0x2,
    /// Knocked out (PLMODE_KNOCKED_OUT)
    KnockedOut = 0x4,
}

/// Attack type classification
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AttackType {
    /// Standing attack (ATTACK_STAND)
    Stand = 0,
    /// Sweep attack (ATTACK_SWEEP)
    Sweep = 1,
    /// Jump attack (ATTACK_JUMP)
    Jump = 2,
    /// Special move (ATTACK_SPECIAL)
    Special = 3,
    /// Priority attack (ATTACK_PRIORITY)
    Priority = 4,
}

/// Next action flags matching C99 NEXTACT system
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NextAction {
    /// Round over (NEXTACT_ROUNDOVER)
    RoundOver = 2,
    /// Turn around (NEXTACT_TURNAROUND)
    TurnAround = 0x4,
    /// Crouch down (NEXTACT_CROUCHDOWN)
    CrouchDown = 0x6,
    /// Jump (NEXTACT_JUMP)
    Jump = 0x8,
    /// Stand up (NEXTACT_STANDUP)
    StandUp = 0xa,
    /// Falling (NEXTACT_FALLING)
    Falling = 0xc,
}

/// Comprehensive fighter state data
#[derive(Debug, Clone, Component, Serialize, Deserialize)]
pub struct FighterStateData {
    /// Primary state (mode1)
    pub state: FighterState,
    /// Sub-state (mode4)
    pub sub_state: FighterSubState,
    /// Fighter mode (mode0)
    pub mode: FighterMode,
    
    /// State timers
    pub state_timer: u16,
    pub sub_state_timer: u16,
    pub mode_timer: u16,
    
    /// Frame counters
    pub state_frame: FrameCount,
    pub animation_frame: u16,
    pub hit_frame: u16,
    
    /// State flags
    pub flags: FighterStateFlags,
    
    /// Next action to perform
    pub next_action: Option<NextAction>,
    
    /// Attack data
    pub current_attack: Option<AttackType>,
    pub attack_frame: u16,
    
    /// Physics state
    pub on_ground: bool,
    pub facing_right: bool,
    pub can_turn: bool,
    
    /// Combat state
    pub hit_stun: u16,
    pub block_stun: u16,
    pub dizzy_count: u16,
    pub dizzy_clear_count: u16,
    
    /// Throw state
    pub throw_stat: i16,
    pub no_throw_timer: u16,
    pub collision_disabled: u16,
}

/// Fighter state flags
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct FighterStateFlags {
    /// Can be thrown
    pub throwable: bool,
    /// In hit stun
    pub in_hit_stun: bool,
    /// In block stun
    pub in_block_stun: bool,
    /// Dizzy state
    pub dizzy: bool,
    /// Invulnerable
    pub invulnerable: bool,
    /// Can cancel current action
    pub cancelable: bool,
    /// Auto-facing enabled
    pub auto_face: bool,
    /// Collision detection enabled
    pub collision_enabled: bool,
}

/// State transition result
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum FighterStateTransition {
    /// Transition successful
    Success,
    /// Transition blocked by condition
    Blocked(String),
    /// Invalid transition attempted
    Invalid(String),
    /// Transition requires frame delay
    Delayed(u16),
}

impl FighterStateData {
    /// Create new fighter state with default values
    pub fn new() -> Self {
        Self {
            state: FighterState::Normal,
            sub_state: FighterSubState::Stand,
            mode: FighterMode::Active,
            state_timer: 0,
            sub_state_timer: 0,
            mode_timer: 0,
            state_frame: 0,
            animation_frame: 0,
            hit_frame: 0,
            flags: FighterStateFlags::default(),
            next_action: None,
            current_attack: None,
            attack_frame: 0,
            on_ground: true,
            facing_right: true,
            can_turn: true,
            hit_stun: 0,
            block_stun: 0,
            dizzy_count: 0,
            dizzy_clear_count: 0,
            throw_stat: 0,
            no_throw_timer: 0,
            collision_disabled: 0,
        }
    }
    
    /// Reset to initial state
    pub fn reset(&mut self) {
        *self = Self::new();
    }
    
    /// Advance frame and update timers
    pub fn advance_frame(&mut self) {
        self.state_frame = self.state_frame.wrapping_add(1);
        self.animation_frame = self.animation_frame.wrapping_add(1);
        
        // Decrement timers
        if self.state_timer > 0 {
            self.state_timer -= 1;
        }
        if self.sub_state_timer > 0 {
            self.sub_state_timer -= 1;
        }
        if self.mode_timer > 0 {
            self.mode_timer -= 1;
        }
        if self.hit_stun > 0 {
            self.hit_stun -= 1;
        }
        if self.block_stun > 0 {
            self.block_stun -= 1;
        }
        if self.no_throw_timer > 0 {
            self.no_throw_timer -= 1;
        }
        if self.collision_disabled > 0 {
            self.collision_disabled -= 1;
        }
        if self.dizzy_clear_count > 0 {
            self.dizzy_clear_count -= 1;
            if self.dizzy_clear_count == 0 {
                self.dizzy_count = 0;
                self.flags.dizzy = false;
            }
        }
        
        // Update flags based on timers
        self.flags.in_hit_stun = self.hit_stun > 0;
        self.flags.in_block_stun = self.block_stun > 0;
        self.flags.throwable = self.no_throw_timer == 0 && !self.flags.invulnerable;
        self.flags.collision_enabled = self.collision_disabled == 0;
    }
    
    /// Check if fighter can perform action
    pub fn can_perform_action(&self, action: NextAction) -> bool {
        match action {
            NextAction::Jump => {
                self.on_ground && 
                matches!(self.state, FighterState::Normal | FighterState::Crouch) &&
                !self.flags.in_hit_stun && !self.flags.in_block_stun
            }
            NextAction::TurnAround => {
                self.can_turn && 
                !self.flags.in_hit_stun && !self.flags.in_block_stun
            }
            NextAction::CrouchDown => {
                self.on_ground && 
                matches!(self.state, FighterState::Normal) &&
                !self.flags.in_hit_stun && !self.flags.in_block_stun
            }
            NextAction::StandUp => {
                matches!(self.state, FighterState::Crouch) &&
                !self.flags.in_hit_stun && !self.flags.in_block_stun
            }
            NextAction::Falling => {
                !self.on_ground
            }
            NextAction::RoundOver => true,
        }
    }
}

impl Default for FighterStateData {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for FighterStateData {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Fighter[{:?}:{:?} | Frame:{} | Stun:{}]",
            self.state, self.sub_state, self.state_frame, self.hit_stun
        )
    }
}

/// State transition implementation
impl FighterStateData {
    /// Transition to new fighter state with validation
    pub fn transition_state(&mut self, new_state: FighterState) -> FighterStateTransition {
        match (self.state, new_state) {
            // Normal state transitions
            (FighterState::Normal, FighterState::Crouch) => {
                if self.on_ground && !self.flags.in_hit_stun {
                    self.state = new_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Cannot crouch while airborne or in hitstun".to_string())
                }
            }

            (FighterState::Normal, FighterState::Jumping) => {
                if self.on_ground && !self.flags.in_hit_stun && !self.flags.in_block_stun {
                    self.state = new_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    self.on_ground = false;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Cannot jump".to_string())
                }
            }

            (FighterState::Normal, FighterState::Attacking) => {
                if !self.flags.in_hit_stun && !self.flags.in_block_stun {
                    self.state = new_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    self.attack_frame = 0;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Cannot attack while stunned".to_string())
                }
            }

            (FighterState::Normal, FighterState::StandBlock) => {
                if self.on_ground {
                    self.state = new_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Cannot block while airborne".to_string())
                }
            }

            // Crouch state transitions
            (FighterState::Crouch, FighterState::Normal) => {
                self.state = new_state;
                self.state_timer = 0;
                self.animation_frame = 0;
                FighterStateTransition::Success
            }

            (FighterState::Crouch, FighterState::Attacking) => {
                if !self.flags.in_hit_stun && !self.flags.in_block_stun {
                    self.state = new_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    self.attack_frame = 0;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Cannot attack while stunned".to_string())
                }
            }

            // Jumping state transitions
            (FighterState::Jumping, FighterState::Normal) => {
                if self.on_ground {
                    self.state = new_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Must land first".to_string())
                }
            }

            (FighterState::Jumping, FighterState::Attacking) => {
                self.state = new_state;
                self.state_timer = 0;
                self.animation_frame = 0;
                self.attack_frame = 0;
                FighterStateTransition::Success
            }

            // Attack state transitions
            (FighterState::Attacking, FighterState::Normal) => {
                self.state = new_state;
                self.state_timer = 0;
                self.animation_frame = 0;
                self.current_attack = None;
                FighterStateTransition::Success
            }

            (FighterState::Attacking, FighterState::InPowerMove) => {
                if self.flags.cancelable {
                    self.state = new_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Attack not cancelable".to_string())
                }
            }

            // Hit reactions
            (_, FighterState::Reel) => {
                self.state = new_state;
                self.state_timer = 0;
                self.animation_frame = 0;
                self.flags.invulnerable = false;
                FighterStateTransition::Success
            }

            // Victory/Loss states
            (_, FighterState::Victory) | (_, FighterState::Loss) => {
                self.state = new_state;
                self.state_timer = 0;
                self.animation_frame = 0;
                FighterStateTransition::Success
            }

            // Turn around
            (current, FighterState::TurnAround) if current != FighterState::TurnAround => {
                if self.can_turn && !self.flags.in_hit_stun {
                    self.state = new_state;
                    self.state_timer = 6; // Turn around takes 6 frames
                    self.animation_frame = 0;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Blocked("Cannot turn around".to_string())
                }
            }

            (FighterState::TurnAround, previous_state) => {
                if self.state_timer == 0 {
                    self.state = previous_state;
                    self.state_timer = 0;
                    self.animation_frame = 0;
                    self.facing_right = !self.facing_right;
                    FighterStateTransition::Success
                } else {
                    FighterStateTransition::Delayed(self.state_timer)
                }
            }

            // Same state is always valid
            (current, new) if current == new => FighterStateTransition::Success,

            // Invalid transitions
            _ => FighterStateTransition::Invalid(format!(
                "Invalid fighter state transition from {:?} to {:?}",
                self.state, new_state
            )),
        }
    }

    /// Apply hit to fighter
    pub fn apply_hit(&mut self, damage: u16, stun: u16, knockback: bool) -> FighterStateTransition {
        if self.flags.invulnerable {
            return FighterStateTransition::Blocked("Fighter is invulnerable".to_string());
        }

        self.hit_stun = stun;
        self.flags.in_hit_stun = true;

        if knockback {
            self.transition_state(FighterState::Reel)
        } else {
            FighterStateTransition::Success
        }
    }

    /// Apply block to fighter
    pub fn apply_block(&mut self, stun: u16) -> FighterStateTransition {
        self.block_stun = stun;
        self.flags.in_block_stun = true;

        match self.state {
            FighterState::Normal => self.transition_state(FighterState::StandBlock),
            FighterState::Crouch => {
                self.sub_state = FighterSubState::CrouchBlock;
                FighterStateTransition::Success
            }
            _ => FighterStateTransition::Success,
        }
    }

    /// Set next action to perform
    pub fn set_next_action(&mut self, action: NextAction) -> bool {
        if self.can_perform_action(action) {
            self.next_action = Some(action);
            true
        } else {
            false
        }
    }

    /// Execute queued next action
    pub fn execute_next_action(&mut self) -> Option<FighterStateTransition> {
        if let Some(action) = self.next_action.take() {
            match action {
                NextAction::Jump => Some(self.transition_state(FighterState::Jumping)),
                NextAction::TurnAround => Some(self.transition_state(FighterState::TurnAround)),
                NextAction::CrouchDown => Some(self.transition_state(FighterState::Crouch)),
                NextAction::StandUp => Some(self.transition_state(FighterState::Normal)),
                NextAction::Falling => {
                    self.on_ground = false;
                    Some(FighterStateTransition::Success)
                }
                NextAction::RoundOver => Some(FighterStateTransition::Success),
            }
        } else {
            None
        }
    }
}
