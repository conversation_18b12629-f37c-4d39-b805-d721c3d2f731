//! # Fixed Point Arithmetic
//!
//! Fixed-point arithmetic types for consistent game physics and positioning.
//! These types ensure deterministic behavior across different platforms.
//!
//! This module provides exact equivalents to the C99 FIXED16_16 and FIXED8_8 types
//! used in the original Street Fighter II engine.

use std::ops::{Add, Sub, Mul, Div, AddAssign, SubAssign, MulAssign, DivAssign, Neg};
use serde::{Deserialize, Serialize};
use crate::endian::EndianConvert;

/// 16.16 fixed-point number (16 bits integer, 16 bits fractional)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
#[repr(transparent)]
pub struct Fixed16_16(i32);

impl Fixed16_16 {
    pub const ZERO: Self = Self(0);
    pub const ONE: Self = Self(1 << 16);
    pub const HALF: Self = Self(1 << 15);
    pub const MINUS_ONE: Self = Self(-1 << 16);

    /// Create from raw fixed-point value
    pub const fn from_raw(raw: i32) -> Self {
        Self(raw)
    }

    /// Get raw fixed-point value
    pub const fn raw(self) -> i32 {
        self.0
    }

    /// Create from integer
    pub const fn from_int(int: i32) -> Self {
        Self(int << 16)
    }

    /// Create from i16 integer (common in SF2)
    pub const fn from_i16(int: i16) -> Self {
        Self((int as i32) << 16)
    }

    /// Create from float (runtime conversion)
    pub fn from_f32(float: f32) -> Self {
        Self((float * 65536.0) as i32)
    }

    /// Convert to float
    pub fn to_f32(self) -> f32 {
        self.0 as f32 / 65536.0
    }

    /// Get integer part (signed 16-bit, matching C99 struct)
    pub const fn integer_part(self) -> i16 {
        (self.0 >> 16) as i16
    }

    /// Get fractional part (unsigned 16-bit, matching C99 struct)
    pub const fn fractional_part(self) -> u16 {
        (self.0 & 0xFFFF) as u16
    }

    /// Set integer part while preserving fractional part
    pub fn set_integer_part(&mut self, int: i16) {
        let frac = self.fractional_part();
        self.0 = ((int as i32) << 16) | (frac as i32);
    }

    /// Set fractional part while preserving integer part
    pub fn set_fractional_part(&mut self, frac: u16) {
        let int = self.integer_part();
        self.0 = ((int as i32) << 16) | (frac as i32);
    }

    /// Absolute value
    pub fn abs(self) -> Self {
        Self(self.0.abs())
    }

    /// Square root (approximate)
    pub fn sqrt(self) -> Self {
        if self.0 <= 0 {
            return Self::ZERO;
        }

        // Newton-Raphson method for fixed-point square root
        let mut x = self;
        let mut prev;

        for _ in 0..8 { // 8 iterations should be enough for 16.16
            prev = x;
            x = (x + self / x) / Fixed16_16::from_int(2);
            if (x - prev).abs().0 < 2 { // Converged
                break;
            }
        }

        x
    }

    /// Clamp value between min and max
    pub fn clamp(self, min: Self, max: Self) -> Self {
        if self < min {
            min
        } else if self > max {
            max
        } else {
            self
        }
    }

    /// Linear interpolation between two values
    pub fn lerp(self, other: Self, t: Self) -> Self {
        self + (other - self) * t
    }
}

/// 8.8 fixed-point number (8 bits integer, 8 bits fractional)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
#[repr(transparent)]
pub struct Fixed8_8(i16);

impl Fixed8_8 {
    pub const ZERO: Self = Self(0);
    pub const ONE: Self = Self(1 << 8);
    pub const HALF: Self = Self(1 << 7);
    pub const MINUS_ONE: Self = Self(-1 << 8);

    /// Create from raw fixed-point value
    pub const fn from_raw(raw: i16) -> Self {
        Self(raw)
    }

    /// Get raw fixed-point value
    pub const fn raw(self) -> i16 {
        self.0
    }

    /// Create from integer
    pub const fn from_int(int: i8) -> Self {
        Self((int as i16) << 8)
    }

    /// Create from i16 integer (with clamping)
    pub fn from_i16(int: i16) -> Self {
        Self((int.clamp(-128, 127) as i16) << 8)
    }

    /// Create from float
    pub fn from_f32(float: f32) -> Self {
        Self((float * 256.0) as i16)
    }

    /// Convert to float
    pub fn to_f32(self) -> f32 {
        self.0 as f32 / 256.0
    }

    /// Get integer part (signed 8-bit, matching C99 struct)
    pub const fn integer_part(self) -> i8 {
        (self.0 >> 8) as i8
    }

    /// Get fractional part (unsigned 8-bit, matching C99 struct)
    pub const fn fractional_part(self) -> u8 {
        (self.0 & 0xFF) as u8
    }

    /// Set integer part while preserving fractional part
    pub fn set_integer_part(&mut self, int: i8) {
        let frac = self.fractional_part();
        self.0 = ((int as i16) << 8) | (frac as i16);
    }

    /// Set fractional part while preserving integer part
    pub fn set_fractional_part(&mut self, frac: u8) {
        let int = self.integer_part();
        self.0 = ((int as i16) << 8) | (frac as i16);
    }

    /// Absolute value
    pub fn abs(self) -> Self {
        Self(self.0.abs())
    }

    /// Clamp value between min and max
    pub fn clamp(self, min: Self, max: Self) -> Self {
        if self < min {
            min
        } else if self > max {
            max
        } else {
            self
        }
    }
}

// Arithmetic operations for Fixed16_16
impl Add for Fixed16_16 {
    type Output = Self;
    fn add(self, rhs: Self) -> Self::Output {
        Self(self.0.saturating_add(rhs.0))
    }
}

impl Sub for Fixed16_16 {
    type Output = Self;
    fn sub(self, rhs: Self) -> Self::Output {
        Self(self.0.saturating_sub(rhs.0))
    }
}

impl Mul for Fixed16_16 {
    type Output = Self;
    fn mul(self, rhs: Self) -> Self::Output {
        let result = (self.0 as i64 * rhs.0 as i64) >> 16;
        Self(result as i32)
    }
}

impl Div for Fixed16_16 {
    type Output = Self;
    fn div(self, rhs: Self) -> Self::Output {
        if rhs.0 == 0 {
            return Self::ZERO;
        }
        let result = ((self.0 as i64) << 16) / rhs.0 as i64;
        Self(result as i32)
    }
}

impl AddAssign for Fixed16_16 {
    fn add_assign(&mut self, rhs: Self) {
        *self = *self + rhs;
    }
}

impl SubAssign for Fixed16_16 {
    fn sub_assign(&mut self, rhs: Self) {
        *self = *self - rhs;
    }
}

impl MulAssign for Fixed16_16 {
    fn mul_assign(&mut self, rhs: Self) {
        *self = *self * rhs;
    }
}

impl DivAssign for Fixed16_16 {
    fn div_assign(&mut self, rhs: Self) {
        *self = *self / rhs;
    }
}

impl Neg for Fixed16_16 {
    type Output = Self;
    fn neg(self) -> Self::Output {
        Self(-self.0)
    }
}

impl EndianConvert for Fixed16_16 {
    fn from_be(self) -> Self {
        Self(self.0.from_be())
    }

    fn to_be(self) -> Self {
        Self(self.0.to_be())
    }
}

// Arithmetic operations for Fixed8_8
impl Add for Fixed8_8 {
    type Output = Self;
    fn add(self, rhs: Self) -> Self::Output {
        Self(self.0.saturating_add(rhs.0))
    }
}

impl Sub for Fixed8_8 {
    type Output = Self;
    fn sub(self, rhs: Self) -> Self::Output {
        Self(self.0.saturating_sub(rhs.0))
    }
}

impl Mul for Fixed8_8 {
    type Output = Self;
    fn mul(self, rhs: Self) -> Self::Output {
        let result = (self.0 as i32 * rhs.0 as i32) >> 8;
        Self(result as i16)
    }
}

impl Div for Fixed8_8 {
    type Output = Self;
    fn div(self, rhs: Self) -> Self::Output {
        if rhs.0 == 0 {
            return Self::ZERO;
        }
        let result = ((self.0 as i32) << 8) / rhs.0 as i32;
        Self(result as i16)
    }
}

impl AddAssign for Fixed8_8 {
    fn add_assign(&mut self, rhs: Self) {
        *self = *self + rhs;
    }
}

impl SubAssign for Fixed8_8 {
    fn sub_assign(&mut self, rhs: Self) {
        *self = *self - rhs;
    }
}

impl MulAssign for Fixed8_8 {
    fn mul_assign(&mut self, rhs: Self) {
        *self = *self * rhs;
    }
}

impl DivAssign for Fixed8_8 {
    fn div_assign(&mut self, rhs: Self) {
        *self = *self / rhs;
    }
}

impl Neg for Fixed8_8 {
    type Output = Self;
    fn neg(self) -> Self::Output {
        Self(-self.0)
    }
}

impl EndianConvert for Fixed8_8 {
    fn from_be(self) -> Self {
        Self(self.0.from_be())
    }

    fn to_be(self) -> Self {
        Self(self.0.to_be())
    }
}

/// Macros for compatibility with C99 SF2 code
///
/// These macros provide the same interface as the original C macros

/// Convert float to 16.16 fixed point (equivalent to FLT2FP16)
#[macro_export]
macro_rules! flt2fp16 {
    ($x:expr) => {
        Fixed16_16::from_f32($x)
    };
}

/// Convert float to 8.8 fixed point (equivalent to FLT2FP8)
#[macro_export]
macro_rules! flt2fp8 {
    ($x:expr) => {
        Fixed8_8::from_f32($x)
    };
}

/// Get integer part of fixed point (equivalent to XPI/YPI macros)
#[macro_export]
macro_rules! fp_int {
    ($x:expr) => {
        $x.integer_part()
    };
}

/// Set integer part of fixed point
#[macro_export]
macro_rules! set_fp_int {
    ($x:expr, $val:expr) => {
        $x.set_integer_part($val)
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_fixed16_16_basic_ops() {
        let a = Fixed16_16::from_int(5);
        let b = Fixed16_16::from_int(3);

        assert_eq!((a + b).integer_part(), 8);
        assert_eq!((a - b).integer_part(), 2);
        assert_eq!((a * b).integer_part(), 15);
        assert_eq!((a / b).integer_part(), 1);
    }

    #[test]
    fn test_fixed16_16_float_conversion() {
        let f = 3.14159;
        let fixed = Fixed16_16::from_f32(f);
        let back = fixed.to_f32();

        assert!((f - back).abs() < 0.001);
    }

    #[test]
    fn test_fixed16_16_parts() {
        let mut fixed = Fixed16_16::from_int(5);
        fixed.set_fractional_part(0x8000); // 0.5

        assert_eq!(fixed.integer_part(), 5);
        assert_eq!(fixed.fractional_part(), 0x8000);
        assert!((fixed.to_f32() - 5.5).abs() < 0.001);
    }

    #[test]
    fn test_fixed8_8_basic_ops() {
        let a = Fixed8_8::from_int(5);
        let b = Fixed8_8::from_int(3);

        assert_eq!((a + b).integer_part(), 8);
        assert_eq!((a - b).integer_part(), 2);
    }

    #[test]
    fn test_endian_conversion() {
        let fixed16 = Fixed16_16::from_int(0x1234);
        let be_fixed16 = fixed16.to_be();
        assert_eq!(be_fixed16.from_be(), fixed16);

        let fixed8 = Fixed8_8::from_int(0x12);
        let be_fixed8 = fixed8.to_be();
        assert_eq!(be_fixed8.from_be(), fixed8);
    }

    #[test]
    fn test_macros() {
        let fp16 = flt2fp16!(3.14159);
        assert!((fp16.to_f32() - 3.14159).abs() < 0.001);

        let fp8 = flt2fp8!(2.5);
        assert!((fp8.to_f32() - 2.5).abs() < 0.01);

        assert_eq!(fp_int!(fp16), 3);
    }
}
