//! # Game State Management
//! 
//! Comprehensive game state management system that mirrors the original C99
//! hierarchical state machine (mode0-mode6) while leveraging Rust's type system
//! for memory safety and performance.

use serde::{Deserialize, Serialize};
use std::fmt;

/// Frame counter type for precise timing
pub type FrameCount = u64;

/// Timer type matching original C99 timer system
pub type GameTimer = u16;

/// Main game state machine matching C99 mode0 system
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum GameMode {
    /// Initial startup and initialization
    Startup = 0,
    /// Attract mode and demo sequences
    AttractMode = 2,
    /// Player selection and character select
    PlayerSelect = 4,
    /// Main gameplay state
    InGame = 6,
    /// Game completion and ending
    GameComplete = 8,
    /// Continue screen
    Continue = 10,
    /// Service mode and testing
    ServiceMode = 12,
}

/// Fight state machine matching C99 mode1 system
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ize, Deserialize)]
pub enum FightMode {
    /// Player selection phase
    PlayerSelection = 0,
    /// Pre-fight initialization
    PreFight = 2,
    /// Main fight gameplay
    Fighting = 4,
    /// Game completion
    Complete = 6,
    /// Continue prompt
    ContinuePrompt = 8,
    /// Post-game cleanup
    Cleanup = 10,
    /// Final cleanup
    FinalCleanup = 12,
}

/// Round state machine matching C99 mode2 system
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RoundMode {
    /// Round initialization
    Init = 0,
    /// Character introduction
    Intro = 2,
    /// VS screen display
    VsScreen = 4,
    /// Round setup
    Setup = 6,
    /// Pre-fight animation
    PreFightAnim = 8,
    /// Main fighting
    Fighting = 10,
    /// Post-fight animation
    PostFightAnim = 12,
    /// Round result processing
    RoundResult = 14,
    /// Fade out
    FadeOut = 16,
    /// Final processing
    Final = 18,
}

/// Sub-state for detailed state tracking (mode3)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SubMode {
    State0 = 0,
    State2 = 2,
    State4 = 4,
    State6 = 6,
    State8 = 8,
    State10 = 10,
    State12 = 12,
    State14 = 14,
}

/// Animation and effect state (mode4)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AnimMode {
    Idle = 0,
    Intro = 2,
    Victory = 4,
    Defeat = 6,
    Special = 8,
    Transition = 10,
}

/// Audio and sound state (mode5)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AudioMode {
    Silent = 0,
    Music = 2,
    Effects = 4,
    Voice = 6,
    Ambient = 8,
}

/// Extended state for complex scenarios (mode6)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ExtendedMode {
    Normal = 0,
    Bonus = 2,
    Secret = 4,
    Demo = 6,
    Test = 8,
}

/// Comprehensive game state structure matching C99 gamestate
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GameState {
    /// Primary game mode (mode0)
    pub game_mode: GameMode,
    /// Game mode timer (timer0)
    pub game_timer: GameTimer,
    
    /// Fight mode (mode1)
    pub fight_mode: FightMode,
    /// Fight timer (timer1)
    pub fight_timer: GameTimer,
    
    /// Round mode (mode2)
    pub round_mode: RoundMode,
    /// Round timer (timer2)
    pub round_timer: GameTimer,
    
    /// Sub mode (mode3)
    pub sub_mode: SubMode,
    /// Sub timer (timer3)
    pub sub_timer: GameTimer,
    
    /// Animation mode (mode4)
    pub anim_mode: AnimMode,
    /// Animation timer (timer4)
    pub anim_timer: GameTimer,
    
    /// Audio mode (mode5)
    pub audio_mode: AudioMode,
    /// Audio timer (timer5)
    pub audio_timer: GameTimer,
    
    /// Extended mode (mode6)
    pub extended_mode: ExtendedMode,
    /// Extended timer (timer6)
    pub extended_timer: GameTimer,
    
    /// Global frame counter for precise timing
    pub frame_count: FrameCount,
    
    /// Tick counter matching C99 tick system
    pub tick: u8,
    
    /// Round counter
    pub round_count: u8,
    
    /// Game flags
    pub flags: GameStateFlags,
}

/// Game state flags matching C99 boolean fields
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct GameStateFlags {
    /// Round is complete
    pub round_complete: bool,
    /// Pre-round animation active
    pub pre_round_anim: bool,
    /// New players joined
    pub new_players: bool,
    /// Player select done
    pub player_select_done: bool,
    /// On bonus stage
    pub on_bonus_stage: bool,
    /// Two humans playing
    pub two_humans: bool,
    /// Wait mode active
    pub wait_mode: bool,
    /// Demo started
    pub demo_started: bool,
    /// In demo mode
    pub in_demo: bool,
    /// Text effect busy
    pub text_effect_busy: bool,
    /// Freeze machine
    pub freeze_machine: bool,
    /// Debug mode
    pub debug: bool,
    /// In test mode
    pub in_test_mode: bool,
}

/// State transition result
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum StateTransition {
    /// Transition successful
    Success,
    /// Transition blocked by condition
    Blocked(String),
    /// Invalid transition attempted
    Invalid(String),
    /// Transition requires additional setup
    Pending(String),
}

impl GameState {
    /// Create new game state with default values
    pub fn new() -> Self {
        Self {
            game_mode: GameMode::Startup,
            game_timer: 0,
            fight_mode: FightMode::PlayerSelection,
            fight_timer: 0,
            round_mode: RoundMode::Init,
            round_timer: 0,
            sub_mode: SubMode::State0,
            sub_timer: 0,
            anim_mode: AnimMode::Idle,
            anim_timer: 0,
            audio_mode: AudioMode::Silent,
            audio_timer: 0,
            extended_mode: ExtendedMode::Normal,
            extended_timer: 0,
            frame_count: 0,
            tick: 0,
            round_count: 0,
            flags: GameStateFlags::default(),
        }
    }
    
    /// Reset state to initial values (matching LBResetState)
    pub fn reset(&mut self) {
        *self = Self::new();
    }
    
    /// Advance frame counter and update timers
    pub fn advance_frame(&mut self) {
        self.frame_count = self.frame_count.wrapping_add(1);
        self.tick = self.tick.wrapping_add(1);
        
        // Decrement active timers
        if self.game_timer > 0 {
            self.game_timer -= 1;
        }
        if self.fight_timer > 0 {
            self.fight_timer -= 1;
        }
        if self.round_timer > 0 {
            self.round_timer -= 1;
        }
        if self.sub_timer > 0 {
            self.sub_timer -= 1;
        }
        if self.anim_timer > 0 {
            self.anim_timer -= 1;
        }
        if self.audio_timer > 0 {
            self.audio_timer -= 1;
        }
        if self.extended_timer > 0 {
            self.extended_timer -= 1;
        }
    }
}

impl Default for GameState {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for GameState {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "GameState[{:?}:{} | {:?}:{} | {:?}:{} | Frame:{}]",
            self.game_mode, self.game_timer,
            self.fight_mode, self.fight_timer,
            self.round_mode, self.round_timer,
            self.frame_count
        )
    }
}

/// State transition manager with validation
impl GameState {
    /// Transition game mode with validation
    pub fn transition_game_mode(&mut self, new_mode: GameMode) -> StateTransition {
        match (self.game_mode, new_mode) {
            // Valid startup transitions
            (GameMode::Startup, GameMode::AttractMode) => {
                self.game_mode = new_mode;
                self.game_timer = 0;
                StateTransition::Success
            }

            // Attract mode transitions
            (GameMode::AttractMode, GameMode::PlayerSelect) => {
                self.game_mode = new_mode;
                self.game_timer = 0;
                StateTransition::Success
            }

            // Player select transitions
            (GameMode::PlayerSelect, GameMode::InGame) => {
                if self.flags.player_select_done {
                    self.game_mode = new_mode;
                    self.game_timer = 0;
                    StateTransition::Success
                } else {
                    StateTransition::Blocked("Player selection not complete".to_string())
                }
            }

            // In-game transitions
            (GameMode::InGame, GameMode::GameComplete) => {
                self.game_mode = new_mode;
                self.game_timer = 0;
                StateTransition::Success
            }

            (GameMode::InGame, GameMode::Continue) => {
                self.game_mode = new_mode;
                self.game_timer = 10 * 60; // 10 seconds at 60 FPS
                StateTransition::Success
            }

            // Continue transitions
            (GameMode::Continue, GameMode::InGame) => {
                self.game_mode = new_mode;
                self.game_timer = 0;
                StateTransition::Success
            }

            (GameMode::Continue, GameMode::AttractMode) => {
                self.game_mode = new_mode;
                self.game_timer = 0;
                StateTransition::Success
            }

            // Service mode can be entered from any state
            (_, GameMode::ServiceMode) => {
                self.game_mode = new_mode;
                self.game_timer = 0;
                StateTransition::Success
            }

            // Return to attract from service
            (GameMode::ServiceMode, GameMode::AttractMode) => {
                self.game_mode = new_mode;
                self.game_timer = 0;
                StateTransition::Success
            }

            // Same state is always valid
            (current, new) if current == new => StateTransition::Success,

            // Invalid transitions
            _ => StateTransition::Invalid(format!(
                "Invalid transition from {:?} to {:?}",
                self.game_mode, new_mode
            )),
        }
    }

    /// Transition fight mode with validation
    pub fn transition_fight_mode(&mut self, new_mode: FightMode) -> StateTransition {
        match (self.fight_mode, new_mode) {
            // Sequential fight mode transitions
            (FightMode::PlayerSelection, FightMode::PreFight) => {
                if self.flags.player_select_done {
                    self.fight_mode = new_mode;
                    self.fight_timer = 0;
                    StateTransition::Success
                } else {
                    StateTransition::Blocked("Player selection not done".to_string())
                }
            }

            (FightMode::PreFight, FightMode::Fighting) => {
                self.fight_mode = new_mode;
                self.fight_timer = 0;
                StateTransition::Success
            }

            (FightMode::Fighting, FightMode::Complete) => {
                self.fight_mode = new_mode;
                self.fight_timer = 0;
                StateTransition::Success
            }

            (FightMode::Fighting, FightMode::ContinuePrompt) => {
                self.fight_mode = new_mode;
                self.fight_timer = 10 * 60; // 10 seconds
                StateTransition::Success
            }

            (FightMode::ContinuePrompt, FightMode::Fighting) => {
                self.fight_mode = new_mode;
                self.fight_timer = 0;
                StateTransition::Success
            }

            (FightMode::ContinuePrompt, FightMode::Complete) => {
                self.fight_mode = new_mode;
                self.fight_timer = 0;
                StateTransition::Success
            }

            // Same state
            (current, new) if current == new => StateTransition::Success,

            // Invalid transitions
            _ => StateTransition::Invalid(format!(
                "Invalid fight mode transition from {:?} to {:?}",
                self.fight_mode, new_mode
            )),
        }
    }

    /// Advance to next mode (matching C99 NEXT macro)
    pub fn next_game_mode(&mut self) -> StateTransition {
        let next_mode = match self.game_mode {
            GameMode::Startup => GameMode::AttractMode,
            GameMode::AttractMode => GameMode::PlayerSelect,
            GameMode::PlayerSelect => GameMode::InGame,
            GameMode::InGame => GameMode::GameComplete,
            GameMode::GameComplete => GameMode::AttractMode,
            GameMode::Continue => GameMode::AttractMode,
            GameMode::ServiceMode => GameMode::AttractMode,
        };
        self.transition_game_mode(next_mode)
    }

    /// Advance to next fight mode
    pub fn next_fight_mode(&mut self) -> StateTransition {
        let next_mode = match self.fight_mode {
            FightMode::PlayerSelection => FightMode::PreFight,
            FightMode::PreFight => FightMode::Fighting,
            FightMode::Fighting => FightMode::Complete,
            FightMode::Complete => FightMode::Cleanup,
            FightMode::ContinuePrompt => FightMode::Complete,
            FightMode::Cleanup => FightMode::FinalCleanup,
            FightMode::FinalCleanup => FightMode::PlayerSelection,
        };
        self.transition_fight_mode(next_mode)
    }

    /// Check if state transition is valid without performing it
    pub fn can_transition_game_mode(&self, new_mode: GameMode) -> bool {
        let mut temp_state = self.clone();
        matches!(temp_state.transition_game_mode(new_mode), StateTransition::Success)
    }

    /// Get current state as a compact string for debugging
    pub fn state_string(&self) -> String {
        format!("G{:?}F{:?}R{:?}#{}",
            self.game_mode as u16,
            self.fight_mode as u16,
            self.round_mode as u16,
            self.frame_count
        )
    }
}
