//! # Geometry Types
//!
//! Geometric types and operations for the Street Fighter II engine.
//! These types match the original C99 structures for compatibility.

use crate::fixed_point::*;
use serde::{Deserialize, Serialize};
use bytemuck::{Pod, Zeroable};

/// Point with fixed-point coordinates
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Serialize, Deserialize)]
pub struct FixedPoint {
    pub x: Fixed16_16,
    pub y: Fixed16_16,
}

impl FixedPoint {
    pub const ZERO: Self = Self {
        x: Fixed16_16::ZERO,
        y: Fixed16_16::ZERO,
    };
    
    pub fn new(x: Fixed16_16, y: Fixed16_16) -> Self {
        Self { x, y }
    }
    
    pub fn from_ints(x: i32, y: i32) -> Self {
        Self {
            x: Fixed16_16::from_int(x),
            y: Fixed16_16::from_int(y),
        }
    }
    
    pub fn from_floats(x: f32, y: f32) -> Self {
        Self {
            x: Fixed16_16::from_f32(x),
            y: Fixed16_16::from_f32(y),
        }
    }
    
    pub fn distance_to(self, other: Self) -> Fixed16_16 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        (dx * dx + dy * dy).sqrt()
    }
}

/// Rectangle with fixed-point coordinates
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct FixedRect {
    pub x: Fixed16_16,
    pub y: Fixed16_16,
    pub width: Fixed16_16,
    pub height: Fixed16_16,
}

impl FixedRect {
    pub fn new(x: Fixed16_16, y: Fixed16_16, width: Fixed16_16, height: Fixed16_16) -> Self {
        Self { x, y, width, height }
    }
    
    pub fn from_ints(x: i32, y: i32, width: i32, height: i32) -> Self {
        Self {
            x: Fixed16_16::from_int(x),
            y: Fixed16_16::from_int(y),
            width: Fixed16_16::from_int(width),
            height: Fixed16_16::from_int(height),
        }
    }
    
    pub fn left(self) -> Fixed16_16 {
        self.x
    }
    
    pub fn right(self) -> Fixed16_16 {
        self.x + self.width
    }
    
    pub fn top(self) -> Fixed16_16 {
        self.y
    }
    
    pub fn bottom(self) -> Fixed16_16 {
        self.y + self.height
    }
    
    pub fn center(self) -> FixedPoint {
        FixedPoint::new(
            self.x + self.width / Fixed16_16::from_int(2),
            self.y + self.height / Fixed16_16::from_int(2),
        )
    }
    
    pub fn contains_point(self, point: FixedPoint) -> bool {
        point.x >= self.left() && point.x <= self.right() &&
        point.y >= self.top() && point.y <= self.bottom()
    }
    
    pub fn intersects(self, other: Self) -> bool {
        self.left() < other.right() && self.right() > other.left() &&
        self.top() < other.bottom() && self.bottom() > other.top()
    }
}

/// Integer-based rectangle for pixel-perfect operations
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct IntRect {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

impl IntRect {
    pub fn new(x: i32, y: i32, width: u32, height: u32) -> Self {
        Self { x, y, width, height }
    }
    
    pub fn left(self) -> i32 {
        self.x
    }
    
    pub fn right(self) -> i32 {
        self.x + self.width as i32
    }
    
    pub fn top(self) -> i32 {
        self.y
    }
    
    pub fn bottom(self) -> i32 {
        self.y + self.height as i32
    }
    
    pub fn contains_point(self, x: i32, y: i32) -> bool {
        x >= self.left() && x < self.right() &&
        y >= self.top() && y < self.bottom()
    }
    
    pub fn intersects(self, other: Self) -> bool {
        self.left() < other.right() && self.right() > other.left() &&
        self.top() < other.bottom() && self.bottom() > other.top()
    }
}

/// Vector with fixed-point components
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct FixedVector {
    pub x: Fixed16_16,
    pub y: Fixed16_16,
}

impl FixedVector {
    pub const ZERO: Self = Self {
        x: Fixed16_16::ZERO,
        y: Fixed16_16::ZERO,
    };

    pub fn new(x: Fixed16_16, y: Fixed16_16) -> Self {
        Self { x, y }
    }

    pub fn from_floats(x: f32, y: f32) -> Self {
        Self {
            x: Fixed16_16::from_f32(x),
            y: Fixed16_16::from_f32(y),
        }
    }

    pub fn magnitude(self) -> Fixed16_16 {
        (self.x * self.x + self.y * self.y).sqrt()
    }

    pub fn normalize(self) -> Self {
        let mag = self.magnitude();
        if mag == Fixed16_16::ZERO {
            return Self::ZERO;
        }
        Self {
            x: self.x / mag,
            y: self.y / mag,
        }
    }

    pub fn dot(self, other: Self) -> Fixed16_16 {
        self.x * other.x + self.y * other.y
    }
}

// ============================================================================
// SF2 Original C99 Structure Equivalents
// ============================================================================

/// 8-bit signed point (equivalent to C99 Point8/POINT8)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct Point8 {
    pub x: i8,
    pub y: i8,
}

unsafe impl Pod for Point8 {}
unsafe impl Zeroable for Point8 {}

impl Point8 {
    pub const ZERO: Self = Self { x: 0, y: 0 };

    pub fn new(x: i8, y: i8) -> Self {
        Self { x, y }
    }
}

/// 16-bit signed point (equivalent to C99 Point16/POINT16)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct Point16 {
    pub x: i16,
    pub y: i16,
}

unsafe impl Pod for Point16 {}
unsafe impl Zeroable for Point16 {}

impl Point16 {
    pub const ZERO: Self = Self { x: 0, y: 0 };

    pub fn new(x: i16, y: i16) -> Self {
        Self { x, y }
    }
}

/// 8-bit size (equivalent to C99 Size8/SIZE8)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct Size8 {
    pub width: i8,
    pub height: i8,
}

unsafe impl Pod for Size8 {}
unsafe impl Zeroable for Size8 {}

impl Size8 {
    pub fn new(width: i8, height: i8) -> Self {
        Self { width, height }
    }
}

/// 8-bit rectangle (equivalent to C99 Rect8/RECT8)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct Rect8 {
    pub origin: Point8,
    pub size: Size8,
}

unsafe impl Pod for Rect8 {}
unsafe impl Zeroable for Rect8 {}

impl Rect8 {
    pub fn new(x: i8, y: i8, width: i8, height: i8) -> Self {
        Self {
            origin: Point8::new(x, y),
            size: Size8::new(width, height),
        }
    }

    pub fn left(self) -> i8 {
        self.origin.x
    }

    pub fn right(self) -> i8 {
        self.origin.x + self.size.width
    }

    pub fn top(self) -> i8 {
        self.origin.y
    }

    pub fn bottom(self) -> i8 {
        self.origin.y + self.size.height
    }

    pub fn contains_point(self, point: Point8) -> bool {
        point.x >= self.left() && point.x < self.right() &&
        point.y >= self.top() && point.y < self.bottom()
    }
}

/// 16-bit vector with 8.8 fixed-point components (equivalent to C99 Vect16/VECT16)
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
#[repr(C)]
pub struct Vect16 {
    pub x: Fixed8_8,
    pub y: Fixed8_8,
}

unsafe impl Pod for Vect16 {}
unsafe impl Zeroable for Vect16 {}

impl Vect16 {
    pub const ZERO: Self = Self {
        x: Fixed8_8::ZERO,
        y: Fixed8_8::ZERO,
    };

    pub fn new(x: Fixed8_8, y: Fixed8_8) -> Self {
        Self { x, y }
    }

    pub fn from_ints(x: i8, y: i8) -> Self {
        Self {
            x: Fixed8_8::from_int(x),
            y: Fixed8_8::from_int(y),
        }
    }

    pub fn from_floats(x: f32, y: f32) -> Self {
        Self {
            x: Fixed8_8::from_f32(x),
            y: Fixed8_8::from_f32(y),
        }
    }
}

/// 16.16 fixed-point vector (equivalent to C99 VectFP16)
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
#[repr(C)]
pub struct VectFp16 {
    pub x: Fixed16_16,
    pub y: Fixed16_16,
}

unsafe impl Pod for VectFp16 {}
unsafe impl Zeroable for VectFp16 {}

impl VectFp16 {
    pub const ZERO: Self = Self {
        x: Fixed16_16::ZERO,
        y: Fixed16_16::ZERO,
    };

    pub fn new(x: Fixed16_16, y: Fixed16_16) -> Self {
        Self { x, y }
    }

    pub fn from_ints(x: i32, y: i32) -> Self {
        Self {
            x: Fixed16_16::from_int(x),
            y: Fixed16_16::from_int(y),
        }
    }

    pub fn from_floats(x: f32, y: f32) -> Self {
        Self {
            x: Fixed16_16::from_f32(x),
            y: Fixed16_16::from_f32(y),
        }
    }
}

/// Trajectory with velocity and acceleration (equivalent to C99 Traj16/TRAJ16)
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
#[repr(C)]
pub struct Traj16 {
    pub vel: Vect16,
    pub acl: Vect16,
}

unsafe impl Pod for Traj16 {}
unsafe impl Zeroable for Traj16 {}

impl Traj16 {
    pub const ZERO: Self = Self {
        vel: Vect16::ZERO,
        acl: Vect16::ZERO,
    };

    pub fn new(vel: Vect16, acl: Vect16) -> Self {
        Self { vel, acl }
    }
}

/// 2D trajectory with separate X/Y components (equivalent to C99 Traj2D_16/TRAJ_2D_16)
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
#[repr(C)]
pub struct Traj2d16 {
    pub vel_x: Fixed8_8,
    pub acl_x: Fixed8_8,
    pub vel_y: Fixed8_8,
    pub acl_y: Fixed8_8,
}

unsafe impl Pod for Traj2d16 {}
unsafe impl Zeroable for Traj2d16 {}

impl Traj2d16 {
    pub const ZERO: Self = Self {
        vel_x: Fixed8_8::ZERO,
        acl_x: Fixed8_8::ZERO,
        vel_y: Fixed8_8::ZERO,
        acl_y: Fixed8_8::ZERO,
    };

    pub fn new(vel_x: Fixed8_8, acl_x: Fixed8_8, vel_y: Fixed8_8, acl_y: Fixed8_8) -> Self {
        Self { vel_x, acl_x, vel_y, acl_y }
    }
}

/// Adjustment structure for sprite positioning (equivalent to C99 adjust)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct Adjust {
    pub x: i8,
    pub y: i8,
    pub flips: u8,
    pub frame: u8,
}

unsafe impl Pod for Adjust {}
unsafe impl Zeroable for Adjust {}

impl Adjust {
    pub fn new(x: i8, y: i8, flips: u8, frame: u8) -> Self {
        Self { x, y, flips, frame }
    }
}

/// Small adjustment structure (equivalent to C99 smalladjust)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct SmallAdjust {
    pub x: i8,
    pub y: i8,
}

unsafe impl Pod for SmallAdjust {}
unsafe impl Zeroable for SmallAdjust {}

impl SmallAdjust {
    pub fn new(x: i8, y: i8) -> Self {
        Self { x, y }
    }
}

/// Dual byte structure (equivalent to C99 DUAL)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct Dual {
    pub p0: u8,
    pub p1: u8,
}

unsafe impl Pod for Dual {}
unsafe impl Zeroable for Dual {}

impl Dual {
    pub fn new(p0: u8, p1: u8) -> Self {
        Self { p0, p1 }
    }

    pub fn from_u16(value: u16) -> Self {
        Self {
            p0: (value & 0xFF) as u8,
            p1: (value >> 8) as u8,
        }
    }

    pub fn to_u16(self) -> u16 {
        (self.p1 as u16) << 8 | (self.p0 as u16)
    }
}

/// Tile attribute pair (equivalent to C99 TileAttributePair)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(C)]
pub struct TileAttributePair {
    pub tile_id: u16,
    pub attribute: u16,
}

unsafe impl Pod for TileAttributePair {}
unsafe impl Zeroable for TileAttributePair {}

impl TileAttributePair {
    pub fn new(tile_id: u16, attribute: u16) -> Self {
        Self { tile_id, attribute }
    }
}

// ============================================================================
// Macros for C99 compatibility
// ============================================================================

/// Create a VectFp16 with integer coordinates (equivalent to MAKE_VECTFP16)
#[macro_export]
macro_rules! make_vectfp16 {
    ($x:expr, $y:expr) => {
        VectFp16::new(
            Fixed16_16::from_int($x),
            Fixed16_16::from_int($y)
        )
    };
}

/// Set VectFp16 integer parts (equivalent to SET_VECTFP16)
#[macro_export]
macro_rules! set_vectfp16 {
    ($vect:expr, $x:expr, $y:expr) => {
        $vect.x.set_integer_part($x);
        $vect.y.set_integer_part($y);
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_point8() {
        let p = Point8::new(10, -5);
        assert_eq!(p.x, 10);
        assert_eq!(p.y, -5);
    }

    #[test]
    fn test_rect8() {
        let rect = Rect8::new(10, 20, 30, 40);
        assert_eq!(rect.left(), 10);
        assert_eq!(rect.right(), 40);
        assert_eq!(rect.top(), 20);
        assert_eq!(rect.bottom(), 60);

        let point_inside = Point8::new(25, 35);
        let point_outside = Point8::new(5, 15);

        assert!(rect.contains_point(point_inside));
        assert!(!rect.contains_point(point_outside));
    }

    #[test]
    fn test_vect16() {
        let v = Vect16::from_ints(3, 4);
        assert_eq!(v.x.integer_part(), 3);
        assert_eq!(v.y.integer_part(), 4);
    }

    #[test]
    fn test_dual() {
        let dual = Dual::from_u16(0x1234);
        assert_eq!(dual.p0, 0x34);
        assert_eq!(dual.p1, 0x12);
        assert_eq!(dual.to_u16(), 0x1234);
    }

    #[test]
    fn test_macros() {
        let vect = make_vectfp16!(5, 10);
        assert_eq!(vect.x.integer_part(), 5);
        assert_eq!(vect.y.integer_part(), 10);
    }
}
