//! # Hit/Hurt Box Management
//!
//! Comprehensive hitbox and hurtbox management system with environment variable
//! configuration for all fighter collision properties.

use crate::collision_shapes::*;
use crate::collision_config::CollisionConfig;
use crate::geometry::*;
use crate::fighter::FighterId;
use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Hitbox collection for a fighter (equivalent to C99 hitboxes struct)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FighterHitboxes {
    /// Head hurtbox
    pub head: Option<Hurtbox>,
    /// Body hurtbox
    pub body: Option<Hurtbox>,
    /// Foot hurtbox
    pub foot: Option<Hurtbox>,
    /// Weak point hurtbox (for special vulnerabilities)
    pub weak: Option<Hurtbox>,
    /// Active attack hitbox
    pub active: Option<ActiveHitbox>,
    /// Pushbox for character collision
    pub pushbox: Option<Pushbox>,
}

impl FighterHitboxes {
    pub const EMPTY: Self = Self {
        head: None,
        body: None,
        foot: None,
        weak: None,
        active: None,
        pushbox: None,
    };
    
    pub fn new() -> Self {
        Self::EMPTY
    }
    
    /// Apply collision configuration to all hitboxes
    pub fn apply_config(&mut self, config: &CollisionConfig, fighter_id: u8) {
        if let Some(ref mut head) = self.head {
            head.apply_config(config, fighter_id);
        }
        if let Some(ref mut body) = self.body {
            body.apply_config(config, fighter_id);
        }
        if let Some(ref mut foot) = self.foot {
            foot.apply_config(config, fighter_id);
        }
        if let Some(ref mut weak) = self.weak {
            weak.apply_config(config, fighter_id);
        }
        if let Some(ref mut active) = self.active {
            active.apply_config(config, fighter_id);
        }
        if let Some(ref mut pushbox) = self.pushbox {
            pushbox.apply_config(config);
        }
    }
    
    /// Get all active hurtboxes
    pub fn get_hurtboxes(&self) -> Vec<&Hurtbox> {
        let mut hurtboxes = Vec::new();
        if let Some(ref head) = self.head {
            if head.is_active() {
                hurtboxes.push(head);
            }
        }
        if let Some(ref body) = self.body {
            if body.is_active() {
                hurtboxes.push(body);
            }
        }
        if let Some(ref foot) = self.foot {
            if foot.is_active() {
                hurtboxes.push(foot);
            }
        }
        if let Some(ref weak) = self.weak {
            if weak.is_active() {
                hurtboxes.push(weak);
            }
        }
        hurtboxes
    }
    
    /// Get active hitbox if any
    pub fn get_active_hitbox(&self) -> Option<&ActiveHitbox> {
        self.active.as_ref().filter(|h| h.is_active())
    }
    
    /// Get pushbox if active
    pub fn get_pushbox(&self) -> Option<&Pushbox> {
        self.pushbox.as_ref().filter(|p| p.is_active())
    }
}

/// Hitbox data for a specific animation frame
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FrameHitboxData {
    /// Frame number this data applies to
    pub frame: u16,
    /// Hitboxes for this frame
    pub hitboxes: FighterHitboxes,
    /// Whether this frame has active attack hitboxes
    pub has_active_hitbox: bool,
    /// Attack properties for this frame
    pub attack_properties: Option<AttackProperties>,
}

/// Attack properties for active hitboxes
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct AttackProperties {
    /// Base damage
    pub damage: u8,
    /// Stun/hitstun frames
    pub stun: u16,
    /// Knockback force
    pub knockback: Point16,
    /// Attack priority
    pub priority: u8,
    /// Cancel window (frames)
    pub cancel_window: u16,
}

/// Animation hitbox data for a fighter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnimationHitboxes {
    /// Animation ID
    pub animation_id: u16,
    /// Frame-by-frame hitbox data
    pub frames: HashMap<u16, FrameHitboxData>,
    /// Default hitboxes when no frame-specific data exists
    pub default_hitboxes: FighterHitboxes,
}

impl AnimationHitboxes {
    pub fn new(animation_id: u16) -> Self {
        Self {
            animation_id,
            frames: HashMap::new(),
            default_hitboxes: FighterHitboxes::new(),
        }
    }
    
    /// Get hitboxes for a specific frame
    pub fn get_frame_hitboxes(&self, frame: u16) -> &FighterHitboxes {
        self.frames
            .get(&frame)
            .map(|data| &data.hitboxes)
            .unwrap_or(&self.default_hitboxes)
    }
    
    /// Set hitboxes for a specific frame
    pub fn set_frame_hitboxes(&mut self, frame: u16, hitboxes: FighterHitboxes) {
        let frame_data = FrameHitboxData {
            frame,
            has_active_hitbox: hitboxes.get_active_hitbox().is_some(),
            attack_properties: None, // Set separately if needed
            hitboxes,
        };
        self.frames.insert(frame, frame_data);
    }
    
    /// Apply collision configuration to all frames
    pub fn apply_config(&mut self, config: &CollisionConfig, fighter_id: u8) {
        self.default_hitboxes.apply_config(config, fighter_id);
        for frame_data in self.frames.values_mut() {
            frame_data.hitboxes.apply_config(config, fighter_id);
        }
    }
}

/// Complete hitbox manager for a fighter
#[derive(Debug, Component, Serialize, Deserialize)]
pub struct HitboxManager {
    /// Fighter ID
    pub fighter_id: FighterId,
    /// Current animation ID
    pub current_animation: u16,
    /// Current frame within animation
    pub current_frame: u16,
    /// Animation hitbox data
    pub animations: HashMap<u16, AnimationHitboxes>,
    /// Global hitbox modifiers
    pub global_modifiers: HitboxModifiers,
    /// Whether hitboxes are currently active
    pub active: bool,
}

/// Global hitbox modifiers
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct HitboxModifiers {
    /// Size multiplier
    pub size_multiplier: f32,
    /// Damage multiplier
    pub damage_multiplier: f32,
    /// Position offset
    pub position_offset: Point8,
    /// Temporary invincibility frames
    pub invincibility_frames: u16,
}

impl Default for HitboxModifiers {
    fn default() -> Self {
        Self {
            size_multiplier: 1.0,
            damage_multiplier: 1.0,
            position_offset: Point8::ZERO,
            invincibility_frames: 0,
        }
    }
}

impl HitboxManager {
    /// Create a new hitbox manager
    pub fn new(fighter_id: FighterId) -> Self {
        Self {
            fighter_id,
            current_animation: 0,
            current_frame: 0,
            animations: HashMap::new(),
            global_modifiers: HitboxModifiers::default(),
            active: true,
        }
    }
    
    /// Get current hitboxes
    pub fn get_current_hitboxes(&self) -> Option<FighterHitboxes> {
        if !self.active {
            return None;
        }
        
        let animation = self.animations.get(&self.current_animation)?;
        let mut hitboxes = animation.get_frame_hitboxes(self.current_frame).clone();
        
        // Apply global modifiers
        self.apply_global_modifiers(&mut hitboxes);
        
        Some(hitboxes)
    }
    
    /// Set current animation and frame
    pub fn set_animation_frame(&mut self, animation_id: u16, frame: u16) {
        self.current_animation = animation_id;
        self.current_frame = frame;
    }
    
    /// Add animation hitbox data
    pub fn add_animation(&mut self, animation: AnimationHitboxes) {
        self.animations.insert(animation.animation_id, animation);
    }
    
    /// Apply collision configuration to all animations
    pub fn apply_config(&mut self, config: &CollisionConfig) {
        let fighter_id = self.fighter_id as u8;
        for animation in self.animations.values_mut() {
            animation.apply_config(config, fighter_id);
        }
    }
    
    /// Set global modifiers
    pub fn set_global_modifiers(&mut self, modifiers: HitboxModifiers) {
        self.global_modifiers = modifiers;
    }
    
    /// Apply temporary invincibility
    pub fn set_invincibility(&mut self, frames: u16) {
        self.global_modifiers.invincibility_frames = frames;
    }
    
    /// Update invincibility frames
    pub fn update_invincibility(&mut self) {
        if self.global_modifiers.invincibility_frames > 0 {
            self.global_modifiers.invincibility_frames -= 1;
        }
    }
    
    /// Check if currently invincible
    pub fn is_invincible(&self) -> bool {
        self.global_modifiers.invincibility_frames > 0
    }
    
    /// Apply global modifiers to hitboxes
    fn apply_global_modifiers(&self, hitboxes: &mut FighterHitboxes) {
        // Apply size multiplier
        if self.global_modifiers.size_multiplier != 1.0 {
            self.apply_size_multiplier(hitboxes, self.global_modifiers.size_multiplier);
        }
        
        // Apply position offset
        if self.global_modifiers.position_offset != Point8::ZERO {
            self.apply_position_offset(hitboxes, self.global_modifiers.position_offset);
        }
        
        // Apply damage multiplier to active hitbox
        if let Some(ref mut active) = hitboxes.active {
            if self.global_modifiers.damage_multiplier != 1.0 {
                active.damage = (active.damage as f32 * self.global_modifiers.damage_multiplier) as u8;
            }
        }
        
        // Remove hitboxes if invincible
        if self.is_invincible() {
            hitboxes.head = None;
            hitboxes.body = None;
            hitboxes.foot = None;
            hitboxes.weak = None;
        }
    }
    
    /// Apply size multiplier to all collision shapes
    fn apply_size_multiplier(&self, hitboxes: &mut FighterHitboxes, multiplier: f32) {
        if let Some(ref mut head) = hitboxes.head {
            head.shape.width = (head.shape.width as f32 * multiplier) as i8;
            head.shape.height = (head.shape.height as f32 * multiplier) as i8;
        }
        if let Some(ref mut body) = hitboxes.body {
            body.shape.width = (body.shape.width as f32 * multiplier) as i8;
            body.shape.height = (body.shape.height as f32 * multiplier) as i8;
        }
        if let Some(ref mut foot) = hitboxes.foot {
            foot.shape.width = (foot.shape.width as f32 * multiplier) as i8;
            foot.shape.height = (foot.shape.height as f32 * multiplier) as i8;
        }
        if let Some(ref mut weak) = hitboxes.weak {
            weak.shape.width = (weak.shape.width as f32 * multiplier) as i8;
            weak.shape.height = (weak.shape.height as f32 * multiplier) as i8;
        }
        if let Some(ref mut active) = hitboxes.active {
            active.shape.width = (active.shape.width as f32 * multiplier) as i8;
            active.shape.height = (active.shape.height as f32 * multiplier) as i8;
        }
        if let Some(ref mut pushbox) = hitboxes.pushbox {
            pushbox.shape.width = (pushbox.shape.width as f32 * multiplier) as i8;
            pushbox.shape.height = (pushbox.shape.height as f32 * multiplier) as i8;
        }
    }
    
    /// Apply position offset to all collision shapes
    fn apply_position_offset(&self, hitboxes: &mut FighterHitboxes, offset: Point8) {
        if let Some(ref mut head) = hitboxes.head {
            head.shape.x = head.shape.x.saturating_add(offset.x);
            head.shape.y = head.shape.y.saturating_add(offset.y);
        }
        if let Some(ref mut body) = hitboxes.body {
            body.shape.x = body.shape.x.saturating_add(offset.x);
            body.shape.y = body.shape.y.saturating_add(offset.y);
        }
        if let Some(ref mut foot) = hitboxes.foot {
            foot.shape.x = foot.shape.x.saturating_add(offset.x);
            foot.shape.y = foot.shape.y.saturating_add(offset.y);
        }
        if let Some(ref mut weak) = hitboxes.weak {
            weak.shape.x = weak.shape.x.saturating_add(offset.x);
            weak.shape.y = weak.shape.y.saturating_add(offset.y);
        }
        if let Some(ref mut active) = hitboxes.active {
            active.shape.x = active.shape.x.saturating_add(offset.x);
            active.shape.y = active.shape.y.saturating_add(offset.y);
        }
        if let Some(ref mut pushbox) = hitboxes.pushbox {
            pushbox.shape.x = pushbox.shape.x.saturating_add(offset.x);
            pushbox.shape.y = pushbox.shape.y.saturating_add(offset.y);
        }
    }
}
