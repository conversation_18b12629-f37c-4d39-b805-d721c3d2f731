//! # Input Configuration
//! 
//! Environment variable-based configuration for input system behavior.
//! Allows fine-tuning of input timing, special move detection, and sensitivity.

use serde::{Deserialize, Serialize};
use std::env;

/// Configuration for input system behavior
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct InputConfig {
    /// Special move input window (frames)
    pub special_move_window: u32,
    /// Charge time required for charge moves (frames)
    pub charge_time_required: u32,
    /// Input buffer size (number of frames to keep)
    pub input_buffer_size: usize,
    /// Direction leniency for special moves
    pub direction_leniency: bool,
    /// Charge move leniency (allows slight direction deviation)
    pub charge_leniency: bool,
    /// Input lag compensation (frames)
    pub input_lag_compensation: u32,
    /// Button sensitivity (0.0 to 1.0)
    pub button_sensitivity: f32,
    /// Joystick deadzone (0.0 to 1.0)
    pub joystick_deadzone: f32,
    /// Enable input display for debugging
    pub debug_input_display: bool,
    /// Enable special move detection logging
    pub debug_special_moves: bool,
}

impl Default for InputConfig {
    fn default() -> Self {
        Self {
            special_move_window: 20,
            charge_time_required: 45,
            input_buffer_size: 16,
            direction_leniency: true,
            charge_leniency: false,
            input_lag_compensation: 0,
            button_sensitivity: 1.0,
            joystick_deadzone: 0.1,
            debug_input_display: false,
            debug_special_moves: false,
        }
    }
}

impl InputConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        let mut config = Self::default();
        
        // Special move timing
        if let Ok(val) = env::var("SF2_SPECIAL_MOVE_WINDOW") {
            if let Ok(frames) = val.parse::<u32>() {
                config.special_move_window = frames.clamp(5, 60);
            }
        }
        
        if let Ok(val) = env::var("SF2_CHARGE_TIME") {
            if let Ok(frames) = val.parse::<u32>() {
                config.charge_time_required = frames.clamp(15, 120);
            }
        }
        
        if let Ok(val) = env::var("SF2_INPUT_BUFFER_SIZE") {
            if let Ok(size) = val.parse::<usize>() {
                config.input_buffer_size = size.clamp(8, 64);
            }
        }
        
        // Leniency settings
        if let Ok(val) = env::var("SF2_DIRECTION_LENIENCY") {
            config.direction_leniency = val.to_lowercase() == "true" || val == "1";
        }
        
        if let Ok(val) = env::var("SF2_CHARGE_LENIENCY") {
            config.charge_leniency = val.to_lowercase() == "true" || val == "1";
        }
        
        // Input lag compensation
        if let Ok(val) = env::var("SF2_INPUT_LAG_COMPENSATION") {
            if let Ok(frames) = val.parse::<u32>() {
                config.input_lag_compensation = frames.clamp(0, 10);
            }
        }
        
        // Sensitivity settings
        if let Ok(val) = env::var("SF2_BUTTON_SENSITIVITY") {
            if let Ok(sensitivity) = val.parse::<f32>() {
                config.button_sensitivity = sensitivity.clamp(0.1, 2.0);
            }
        }
        
        if let Ok(val) = env::var("SF2_JOYSTICK_DEADZONE") {
            if let Ok(deadzone) = val.parse::<f32>() {
                config.joystick_deadzone = deadzone.clamp(0.0, 0.5);
            }
        }
        
        // Debug settings
        if let Ok(val) = env::var("SF2_DEBUG_INPUT_DISPLAY") {
            config.debug_input_display = val.to_lowercase() == "true" || val == "1";
        }
        
        if let Ok(val) = env::var("SF2_DEBUG_SPECIAL_MOVES") {
            config.debug_special_moves = val.to_lowercase() == "true" || val == "1";
        }
        
        config
    }
    
    /// Get configuration with custom overrides
    pub fn with_overrides(mut self, overrides: InputConfigOverrides) -> Self {
        if let Some(window) = overrides.special_move_window {
            self.special_move_window = window;
        }
        if let Some(charge_time) = overrides.charge_time_required {
            self.charge_time_required = charge_time;
        }
        if let Some(buffer_size) = overrides.input_buffer_size {
            self.input_buffer_size = buffer_size;
        }
        if let Some(leniency) = overrides.direction_leniency {
            self.direction_leniency = leniency;
        }
        if let Some(charge_leniency) = overrides.charge_leniency {
            self.charge_leniency = charge_leniency;
        }
        if let Some(lag_comp) = overrides.input_lag_compensation {
            self.input_lag_compensation = lag_comp;
        }
        if let Some(sensitivity) = overrides.button_sensitivity {
            self.button_sensitivity = sensitivity;
        }
        if let Some(deadzone) = overrides.joystick_deadzone {
            self.joystick_deadzone = deadzone;
        }
        if let Some(debug_display) = overrides.debug_input_display {
            self.debug_input_display = debug_display;
        }
        if let Some(debug_moves) = overrides.debug_special_moves {
            self.debug_special_moves = debug_moves;
        }
        
        self
    }
    
    /// Validate configuration values
    pub fn validate(&self) -> Result<(), String> {
        if self.special_move_window < 5 || self.special_move_window > 60 {
            return Err("Special move window must be between 5 and 60 frames".to_string());
        }
        
        if self.charge_time_required < 15 || self.charge_time_required > 120 {
            return Err("Charge time must be between 15 and 120 frames".to_string());
        }
        
        if self.input_buffer_size < 8 || self.input_buffer_size > 64 {
            return Err("Input buffer size must be between 8 and 64".to_string());
        }
        
        if self.button_sensitivity < 0.1 || self.button_sensitivity > 2.0 {
            return Err("Button sensitivity must be between 0.1 and 2.0".to_string());
        }
        
        if self.joystick_deadzone < 0.0 || self.joystick_deadzone > 0.5 {
            return Err("Joystick deadzone must be between 0.0 and 0.5".to_string());
        }
        
        Ok(())
    }
    
    /// Print current configuration (for debugging)
    pub fn print_config(&self) {
        println!("SF2 Input Configuration:");
        println!("  Special Move Window: {} frames", self.special_move_window);
        println!("  Charge Time Required: {} frames", self.charge_time_required);
        println!("  Input Buffer Size: {} frames", self.input_buffer_size);
        println!("  Direction Leniency: {}", self.direction_leniency);
        println!("  Charge Leniency: {}", self.charge_leniency);
        println!("  Input Lag Compensation: {} frames", self.input_lag_compensation);
        println!("  Button Sensitivity: {:.2}", self.button_sensitivity);
        println!("  Joystick Deadzone: {:.2}", self.joystick_deadzone);
        println!("  Debug Input Display: {}", self.debug_input_display);
        println!("  Debug Special Moves: {}", self.debug_special_moves);
    }
}

/// Optional overrides for input configuration
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct InputConfigOverrides {
    pub special_move_window: Option<u32>,
    pub charge_time_required: Option<u32>,
    pub input_buffer_size: Option<usize>,
    pub direction_leniency: Option<bool>,
    pub charge_leniency: Option<bool>,
    pub input_lag_compensation: Option<u32>,
    pub button_sensitivity: Option<f32>,
    pub joystick_deadzone: Option<f32>,
    pub debug_input_display: Option<bool>,
    pub debug_special_moves: Option<bool>,
}

/// Preset configurations for different play styles
impl InputConfig {
    /// Tournament-standard configuration
    pub fn tournament() -> Self {
        Self {
            special_move_window: 15,
            charge_time_required: 45,
            input_buffer_size: 12,
            direction_leniency: false,
            charge_leniency: false,
            input_lag_compensation: 0,
            button_sensitivity: 1.0,
            joystick_deadzone: 0.05,
            debug_input_display: false,
            debug_special_moves: false,
        }
    }
    
    /// Casual/beginner-friendly configuration
    pub fn casual() -> Self {
        Self {
            special_move_window: 25,
            charge_time_required: 40,
            input_buffer_size: 20,
            direction_leniency: true,
            charge_leniency: true,
            input_lag_compensation: 2,
            button_sensitivity: 1.2,
            joystick_deadzone: 0.15,
            debug_input_display: false,
            debug_special_moves: false,
        }
    }
    
    /// Development/debugging configuration
    pub fn debug() -> Self {
        Self {
            special_move_window: 30,
            charge_time_required: 30,
            input_buffer_size: 32,
            direction_leniency: true,
            charge_leniency: true,
            input_lag_compensation: 0,
            button_sensitivity: 1.0,
            joystick_deadzone: 0.1,
            debug_input_display: true,
            debug_special_moves: true,
        }
    }
}

/// Environment variable documentation
pub const ENV_VAR_DOCS: &str = r#"
SF2 Input System Environment Variables:

Timing Configuration:
  SF2_SPECIAL_MOVE_WINDOW     - Frames for special move input (5-60, default: 20)
  SF2_CHARGE_TIME             - Frames required for charge moves (15-120, default: 45)
  SF2_INPUT_BUFFER_SIZE       - Input history size (8-64, default: 16)

Leniency Settings:
  SF2_DIRECTION_LENIENCY      - Allow imprecise directions (true/false, default: true)
  SF2_CHARGE_LENIENCY         - Allow charge direction deviation (true/false, default: false)

Performance Tuning:
  SF2_INPUT_LAG_COMPENSATION  - Frames to compensate input lag (0-10, default: 0)
  SF2_BUTTON_SENSITIVITY      - Button press sensitivity (0.1-2.0, default: 1.0)
  SF2_JOYSTICK_DEADZONE       - Joystick deadzone (0.0-0.5, default: 0.1)

Debug Options:
  SF2_DEBUG_INPUT_DISPLAY     - Show input display (true/false, default: false)
  SF2_DEBUG_SPECIAL_MOVES     - Log special move detection (true/false, default: false)

Example usage:
  export SF2_SPECIAL_MOVE_WINDOW=25
  export SF2_CHARGE_TIME=40
  export SF2_DIRECTION_LENIENCY=true
  export SF2_DEBUG_INPUT_DISPLAY=true
"#;
