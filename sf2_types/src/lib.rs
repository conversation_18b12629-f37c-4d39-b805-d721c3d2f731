//! # SF2 Types
//!
//! Core data types for the Street Fighter II engine, including fixed-point
//! arithmetic, geometric types, and game-specific data structures.
//!
//! This module provides exact Rust equivalents to the original C99 Street Fighter II engine types,
//! ensuring compatibility while leveraging Rust's safety and performance features.

pub mod fixed_point;
pub mod geometry;
pub mod fighter;
pub mod input;
pub mod endian;
pub mod constants;

pub use fixed_point::*;
pub use geometry::*;
pub use fighter::*;
pub use input::*;
pub use endian::*;
pub use constants::*;

/// Re-export commonly used types
pub use glam::{Vec2, Vec3, IVec2, UVec2};

/// Boolean type for game logic
pub type FBBool = bool;

/// Tile and palette types
pub type TileWord = u16;
pub type PaletteWord = u16;

/// CPS coordinate type
pub type CpsCoord = u16;

// Additional type aliases matching C99 definitions for compatibility
pub type FIXED16_16 = Fixed16_16;
pub type FIXED8_8 = Fixed8_8;
pub type POINT8 = Point8;
pub type POINT16 = Point16;
pub type SIZE8 = Size8;
pub type RECT8 = Rect8;
pub type VECT16 = Vect16;
pub type VECTFP16 = VectFp16;
pub type TRAJ16 = Traj16;
#[allow(non_camel_case_types)]
pub type TRAJ_2D_16 = Traj2d16;
pub type DUAL = Dual;
