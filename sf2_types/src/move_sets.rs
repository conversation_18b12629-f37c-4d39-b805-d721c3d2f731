//! # Move Sets
//!
//! Data-driven move set definitions for all Street Fighter II characters.
//! This module contains the frame data, hitboxes, and properties for every move.

use crate::fixed_point::*;
use crate::geometry::*;
use crate::fighter::*;
use crate::character_traits::*;
use crate::input::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Complete move set for a character
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MoveSet {
    pub character_id: FighterId,
    pub normal_moves: HashMap<MoveId, MoveDefinition>,
    pub special_moves: HashMap<SpecialMoveId, SpecialMoveDefinition>,
    pub throw_moves: HashMap<ThrowId, ThrowDefinition>,
}

/// Definition of a single move
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MoveDefinition {
    pub move_id: MoveId,
    pub name: String,
    pub frame_data: FrameData,
    pub animation_frames: Vec<AnimationFrame>,
    pub input_requirements: InputRequirement,
    pub cancel_options: Vec<CancelOption>,
    pub properties: MoveProperties,
}

/// Special move definition with command inputs
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SpecialMoveDefinition {
    pub move_id: SpecialMoveId,
    pub name: String,
    pub command_input: CommandInput,
    pub frame_data: FrameData,
    pub animation_frames: Vec<AnimationFrame>,
    pub meter_cost: u16,
    pub properties: SpecialMoveProperties,
    pub projectile_data: Option<ProjectileData>,
}

/// Throw move definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThrowDefinition {
    pub throw_id: ThrowId,
    pub name: String,
    pub range: u8,
    pub damage: u16,
    pub animation_frames: Vec<AnimationFrame>,
    pub tech_window: u8, // frames where throw can be teched
}

/// Animation frame with hitbox/hurtbox data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnimationFrame {
    pub frame_number: u16,
    pub duration: u16, // frames to display this frame
    pub sprite_id: u16,
    pub hitboxes: Vec<HitboxData>,
    pub hurtboxes: Vec<HurtboxData>,
    pub pushbox: Option<Rect8>,
    pub offset: Point8,
    pub effects: Vec<FrameEffect>,
}

/// Effects that occur on specific frames
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FrameEffect {
    PlaySound(u16),
    SpawnEffect(EffectId, Point8),
    SetVelocity(Fixed8_8, Fixed8_8),
    SpawnProjectile(ProjectileData),
    ScreenShake(u8),
}

/// Input requirement for moves
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InputRequirement {
    /// Simple button press
    Button(ButtonFlags),
    /// Direction + button
    DirectionButton(InputDirection, ButtonFlags),
    /// Charge move (charge direction, release direction, button)
    Charge(InputDirection, InputDirection, ButtonFlags, u8), // frames to charge
    /// Complex command input
    Command(CommandInput),
}

/// Command input for special moves
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandInput {
    pub sequence: Vec<InputDirection>,
    pub button: ButtonFlags,
    pub max_input_time: u8, // frames to complete input
    pub charge_time: Option<u8>, // for charge moves
}

/// Move cancellation options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CancelOption {
    /// Can cancel into special moves
    Special(Vec<SpecialMoveId>),
    /// Can cancel into super moves
    Super(Vec<SuperMoveId>),
    /// Can cancel on hit only
    OnHitOnly(Vec<MoveId>),
    /// Can cancel on block only
    OnBlockOnly(Vec<MoveId>),
}

/// Move properties
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MoveProperties {
    pub can_be_blocked: bool,
    pub overhead: bool, // must be blocked standing
    pub low: bool, // must be blocked crouching
    pub air_only: bool,
    pub ground_only: bool,
    pub counter_hit_bonus: u16,
    pub chip_damage: u16,
    pub meter_gain_on_hit: u16,
    pub meter_gain_on_block: u16,
}

/// Special move properties
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpecialMoveProperties {
    pub base_properties: MoveProperties,
    pub invincibility_frames: u16,
    pub armor_frames: u16,
    pub projectile_invincible: bool,
    pub throw_invincible: bool,
    pub can_be_air_blocked: bool,
}

/// Projectile data for special moves
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectileData {
    pub sprite_id: u16,
    pub velocity: Vect16,
    pub lifetime: u16, // frames
    pub hitbox: HitboxData,
    pub hit_limit: u8, // how many times it can hit
    pub properties: ProjectileProperties,
}

/// Projectile properties
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectileProperties {
    pub piercing: bool, // goes through opponents
    pub reflecting: bool, // can be reflected
    pub destroyable: bool, // can be destroyed by other projectiles
    pub gravity_affected: bool,
    pub screen_bound: bool, // disappears at screen edge
}

/// Throw identifiers
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ThrowId {
    ForwardThrow,
    BackwardThrow,
    AirThrow,
    CommandThrow(SpecialMoveId), // for command grabs
}

/// Move set factory for creating character move sets
pub struct MoveSetFactory;

impl MoveSetFactory {
    /// Create move set for a specific character
    pub fn create_move_set(character_id: FighterId) -> MoveSet {
        match character_id {
            FighterId::Ryu => Self::create_ryu_move_set(),
            FighterId::Ken => Self::create_ken_move_set(),
            FighterId::ChunLi => Self::create_chunli_move_set(),
            FighterId::Guile => Self::create_guile_move_set(),
            FighterId::Blanka => Self::create_blanka_move_set(),
            FighterId::EHonda => Self::create_honda_move_set(),
            FighterId::Zangief => Self::create_zangief_move_set(),
            FighterId::Dhalsim => Self::create_dhalsim_move_set(),
            _ => Self::create_default_move_set(character_id),
        }
    }
    
    /// Create Ryu's move set with authentic frame data
    fn create_ryu_move_set() -> MoveSet {
        let mut normal_moves = HashMap::new();
        let mut special_moves = HashMap::new();
        let mut throw_moves = HashMap::new();
        
        // Ryu's standing light punch
        normal_moves.insert(MoveId::StandingLightPunch, MoveDefinition {
            move_id: MoveId::StandingLightPunch,
            name: "Standing Light Punch".to_string(),
            frame_data: FrameData {
                startup_frames: 3,
                active_frames: 2,
                recovery_frames: 6,
                block_stun: 8,
                hit_stun: 12,
                damage: 200, // 20 damage in SF2 scale
                stun_damage: 100,
                knockback_x: 100,
                knockback_y: 0,
            },
            animation_frames: vec![
                AnimationFrame {
                    frame_number: 0,
                    duration: 3,
                    sprite_id: 100,
                    hitboxes: vec![],
                    hurtboxes: vec![HurtboxData {
                        rect: Rect8::new(-20, -60, 40, 60),
                        vulnerability: 1.0,
                        body_part: BodyPart::Body,
                    }],
                    pushbox: Some(Rect8::new(-15, -50, 30, 50)),
                    offset: Point8::new(0, 0),
                    effects: vec![],
                },
                AnimationFrame {
                    frame_number: 1,
                    duration: 2,
                    sprite_id: 101,
                    hitboxes: vec![HitboxData {
                        rect: Rect8::new(10, -40, 25, 15),
                        damage: 200,
                        stun: 100,
                        knockback: Vect16::new(Fixed8_8::from_i16(100), Fixed8_8::ZERO),
                        hit_type: HitType::Normal,
                        priority: 3,
                    }],
                    hurtboxes: vec![HurtboxData {
                        rect: Rect8::new(-20, -60, 40, 60),
                        vulnerability: 1.0,
                        body_part: BodyPart::Body,
                    }],
                    pushbox: Some(Rect8::new(-15, -50, 30, 50)),
                    offset: Point8::new(0, 0),
                    effects: vec![FrameEffect::PlaySound(0x10)],
                },
            ],
            input_requirements: InputRequirement::Button(ButtonFlags::LIGHT_PUNCH),
            cancel_options: vec![
                CancelOption::Special(vec![
                    SpecialMoveId::Hadoken,
                    SpecialMoveId::Shoryuken,
                    SpecialMoveId::TatsumakiSenpukyaku,
                ]),
            ],
            properties: MoveProperties {
                can_be_blocked: true,
                overhead: false,
                low: false,
                air_only: false,
                ground_only: true,
                counter_hit_bonus: 50,
                chip_damage: 10,
                meter_gain_on_hit: 50,
                meter_gain_on_block: 25,
            },
        });
        
        // Ryu's Hadoken
        special_moves.insert(SpecialMoveId::Hadoken, SpecialMoveDefinition {
            move_id: SpecialMoveId::Hadoken,
            name: "Hadoken".to_string(),
            command_input: CommandInput {
                sequence: vec![
                    InputDirection::Down,
                    InputDirection::DownRight,
                    InputDirection::Right,
                ],
                button: ButtonFlags::LIGHT_PUNCH,
                max_input_time: 20,
                charge_time: None,
            },
            frame_data: FrameData {
                startup_frames: 13,
                active_frames: 0, // projectile handles active frames
                recovery_frames: 30,
                block_stun: 0,
                hit_stun: 0,
                damage: 0, // projectile handles damage
                stun_damage: 0,
                knockback_x: 0,
                knockback_y: 0,
            },
            animation_frames: vec![], // Will be populated with full animation data
            meter_cost: 0,
            properties: SpecialMoveProperties {
                base_properties: MoveProperties {
                    can_be_blocked: false, // projectile handles blocking
                    overhead: false,
                    low: false,
                    air_only: false,
                    ground_only: true,
                    counter_hit_bonus: 0,
                    chip_damage: 0,
                    meter_gain_on_hit: 100,
                    meter_gain_on_block: 50,
                },
                invincibility_frames: 0,
                armor_frames: 0,
                projectile_invincible: false,
                throw_invincible: false,
                can_be_air_blocked: false,
            },
            projectile_data: Some(ProjectileData {
                sprite_id: 200,
                velocity: Vect16::new(Fixed8_8::from_i16(300), Fixed8_8::ZERO),
                lifetime: 120, // 2 seconds at 60fps
                hitbox: HitboxData {
                    rect: Rect8::new(-10, -10, 20, 20),
                    damage: 600,
                    stun: 300,
                    knockback: Vect16::new(Fixed8_8::from_i16(200), Fixed8_8::ZERO),
                    hit_type: HitType::Projectile,
                    priority: 5,
                },
                hit_limit: 1,
                properties: ProjectileProperties {
                    piercing: false,
                    reflecting: true,
                    destroyable: true,
                    gravity_affected: false,
                    screen_bound: true,
                },
            }),
        });
        
        // Ryu's forward throw
        throw_moves.insert(ThrowId::ForwardThrow, ThrowDefinition {
            throw_id: ThrowId::ForwardThrow,
            name: "Forward Throw".to_string(),
            range: 20,
            damage: 3200, // 32 damage
            animation_frames: vec![], // Will be populated
            tech_window: 7,
        });
        
        MoveSet {
            character_id: FighterId::Ryu,
            normal_moves,
            special_moves,
            throw_moves,
        }
    }
    
    /// Create Ken's move set with differences from Ryu
    fn create_ken_move_set() -> MoveSet {
        // Start with Ryu's move set and modify
        let mut ken_moves = Self::create_ryu_move_set();
        ken_moves.character_id = FighterId::Ken;
        
        // Ken's Hadoken has faster recovery
        if let Some(hadoken) = ken_moves.special_moves.get_mut(&SpecialMoveId::Hadoken) {
            hadoken.frame_data.recovery_frames = 25; // 5 frames faster than Ryu
        }
        
        // Ken's Shoryuken has different arc and multi-hit
        // This will be expanded with full Ken-specific data
        
        ken_moves
    }
    
    /// Create default move set for unimplemented characters
    fn create_default_move_set(character_id: FighterId) -> MoveSet {
        MoveSet {
            character_id,
            normal_moves: HashMap::new(),
            special_moves: HashMap::new(),
            throw_moves: HashMap::new(),
        }
    }
    
    // Additional character move set creation methods will be added
    fn create_chunli_move_set() -> MoveSet { Self::create_default_move_set(FighterId::ChunLi) }
    fn create_guile_move_set() -> MoveSet { Self::create_default_move_set(FighterId::Guile) }
    fn create_blanka_move_set() -> MoveSet { Self::create_default_move_set(FighterId::Blanka) }
    fn create_honda_move_set() -> MoveSet { Self::create_default_move_set(FighterId::EHonda) }
    fn create_zangief_move_set() -> MoveSet { Self::create_default_move_set(FighterId::Zangief) }
    fn create_dhalsim_move_set() -> MoveSet { Self::create_default_move_set(FighterId::Dhalsim) }
}
