//! # State Transition Validation
//! 
//! Compile-time and runtime state transition validation using <PERSON><PERSON>'s type system
//! to prevent invalid state changes and ensure game logic correctness.

use std::collections::HashMap;
use std::fmt;
use serde::{Deserialize, Serialize};

use crate::game_state::*;
use crate::fighter_state::*;

/// State transition validator with compile-time and runtime checks
#[derive(Debug, Clone)]
pub struct StateValidator {
    /// Valid game mode transitions
    game_mode_transitions: HashMap<GameMode, Vec<GameMode>>,
    
    /// Valid fight mode transitions
    fight_mode_transitions: HashMap<FightMode, Vec<FightMode>>,
    
    /// Valid fighter state transitions
    fighter_state_transitions: HashMap<FighterState, Vec<FighterState>>,
    
    /// Validation rules
    rules: ValidationRules,
}

/// Validation rules configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRules {
    /// Strict mode - reject all invalid transitions
    pub strict_mode: bool,
    
    /// Allow emergency transitions (for debugging)
    pub allow_emergency_transitions: bool,
    
    /// Log all transition attempts
    pub log_transitions: bool,
    
    /// Maximum transition depth for cycle detection
    pub max_transition_depth: u8,
}

/// Validation result with detailed information
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ValidationResult {
    /// Transition is valid
    Valid,
    
    /// Transition is valid with warning
    ValidWithWarning(String),
    
    /// Transition is invalid
    Invalid(ValidationError),
    
    /// Transition requires additional conditions
    Conditional(Vec<ValidationCondition>),
}

/// Validation error types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationError {
    /// Transition not allowed between these states
    TransitionNotAllowed {
        from: String,
        to: String,
        reason: String,
    },
    
    /// Precondition not met
    PreconditionFailed {
        condition: String,
        current_value: String,
        required_value: String,
    },
    
    /// State cycle detected
    CycleDetected {
        cycle_path: Vec<String>,
    },
    
    /// Invalid state combination
    InvalidCombination {
        states: Vec<String>,
        reason: String,
    },
    
    /// Timing constraint violation
    TimingViolation {
        constraint: String,
        current_time: u64,
        required_time: u64,
    },
}

/// Validation condition that must be met
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationCondition {
    /// Timer must be zero or expired
    TimerExpired(String),
    
    /// Flag must be set
    FlagSet(String),
    
    /// Flag must be clear
    FlagClear(String),
    
    /// Minimum frame count required
    MinimumFrames(u64),
    
    /// Custom condition
    Custom(String),
}

/// State transition context for validation
#[derive(Debug, Clone)]
pub struct TransitionContext {
    /// Current game state
    pub game_state: GameState,
    
    /// Current fighter states (if applicable)
    pub fighter_states: Vec<FighterStateData>,
    
    /// Frame count when transition was requested
    pub frame_count: u64,
    
    /// Additional context data
    pub context_data: HashMap<String, String>,
}

impl StateValidator {
    /// Create new state validator with default rules
    pub fn new() -> Self {
        let mut validator = Self {
            game_mode_transitions: HashMap::new(),
            fight_mode_transitions: HashMap::new(),
            fighter_state_transitions: HashMap::new(),
            rules: ValidationRules::default(),
        };
        
        validator.initialize_default_transitions();
        validator
    }
    
    /// Create validator with custom rules
    pub fn with_rules(rules: ValidationRules) -> Self {
        let mut validator = Self::new();
        validator.rules = rules;
        validator
    }
    
    /// Initialize default valid transitions
    fn initialize_default_transitions(&mut self) {
        // Game mode transitions
        self.add_game_mode_transition(GameMode::Startup, GameMode::AttractMode);
        self.add_game_mode_transition(GameMode::AttractMode, GameMode::PlayerSelect);
        self.add_game_mode_transition(GameMode::PlayerSelect, GameMode::InGame);
        self.add_game_mode_transition(GameMode::InGame, GameMode::GameComplete);
        self.add_game_mode_transition(GameMode::InGame, GameMode::Continue);
        self.add_game_mode_transition(GameMode::Continue, GameMode::InGame);
        self.add_game_mode_transition(GameMode::Continue, GameMode::AttractMode);
        self.add_game_mode_transition(GameMode::GameComplete, GameMode::AttractMode);
        
        // Service mode can be entered from any state
        for mode in [GameMode::Startup, GameMode::AttractMode, GameMode::PlayerSelect, 
                     GameMode::InGame, GameMode::GameComplete, GameMode::Continue] {
            self.add_game_mode_transition(mode, GameMode::ServiceMode);
        }
        self.add_game_mode_transition(GameMode::ServiceMode, GameMode::AttractMode);
        
        // Fight mode transitions
        self.add_fight_mode_transition(FightMode::PlayerSelection, FightMode::PreFight);
        self.add_fight_mode_transition(FightMode::PreFight, FightMode::Fighting);
        self.add_fight_mode_transition(FightMode::Fighting, FightMode::Complete);
        self.add_fight_mode_transition(FightMode::Fighting, FightMode::ContinuePrompt);
        self.add_fight_mode_transition(FightMode::ContinuePrompt, FightMode::Fighting);
        self.add_fight_mode_transition(FightMode::ContinuePrompt, FightMode::Complete);
        self.add_fight_mode_transition(FightMode::Complete, FightMode::Cleanup);
        self.add_fight_mode_transition(FightMode::Cleanup, FightMode::FinalCleanup);
        self.add_fight_mode_transition(FightMode::FinalCleanup, FightMode::PlayerSelection);
        
        // Fighter state transitions
        self.initialize_fighter_state_transitions();
    }
    
    /// Initialize fighter state transitions
    fn initialize_fighter_state_transitions(&mut self) {
        use FighterState::*;
        
        // Normal state transitions
        self.add_fighter_state_transition(Normal, Crouch);
        self.add_fighter_state_transition(Normal, Jumping);
        self.add_fighter_state_transition(Normal, Attacking);
        self.add_fighter_state_transition(Normal, StandBlock);
        self.add_fighter_state_transition(Normal, TurnAround);
        
        // Crouch state transitions
        self.add_fighter_state_transition(Crouch, Normal);
        self.add_fighter_state_transition(Crouch, Attacking);
        self.add_fighter_state_transition(Crouch, TurnAround);
        
        // Jumping state transitions
        self.add_fighter_state_transition(Jumping, Normal);
        self.add_fighter_state_transition(Jumping, Attacking);
        
        // Attacking state transitions
        self.add_fighter_state_transition(Attacking, Normal);
        self.add_fighter_state_transition(Attacking, Crouch);
        self.add_fighter_state_transition(Attacking, InPowerMove);
        
        // Power move transitions
        self.add_fighter_state_transition(InPowerMove, Normal);
        self.add_fighter_state_transition(InPowerMove, Crouch);
        
        // Block state transitions
        self.add_fighter_state_transition(StandBlock, Normal);
        self.add_fighter_state_transition(StandBlock, Crouch);
        
        // Turn around transitions
        self.add_fighter_state_transition(TurnAround, Normal);
        self.add_fighter_state_transition(TurnAround, Crouch);
        
        // Hit reactions - can be entered from any state
        for state in [Normal, Crouch, Jumping, Attacking, InPowerMove, StandBlock, TurnAround] {
            self.add_fighter_state_transition(state, Reel);
            self.add_fighter_state_transition(state, Tumble);
            self.add_fighter_state_transition(state, Victory);
            self.add_fighter_state_transition(state, Loss);
        }
        
        // Recovery from hit reactions
        self.add_fighter_state_transition(Reel, Normal);
        self.add_fighter_state_transition(Tumble, Normal);
    }
    
    /// Add valid game mode transition
    fn add_game_mode_transition(&mut self, from: GameMode, to: GameMode) {
        self.game_mode_transitions.entry(from).or_insert_with(Vec::new).push(to);
    }
    
    /// Add valid fight mode transition
    fn add_fight_mode_transition(&mut self, from: FightMode, to: FightMode) {
        self.fight_mode_transitions.entry(from).or_insert_with(Vec::new).push(to);
    }
    
    /// Add valid fighter state transition
    fn add_fighter_state_transition(&mut self, from: FighterState, to: FighterState) {
        self.fighter_state_transitions.entry(from).or_insert_with(Vec::new).push(to);
    }
    
    /// Validate game mode transition
    pub fn validate_game_mode_transition(
        &self,
        from: GameMode,
        to: GameMode,
        context: &TransitionContext,
    ) -> ValidationResult {
        // Same state is always valid
        if from == to {
            return ValidationResult::Valid;
        }
        
        // Check if transition is in allowed list
        if let Some(allowed_transitions) = self.game_mode_transitions.get(&from) {
            if allowed_transitions.contains(&to) {
                // Check additional conditions
                self.check_game_mode_conditions(from, to, context)
            } else {
                ValidationResult::Invalid(ValidationError::TransitionNotAllowed {
                    from: format!("{:?}", from),
                    to: format!("{:?}", to),
                    reason: "Transition not in allowed list".to_string(),
                })
            }
        } else {
            ValidationResult::Invalid(ValidationError::TransitionNotAllowed {
                from: format!("{:?}", from),
                to: format!("{:?}", to),
                reason: "No transitions defined for source state".to_string(),
            })
        }
    }
    
    /// Check additional conditions for game mode transitions
    fn check_game_mode_conditions(
        &self,
        from: GameMode,
        to: GameMode,
        context: &TransitionContext,
    ) -> ValidationResult {
        match (from, to) {
            (GameMode::PlayerSelect, GameMode::InGame) => {
                if context.game_state.flags.player_select_done {
                    ValidationResult::Valid
                } else {
                    ValidationResult::Conditional(vec![
                        ValidationCondition::FlagSet("player_select_done".to_string())
                    ])
                }
            }
            
            (GameMode::Continue, GameMode::InGame) => {
                if context.game_state.game_timer > 0 {
                    ValidationResult::Valid
                } else {
                    ValidationResult::Invalid(ValidationError::TimingViolation {
                        constraint: "Continue timer must be active".to_string(),
                        current_time: context.game_state.game_timer as u64,
                        required_time: 1,
                    })
                }
            }
            
            _ => ValidationResult::Valid,
        }
    }
    
    /// Validate fighter state transition
    pub fn validate_fighter_state_transition(
        &self,
        from: FighterState,
        to: FighterState,
        fighter_context: &FighterStateData,
    ) -> ValidationResult {
        // Same state is always valid
        if from == to {
            return ValidationResult::Valid;
        }
        
        // Check if transition is in allowed list
        if let Some(allowed_transitions) = self.fighter_state_transitions.get(&from) {
            if allowed_transitions.contains(&to) {
                // Check additional conditions
                self.check_fighter_state_conditions(from, to, fighter_context)
            } else {
                ValidationResult::Invalid(ValidationError::TransitionNotAllowed {
                    from: format!("{:?}", from),
                    to: format!("{:?}", to),
                    reason: "Transition not in allowed list".to_string(),
                })
            }
        } else {
            ValidationResult::Invalid(ValidationError::TransitionNotAllowed {
                from: format!("{:?}", from),
                to: format!("{:?}", to),
                reason: "No transitions defined for source state".to_string(),
            })
        }
    }
    
    /// Check additional conditions for fighter state transitions
    fn check_fighter_state_conditions(
        &self,
        from: FighterState,
        to: FighterState,
        fighter_context: &FighterStateData,
    ) -> ValidationResult {
        match (from, to) {
            (FighterState::Normal, FighterState::Jumping) => {
                if !fighter_context.on_ground {
                    ValidationResult::Invalid(ValidationError::PreconditionFailed {
                        condition: "on_ground".to_string(),
                        current_value: "false".to_string(),
                        required_value: "true".to_string(),
                    })
                } else if fighter_context.flags.in_hit_stun {
                    ValidationResult::Invalid(ValidationError::PreconditionFailed {
                        condition: "not_in_hit_stun".to_string(),
                        current_value: "true".to_string(),
                        required_value: "false".to_string(),
                    })
                } else {
                    ValidationResult::Valid
                }
            }
            
            (FighterState::Attacking, FighterState::InPowerMove) => {
                if !fighter_context.flags.cancelable {
                    ValidationResult::Conditional(vec![
                        ValidationCondition::FlagSet("cancelable".to_string())
                    ])
                } else {
                    ValidationResult::Valid
                }
            }
            
            _ => ValidationResult::Valid,
        }
    }
    
    /// Enable strict validation mode
    pub fn set_strict_mode(&mut self, strict: bool) {
        self.rules.strict_mode = strict;
    }
    
    /// Check if validator is in strict mode
    pub fn is_strict_mode(&self) -> bool {
        self.rules.strict_mode
    }
}

impl Default for ValidationRules {
    fn default() -> Self {
        Self {
            strict_mode: true,
            allow_emergency_transitions: false,
            log_transitions: false,
            max_transition_depth: 10,
        }
    }
}

impl Default for StateValidator {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for ValidationError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ValidationError::TransitionNotAllowed { from, to, reason } => {
                write!(f, "Transition from {} to {} not allowed: {}", from, to, reason)
            }
            ValidationError::PreconditionFailed { condition, current_value, required_value } => {
                write!(f, "Precondition '{}' failed: current={}, required={}", 
                       condition, current_value, required_value)
            }
            ValidationError::CycleDetected { cycle_path } => {
                write!(f, "State cycle detected: {}", cycle_path.join(" -> "))
            }
            ValidationError::InvalidCombination { states, reason } => {
                write!(f, "Invalid state combination [{}]: {}", states.join(", "), reason)
            }
            ValidationError::TimingViolation { constraint, current_time, required_time } => {
                write!(f, "Timing violation '{}': current={}, required={}", 
                       constraint, current_time, required_time)
            }
        }
    }
}
