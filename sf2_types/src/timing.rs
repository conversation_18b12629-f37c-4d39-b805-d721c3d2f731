//! # Frame Data and Timing System
//! 
//! Precise frame-accurate timing system matching the original C99 engine's
//! 60 FPS behavior with deterministic update cycles and performance optimization.

use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// Target frames per second (matching original arcade hardware)
pub const TARGET_FPS: u32 = 60;

/// Frame duration in nanoseconds (1/60th of a second)
pub const FRAME_DURATION_NS: u64 = 1_000_000_000 / TARGET_FPS as u64;

/// Frame duration as Duration
pub const FRAME_DURATION: Duration = Duration::from_nanos(FRAME_DURATION_NS);

/// Ticks per second (matching C99 TICKS_PER_SECOND)
pub const TICKS_PER_SECOND: u16 = 60;

/// Maximum frame skip to prevent spiral of death
pub const MAX_FRAME_SKIP: u8 = 5;

/// Frame counter type for precise timing
pub type FrameCount = u64;

/// Tick counter type matching C99 system
pub type TickCount = u8;

/// Timer type for game timers
pub type GameTimer = u16;

/// Frame budget for rendering operations
pub type FrameBudget = u16;

/// Frame-accurate timing manager
#[derive(Debug, Clone)]
pub struct FrameTimer {
    /// Current frame number
    pub frame_count: FrameCount,
    
    /// Current tick within frame
    pub tick: TickCount,
    
    /// Accumulated time for frame timing
    pub accumulator: Duration,
    
    /// Last frame time
    pub last_frame_time: Option<Instant>,
    
    /// Frame delta time
    pub delta_time: Duration,
    
    /// Running average FPS
    pub fps: f32,
    
    /// Frame skip counter
    pub frame_skip: u8,
    
    /// Performance metrics
    pub metrics: FrameMetrics,
}

/// Performance metrics for frame timing
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct FrameMetrics {
    /// Total frames processed
    pub total_frames: u64,
    
    /// Frames skipped due to performance
    pub skipped_frames: u64,
    
    /// Average frame time in microseconds
    pub avg_frame_time_us: f32,
    
    /// Minimum frame time in microseconds
    pub min_frame_time_us: f32,
    
    /// Maximum frame time in microseconds
    pub max_frame_time_us: f32,
    
    /// Frame time samples for averaging
    frame_time_samples: Vec<f32>,
    
    /// Sample index for circular buffer
    sample_index: usize,
}

/// Frame budget manager for rendering operations
#[derive(Debug, Clone)]
pub struct FrameBudgetManager {
    /// Total budget per frame (in arbitrary units)
    pub total_budget: FrameBudget,
    
    /// Remaining budget for current frame
    pub remaining_budget: FrameBudget,
    
    /// Budget used for sprites
    pub sprite_budget_used: FrameBudget,
    
    /// Budget used for backgrounds
    pub background_budget_used: FrameBudget,
    
    /// Budget used for effects
    pub effect_budget_used: FrameBudget,
    
    /// Budget overrun counter
    pub overrun_count: u32,
}

/// Game timer with automatic countdown
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct CountdownTimer {
    /// Current timer value
    pub value: GameTimer,
    
    /// Initial timer value
    pub initial_value: GameTimer,
    
    /// Auto-decrement flag
    pub auto_decrement: bool,
    
    /// Timer active flag
    pub active: bool,
}

impl FrameTimer {
    /// Create new frame timer
    pub fn new() -> Self {
        Self {
            frame_count: 0,
            tick: 0,
            accumulator: Duration::ZERO,
            last_frame_time: None,
            delta_time: FRAME_DURATION,
            fps: TARGET_FPS as f32,
            frame_skip: 0,
            metrics: FrameMetrics::new(),
        }
    }
    
    /// Update timer with current time
    pub fn update(&mut self, current_time: Instant) -> u8 {
        let delta = if let Some(last_time) = self.last_frame_time {
            current_time.duration_since(last_time)
        } else {
            FRAME_DURATION
        };
        
        self.last_frame_time = Some(current_time);
        self.delta_time = delta;
        self.accumulator += delta;
        
        // Calculate FPS
        let delta_seconds = delta.as_secs_f32();
        if delta_seconds > 0.0 {
            let current_fps = 1.0 / delta_seconds;
            self.fps = self.fps * 0.9 + current_fps * 0.1; // Smooth FPS
        }
        
        // Update metrics
        self.metrics.update_frame_time(delta.as_micros() as f32);
        
        // Calculate frames to advance
        let mut frames_to_advance = 0u8;
        while self.accumulator >= FRAME_DURATION && frames_to_advance < MAX_FRAME_SKIP {
            self.accumulator -= FRAME_DURATION;
            frames_to_advance += 1;
        }
        
        // Update frame skip tracking
        if frames_to_advance > 1 {
            self.frame_skip = frames_to_advance - 1;
            self.metrics.skipped_frames += (frames_to_advance - 1) as u64;
        } else {
            self.frame_skip = 0;
        }
        
        frames_to_advance
    }
    
    /// Advance frame counter and tick
    pub fn advance_frame(&mut self) {
        self.frame_count = self.frame_count.wrapping_add(1);
        self.tick = self.tick.wrapping_add(1);
        self.metrics.total_frames += 1;
    }
    
    /// Get current frame count
    pub fn current_frame(&self) -> FrameCount {
        self.frame_count
    }
    
    /// Get current tick
    pub fn current_tick(&self) -> TickCount {
        self.tick
    }
    
    /// Check if we're running at target FPS
    pub fn is_running_smoothly(&self) -> bool {
        self.fps >= (TARGET_FPS as f32 * 0.9) && self.frame_skip == 0
    }
    
    /// Get frame time in seconds
    pub fn frame_time_seconds(&self) -> f32 {
        self.delta_time.as_secs_f32()
    }
    
    /// Reset timer
    pub fn reset(&mut self) {
        *self = Self::new();
    }
}

impl FrameMetrics {
    /// Create new metrics tracker
    pub fn new() -> Self {
        Self {
            total_frames: 0,
            skipped_frames: 0,
            avg_frame_time_us: 0.0,
            min_frame_time_us: f32::MAX,
            max_frame_time_us: 0.0,
            frame_time_samples: vec![0.0; 60], // 1 second of samples at 60 FPS
            sample_index: 0,
        }
    }
    
    /// Update frame time metrics
    pub fn update_frame_time(&mut self, frame_time_us: f32) {
        // Update min/max
        self.min_frame_time_us = self.min_frame_time_us.min(frame_time_us);
        self.max_frame_time_us = self.max_frame_time_us.max(frame_time_us);
        
        // Update circular buffer
        self.frame_time_samples[self.sample_index] = frame_time_us;
        self.sample_index = (self.sample_index + 1) % self.frame_time_samples.len();
        
        // Calculate average
        self.avg_frame_time_us = self.frame_time_samples.iter().sum::<f32>() / self.frame_time_samples.len() as f32;
    }
    
    /// Get frame skip percentage
    pub fn frame_skip_percentage(&self) -> f32 {
        if self.total_frames > 0 {
            (self.skipped_frames as f32 / self.total_frames as f32) * 100.0
        } else {
            0.0
        }
    }
}

impl FrameBudgetManager {
    /// Create new budget manager
    pub fn new(total_budget: FrameBudget) -> Self {
        Self {
            total_budget,
            remaining_budget: total_budget,
            sprite_budget_used: 0,
            background_budget_used: 0,
            effect_budget_used: 0,
            overrun_count: 0,
        }
    }
    
    /// Reset budget for new frame
    pub fn reset_frame(&mut self) {
        self.remaining_budget = self.total_budget;
        self.sprite_budget_used = 0;
        self.background_budget_used = 0;
        self.effect_budget_used = 0;
    }
    
    /// Allocate budget for operation
    pub fn allocate(&mut self, amount: FrameBudget, category: BudgetCategory) -> bool {
        if self.remaining_budget >= amount {
            self.remaining_budget -= amount;
            match category {
                BudgetCategory::Sprite => self.sprite_budget_used += amount,
                BudgetCategory::Background => self.background_budget_used += amount,
                BudgetCategory::Effect => self.effect_budget_used += amount,
            }
            true
        } else {
            self.overrun_count += 1;
            false
        }
    }
    
    /// Check if budget is available
    pub fn can_allocate(&self, amount: FrameBudget) -> bool {
        self.remaining_budget >= amount
    }
    
    /// Get budget utilization percentage
    pub fn utilization_percentage(&self) -> f32 {
        let used = self.total_budget - self.remaining_budget;
        (used as f32 / self.total_budget as f32) * 100.0
    }
}

/// Budget category for tracking
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BudgetCategory {
    Sprite,
    Background,
    Effect,
}

impl CountdownTimer {
    /// Create new countdown timer
    pub fn new(initial_value: GameTimer, auto_decrement: bool) -> Self {
        Self {
            value: initial_value,
            initial_value,
            auto_decrement,
            active: true,
        }
    }
    
    /// Update timer (decrement if auto-decrement is enabled)
    pub fn update(&mut self) {
        if self.active && self.auto_decrement && self.value > 0 {
            self.value -= 1;
        }
    }
    
    /// Check if timer has expired
    pub fn is_expired(&self) -> bool {
        self.value == 0
    }
    
    /// Reset timer to initial value
    pub fn reset(&mut self) {
        self.value = self.initial_value;
        self.active = true;
    }
    
    /// Set timer value
    pub fn set(&mut self, value: GameTimer) {
        self.value = value;
        self.active = true;
    }
    
    /// Stop timer
    pub fn stop(&mut self) {
        self.active = false;
    }
    
    /// Get remaining time in seconds (at 60 FPS)
    pub fn remaining_seconds(&self) -> f32 {
        self.value as f32 / TICKS_PER_SECOND as f32
    }
}

impl Default for FrameTimer {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for CountdownTimer {
    fn default() -> Self {
        Self::new(0, false)
    }
}
