{"rustc": 15497389221046826682, "features": "[\"arrayvec\", \"bevy_reflect\", \"default\", \"multi_threaded\", \"serialize\"]", "declared_features": "[\"arrayvec\", \"bevy_debug_stepping\", \"bevy_reflect\", \"default\", \"detailed_trace\", \"multi_threaded\", \"reflect_functions\", \"serialize\", \"trace\", \"track_change_detection\"]", "target": 6640942296801946140, "profile": 14918179532085112269, "path": 16695109006893347096, "deps": [[2425088982514975140, "bevy_ptr", false, 7605111557776598776], [3666196340704888985, "smallvec", false, 12134178849110255026], [4064098156404486754, "bevy_tasks", false, 16365968902015619039], [6234078840545730324, "fixedbitset", false, 14712714340922248530], [7896293946984509699, "bitflags", false, 13770117782452544914], [9018292148362813592, "bevy_ecs_macros", false, 10659455791240494724], [9687787840817006220, "nonmax", false, 9797276724660520912], [9689903380558560274, "serde", false, 10109379562410250594], [12100481297174703255, "concurrent_queue", false, 8965180509518865447], [13487854193495724092, "derive_more", false, 3330522204144507879], [13543630808651616527, "bevy_utils", false, 2968915845522474056], [13847662864258534762, "arrayvec", false, 2529152618593209120], [15918101059100394961, "disqualified", false, 10393886580128804908], [15953891987145646537, "bevy_reflect", false, 3517104538278723685], [16532555906320553198, "petgraph", false, 14893216698716614274]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_ecs-e202c4ce03f191c3/dep-lib-bevy_ecs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}