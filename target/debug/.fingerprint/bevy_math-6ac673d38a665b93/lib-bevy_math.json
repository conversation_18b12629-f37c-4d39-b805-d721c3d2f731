{"rustc": 15497389221046826682, "features": "[\"bevy_reflect\", \"curve\", \"default\", \"rand\", \"serialize\"]", "declared_features": "[\"approx\", \"bevy_reflect\", \"curve\", \"debug_glam_assert\", \"default\", \"glam_assert\", \"libm\", \"mint\", \"rand\", \"serialize\"]", "target": 18389758704779971090, "profile": 14918179532085112269, "path": 3267518287668169047, "deps": [[2831851536307977279, "glam", false, 3649045490533489421], [3317542222502007281, "itertools", false, 16551932633497180997], [3666196340704888985, "smallvec", false, 12134178849110255026], [9196727883430091646, "rand_distr", false, 16493293077972729691], [9689903380558560274, "serde", false, 10109379562410250594], [13208667028893622512, "rand", false, 5565137417873689365], [13487854193495724092, "derive_more", false, 3330522204144507879], [15953891987145646537, "bevy_reflect", false, 3517104538278723685]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_math-6ac673d38a665b93/dep-lib-bevy_math", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}