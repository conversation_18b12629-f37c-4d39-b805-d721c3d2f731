{"rustc": 15497389221046826682, "features": "[\"alloc\", \"bevy\", \"debug\", \"debug_stack\", \"default\", \"glam\", \"petgraph\", \"smallvec\", \"smol_str\", \"uuid\"]", "declared_features": "[\"alloc\", \"bevy\", \"debug\", \"debug_stack\", \"default\", \"documentation\", \"functions\", \"glam\", \"petgraph\", \"smallvec\", \"smol_str\", \"uuid\", \"wgpu-types\"]", "target": 16754485857268995592, "profile": 14918179532085112269, "path": 16419020219377713684, "deps": [[2425088982514975140, "bevy_ptr", false, 7605111557776598776], [2831851536307977279, "glam", false, 3649045490533489421], [3571374251074753029, "smol_str", false, 2804576115562584368], [3666196340704888985, "smallvec", false, 12134178849110255026], [8942107105684987126, "erased_serde", false, 15833843976828127059], [9689903380558560274, "serde", false, 10109379562410250594], [11434239582363224126, "downcast_rs", false, 12221962918341173282], [13487854193495724092, "derive_more", false, 3330522204144507879], [13543630808651616527, "bevy_utils", false, 2968915845522474056], [14966697905761911795, "assert_type_match", false, 5901849107914872144], [15918101059100394961, "disqualified", false, 10393886580128804908], [15949116387755617522, "uuid", false, 6693244624363672794], [16532555906320553198, "petgraph", false, 14893216698716614274], [17052824281933501444, "bevy_reflect_derive", false, 14722163515749878484]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_reflect-f7d52f3ed93f6774/dep-lib-bevy_reflect", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}