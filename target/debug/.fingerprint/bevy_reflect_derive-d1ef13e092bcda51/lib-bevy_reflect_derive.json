{"rustc": 15497389221046826682, "features": "[\"default\"]", "declared_features": "[\"default\", \"documentation\", \"functions\"]", "target": 1736263705675780695, "profile": 10659492051931546360, "path": 5443612649198867505, "deps": [[2196764441672005416, "bevy_macro_utils", false, 2892872308064582718], [3060637413840920116, "proc_macro2", false, 6827353163855665980], [4974441333307933176, "syn", false, 2151355000555186979], [15949116387755617522, "uuid", false, 1256884785789936533], [17990358020177143287, "quote", false, 13812412847552245210]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_reflect_derive-d1ef13e092bcda51/dep-lib-bevy_reflect_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}