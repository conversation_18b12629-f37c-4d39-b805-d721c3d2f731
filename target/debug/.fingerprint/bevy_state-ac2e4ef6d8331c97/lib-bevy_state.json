{"rustc": 15497389221046826682, "features": "[\"bevy_app\", \"bevy_hierarchy\", \"bevy_reflect\", \"default\"]", "declared_features": "[\"bevy_app\", \"bevy_hierarchy\", \"bevy_reflect\", \"default\"]", "target": 4104521996849610945, "profile": 14918179532085112269, "path": 2160350834066085205, "deps": [[5075818945153588980, "bevy_hierarchy", false, 1178039295908606348], [6214442870082674230, "bevy_ecs", false, 2417968210084664870], [8922671035508983237, "bevy_state_macros", false, 11861451182548055331], [13543630808651616527, "bevy_utils", false, 2968915845522474056], [13932235871545406073, "bevy_app", false, 8582004572855025195], [15953891987145646537, "bevy_reflect", false, 3517104538278723685]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_state-ac2e4ef6d8331c97/dep-lib-bevy_state", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}