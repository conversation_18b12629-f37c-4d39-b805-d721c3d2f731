{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"serde\", \"serialize\"]", "target": 9244520282509078292, "profile": 14918179532085112269, "path": 4950616850089845891, "deps": [[3571374251074753029, "smol_str", false, 2804576115562584368], [4143744114649553716, "raw_window_handle", false, 13154326847932084629], [6214442870082674230, "bevy_ecs", false, 2417968210084664870], [13543630808651616527, "bevy_utils", false, 2968915845522474056], [13932235871545406073, "bevy_app", false, 8582004572855025195], [15835720244494343315, "bevy_input", false, 18159052005403940419], [15953891987145646537, "bevy_reflect", false, 3517104538278723685], [17516170499218035890, "bevy_math", false, 11847790449575896374], [17865914693967143568, "bevy_a11y", false, 17740732275722327392]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_window-da1f4d5125e5d1e9/dep-lib-bevy_window", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}