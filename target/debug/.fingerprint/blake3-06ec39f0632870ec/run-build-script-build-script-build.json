{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9241925498456048256, "build_script_build", false, 11743626809726327728]], "local": [{"RerunIfChanged": {"output": "debug/build/blake3-06ec39f0632870ec/output", "paths": ["c/blake3_sse41_x86-64_windows_msvc.asm", "c/blake3_avx512_x86-64_windows_msvc.asm", "c/blake3_sse2_x86-64_unix.S", "c/blake3_sse2_x86-64_windows_msvc.asm", "c/CMakeLists.txt", "c/libblake3.pc.in", "c/cmake", "c/blake3_sse41_x86-64_unix.S", "c/blake3-config.cmake.in", "c/blake3.h", "c/blake3_dispatch.c", "c/blake3_sse41.c", "c/blake3_avx512_x86-64_windows_gnu.S", "c/dependencies", "c/Makefile.testing", "c/test.py", "c/blake3_portable.c", "c/blake3_tbb.cpp", "c/blake3_neon.c", "c/blake3_avx512.c", "c/README.md", "c/CMakePresets.json", "c/example.c", "c/blake3_avx2.c", "c/main.c", "c/.giti<PERSON>re", "c/blake3_avx2_x86-64_unix.S", "c/blake3_avx2_x86-64_windows_gnu.S", "c/blake3.c", "c/example_tbb.c", "c/blake3_sse2_x86-64_windows_gnu.S", "c/blake3_impl.h", "c/blake3_sse41_x86-64_windows_gnu.S", "c/blake3_avx2_x86-64_windows_msvc.asm", "c/blake3_sse2.c", "c/blake3_avx512_x86-64_unix.S"]}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}