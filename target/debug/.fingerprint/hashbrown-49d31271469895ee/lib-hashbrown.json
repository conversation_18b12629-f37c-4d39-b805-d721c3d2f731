{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"serde\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 1600468521343914604, "path": 8979737143335350578, "deps": [[966925859616469517, "ahash", false, 7197554114305460374], [9150530836556604396, "allocator_api2", false, 16099003257343065164], [9689903380558560274, "serde", false, 10109379562410250594]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-49d31271469895ee/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}