{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 1600468521343914604, "path": 11648099305154891604, "deps": [[555019317135488525, "regex_automata", false, 13959264703660233885], [9408802513701742484, "regex_syntax", false, 3385905717438266814]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-71b844ffe5d3a586/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}