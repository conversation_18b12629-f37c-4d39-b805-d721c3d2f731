{"$message_type":"diagnostic","message":"unused imports: `AudioSampleManager`, `SF2AudioConfig`, and `SF2AudioSystemPlugin`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_assets/src/test_rom_system.rs","byte_start":226,"byte_end":246,"line_start":9,"line_end":9,"column_start":37,"column_end":57,"is_primary":true,"text":[{"text":"    SF2AssetsPlugin, SF2AudioEvent, SF2AudioSystemPlugin,","highlight_start":37,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_assets/src/test_rom_system.rs","byte_start":324,"byte_end":342,"line_start":11,"line_end":11,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    AudioSampleManager, SF2AudioConfig,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_assets/src/test_rom_system.rs","byte_start":344,"byte_end":358,"line_start":11,"line_end":11,"column_start":25,"column_end":39,"is_primary":true,"text":[{"text":"    AudioSampleManager, SF2AudioConfig,","highlight_start":25,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is a test module, consider adding a `#[cfg(test)]` to the containing module","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/lib.rs","byte_start":470,"byte_end":494,"line_start":19,"line_end":19,"column_start":1,"column_end":25,"is_primary":true,"text":[{"text":"pub mod test_rom_system;","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/test_rom_system.rs","byte_start":224,"byte_end":246,"line_start":9,"line_end":9,"column_start":35,"column_end":57,"is_primary":true,"text":[{"text":"    SF2AssetsPlugin, SF2AudioEvent, SF2AudioSystemPlugin,","highlight_start":35,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_assets/src/test_rom_system.rs","byte_start":318,"byte_end":358,"line_start":10,"line_end":11,"column_start":71,"column_end":39,"is_primary":true,"text":[{"text":"    RomData, AssetExtractionProgress, ExtractedSprites, ExtractedAudio,","highlight_start":71,"highlight_end":72},{"text":"    AudioSampleManager, SF2AudioConfig,","highlight_start":1,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AudioSampleManager`, `SF2AudioConfig`, and `SF2AudioSystemPlugin`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/test_rom_system.rs:9:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SF2AssetsPlugin, SF2AudioEvent, SF2AudioSystemPlugin,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    RomData, AssetExtractionProgress, ExtractedSprites, ExtractedAudio,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AudioSampleManager, SF2AudioConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if this is a test module, consider adding a `#[cfg(test)]` to the containing module\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/lib.rs:19:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod test_rom_system;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fade_duration`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_assets/src/audio_system.rs","byte_start":4021,"byte_end":4034,"line_start":125,"line_end":125,"column_start":33,"column_end":46,"is_primary":true,"text":[{"text":"                    if let Some(fade_duration) = fade_in_duration {","highlight_start":33,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/audio_system.rs","byte_start":4021,"byte_end":4034,"line_start":125,"line_end":125,"column_start":33,"column_end":46,"is_primary":true,"text":[{"text":"                    if let Some(fade_duration) = fade_in_duration {","highlight_start":33,"highlight_end":46}],"label":null,"suggested_replacement":"_fade_duration","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fade_duration`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/audio_system.rs:125:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    if let Some(fade_duration) = fade_in_duration {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fade_duration`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `music_entity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_assets/src/audio_system.rs","byte_start":4930,"byte_end":4942,"line_start":147,"line_end":147,"column_start":25,"column_end":37,"is_primary":true,"text":[{"text":"                    let music_entity = commands.spawn((audio_player, playback_settings)).id();","highlight_start":25,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/audio_system.rs","byte_start":4930,"byte_end":4942,"line_start":147,"line_end":147,"column_start":25,"column_end":37,"is_primary":true,"text":[{"text":"                    let music_entity = commands.spawn((audio_player, playback_settings)).id();","highlight_start":25,"highlight_end":37}],"label":null,"suggested_replacement":"_music_entity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `music_entity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/audio_system.rs:147:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let music_entity = commands.spawn((audio_player, playback_settings)).id();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_music_entity`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `asset_server`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_assets/src/audio_system.rs","byte_start":2201,"byte_end":2213,"line_start":86,"line_end":86,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    asset_server: Res<AssetServer>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/audio_system.rs","byte_start":2201,"byte_end":2213,"line_start":86,"line_end":86,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    asset_server: Res<AssetServer>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"_asset_server","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `asset_server`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/audio_system.rs:86:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    asset_server: Res<AssetServer>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_asset_server`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `audio_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_assets/src/lib.rs","byte_start":9794,"byte_end":9807,"line_start":303,"line_end":303,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    mut audio_manager: ResMut<AudioSampleManager>,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/lib.rs","byte_start":9794,"byte_end":9807,"line_start":303,"line_end":303,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    mut audio_manager: ResMut<AudioSampleManager>,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_audio_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `audio_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/lib.rs:303:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut audio_manager: ResMut<AudioSampleManager>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_audio_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `asset_server`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_assets/src/lib.rs","byte_start":9841,"byte_end":9853,"line_start":304,"line_end":304,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    asset_server: Res<AssetServer>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/lib.rs","byte_start":9841,"byte_end":9853,"line_start":304,"line_end":304,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    asset_server: Res<AssetServer>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"_asset_server","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `asset_server`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/lib.rs:304:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    asset_server: Res<AssetServer>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_asset_server`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_assets/src/lib.rs","byte_start":9790,"byte_end":9807,"line_start":303,"line_end":303,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    mut audio_manager: ResMut<AudioSampleManager>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_assets/src/lib.rs","byte_start":9790,"byte_end":9794,"line_start":303,"line_end":303,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut audio_manager: ResMut<AudioSampleManager>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_assets/src/lib.rs:303:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut audio_manager: ResMut<AudioSampleManager>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 7 warnings emitted\u001b[0m\n\n"}
