{"$message_type":"diagnostic","message":"`CharacterRegistry` is ambiguous","code":{"code":"E0659","explanation":"An item usage is ambiguous.\n\nErroneous code example:\n\n```compile_fail,edition2018,E0659\npub mod moon {\n    pub fn foo() {}\n}\n\npub mod earth {\n    pub fn foo() {}\n}\n\nmod collider {\n    pub use crate::moon::*;\n    pub use crate::earth::*;\n}\n\nfn main() {\n    crate::collider::foo(); // ERROR: `foo` is ambiguous\n}\n```\n\nThis error generally appears when two items with the same name are imported into\na module. Here, the `foo` functions are imported and reexported from the\n`collider` module and therefore, when we're using `collider::foo()`, both\nfunctions collide.\n\nTo solve this error, the best solution is generally to keep the path before the\nitem when using it. Example:\n\n```edition2018\npub mod moon {\n    pub fn foo() {}\n}\n\npub mod earth {\n    pub fn foo() {}\n}\n\nmod collider {\n    pub use crate::moon;\n    pub use crate::earth;\n}\n\nfn main() {\n    crate::collider::moon::foo(); // ok!\n    crate::collider::earth::foo(); // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":2163,"byte_end":2180,"line_start":62,"line_end":62,"column_start":30,"column_end":47,"is_primary":true,"text":[{"text":"            .init_resource::<CharacterRegistry>()","highlight_start":30,"highlight_end":47}],"label":"ambiguous name","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"ambiguous because of multiple glob imports of a name in the same module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`CharacterRegistry` could refer to the struct imported here","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":1260,"byte_end":1279,"line_start":39,"line_end":39,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"pub use character_system::*;","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"consider adding an explicit import of `CharacterRegistry` to disambiguate","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`CharacterRegistry` could also refer to the struct imported here","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":1289,"byte_end":1302,"line_start":40,"line_end":40,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use characters::*;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"consider adding an explicit import of `CharacterRegistry` to disambiguate","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0659]\u001b[0m\u001b[0m\u001b[1m: `CharacterRegistry` is ambiguous\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:62:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .init_resource::<CharacterRegistry>()\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mambiguous name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `CharacterRegistry` could refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:39:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use character_system::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `CharacterRegistry` to disambiguate\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `CharacterRegistry` could also refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:40:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use characters::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `CharacterRegistry` to disambiguate\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AirborneState as SF2AirborneState`, `ButtonInput as SF2ButtonInput`, `CollisionConfig`, `CountdownTimer`, `Direction as SF2Direction`, `FbDirection`, `FightMode`, `FighterId as SF2FighterId`, `FighterMode`, `FighterState as SF2FighterState`, `FighterSubState`, `Fixed16_16`, `Fixed8_8`, `FrameBudgetManager`, `FrameInputBuffer`, `FrameTimer`, `GameMode`, `GameState as SF2GameState`, `InputConfig`, `InputDirection`, `Point16`, `Projectile`, `Rect8`, `RoundMode`, `SpecialMoveDetector`, `SpecialMovePattern`, `StateTransition`, `StateValidator`, and `Vect16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":248,"byte_end":258,"line_start":10,"line_end":10,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":260,"byte_end":268,"line_start":10,"line_end":10,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":270,"byte_end":277,"line_start":10,"line_end":10,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":279,"byte_end":285,"line_start":10,"line_end":10,"column_start":36,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":36,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":287,"byte_end":292,"line_start":10,"line_end":10,"column_start":44,"column_end":49,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":44,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":298,"byte_end":308,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Projectile,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":332,"byte_end":363,"line_start":12,"line_end":12,"column_start":23,"column_end":54,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":23,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":365,"byte_end":380,"line_start":12,"line_end":12,"column_start":56,"column_end":71,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":56,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":382,"byte_end":393,"line_start":12,"line_end":12,"column_start":73,"column_end":84,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":73,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":399,"byte_end":407,"line_start":13,"line_end":13,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":409,"byte_end":418,"line_start":13,"line_end":13,"column_start":15,"column_end":24,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":15,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":420,"byte_end":429,"line_start":13,"line_end":13,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":431,"byte_end":456,"line_start":13,"line_end":13,"column_start":37,"column_end":62,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":37,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":462,"byte_end":472,"line_start":14,"line_end":14,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":474,"byte_end":492,"line_start":14,"line_end":14,"column_start":17,"column_end":35,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":17,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":494,"byte_end":508,"line_start":14,"line_end":14,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":514,"byte_end":528,"line_start":15,"line_end":15,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":530,"byte_end":545,"line_start":15,"line_end":15,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition,","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":551,"byte_end":576,"line_start":16,"line_end":16,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"    FighterId as SF2FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":578,"byte_end":603,"line_start":16,"line_end":16,"column_start":32,"column_end":57,"is_primary":true,"text":[{"text":"    FighterId as SF2FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,","highlight_start":32,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":605,"byte_end":638,"line_start":16,"line_end":16,"column_start":59,"column_end":92,"is_primary":true,"text":[{"text":"    FighterId as SF2FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,","highlight_start":59,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":644,"byte_end":673,"line_start":17,"line_end":17,"column_start":5,"column_end":34,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":5,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":675,"byte_end":689,"line_start":17,"line_end":17,"column_start":36,"column_end":50,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":36,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":691,"byte_end":709,"line_start":17,"line_end":17,"column_start":52,"column_end":70,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":52,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":711,"byte_end":722,"line_start":17,"line_end":17,"column_start":72,"column_end":83,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":72,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":728,"byte_end":744,"line_start":18,"line_end":18,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":746,"byte_end":765,"line_start":18,"line_end":18,"column_start":23,"column_end":42,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":23,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":767,"byte_end":778,"line_start":18,"line_end":18,"column_start":44,"column_end":55,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":44,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":784,"byte_end":799,"line_start":19,"line_end":19,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    CollisionConfig, SpatialGrid, CollisionResponseProcessor, HitboxManager,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":248,"byte_end":314,"line_start":10,"line_end":12,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,","highlight_start":5,"highlight_end":50},{"text":"    Projectile,","highlight_start":1,"highlight_end":16},{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":330,"byte_end":799,"line_start":12,"line_end":19,"column_start":21,"column_end":20,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":21,"highlight_end":85},{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState,","highlight_start":1,"highlight_end":63},{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":1,"highlight_end":52},{"text":"    StateValidator, StateTransition,","highlight_start":1,"highlight_end":37},{"text":"    FighterId as SF2FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,","highlight_start":1,"highlight_end":93},{"text":"    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,","highlight_start":1,"highlight_end":84},{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":1,"highlight_end":56},{"text":"    CollisionConfig, SpatialGrid, CollisionResponseProcessor, HitboxManager,","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AirborneState as SF2AirborneState`, `ButtonInput as SF2ButtonInput`, `CollisionConfig`, `CountdownTimer`, `Direction as SF2Direction`, `FbDirection`, `FightMode`, `FighterId as SF2FighterId`, `FighterMode`, `FighterState as SF2FighterState`, `FighterSubState`, `Fixed16_16`, `Fixed8_8`, `FrameBudgetManager`, `FrameInputBuffer`, `FrameTimer`, `GameMode`, `GameState as SF2GameState`, `InputConfig`, `InputDirection`, `Point16`, `Projectile`, `Rect8`, `RoundMode`, `SpecialMoveDetector`, `SpecialMovePattern`, `StateTransition`, `StateValidator`, and `Vect16`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, Point16, Vect16, Rect8,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Projectile,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    GameMode, FightMode, RoundMode, GameState as SF2GameState,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameTimer, FrameBudgetManager, CountdownTimer,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    StateValidator, StateTransition,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterId as SF2FighterId, Direction as SF2Direction, AirborneState as SF2AirborneState,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ButtonInput as SF2ButtonInput, InputDirection, SpecialMovePattern, FbDirection,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameInputBuffer, SpecialMoveDetector, InputConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CollisionConfig, SpatialGrid, CollisionResponseProcessor, HitboxManager,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"private item shadows public glob re-export","code":{"code":"hidden_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":298,"byte_end":308,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Projectile,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the name `Projectile` in the type namespace is supposed to be publicly re-exported here","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":1071,"byte_end":1084,"line_start":33,"line_end":33,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use components::*;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"but the private item here shadows it","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":298,"byte_end":308,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Projectile,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`#[warn(hidden_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: private item shadows public glob re-export\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Projectile,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the name `Projectile` in the type namespace is supposed to be publicly re-exported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:33:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use components::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: but the private item here shadows it\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Projectile,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(hidden_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FbDirection`, `FrameInputBuffer`, `InputConfig`, `SpecialMoveDetector`, and `SpecialMovePattern`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":269,"byte_end":287,"line_start":10,"line_end":10,"column_start":36,"column_end":54,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,","highlight_start":36,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":289,"byte_end":300,"line_start":10,"line_end":10,"column_start":56,"column_end":67,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,","highlight_start":56,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":306,"byte_end":322,"line_start":11,"line_end":11,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":324,"byte_end":343,"line_start":11,"line_end":11,"column_start":23,"column_end":42,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":23,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/systems.rs","byte_start":345,"byte_end":356,"line_start":11,"line_end":11,"column_start":44,"column_end":55,"is_primary":true,"text":[{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":44,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":267,"byte_end":356,"line_start":10,"line_end":11,"column_start":34,"column_end":55,"is_primary":true,"text":[{"text":"    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,","highlight_start":34,"highlight_end":68},{"text":"    FrameInputBuffer, SpecialMoveDetector, InputConfig,","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `FbDirection`, `FrameInputBuffer`, `InputConfig`, `SpecialMoveDetector`, and `SpecialMovePattern`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:10:36\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ButtonInput as SF2ButtonInput, SpecialMovePattern, FbDirection,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameInputBuffer, SpecialMoveDetector, InputConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FightMode`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `GameMode`, `Point16`, `StateTransition`, `StateValidator`, and `Vect16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":283,"byte_end":298,"line_start":8,"line_end":8,"column_start":56,"column_end":71,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":56,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":300,"byte_end":311,"line_start":8,"line_end":8,"column_start":73,"column_end":84,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":73,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":317,"byte_end":325,"line_start":9,"line_end":9,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":327,"byte_end":336,"line_start":9,"line_end":9,"column_start":15,"column_end":24,"is_primary":true,"text":[{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,","highlight_start":15,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":476,"byte_end":490,"line_start":11,"line_end":11,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition, FighterStateTransition,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":492,"byte_end":507,"line_start":11,"line_end":11,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"    StateValidator, StateTransition, FighterStateTransition,","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":549,"byte_end":557,"line_start":12,"line_end":12,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":559,"byte_end":566,"line_start":12,"line_end":12,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":568,"byte_end":574,"line_start":12,"line_end":12,"column_start":36,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":36,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":281,"byte_end":336,"line_start":8,"line_end":9,"column_start":54,"column_end":24,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":54,"highlight_end":85},{"text":"    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":470,"byte_end":507,"line_start":10,"line_end":11,"column_start":51,"column_end":36,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer,","highlight_start":51,"highlight_end":52},{"text":"    StateValidator, StateTransition, FighterStateTransition,","highlight_start":1,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/game_logic.rs","byte_start":547,"byte_end":574,"line_start":12,"line_end":12,"column_start":15,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":15,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `FightMode`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `GameMode`, `Point16`, `StateTransition`, `StateValidator`, and `Vect16`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:8:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    GameMode, FightMode, RoundMode, GameState as SF2GameState, SubMode, AnimMode, AudioMode, ExtendedMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameTimer, FrameBudgetManager, CountdownTimer,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    StateValidator, StateTransition, FighterStateTransition,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, Point16, Vect16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::ecs::query::QueryFilter`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":227,"byte_end":256,"line_start":7,"line_end":7,"column_start":5,"column_end":34,"is_primary":true,"text":[{"text":"use bevy::ecs::query::QueryFilter;","highlight_start":5,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":223,"byte_end":258,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::ecs::query::QueryFilter;","highlight_start":1,"highlight_end":35},{"text":"use bevy::ecs::system::SystemParam;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::ecs::query::QueryFilter`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::ecs::query::QueryFilter;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::marker::PhantomData`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":298,"byte_end":322,"line_start":9,"line_end":9,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use std::marker::PhantomData;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":294,"byte_end":324,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::marker::PhantomData;","highlight_start":1,"highlight_end":30},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::marker::PhantomData`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::marker::PhantomData;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CountdownTimer`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `FrameBudgetManager`, `FrameTimer`, `Point16`, and `Vect16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":397,"byte_end":412,"line_start":12,"line_end":12,"column_start":56,"column_end":71,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":56,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":414,"byte_end":425,"line_start":12,"line_end":12,"column_start":73,"column_end":84,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":73,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":431,"byte_end":441,"line_start":13,"line_end":13,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":443,"byte_end":461,"line_start":13,"line_end":13,"column_start":17,"column_end":35,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":17,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":463,"byte_end":477,"line_start":13,"line_end":13,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":519,"byte_end":527,"line_start":14,"line_end":14,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":529,"byte_end":536,"line_start":14,"line_end":14,"column_start":27,"column_end":34,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":27,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":538,"byte_end":544,"line_start":14,"line_end":14,"column_start":36,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":36,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":395,"byte_end":477,"line_start":12,"line_end":13,"column_start":54,"column_end":51,"is_primary":true,"text":[{"text":"    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,","highlight_start":54,"highlight_end":85},{"text":"    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,","highlight_start":1,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/performance.rs","byte_start":517,"byte_end":544,"line_start":14,"line_end":14,"column_start":15,"column_end":42,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, Point16, Vect16,","highlight_start":15,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CountdownTimer`, `FighterMode`, `FighterSubState`, `Fixed8_8`, `FrameBudgetManager`, `FrameTimer`, `Point16`, and `Vect16`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:12:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterMode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FrameTimer, FrameBudgetManager, CountdownTimer, FighterStateTransition,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, Point16, Vect16,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ActiveHitbox`, `CollisionDetector`, `Fixed8_8`, `GridCell`, `Hurtbox`, and `Pushbox`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":303,"byte_end":320,"line_start":11,"line_end":11,"column_start":22,"column_end":39,"is_primary":true,"text":[{"text":"    CollisionConfig, CollisionDetector, SF2CollisionChecker, CollisionResult,","highlight_start":22,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":407,"byte_end":415,"line_start":12,"line_end":12,"column_start":48,"column_end":56,"is_primary":true,"text":[{"text":"    SpatialGrid, SpatialEntry, CollisionLayer, GridCell,","highlight_start":48,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":453,"byte_end":465,"line_start":13,"line_end":13,"column_start":37,"column_end":49,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":37,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":467,"byte_end":474,"line_start":13,"line_end":13,"column_start":51,"column_end":58,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":51,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":476,"byte_end":483,"line_start":13,"line_end":13,"column_start":60,"column_end":67,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":60,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":587,"byte_end":595,"line_start":15,"line_end":15,"column_start":29,"column_end":37,"is_primary":true,"text":[{"text":"    Point16, Point8, Rect8, Fixed8_8,","highlight_start":29,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":301,"byte_end":320,"line_start":11,"line_end":11,"column_start":20,"column_end":39,"is_primary":true,"text":[{"text":"    CollisionConfig, CollisionDetector, SF2CollisionChecker, CollisionResult,","highlight_start":20,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":405,"byte_end":415,"line_start":12,"line_end":12,"column_start":46,"column_end":56,"is_primary":true,"text":[{"text":"    SpatialGrid, SpatialEntry, CollisionLayer, GridCell,","highlight_start":46,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":451,"byte_end":483,"line_start":13,"line_end":13,"column_start":35,"column_end":67,"is_primary":true,"text":[{"text":"    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,","highlight_start":35,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/collision_system.rs","byte_start":585,"byte_end":595,"line_start":15,"line_end":15,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"    Point16, Point8, Rect8, Fixed8_8,","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ActiveHitbox`, `CollisionDetector`, `Fixed8_8`, `GridCell`, `Hurtbox`, and `Pushbox`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:11:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CollisionConfig, CollisionDetector, SF2CollisionChecker, CollisionResult,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SpatialGrid, SpatialEntry, CollisionLayer, GridCell,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    HitboxManager, FighterHitboxes, ActiveHitbox, Hurtbox, Pushbox,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CollisionResponseProcessor, CollisionResponse, CollisionResponseType,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Point16, Point8, Rect8, Fixed8_8,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ButtonFlags`, `InputDirection`, `InputState`, `Point16`, and `Vect16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":279,"byte_end":286,"line_start":8,"line_end":8,"column_start":46,"column_end":53,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Fixed16_16, Point16, Vect16,","highlight_start":46,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":288,"byte_end":294,"line_start":8,"line_end":8,"column_start":55,"column_end":61,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Fixed16_16, Point16, Vect16,","highlight_start":55,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":300,"byte_end":310,"line_start":9,"line_end":9,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":312,"byte_end":323,"line_start":9,"line_end":9,"column_start":17,"column_end":28,"is_primary":true,"text":[{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":17,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":325,"byte_end":339,"line_start":9,"line_end":9,"column_start":30,"column_end":44,"is_primary":true,"text":[{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":30,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":277,"byte_end":339,"line_start":8,"line_end":9,"column_start":44,"column_end":44,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Fixed16_16, Point16, Vect16,","highlight_start":44,"highlight_end":62},{"text":"    InputState, ButtonFlags, InputDirection,","highlight_start":1,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ButtonFlags`, `InputDirection`, `InputState`, `Point16`, and `Vect16`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:8:46\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FighterId, FighterStateData, Fixed16_16, Point16, Vect16,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InputState, ButtonFlags, InputDirection,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::prelude::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":166,"byte_end":182,"line_start":6,"line_end":6,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":162,"byte_end":184,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":1,"highlight_end":22},{"text":"use sf2_types::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::prelude::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::prelude::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Fixed16_16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":255,"byte_end":265,"line_start":9,"line_end":9,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":249,"byte_end":265,"line_start":8,"line_end":9,"column_start":49,"column_end":15,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Point16, Vect16,","highlight_start":49,"highlight_end":50},{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Fixed16_16`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `bevy::prelude::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":178,"byte_end":194,"line_start":6,"line_end":6,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":174,"byte_end":196,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bevy::prelude::*;","highlight_start":1,"highlight_end":22},{"text":"use sf2_types::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `bevy::prelude::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bevy::prelude::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ButtonFlags` and `Fixed16_16`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":267,"byte_end":277,"line_start":9,"line_end":9,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":301,"byte_end":312,"line_start":9,"line_end":9,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":261,"byte_end":277,"line_start":8,"line_end":9,"column_start":49,"column_end":15,"is_primary":true,"text":[{"text":"    FighterId, FighterStateData, Point16, Vect16,","highlight_start":49,"highlight_end":50},{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":299,"byte_end":312,"line_start":9,"line_end":9,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ButtonFlags` and `Fixed16_16`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Fixed16_16, Fixed8_8, InputState, ButtonFlags, InputDirection, ButtonInput,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/lib.rs","byte_start":1260,"byte_end":1279,"line_start":39,"line_end":39,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"pub use character_system::*;","highlight_start":9,"highlight_end":28}],"label":"the name `CharacterRegistry` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/lib.rs","byte_start":1289,"byte_end":1302,"line_start":40,"line_end":40,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use characters::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `CharacterRegistry` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(ambiguous_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/lib.rs:39:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use character_system::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `CharacterRegistry` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use characters::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `CharacterRegistry` in the type namespace is also re-exported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(ambiguous_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":3952,"byte_end":3970,"line_start":120,"line_end":120,"column_start":29,"column_end":47,"is_primary":true,"text":[{"text":"                fighter_id: event.character_id,","highlight_start":29,"highlight_end":47}],"label":"expected `components::FighterId`, found `sf2_types::FighterId`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`sf2_types::FighterId` and `components::FighterId` have similar names, but are actually distinct types","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`sf2_types::FighterId` is defined in crate `sf2_types`","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fighter.rs","byte_start":444,"byte_end":462,"line_start":15,"line_end":15,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub enum FighterId {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`components::FighterId` is defined in the current crate","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/components.rs","byte_start":2283,"byte_end":2301,"line_start":98,"line_end":98,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub enum FighterId {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:120:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m120\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                fighter_id: event.character_id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `components::FighterId`, found `sf2_types::FighterId`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `sf2_types::FighterId` and `components::FighterId` have similar names, but are actually distinct types\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `sf2_types::FighterId` is defined in crate `sf2_types`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fighter.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum FighterId {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `components::FighterId` is defined in the current crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/components.rs:98:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum FighterId {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `from_i32` found for struct `sf2_types::Fixed16_16` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":4093,"byte_end":4101,"line_start":124,"line_end":124,"column_start":32,"column_end":40,"is_primary":true,"text":[{"text":"                x: Fixed16_16::from_i32(if event.player_number == 1 { -100 } else { 100 }),","highlight_start":32,"highlight_end":40}],"label":"function or associated item not found in `Fixed16_16`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `sf2_types::Fixed16_16` consider using one of the following associated functions:\nsf2_types::Fixed16_16::from_raw\nsf2_types::Fixed16_16::from_int\nsf2_types::Fixed16_16::from_i16\nsf2_types::Fixed16_16::from_f32","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":917,"byte_end":956,"line_start":25,"line_end":25,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_raw(raw: i32) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":1108,"byte_end":1147,"line_start":35,"line_end":35,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_int(int: i32) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":1233,"byte_end":1272,"line_start":40,"line_end":40,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_i16(int: i16) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":1366,"byte_end":1401,"line_start":45,"line_end":45,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn from_f32(float: f32) -> Self {","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `from_f32` with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":4093,"byte_end":4101,"line_start":124,"line_end":124,"column_start":32,"column_end":40,"is_primary":true,"text":[{"text":"                x: Fixed16_16::from_i32(if event.player_number == 1 { -100 } else { 100 }),","highlight_start":32,"highlight_end":40}],"label":null,"suggested_replacement":"from_f32","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `from_i32` found for struct `sf2_types::Fixed16_16` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:124:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                x: Fixed16_16::from_i32(if event.player_number == 1 { -100 } else { 100 }),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `Fixed16_16`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `sf2_types::Fixed16_16` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      sf2_types::Fixed16_16::from_raw\u001b[0m\n\u001b[0m      sf2_types::Fixed16_16::from_int\u001b[0m\n\u001b[0m      sf2_types::Fixed16_16::from_i16\u001b[0m\n\u001b[0m      sf2_types::Fixed16_16::from_f32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs:25:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_raw(raw: i32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_int(int: i32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_i16(int: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m45\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_f32(float: f32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `from_f32` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                x: Fixed16_16::\u001b[0m\u001b[0m\u001b[38;5;9mfrom_i32\u001b[0m\u001b[0m(if event.player_number == 1 { -100 } else { 100 }),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                x: Fixed16_16::\u001b[0m\u001b[0m\u001b[38;5;10mfrom_f32\u001b[0m\u001b[0m(if event.player_number == 1 { -100 } else { 100 }),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":5728,"byte_end":5747,"line_start":178,"line_end":178,"column_start":88,"column_end":107,"is_primary":true,"text":[{"text":"        if let Some(character_impl) = character_registry.character_implementations.get(&fighter.fighter_id) {","highlight_start":88,"highlight_end":107}],"label":"expected `sf2_types::FighterId`, found `components::FighterId`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":5724,"byte_end":5727,"line_start":178,"line_end":178,"column_start":84,"column_end":87,"is_primary":false,"text":[{"text":"        if let Some(character_impl) = character_registry.character_implementations.get(&fighter.fighter_id) {","highlight_start":84,"highlight_end":87}],"label":"arguments to this method are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`components::FighterId` and `sf2_types::FighterId` have similar names, but are actually distinct types","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`components::FighterId` is defined in the current crate","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/components.rs","byte_start":2283,"byte_end":2301,"line_start":98,"line_end":98,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub enum FighterId {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`sf2_types::FighterId` is defined in crate `sf2_types`","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fighter.rs","byte_start":444,"byte_end":462,"line_start":15,"line_end":15,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub enum FighterId {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359/library/std/src/collections/hash/map.rs","byte_start":30359,"byte_end":30362,"line_start":906,"line_end":906,"column_start":12,"column_end":15,"is_primary":true,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:178:88\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(character_impl) = character_registry.character_implementations.get(&fighter.fighter_id) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `sf2_types::FighterId`, found `components::FighterId`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12marguments to this method are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `components::FighterId` and `sf2_types::FighterId` have similar names, but are actually distinct types\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `components::FighterId` is defined in the current crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/components.rs:98:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum FighterId {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `sf2_types::FighterId` is defined in crate `sf2_types`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fighter.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum FighterId {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359/library/std/src/collections/hash/map.rs:906:12\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this method are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":6833,"byte_end":6847,"line_start":203,"line_end":203,"column_start":28,"column_end":42,"is_primary":true,"text":[{"text":"            character_impl.update_physics(&mut state, &mut position, &mut velocity);","highlight_start":28,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `&mut Point16`, found `&mut Mut<'_, Position>`","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":6860,"byte_end":6873,"line_start":203,"line_end":203,"column_start":55,"column_end":68,"is_primary":true,"text":[{"text":"            character_impl.update_physics(&mut state, &mut position, &mut velocity);","highlight_start":55,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected mutable reference `&mut Point16`\n   found mutable reference `&mut Mut<'_, components::Position>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"expected `&mut Vect16`, found `&mut Mut<'_, Velocity>`","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":6875,"byte_end":6888,"line_start":203,"line_end":203,"column_start":70,"column_end":83,"is_primary":true,"text":[{"text":"            character_impl.update_physics(&mut state, &mut position, &mut velocity);","highlight_start":70,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected mutable reference `&mut Vect16`\n   found mutable reference `&mut Mut<'_, components::Velocity>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/character_traits.rs","byte_start":919,"byte_end":933,"line_start":29,"line_end":29,"column_start":8,"column_end":22,"is_primary":true,"text":[{"text":"    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16);","highlight_start":8,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: arguments to this method are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:203:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            character_impl.update_physics(&mut state, &mut position, &mut velocity);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `&mut Point16`, found `&mut Mut<'_, Position>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:203:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            character_impl.update_physics(&mut state, &mut position, &mut velocity);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected mutable reference `&mut \u001b[0m\u001b[0m\u001b[1m\u001b[35mPoint16\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found mutable reference `&mut \u001b[0m\u001b[0m\u001b[1m\u001b[35mMut<'_, components::Position>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `&mut Vect16`, found `&mut Mut<'_, Velocity>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:203:70\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            character_impl.update_physics(&mut state, &mut position, &mut velocity);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected mutable reference `&mut \u001b[0m\u001b[0m\u001b[1m\u001b[35mVect16\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found mutable reference `&mut \u001b[0m\u001b[0m\u001b[1m\u001b[35mMut<'_, components::Velocity>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/character_traits.rs:29:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn update_physics(&self, state: &mut FighterStateData, position: &mut Point16, velocity: &mut Vect16);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":7326,"byte_end":7344,"line_start":217,"line_end":217,"column_start":28,"column_end":46,"is_primary":true,"text":[{"text":"            .get_character(fighter.fighter_id)","highlight_start":28,"highlight_end":46}],"label":"expected `sf2_types::FighterId`, found `components::FighterId`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":7312,"byte_end":7325,"line_start":217,"line_end":217,"column_start":14,"column_end":27,"is_primary":false,"text":[{"text":"            .get_character(fighter.fighter_id)","highlight_start":14,"highlight_end":27}],"label":"arguments to this method are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`components::FighterId` and `sf2_types::FighterId` have similar names, but are actually distinct types","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`components::FighterId` is defined in the current crate","code":null,"level":"note","spans":[{"file_name":"sf2_engine/src/components.rs","byte_start":2283,"byte_end":2301,"line_start":98,"line_end":98,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub enum FighterId {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`sf2_types::FighterId` is defined in crate `sf2_types`","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fighter.rs","byte_start":444,"byte_end":462,"line_start":15,"line_end":15,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub enum FighterId {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/character_data.rs","byte_start":11253,"byte_end":11266,"line_start":279,"line_end":279,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn get_character(&self, fighter_id: FighterId) -> Option<&CharacterData> {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:217:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m217\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .get_character(fighter.fighter_id)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `sf2_types::FighterId`, found `components::FighterId`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12marguments to this method are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `components::FighterId` and `sf2_types::FighterId` have similar names, but are actually distinct types\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `components::FighterId` is defined in the current crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/components.rs:98:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum FighterId {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `sf2_types::FighterId` is defined in crate `sf2_types`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fighter.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum FighterId {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/character_data.rs:279:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_character(&self, fighter_id: FighterId) -> Option<&CharacterData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":10018,"byte_end":10030,"line_start":287,"line_end":287,"column_start":67,"column_end":79,"is_primary":true,"text":[{"text":"        character_implementations.insert(FighterId::Ken, Box::new(KenCharacter));","highlight_start":67,"highlight_end":79}],"label":"expected `RyuCharacter`, found `KenCharacter`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":10009,"byte_end":10017,"line_start":287,"line_end":287,"column_start":58,"column_end":66,"is_primary":false,"text":[{"text":"        character_implementations.insert(FighterId::Ken, Box::new(KenCharacter));","highlight_start":58,"highlight_end":66}],"label":"arguments to this function are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359/library/alloc/src/boxed.rs","byte_start":9958,"byte_end":9961,"line_start":260,"line_end":260,"column_start":12,"column_end":15,"is_primary":true,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:287:67\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        character_implementations.insert(FighterId::Ken, Box::new(KenCharacter));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `RyuCharacter`, found `KenCharacter`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12marguments to this function are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/rustc/17067e9ac6d7ecb70e50f92c1944e545188d2359/library/alloc/src/boxed.rs:260:12\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/character_system.rs","byte_start":10115,"byte_end":10140,"line_start":292,"line_end":292,"column_start":13,"column_end":38,"is_primary":true,"text":[{"text":"            character_implementations,","highlight_start":13,"highlight_end":38}],"label":"expected `HashMap<FighterId, Box<...>>`, found `HashMap<FighterId, Box<RyuCharacter>>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":9927,"byte_end":9949,"line_start":286,"line_end":286,"column_start":58,"column_end":80,"is_primary":false,"text":[{"text":"        character_implementations.insert(FighterId::Ryu, Box::new(RyuCharacter));","highlight_start":58,"highlight_end":80}],"label":"this argument has type `Box<ryu::RyuCharacter>`...","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"sf2_engine/src/character_system.rs","byte_start":9878,"byte_end":9903,"line_start":286,"line_end":286,"column_start":9,"column_end":34,"is_primary":false,"text":[{"text":"        character_implementations.insert(FighterId::Ryu, Box::new(RyuCharacter));","highlight_start":9,"highlight_end":34}],"label":"... which causes `character_implementations` to have type `std::collections::HashMap<sf2_types::FighterId, Box<ryu::RyuCharacter>>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected struct `std::collections::HashMap<_, Box<(dyn FighterCharacter + 'static)>>`\n   found struct `std::collections::HashMap<_, Box<ryu::RyuCharacter>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`ryu::RyuCharacter` implements `FighterCharacter` so you could box the found value and coerce it to the trait object `Box<dyn FighterCharacter>`, you will have to change the expected type as well","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/character_system.rs:292:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        character_implementations.insert(FighterId::Ryu, Box::new(RyuCharacter));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis argument has type `Box<ryu::RyuCharacter>`...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m... which causes `character_implementations` to have type `std::collections::HashMap<sf2_types::FighterId, Box<ryu::RyuCharacter>>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            character_implementations,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `HashMap<FighterId, Box<...>>`, found `HashMap<FighterId, Box<RyuCharacter>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `std::collections::HashMap<_, Box<\u001b[0m\u001b[0m\u001b[1m\u001b[35m(dyn FighterCharacter + 'static)\u001b[0m\u001b[0m>>`\u001b[0m\n\u001b[0m               found struct `std::collections::HashMap<_, Box<\u001b[0m\u001b[0m\u001b[1m\u001b[35mryu::RyuCharacter\u001b[0m\u001b[0m>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: `ryu::RyuCharacter` implements `FighterCharacter` so you could box the found value and coerce it to the trait object `Box<dyn FighterCharacter>`, you will have to change the expected type as well\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":4707,"byte_end":4727,"line_start":131,"line_end":131,"column_start":28,"column_end":48,"is_primary":true,"text":[{"text":"            SpecialMoveId::TatsumakirSenpukyaku => self.execute_tatsumaki(state),","highlight_start":28,"highlight_end":48}],"label":"variant or associated item not found in `SpecialMoveId`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a variant with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":4707,"byte_end":4727,"line_start":131,"line_end":131,"column_start":28,"column_end":48,"is_primary":true,"text":[{"text":"            SpecialMoveId::TatsumakirSenpukyaku => self.execute_tatsumaki(state),","highlight_start":28,"highlight_end":48}],"label":null,"suggested_replacement":"TatsumakiSenpukyaku","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:131:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            SpecialMoveId::TatsumakirSenpukyaku => self.execute_tatsumaki(state),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvariant or associated item not found in `SpecialMoveId`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a variant with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;9mTatsumakirSenpukyaku\u001b[0m\u001b[0m => self.execute_tatsumaki(state),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;10mTatsumakiSenpukyaku\u001b[0m\u001b[0m => self.execute_tatsumaki(state),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no variant or associated item named `HitStun` found for enum `sf2_types::FighterState` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7375,"byte_end":7382,"line_start":212,"line_end":212,"column_start":49,"column_end":56,"is_primary":true,"text":[{"text":"            new_state: sf2_types::FighterState::HitStun,","highlight_start":49,"highlight_end":56}],"label":"variant or associated item not found in `FighterState`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no variant or associated item named `HitStun` found for enum `sf2_types::FighterState` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:212:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            new_state: sf2_types::FighterState::HitStun,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvariant or associated item not found in `FighterState`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7803,"byte_end":7811,"line_start":222,"line_end":222,"column_start":33,"column_end":41,"is_primary":true,"text":[{"text":"            pushback: Fixed8_8::from_i32(50),","highlight_start":33,"highlight_end":41}],"label":"function or associated item not found in `Fixed8_8`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\nFixed8_8::from_raw\nFixed8_8::from_int\nFixed8_8::from_i16\nFixed8_8::from_f32","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3672,"byte_end":3711,"line_start":131,"line_end":131,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_raw(raw: i16) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3863,"byte_end":3901,"line_start":141,"line_end":141,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"    pub const fn from_int(int: i8) -> Self {","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3995,"byte_end":4028,"line_start":146,"line_end":146,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"    pub fn from_i16(int: i16) -> Self {","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":4117,"byte_end":4152,"line_start":151,"line_end":151,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn from_f32(float: f32) -> Self {","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `from_f32` with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":7803,"byte_end":7811,"line_start":222,"line_end":222,"column_start":33,"column_end":41,"is_primary":true,"text":[{"text":"            pushback: Fixed8_8::from_i32(50),","highlight_start":33,"highlight_end":41}],"label":null,"suggested_replacement":"from_f32","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:222:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            pushback: Fixed8_8::from_i32(50),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `Fixed8_8`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      Fixed8_8::from_raw\u001b[0m\n\u001b[0m      Fixed8_8::from_int\u001b[0m\n\u001b[0m      Fixed8_8::from_i16\u001b[0m\n\u001b[0m      Fixed8_8::from_f32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs:131:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_raw(raw: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_int(int: i8) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_i16(int: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_f32(float: f32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `from_f32` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            pushback: Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;9mfrom_i32\u001b[0m\u001b[0m(50),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            pushback: Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;10mfrom_f32\u001b[0m\u001b[0m(50),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":8338,"byte_end":8346,"line_start":236,"line_end":236,"column_start":43,"column_end":51,"is_primary":true,"text":[{"text":"            preferred_distance: Fixed8_8::from_i32(150),","highlight_start":43,"highlight_end":51}],"label":"function or associated item not found in `Fixed8_8`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\nFixed8_8::from_raw\nFixed8_8::from_int\nFixed8_8::from_i16\nFixed8_8::from_f32","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3672,"byte_end":3711,"line_start":131,"line_end":131,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_raw(raw: i16) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3863,"byte_end":3901,"line_start":141,"line_end":141,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"    pub const fn from_int(int: i8) -> Self {","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3995,"byte_end":4028,"line_start":146,"line_end":146,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"    pub fn from_i16(int: i16) -> Self {","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":4117,"byte_end":4152,"line_start":151,"line_end":151,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn from_f32(float: f32) -> Self {","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `from_f32` with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":8338,"byte_end":8346,"line_start":236,"line_end":236,"column_start":43,"column_end":51,"is_primary":true,"text":[{"text":"            preferred_distance: Fixed8_8::from_i32(150),","highlight_start":43,"highlight_end":51}],"label":null,"suggested_replacement":"from_f32","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:236:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            preferred_distance: Fixed8_8::from_i32(150),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `Fixed8_8`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      Fixed8_8::from_raw\u001b[0m\n\u001b[0m      Fixed8_8::from_int\u001b[0m\n\u001b[0m      Fixed8_8::from_i16\u001b[0m\n\u001b[0m      Fixed8_8::from_f32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs:131:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_raw(raw: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_int(int: i8) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_i16(int: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_f32(float: f32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `from_f32` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            preferred_distance: Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;9mfrom_i32\u001b[0m\u001b[0m(150),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            preferred_distance: Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;10mfrom_f32\u001b[0m\u001b[0m(150),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":9865,"byte_end":9885,"line_start":275,"line_end":275,"column_start":83,"column_end":103,"is_primary":true,"text":[{"text":"            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::TatsumakirSenpukyaku)));","highlight_start":83,"highlight_end":103}],"label":"variant or associated item not found in `SpecialMoveId`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a variant with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":9865,"byte_end":9886,"line_start":275,"line_end":275,"column_start":83,"column_end":104,"is_primary":true,"text":[{"text":"            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::TatsumakirSenpukyaku)));","highlight_start":83,"highlight_end":104}],"label":null,"suggested_replacement":"TatsumakiSenpukyaku","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:275:83\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::TatsumakirSenpukyaku)));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvariant or associated item not found in `SpecialMoveId`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a variant with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;9mTatsumakirSenpukyaku)\u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;10mTatsumakiSenpukyaku\u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `contains` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10138,"byte_end":10146,"line_start":284,"line_end":284,"column_start":23,"column_end":31,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()","highlight_start":23,"highlight_end":31}],"label":"method not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `contains` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:284:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m284\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no associated item named `PUNCH` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10160,"byte_end":10165,"line_start":284,"line_end":284,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()","highlight_start":45,"highlight_end":50}],"label":"associated item not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no associated item named `PUNCH` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:284:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m284\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9massociated item not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `just_pressed` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10184,"byte_end":10196,"line_start":284,"line_end":284,"column_start":69,"column_end":81,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()","highlight_start":69,"highlight_end":81}],"label":"method not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `just_pressed` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:284:69\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m284\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `contains` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10374,"byte_end":10382,"line_start":290,"line_end":290,"column_start":23,"column_end":31,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()","highlight_start":23,"highlight_end":31}],"label":"method not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `contains` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:290:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no associated item named `PUNCH` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10396,"byte_end":10401,"line_start":290,"line_end":290,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()","highlight_start":45,"highlight_end":50}],"label":"associated item not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no associated item named `PUNCH` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:290:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9massociated item not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `just_pressed` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10420,"byte_end":10432,"line_start":290,"line_end":290,"column_start":69,"column_end":81,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()","highlight_start":69,"highlight_end":81}],"label":"method not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `just_pressed` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:290:69\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::PUNCH) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `contains` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10610,"byte_end":10618,"line_start":296,"line_end":296,"column_start":23,"column_end":31,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::KICK) && input.buttons.just_pressed()","highlight_start":23,"highlight_end":31}],"label":"method not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `contains` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:296:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::KICK) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no associated item named `KICK` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10632,"byte_end":10636,"line_start":296,"line_end":296,"column_start":45,"column_end":49,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::KICK) && input.buttons.just_pressed()","highlight_start":45,"highlight_end":49}],"label":"associated item not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no associated item named `KICK` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:296:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::KICK) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9massociated item not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `just_pressed` found for struct `ButtonFlags` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10655,"byte_end":10667,"line_start":296,"line_end":296,"column_start":68,"column_end":80,"is_primary":true,"text":[{"text":"        input.buttons.contains(ButtonFlags::KICK) && input.buttons.just_pressed()","highlight_start":68,"highlight_end":80}],"label":"method not found in `ButtonFlags`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `just_pressed` found for struct `ButtonFlags` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:296:68\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input.buttons.contains(ButtonFlags::KICK) && input.buttons.just_pressed()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ButtonFlags`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11518,"byte_end":11526,"line_start":319,"line_end":319,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::from_i32(500)),","highlight_start":54,"highlight_end":62}],"label":"function or associated item not found in `Fixed8_8`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\nFixed8_8::from_raw\nFixed8_8::from_int\nFixed8_8::from_i16\nFixed8_8::from_f32","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3672,"byte_end":3711,"line_start":131,"line_end":131,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_raw(raw: i16) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3863,"byte_end":3901,"line_start":141,"line_end":141,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"    pub const fn from_int(int: i8) -> Self {","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3995,"byte_end":4028,"line_start":146,"line_end":146,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"    pub fn from_i16(int: i16) -> Self {","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":4117,"byte_end":4152,"line_start":151,"line_end":151,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn from_f32(float: f32) -> Self {","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `from_f32` with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11518,"byte_end":11526,"line_start":319,"line_end":319,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::from_i32(500)),","highlight_start":54,"highlight_end":62}],"label":null,"suggested_replacement":"from_f32","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:319:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::from_i32(500)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `Fixed8_8`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      Fixed8_8::from_raw\u001b[0m\n\u001b[0m      Fixed8_8::from_int\u001b[0m\n\u001b[0m      Fixed8_8::from_i16\u001b[0m\n\u001b[0m      Fixed8_8::from_f32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs:131:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_raw(raw: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_int(int: i8) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_i16(int: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_f32(float: f32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `from_f32` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;9mfrom_i32\u001b[0m\u001b[0m(100), Fixed8_8::from_i32(500)),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;10mfrom_f32\u001b[0m\u001b[0m(100), Fixed8_8::from_i32(500)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11543,"byte_end":11551,"line_start":319,"line_end":319,"column_start":79,"column_end":87,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::from_i32(500)),","highlight_start":79,"highlight_end":87}],"label":"function or associated item not found in `Fixed8_8`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\nFixed8_8::from_raw\nFixed8_8::from_int\nFixed8_8::from_i16\nFixed8_8::from_f32","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3672,"byte_end":3711,"line_start":131,"line_end":131,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_raw(raw: i16) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3863,"byte_end":3901,"line_start":141,"line_end":141,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"    pub const fn from_int(int: i8) -> Self {","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3995,"byte_end":4028,"line_start":146,"line_end":146,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"    pub fn from_i16(int: i16) -> Self {","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":4117,"byte_end":4152,"line_start":151,"line_end":151,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn from_f32(float: f32) -> Self {","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `from_f32` with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":11543,"byte_end":11551,"line_start":319,"line_end":319,"column_start":79,"column_end":87,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::from_i32(500)),","highlight_start":79,"highlight_end":87}],"label":null,"suggested_replacement":"from_f32","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:319:79\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::from_i32(500)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `Fixed8_8`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      Fixed8_8::from_raw\u001b[0m\n\u001b[0m      Fixed8_8::from_int\u001b[0m\n\u001b[0m      Fixed8_8::from_i16\u001b[0m\n\u001b[0m      Fixed8_8::from_f32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs:131:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_raw(raw: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_int(int: i8) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_i16(int: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_f32(float: f32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `from_f32` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;9mfrom_i32\u001b[0m\u001b[0m(500)),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(100), Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;10mfrom_f32\u001b[0m\u001b[0m(500)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":12056,"byte_end":12064,"line_start":333,"line_end":333,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::from_i32(300)),","highlight_start":54,"highlight_end":62}],"label":"function or associated item not found in `Fixed8_8`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\nFixed8_8::from_raw\nFixed8_8::from_int\nFixed8_8::from_i16\nFixed8_8::from_f32","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3672,"byte_end":3711,"line_start":131,"line_end":131,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_raw(raw: i16) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3863,"byte_end":3901,"line_start":141,"line_end":141,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"    pub const fn from_int(int: i8) -> Self {","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3995,"byte_end":4028,"line_start":146,"line_end":146,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"    pub fn from_i16(int: i16) -> Self {","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":4117,"byte_end":4152,"line_start":151,"line_end":151,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn from_f32(float: f32) -> Self {","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `from_f32` with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":12056,"byte_end":12064,"line_start":333,"line_end":333,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::from_i32(300)),","highlight_start":54,"highlight_end":62}],"label":null,"suggested_replacement":"from_f32","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:333:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::from_i32(300)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `Fixed8_8`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      Fixed8_8::from_raw\u001b[0m\n\u001b[0m      Fixed8_8::from_int\u001b[0m\n\u001b[0m      Fixed8_8::from_i16\u001b[0m\n\u001b[0m      Fixed8_8::from_f32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs:131:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_raw(raw: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_int(int: i8) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_i16(int: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_f32(float: f32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `from_f32` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;9mfrom_i32\u001b[0m\u001b[0m(200), Fixed8_8::from_i32(300)),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;10mfrom_f32\u001b[0m\u001b[0m(200), Fixed8_8::from_i32(300)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":12081,"byte_end":12089,"line_start":333,"line_end":333,"column_start":79,"column_end":87,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::from_i32(300)),","highlight_start":79,"highlight_end":87}],"label":"function or associated item not found in `Fixed8_8`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\nFixed8_8::from_raw\nFixed8_8::from_int\nFixed8_8::from_i16\nFixed8_8::from_f32","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3672,"byte_end":3711,"line_start":131,"line_end":131,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"    pub const fn from_raw(raw: i16) -> Self {","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3863,"byte_end":3901,"line_start":141,"line_end":141,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"    pub const fn from_int(int: i8) -> Self {","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":3995,"byte_end":4028,"line_start":146,"line_end":146,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"    pub fn from_i16(int: i16) -> Self {","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs","byte_start":4117,"byte_end":4152,"line_start":151,"line_end":151,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn from_f32(float: f32) -> Self {","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `from_f32` with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":12081,"byte_end":12089,"line_start":333,"line_end":333,"column_start":79,"column_end":87,"is_primary":true,"text":[{"text":"                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::from_i32(300)),","highlight_start":79,"highlight_end":87}],"label":null,"suggested_replacement":"from_f32","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no function or associated item named `from_i32` found for struct `Fixed8_8` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:333:79\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::from_i32(300)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `Fixed8_8`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `Fixed8_8` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      Fixed8_8::from_raw\u001b[0m\n\u001b[0m      Fixed8_8::from_int\u001b[0m\n\u001b[0m      Fixed8_8::from_i16\u001b[0m\n\u001b[0m      Fixed8_8::from_f32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/sf2ww/sf2_types/src/fixed_point.rs:131:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_raw(raw: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_int(int: i8) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_i16(int: i16) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_f32(float: f32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `from_f32` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;9mfrom_i32\u001b[0m\u001b[0m(300)),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                FighterAction::SetVelocity(Fixed8_8::from_i32(200), Fixed8_8::\u001b[0m\u001b[0m\u001b[38;5;10mfrom_f32\u001b[0m\u001b[0m(300)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":4843,"byte_end":4863,"line_start":130,"line_end":130,"column_start":28,"column_end":48,"is_primary":true,"text":[{"text":"            SpecialMoveId::TatsumakirSenpukyaku => self.execute_tatsumaki(state),","highlight_start":28,"highlight_end":48}],"label":"variant or associated item not found in `SpecialMoveId`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a variant with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":4843,"byte_end":4863,"line_start":130,"line_end":130,"column_start":28,"column_end":48,"is_primary":true,"text":[{"text":"            SpecialMoveId::TatsumakirSenpukyaku => self.execute_tatsumaki(state),","highlight_start":28,"highlight_end":48}],"label":null,"suggested_replacement":"TatsumakiSenpukyaku","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:130:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            SpecialMoveId::TatsumakirSenpukyaku => self.execute_tatsumaki(state),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvariant or associated item not found in `SpecialMoveId`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a variant with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;9mTatsumakirSenpukyaku\u001b[0m\u001b[0m => self.execute_tatsumaki(state),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;10mTatsumakiSenpukyaku\u001b[0m\u001b[0m => self.execute_tatsumaki(state),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":9829,"byte_end":9849,"line_start":270,"line_end":270,"column_start":83,"column_end":103,"is_primary":true,"text":[{"text":"            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::TatsumakirSenpukyaku)));","highlight_start":83,"highlight_end":103}],"label":"variant or associated item not found in `SpecialMoveId`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a variant with a similar name","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":9829,"byte_end":9850,"line_start":270,"line_end":270,"column_start":83,"column_end":104,"is_primary":true,"text":[{"text":"            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::TatsumakirSenpukyaku)));","highlight_start":83,"highlight_end":104}],"label":null,"suggested_replacement":"TatsumakiSenpukyaku","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no variant or associated item named `TatsumakirSenpukyaku` found for enum `SpecialMoveId` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:270:83\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::TatsumakirSenpukyaku)));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvariant or associated item not found in `SpecialMoveId`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a variant with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;9mTatsumakirSenpukyaku)\u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            return Some(FighterAction::ExecuteMove(MoveId::Special(SpecialMoveId::\u001b[0m\u001b[0m\u001b[38;5;10mTatsumakiSenpukyaku\u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `direction` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":939,"byte_end":948,"line_start":28,"line_end":28,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut direction = InputDirection::Neutral;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `direction` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:28:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut direction = InputDirection::Neutral;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `buffer_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":559,"byte_end":571,"line_start":18,"line_end":18,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":559,"byte_end":571,"line_start":18,"line_end":18,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_buffer_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `buffer_query`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:18:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_buffer_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":555,"byte_end":571,"line_start":18,"line_end":18,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/systems.rs","byte_start":555,"byte_end":559,"line_start":18,"line_end":18,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/systems.rs:18:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut buffer_query: Query<&mut InputBufferComponent, With<Fighter>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `health`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5388,"byte_end":5394,"line_start":189,"line_end":189,"column_start":69,"column_end":75,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":69,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5388,"byte_end":5394,"line_start":189,"line_end":189,"column_start":69,"column_end":75,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":69,"highlight_end":75}],"label":null,"suggested_replacement":"_health","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `health`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:189:69\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_health`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `game_logic`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":4947,"byte_end":4957,"line_start":176,"line_end":176,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":4947,"byte_end":4957,"line_start":176,"line_end":176,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_game_logic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `game_logic`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:176:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    game_logic: &mut ResMut<GameLogic>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_game_logic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5384,"byte_end":5394,"line_start":189,"line_end":189,"column_start":65,"column_end":75,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":65,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":5384,"byte_end":5388,"line_start":189,"line_end":189,"column_start":65,"column_end":69,"is_primary":true,"text":[{"text":"    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {","highlight_start":65,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:189:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (entity, mut fighter_state, mut position, mut velocity, mut health, input_buffer) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9034,"byte_end":9051,"line_start":280,"line_end":280,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"    for (_, mut fighter_state, _, mut velocity, _, _) in fighter_query.iter_mut() {","highlight_start":13,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9034,"byte_end":9038,"line_start":280,"line_end":280,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"    for (_, mut fighter_state, _, mut velocity, _, _) in fighter_query.iter_mut() {","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:280:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (_, mut fighter_state, _, mut velocity, _, _) in fighter_query.iter_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9690,"byte_end":9700,"line_start":303,"line_end":303,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/game_logic.rs","byte_start":9690,"byte_end":9700,"line_start":303,"line_end":303,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/game_logic.rs:303:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":4868,"byte_end":4872,"line_start":172,"line_end":172,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":4868,"byte_end":4872,"line_start":172,"line_end":172,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    time: Res<Time>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:172:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time: Res<Time>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `game_logic`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8707,"byte_end":8717,"line_start":297,"line_end":297,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8707,"byte_end":8717,"line_start":297,"line_end":297,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    game_logic: &mut ResMut<GameLogic>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_game_logic","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `game_logic`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:297:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m297\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    game_logic: &mut ResMut<GameLogic>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_game_logic`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `special_move_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8794,"byte_end":8813,"line_start":299,"line_end":299,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    special_move_events: &mut EventWriter<SpecialMoveEvent>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8794,"byte_end":8813,"line_start":299,"line_end":299,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    special_move_events: &mut EventWriter<SpecialMoveEvent>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"_special_move_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `special_move_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:299:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    special_move_events: &mut EventWriter<SpecialMoveEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_special_move_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8855,"byte_end":8865,"line_start":300,"line_end":300,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":8855,"byte_end":8865,"line_start":300,"line_end":300,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:300:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `physics_start`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11215,"byte_end":11228,"line_start":361,"line_end":361,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let physics_start = std::time::Instant::now();","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11215,"byte_end":11228,"line_start":361,"line_end":361,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let physics_start = std::time::Instant::now();","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_physics_start","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `physics_start`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:361:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let physics_start = std::time::Instant::now();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_physics_start`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `performance_config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11158,"byte_end":11176,"line_start":359,"line_end":359,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    performance_config: &Res<PerformanceConfig>,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":11158,"byte_end":11176,"line_start":359,"line_end":359,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    performance_config: &Res<PerformanceConfig>,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":"_performance_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `performance_config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:359:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    performance_config: &Res<PerformanceConfig>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_performance_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hit_events`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":13119,"byte_end":13129,"line_start":415,"line_end":415,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/performance.rs","byte_start":13119,"byte_end":13129,"line_start":415,"line_end":415,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    hit_events: &mut EventWriter<HitEvent>,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_hit_events","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hit_events`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/performance.rs:415:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m415\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    hit_events: &mut EventWriter<HitEvent>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hit_events`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2624,"byte_end":2644,"line_start":73,"line_end":73,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    mut collision_config: ResMut<CollisionSystemConfig>,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2624,"byte_end":2628,"line_start":73,"line_end":73,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut collision_config: ResMut<CollisionSystemConfig>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:73:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut collision_config: ResMut<CollisionSystemConfig>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2841,"byte_end":2860,"line_start":77,"line_end":77,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    mut collision_query: CollisionQuery,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":2841,"byte_end":2845,"line_start":77,"line_end":77,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    mut collision_query: CollisionQuery,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:77:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut collision_query: CollisionQuery,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":5004,"byte_end":5010,"line_start":147,"line_end":147,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":5004,"byte_end":5010,"line_start":147,"line_end":147,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:147:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &CollisionConfig,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collision_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11201,"byte_end":11216,"line_start":320,"line_end":320,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11201,"byte_end":11216,"line_start":320,"line_end":320,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"_collision_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `collision_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:320:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m320\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collision_query: &CollisionQuery,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_collision_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11239,"byte_end":11245,"line_start":321,"line_end":321,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":11239,"byte_end":11245,"line_start":321,"line_end":321,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &CollisionConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:321:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m321\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &CollisionConfig,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collision_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":12092,"byte_end":12107,"line_start":348,"line_end":348,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/collision_system.rs","byte_start":12092,"byte_end":12107,"line_start":348,"line_end":348,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    collision_query: &CollisionQuery,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"_collision_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `collision_query`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/collision_system.rs:348:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collision_query: &CollisionQuery,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_collision_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6529,"byte_end":6534,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6529,"byte_end":6534,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:190:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6771,"byte_end":6776,"line_start":196,"line_end":196,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":6771,"byte_end":6776,"line_start":196,"line_end":196,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:196:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10748,"byte_end":10753,"line_start":300,"line_end":300,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ryu.rs","byte_start":10748,"byte_end":10753,"line_start":300,"line_end":300,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ryu.rs:300:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6727,"byte_end":6732,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6727,"byte_end":6732,"line_start":190,"line_end":190,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:190:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hitboxes(&self, state: &FighterStateData) -> Vec<HitboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6828,"byte_end":6833,"line_start":194,"line_end":194,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":6828,"byte_end":6833,"line_start":194,"line_end":194,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:194:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_hurtboxes(&self, state: &FighterStateData) -> Vec<HurtboxData> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7135,"byte_end":7140,"line_start":204,"line_end":204,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7135,"byte_end":7140,"line_start":204,"line_end":204,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:204:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_hit(&self, attack: &AttackData, state: &mut FighterStateData) -> HitResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7545,"byte_end":7550,"line_start":215,"line_end":215,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":7545,"byte_end":7550,"line_start":215,"line_end":215,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:215:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_block(&self, attack: &AttackData, state: &mut FighterStateData) -> BlockResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10306,"byte_end":10311,"line_start":289,"line_end":289,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10306,"byte_end":10311,"line_start":289,"line_end":289,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:289:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_hadoken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10769,"byte_end":10774,"line_start":302,"line_end":302,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":10769,"byte_end":10774,"line_start":302,"line_end":302,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:302:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_shoryuken(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":11400,"byte_end":11405,"line_start":317,"line_end":317,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"sf2_engine/src/characters/ken.rs","byte_start":11400,"byte_end":11405,"line_start":317,"line_end":317,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msf2_engine/src/characters/ken.rs:317:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m317\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn execute_tatsumaki(&self, state: &mut FighterStateData) -> SpecialMoveResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 28 previous errors; 45 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 28 previous errors; 45 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0308, E0599, E0659.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0308, E0599, E0659.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0308`.\u001b[0m\n"}
