{"rustc": 15497389221046826682, "features": "[\"clone-impls\", \"default\", \"derive\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 13756204469667138204, "path": 12382481021088851956, "deps": [[1988483478007900009, "unicode_ident", false, 2726567092171077066], [3060637413840920116, "proc_macro2", false, 10081079551674933502], [17990358020177143287, "quote", false, 13059664243777791692]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-e04719507ef94320/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}