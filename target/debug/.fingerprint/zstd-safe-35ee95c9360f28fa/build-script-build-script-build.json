{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 17883862002600103897, "profile": 301201000881636927, "path": 14470220220077857376, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-safe-35ee95c9360f28fa/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}