{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 10095915848950297568, "path": 13535854932407606048, "deps": [[8373447648276846408, "zstd_sys", false, 3775360636638181625], [15788444815745660356, "build_script_build", false, 10562379804091412256]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-safe-a96da8e46c73a46b/dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}