(bevy::prelude::Entity, &'static mut sf2_types::FighterStateData, &'static mut components::Position, &'static mut components::Velocity)
bevy::prelude::Query<'static, 'static, (bevy::prelude::Entity, &'static mut sf2_types::FighterStateData, &'static mut components::Position, &'static mut components::Velocity), bevy::prelude::With<components::Fighter>>
bevy::prelude::Query<'_, '_, (bevy::prelude::Entity, &'static mut sf2_types::FighterStateData, &'static mut components::Position, &'static mut components::Velocity), bevy::prelude::With<components::Fighter>>
bevy::prelude::Query<'w, 's, (bevy::prelude::Entity, &'static mut sf2_types::FighterStateData, &'static mut components::Position, &'static mut components::Velocity), bevy::prelude::With<components::Fighter>>
